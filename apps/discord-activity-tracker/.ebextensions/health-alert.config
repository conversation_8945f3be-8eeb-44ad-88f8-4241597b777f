Resources:
  HealthCheckTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'health-check' ] ] }

  HealthCheckSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      Endpoint: <EMAIL>
      Protocol: email
      TopicArn:
        Ref: HealthCheckTopic

  EnvironmentHealthAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'health-check' ] ] }
      MetricName: EnvironmentHealth
      Namespace: AWS/ElasticBeanstalk
      Statistic: Average
      Period: 60
      EvaluationPeriods: 1
      Threshold: 0
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - Ref: HealthCheckTopic
      Dimensions:
        - Name: EnvironmentName
          Value:
            Ref: AWSEBEnvironmentName

Outputs:
  HealthCheckTopicArn:
    Description: "SNS topic ARN for health check notifications"
    Value:
      Ref: HealthCheckTopic
