import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateVoiceChannelConnectionUpdate1724780596608
    implements MigrationInterface
{
    name = 'CreateVoiceChannelConnectionUpdate1724780596608';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `
                CREATE TABLE "user_voice_channel_connection_update" (
                    "id" BIGSERIAL NOT NULL,
                    "discord_user_id" character varying NOT NULL,
                    "discord_user_tag" character varying NOT NULL,
                    "discord_guild_id" character varying NOT NULL,
                    "discord_guild_name" character varying NOT NULL,
                    "channel_id" character varying NOT NULL,
                    "channel_name" character varying NOT NULL,
                    "action" character varying NOT NULL,
                    "timestamp" TIMESTAMP WITH TIME ZONE NOT NULL,
                    "recorded_by" character varying NOT NULL,
                    CONSTRAINT "PK_85cf2b7fa226128cc36ac839746" PRIMARY KEY ("id")
                )
            `,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DROP TABLE "user_voice_channel_connection_update"`,
        );
    }
}
