import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateMigrationsTable1623456789001 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE IF NOT EXISTS "migrations" (
                "id" SERIAL NOT NULL, 
                "timestamp" BIGINT NOT NULL, 
                "name" character varying NOT NULL, 
                CONSTRAINT "PK_8c82d7f526340ab734260ea46be" PRIMARY KEY ("id")
            )
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP TABLE "migrations"
        `);
    }
}
