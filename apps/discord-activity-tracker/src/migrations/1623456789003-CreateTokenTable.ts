import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateTokenTable1623456789003 implements MigrationInterface {
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "token" (
                "id" SERIAL NOT NULL, 
                "token" character varying NOT NULL, 
                "client" character varying NOT NULL, 
                "valid_until" TIMESTAMPTZ NOT NULL, 
                CONSTRAINT "PK_82fae97f905930df5d62a702fc9" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            ALTER TABLE "token" ADD CONSTRAINT "UQ_d9959ee7e17e2293893444ea371" UNIQUE ("token")
        `);
        await queryRunner.query(`
            ALTER TABLE "token" ADD CONSTRAINT "UQ_03218c73c91c034d8c948ef54cd" UNIQUE ("client")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "token" DROP CONSTRAINT "UQ_03218c73c91c034d8c948ef54cd"
        `);
        await queryRunner.query(`
            ALTER TABLE "token" DROP CONSTRAINT "UQ_d9959ee7e17e2293893444ea371"
        `);
        await queryRunner.query(`
            DROP TABLE "token"
        `);
    }
}
