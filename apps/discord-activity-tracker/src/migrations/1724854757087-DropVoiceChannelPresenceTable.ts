import { MigrationInterface, QueryRunner } from 'typeorm';

export class DropVoiceChannelPresenceTable1724854757087
    implements MigrationInterface
{
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            INSERT INTO "user_voice_channel_connection_update" (
                "discord_user_id",
                "discord_user_tag",
                "discord_guild_id",
                "discord_guild_name",
                "channel_id",
                "channel_name",
                "action",
                "timestamp",
                "recorded_by"
            ) SELECT
                "discord_user_id",
                "discord_user_tag",
                "discord_guild_id",
                "discord_guild_name",
                "channel_id",
                "channel_name",
                "action",
                "timestamp",
                "recorded_by"
            FROM "user_voice_channel_presence_update"
        `);
        await queryRunner.query(
            `DROP TABLE "user_voice_channel_presence_update"`,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "user_voice_channel_presence_update" (
                "id" BIGSERIAL NOT NULL,
                "discord_user_id" character varying NOT NULL,
                "discord_user_tag" character varying NOT NULL,
                "discord_guild_id" character varying NOT NULL,
                "discord_guild_name" character varying NOT NULL,
                "channel_id" character varying NOT NULL,
                "channel_name" character varying NOT NULL,
                "action" character varying NOT NULL,
                "timestamp" TIMESTAMP WITH TIME ZONE NOT NULL,
                "recorded_by" character varying NOT NULL,
                CONSTRAINT "PK_14548cb7a2d18846311808418f1" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            INSERT INTO "user_voice_channel_presence_update" (
                "discord_user_id",
                "discord_user_tag",
                "discord_guild_id",
                "discord_guild_name",
                "channel_id",
                "channel_name",
                "action",
                "timestamp",
                "recorded_by"
            ) SELECT
                "discord_user_id",
                "discord_user_tag",
                "discord_guild_id",
                "discord_guild_name",
                "channel_id",
                "channel_name",
                "action",
                "timestamp",
                "recorded_by"
            FROM "user_voice_channel_connection_update"
        `);
    }
}
