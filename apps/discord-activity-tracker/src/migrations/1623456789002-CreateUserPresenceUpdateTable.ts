import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUserPresenceUpdateTable1623456789002
    implements MigrationInterface
{
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "user_presence_update" (
                "id" BIGSERIAL NOT NULL, 
                "discord_user_id" character varying NOT NULL, 
                "discord_guild_id" character varying NOT NULL, 
                "old_presence" character varying NOT NULL DEFAULT 'offline', 
                "new_presence" character varying NOT NULL, 
                "timestamp" TIMESTAMPTZ NOT NULL, 
                CONSTRAINT "PK_615d416c7394e6a81bf2630d962" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_1f3307a2dc487b5319a0d360ab" ON "user_presence_update" ("discord_user_id", "timestamp")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_279c097202c2b7ee1b4f477069" ON "user_presence_update" ("discord_user_id")
        `);
        await queryRunner.query(`
            CREATE INDEX "IDX_3ee7aaa95aed57af914a5ee2c4" ON "user_presence_update" ("timestamp")
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            DROP INDEX "IDX_3ee7aaa95aed57af914a5ee2c4"
        `);
        await queryRunner.query(`
            DROP INDEX "IDX_279c097202c2b7ee1b4f477069"
        `);
        await queryRunner.query(`
            DROP INDEX "IDX_1f3307a2dc487b5319a0d360ab"
        `);
        await queryRunner.query(`
            DROP TABLE "user_presence_update"
        `);
    }
}
