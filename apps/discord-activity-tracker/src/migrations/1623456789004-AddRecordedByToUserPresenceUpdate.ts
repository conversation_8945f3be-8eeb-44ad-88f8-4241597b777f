import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddRecordedByToUserPresenceUpdate1623456789004
    implements MigrationInterface
{
    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_presence_update"
            ADD COLUMN "recorded_by" character varying;
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "user_presence_update"
            DROP COLUMN "recorded_by";
        `);
    }
}
