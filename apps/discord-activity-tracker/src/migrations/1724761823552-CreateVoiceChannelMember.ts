import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateVoiceChannelMember1724761823552
    implements MigrationInterface
{
    name = 'CreateVoiceChannelMember1724761823552';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "voice_channel_member" (
                "id" BIGSERIAL NOT NULL,
                "discord_user_id" character varying NOT NULL,
                "discord_user_tag" character varying NOT NULL,
                "discord_guild_id" character varying NOT NULL,
                "discord_guild_name" character varying NOT NULL,
                "channel_id" character varying NOT NULL,
                "channel_name" character varying NOT NULL,
                "timestamp" TIMESTAMP WITH TIME ZONE NOT NULL,
                "recorded_by" character varying NOT NULL,
                CONSTRAINT "PK_50b8d65215c5bbc6f6c361447d8" PRIMARY KEY ("id")
            )
        `);
        await queryRunner.query(
            `CREATE INDEX "IDX_23b44f17ade6c0ba65b72b68f2" ON "voice_channel_member" ("timestamp") `,
        );
        await queryRunner.query(
            `CREATE INDEX "IDX_1433d005eda0aa04b4dc9c6bf8" ON "voice_channel_member" ("channel_id", "timestamp") `,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            `DROP INDEX "public"."IDX_1433d005eda0aa04b4dc9c6bf8"`,
        );
        await queryRunner.query(
            `DROP INDEX "public"."IDX_23b44f17ade6c0ba65b72b68f2"`,
        );
        await queryRunner.query(`DROP TABLE "voice_channel_member"`);
    }
}
