import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

export enum VoiceChannelConnectionAction {
    CONNECTED = 'connected',
    DISCONNECTED = 'disconnected',
}

@Entity('user_voice_channel_connection_update')
export class VoiceChannelConnectionUpdateEntity {
    @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
    id: bigint;

    @Column({ name: 'discord_user_id', type: 'varchar' })
    discordUserId: string;

    @Column({ name: 'discord_user_tag', type: 'varchar' })
    discordUserTag: string;

    @Column({ name: 'discord_guild_id', type: 'varchar' })
    discordGuildId: string;

    @Column({ name: 'discord_guild_name', type: 'varchar' })
    discordGuildName: string;

    @Column({ name: 'channel_id', type: 'varchar' })
    channelId: string;

    @Column({ name: 'channel_name', type: 'varchar' })
    channelName: string;

    @Column({ name: 'action', type: 'varchar' })
    action: VoiceChannelConnectionAction;

    @Column({ name: 'timestamp', type: 'timestamp with time zone' })
    timestamp: Date;

    @Column({ name: 'recorded_by', type: 'varchar' })
    recordedBy: string;
}
