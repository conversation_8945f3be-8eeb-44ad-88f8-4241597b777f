import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VoiceChannelConnectionTrackerService } from './voice-channel-connection-tracker.service';
import { VoiceChannelConnectionUpdateEntity } from './voice-channel-connection-update.entity';

@Module({
    imports: [TypeOrmModule.forFeature([VoiceChannelConnectionUpdateEntity])],
    providers: [VoiceChannelConnectionTrackerService],
    exports: [VoiceChannelConnectionTrackerService],
})
export class VoiceChannelConnectionTrackerModule {}
