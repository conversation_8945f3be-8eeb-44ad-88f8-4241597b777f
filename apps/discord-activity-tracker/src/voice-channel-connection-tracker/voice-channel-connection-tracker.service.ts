import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import {
    VoiceChannelConnectionAction,
    VoiceChannelConnectionUpdateEntity,
} from './voice-channel-connection-update.entity';
import { User } from '../discord/User';
import { VoiceChannel } from '../discord/VoiceChannel';

@Injectable()
export class VoiceChannelConnectionTrackerService {
    constructor(
        @InjectRepository(VoiceChannelConnectionUpdateEntity)
        private readonly voiceChannelConnectionUpdateEntityRepository: Repository<VoiceChannelConnectionUpdateEntity>,
    ) {}

    async connected(params: {
        user: User;
        channel: VoiceChannel;
        recordedBy: string;
    }): Promise<void> {
        await this.voiceChannelConnectionUpdateEntityRepository.insert({
            discordUserId: params.user.id,
            discordUserTag: params.user.tag,
            discordGuildId: params.channel.guild.id,
            discordGuildName: params.channel.guild.name,
            channelId: params.channel.id,
            channelName: params.channel.name,
            action: VoiceChannelConnectionAction.CONNECTED,
            timestamp: new Date(),
            recordedBy: params.recordedBy,
        });
    }

    async disconnected(params: {
        user: User;
        channel: VoiceChannel;
        recordedBy: string;
    }): Promise<void> {
        await this.voiceChannelConnectionUpdateEntityRepository.insert({
            discordUserId: params.user.id,
            discordUserTag: params.user.tag,
            discordGuildId: params.channel.guild.id,
            discordGuildName: params.channel.guild.name,
            channelId: params.channel.id,
            channelName: params.channel.name,
            action: VoiceChannelConnectionAction.DISCONNECTED,
            timestamp: new Date(),
            recordedBy: params.recordedBy,
        });
    }
}
