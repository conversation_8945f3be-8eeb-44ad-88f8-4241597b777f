import { Module } from '@nestjs/common';
import { HealthModule } from './health/health.module';
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PresenceTrackerModule } from './presence-tracker/presence-tracker.module';
import { AuthModule } from './auth/auth.module';
import { TrackerBotModule } from './tracker-bot/tracker-bot.module';
import { loadConfiguration } from './app-configuration/configuration';
import { AppConfigurationModule } from './app-configuration/app-configuration.module';
import { AppConfigurationService } from './app-configuration/app-configuration.service';
import { ScheduleModule } from '@nestjs/schedule';

@Module({
    imports: [
        ConfigModule.forRoot({ load: [loadConfiguration] }),
        AppConfigurationModule,
        TypeOrmModule.forRootAsync({
            imports: [AppConfigurationModule],
            inject: [AppConfigurationService],
            useFactory: (configuration: AppConfigurationService) =>
                configuration.database,
        }),
        ScheduleModule.forRoot(),
        HealthModule,
        AuthModule,
        PresenceTrackerModule,
        TrackerBotModule,
    ],
})
export class AppModule {}
