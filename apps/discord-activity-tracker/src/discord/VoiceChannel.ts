import { VoiceBasedChannel } from 'discord.js';
import { User } from './User';
import { Guild } from './Guild';

export class VoiceChannel {
    readonly id: string;

    readonly name: string;

    readonly guild: Guild;

    readonly members: User[];

    constructor(params: {
        id: string;
        name: string;
        guild: Guild;
        members: User[];
    }) {
        if (!params.id || !params.name || !params.guild) {
            throw new Error('Invalid Channel');
        }

        this.id = params.id;
        this.name = params.name;
        this.guild = params.guild;
        this.members = params.members || [];
    }

    static fromVoiceBaseChannel(channel: VoiceBasedChannel): VoiceChannel {
        return new VoiceChannel({
            id: channel.id,
            name: channel.name,
            guild: Guild.fromDiscordGuild(channel.guild),
            members: channel.members.map((guildMember) =>
                User.fromGuildMember(guildMember),
            ),
        });
    }

    toString(): string {
        return JSON.stringify({ id: this.id, name: this.name });
    }
}
