import { GuildMember } from 'discord.js';

export class User {
    readonly id: string;

    readonly tag: string;

    constructor(params: { id: string; tag: string }) {
        if (!params.id || !params.tag) {
            throw new Error('Invalid User');
        }

        this.id = params.id;
        this.tag = params.tag;
    }

    static fromGuildMember(guildMember: GuildMember): User {
        return new User({
            id: guildMember.id,
            tag: guildMember.user.tag,
        });
    }

    toString(): string {
        return JSON.stringify({ id: this.id, tag: this.tag });
    }
}
