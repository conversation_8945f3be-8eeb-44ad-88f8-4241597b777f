import { Guild as DiscordGuild } from 'discord.js';

export class Guild {
    readonly id: string;

    readonly name: string;

    constructor(params: { id: string; name: string }) {
        if (!params.id || !params.name) {
            throw new Error('Invalid Guild');
        }

        this.id = params.id;
        this.name = params.name;
    }

    static fromDiscordGuild(guild: DiscordGuild): Guild {
        return new Guild({
            id: guild.id,
            name: guild.name,
        });
    }

    toString(): string {
        return JSON.stringify({ id: this.id, name: this.name });
    }
}
