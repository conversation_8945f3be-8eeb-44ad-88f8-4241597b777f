import { Injectable } from '@nestjs/common';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { PresenceUpdateEntity } from './presence-update.entity';

@Injectable()
export class PresenceTrackerService {
    constructor(
        @InjectRepository(PresenceUpdateEntity)
        private readonly userPresenceRepository: Repository<PresenceUpdateEntity>,
    ) {}

    async trackUserPresenceUpdate(params: {
        discordUserId: string;
        discordGuildId: string;
        oldPresence: string;
        newPresence: string;
        timestamp: Date;
        recordedBy?: string;
    }): Promise<void> {
        await this.userPresenceRepository.insert(params);
    }
}
