import { Injectable } from '@nestjs/common';
import { PresenceIntervalRepository } from './presence-interval.repository';
import { Interval } from '../utils/interval';

@Injectable()
export class PresenceIntervalService {
    constructor(
        private readonly presenceIntervalRepository: PresenceIntervalRepository,
    ) {}

    async getPresenceIntervals(params: {
        discordUserId: string;
        discordGuildId: string;
        range: Interval<Date>;
    }): Promise<Interval<Date>[]> {
        return this.presenceIntervalRepository.getPresenceIntervals(params);
    }
}
