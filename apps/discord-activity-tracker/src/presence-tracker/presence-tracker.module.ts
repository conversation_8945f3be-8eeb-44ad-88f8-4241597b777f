import { Modu<PERSON> } from '@nestjs/common';
import { PresenceTrackerService } from './presence-tracker.service';
import { TypeOrmModule } from '@nestjs/typeorm';
import { PresenceUpdateEntity } from './presence-update.entity';
import { PresenceIntervalService } from './presence-interval.service';
import { PresenceIntervalRepository } from './presence-interval.repository';
import { PresenceIntervalController } from './presence-interval.controller';

@Module({
    imports: [TypeOrmModule.forFeature([PresenceUpdateEntity])],
    providers: [
        PresenceTrackerService,
        PresenceIntervalService,
        PresenceIntervalRepository,
    ],
    exports: [PresenceTrackerService],
    controllers: [PresenceIntervalController],
})
export class PresenceTrackerModule {}
