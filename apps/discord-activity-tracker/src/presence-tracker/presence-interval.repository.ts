import { PresenceUpdateEntity } from './presence-update.entity';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Injectable } from '@nestjs/common';
import { Interval } from '../utils/interval';

@Injectable()
export class PresenceIntervalRepository {
    constructor(
        @InjectRepository(PresenceUpdateEntity)
        private readonly userPresenceRepository: Repository<PresenceUpdateEntity>,
    ) {}

    async getPresenceIntervals(params: {
        discordUserId: string;
        discordGuildId: string;
        range: Interval<Date>;
    }): Promise<Interval<Date>[]> {
        const intervalsBeforeRange: { start_time: string }[] =
            await this.userPresenceRepository.query(
                `
                SELECT MAX(timestamp) AS start_time
                FROM user_presence_update
                WHERE discord_user_id = $1
                    AND discord_guild_id = $2
                    AND old_presence = 'offline'
                    AND new_presence != 'offline'
                    AND timestamp <= $3
                `,
                [
                    params.discordUserId,
                    params.discordGuildId,
                    params.range.from.toISOString(),
                ],
            );
        const intervalsAfterRange: { end_time: string }[] =
            await this.userPresenceRepository.query(
                `
                SELECT MIN(timestamp) AS end_time
                FROM user_presence_update
                WHERE discord_user_id = $1
                    AND discord_guild_id = $2
                    AND old_presence != 'offline'
                    AND new_presence = 'offline'
                    AND timestamp >= $3
                `,
                [
                    params.discordUserId,
                    params.discordGuildId,
                    params.range.to.toISOString(),
                ],
            );

        const intervals: { start_time: string; end_time: string }[] =
            await this.userPresenceRepository.query(
                `
                WITH transitions AS (
                    SELECT
                        id,
                        discord_user_id,
                        discord_guild_id,
                        old_presence,
                        new_presence,
                        timestamp,
                        CASE
                            WHEN old_presence = 'offline' AND new_presence != 'offline' THEN 'start'
                            WHEN old_presence != 'offline' AND new_presence = 'offline' THEN 'end'
                        END AS transition
                    FROM user_presence_update
                    WHERE discord_user_id = $1
                    AND discord_guild_id = $2
                    AND timestamp BETWEEN $3 AND $4
                ),
                filtered_transitions AS (
                    SELECT
                        id,
                        discord_user_id,
                        discord_guild_id,
                        old_presence,
                        new_presence,
                        timestamp,
                        transition,
                        LAG(transition) OVER (PARTITION BY discord_user_id, discord_guild_id ORDER BY timestamp) AS prev_transition
                    FROM transitions
                ),
                distinct_transitions AS (
                    SELECT
                        id,
                        discord_user_id,
                        discord_guild_id,
                        old_presence,
                        new_presence,
                        timestamp,
                        transition
                    FROM filtered_transitions
                    WHERE transition IS NOT NULL
                        AND (prev_transition IS NULL OR prev_transition != transition)
                ),
                paired_transitions AS (
                    SELECT
                        s.timestamp AS start_time,
                        MIN(e.timestamp) AS end_time
                    FROM distinct_transitions s
                    LEFT JOIN distinct_transitions e
                    ON s.discord_user_id = e.discord_user_id
                    AND s.discord_guild_id = e.discord_guild_id
                    AND e.transition = 'end'
                    AND e.timestamp > s.timestamp
                    WHERE s.transition = 'start'
                    GROUP BY s.timestamp
                ),
                final_pairs AS (
                    SELECT
                        GREATEST(start_time, $5) AS start_time,
                        LEAST(COALESCE(end_time, NOW()), $6) AS end_time
                    FROM paired_transitions
                    WHERE (end_time IS NULL OR end_time > $5)
                        AND start_time < $6
                )
                SELECT
                    start_time,
                    end_time
                FROM final_pairs
                ORDER BY start_time;
            `,
                [
                    params.discordUserId,
                    params.discordGuildId,
                    intervalsBeforeRange[0]?.start_time ??
                        params.range.from.toISOString(),
                    intervalsAfterRange[0]?.end_time ??
                        params.range.to.toISOString(),
                    params.range.from.toISOString(),
                    params.range.to.toISOString(),
                ],
            );

        return intervals.map((interval) =>
            Interval.ofDates(
                new Date(interval.start_time),
                new Date(interval.end_time),
            ),
        );
    }
}
