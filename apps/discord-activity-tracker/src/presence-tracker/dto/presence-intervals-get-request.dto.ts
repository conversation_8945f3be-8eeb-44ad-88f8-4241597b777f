import { IsDate, IsNotEmpty, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class PresenceIntervalsGetRequestDto {
    @IsString()
    @IsNotEmpty()
    discordUserId: string;

    @IsString()
    @IsNotEmpty()
    discordGuildId: string;

    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsNotEmpty()
    from: Date;

    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsNotEmpty()
    to: Date;
}
