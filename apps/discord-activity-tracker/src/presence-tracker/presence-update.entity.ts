import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

@Entity('user_presence_update')
@Index(['discordUserId', 'timestamp'])
export class PresenceUpdateEntity {
    @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
    id: bigint;

    @Index()
    @Column({ name: 'discord_user_id', type: 'varchar' })
    discordUserId: string;

    @Column({ name: 'discord_guild_id', type: 'varchar' })
    discordGuildId: string;

    @Column({ name: 'old_presence', type: 'varchar', default: 'offline' })
    oldPresence: string;

    @Column({ name: 'new_presence', type: 'varchar' })
    newPresence: string;

    @Index()
    @Column({ name: 'timestamp', type: 'timestamp with time zone' })
    timestamp: Date;

    @Column({ name: 'recorded_by', type: 'varchar', nullable: true })
    recordedBy: string | null;
}
