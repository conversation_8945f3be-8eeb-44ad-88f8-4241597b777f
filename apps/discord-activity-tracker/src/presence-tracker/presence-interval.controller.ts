import {
    BadRequestException,
    Controller,
    Get,
    Query,
    UseGuards,
    UsePipes,
    ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import * as moment from 'moment/moment';
import { PresenceIntervalsGetRequestDto } from './dto/presence-intervals-get-request.dto';
import { PresenceIntervalService } from './presence-interval.service';
import { PresenceIntervalsDto } from './dto/presence-intervals.dto';
import { IntervalDto } from './dto/interval.dto';
import { Interval } from '../utils/interval';

@UseGuards(AuthGuard)
@Controller('/presence-intervals')
export class PresenceIntervalController {
    private static readonly MAX_RANGE_DAYS = 10;

    constructor(
        private readonly presenceIntervalService: PresenceIntervalService,
    ) {}

    @Get()
    @UsePipes(new ValidationPipe({ transform: true }))
    async getWeeklyPresenceReport(
        @Query() params: PresenceIntervalsGetRequestDto,
    ): Promise<PresenceIntervalsDto> {
        this.validateRange(params);

        const intervals =
            await this.presenceIntervalService.getPresenceIntervals({
                discordUserId: params.discordUserId,
                discordGuildId: params.discordGuildId,
                range: Interval.ofDates(params.from, params.to),
            });

        return {
            discordUserId: params.discordUserId,
            discordGuildId: params.discordGuildId,
            intervals: intervals.map(IntervalDto.fromDateInterval),
        };
    }

    private validateRange(params: Interval<Date>) {
        if (
            moment(params.from).isAfter(params.to) ||
            moment(params.to).diff(params.from, 'days') >
                PresenceIntervalController.MAX_RANGE_DAYS
        ) {
            throw new BadRequestException('Invalid range');
        }
    }
}
