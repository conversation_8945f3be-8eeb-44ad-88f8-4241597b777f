import { Injectable, Logger } from '@nestjs/common';
import { Cron, CronExpression } from '@nestjs/schedule';
import { TrackerBotService } from '../tracker-bot.service';
import { AppConfigurationService } from '../../app-configuration/app-configuration.service';
import { VoiceChannelMembersTrackerService } from '../../voice-channel-members-tracker/voice-channel-members-tracker.service';

@Injectable()
export class PeriodicVoiceChannelMembersTrackerService {
    private readonly logger = new Logger(
        PeriodicVoiceChannelMembersTrackerService.name,
    );

    constructor(
        private readonly trackerBot: TrackerBotService,
        private readonly voiceChannelMembersTracker: VoiceChannelMembersTrackerService,
        private readonly configuration: AppConfigurationService,
    ) {}

    @Cron(CronExpression.EVERY_MINUTE)
    async execute(): Promise<void> {
        if (
            !this.configuration.bot.isEnabled ||
            !this.configuration.bot.isVoiceChannelMembersTrackerEnabled
        ) {
            return;
        }

        const channels = await this.trackerBot.getVoiceChannels();
        this.logger.debug(`Found ${channels.length} voice channels`);
        channels.forEach((channel) => {
            this.logger.debug(
                `Channel ${channel} members: ${channel.members.join(', ')}`,
            );
        });
        await this.voiceChannelMembersTracker.track(
            channels,
            this.configuration.bot.name,
        );
    }
}
