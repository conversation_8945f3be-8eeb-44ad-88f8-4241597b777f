import { Module } from '@nestjs/common';
import { TrackerBotService } from './tracker-bot.service';
import { Client } from 'discord.js';
import { PresenceTrackerModule } from '../presence-tracker/presence-tracker.module';
import { TrackerBotHealthService } from './tracker-bot-health.service';
import { UserPresenceEventHandlerService } from './event-handlers/user-presence-event-handler.service';
import { VoiceStateUpdateEventHandlerService } from './event-handlers/voice-state-update-event-handler.service';
import { MetaEventsHandlerService } from './event-handlers/meta-events-handler.service';
import { VoiceChannelConnectionTrackerModule } from '../voice-channel-connection-tracker/voice-channel-connection-tracker.module';
import { AppConfigurationModule } from '../app-configuration/app-configuration.module';
import { PeriodicVoiceChannelMembersTrackerService } from './cron-actions/periodic-voice-channel-members-tracker.service';
import { VoiceChannelMembersTrackerModule } from '../voice-channel-members-tracker/voice-channel-members-tracker.module';

const discordClientProvider = {
    provide: Client,
    useFactory: () => {
        return new Client({
            intents: [
                'Guilds',
                'GuildMembers',
                'GuildPresences',
                'GuildVoiceStates',
            ],
        });
    },
};

@Module({
    imports: [
        AppConfigurationModule,
        PresenceTrackerModule,
        VoiceChannelConnectionTrackerModule,
        VoiceChannelMembersTrackerModule,
    ],
    providers: [
        discordClientProvider,
        TrackerBotService,
        TrackerBotHealthService,
        MetaEventsHandlerService,
        UserPresenceEventHandlerService,
        VoiceStateUpdateEventHandlerService,
        PeriodicVoiceChannelMembersTrackerService,
    ],
    exports: [TrackerBotHealthService],
})
export class TrackerBotModule {}
