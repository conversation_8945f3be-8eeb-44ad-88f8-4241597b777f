import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Client, VoiceBasedChannel } from 'discord.js';
import { AppConfigurationService } from '../app-configuration/app-configuration.service';
import { VoiceChannel } from '../discord/VoiceChannel';

@Injectable()
export class TrackerBotService implements OnModuleInit {
    private readonly logger = new Logger(TrackerBotService.name);

    constructor(
        private readonly client: Client,
        private readonly configuration: AppConfigurationService,
    ) {}

    async onModuleInit(): Promise<void> {
        if (this.configuration.bot.isEnabled) {
            await this.login();
        } else {
            this.logger.debug('Bot is disabled');
        }
    }

    isEnabled(): boolean {
        return this.configuration.bot.isEnabled;
    }

    isReady(): boolean {
        return this.client.isReady();
    }

    async getVoiceChannels(): Promise<VoiceChannel[]> {
        if (!this.isEnabled || !this.client.isReady()) {
            this.logger.warn('Accessing voice channels while bot is not ready');
            return [];
        }

        return this.client.channels.cache
            .filter((c) => c.isVoiceBased())
            .map((c: VoiceBasedChannel) =>
                VoiceChannel.fromVoiceBaseChannel(c),
            );
    }

    private async login(): Promise<void> {
        this.logger.debug('Bot is logging in...');
        await this.client.login(this.configuration.bot.token);
        this.logger.debug('Bot is logged in');
    }
}
