import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Client, Presence } from 'discord.js';
import { PresenceTrackerService } from '../../presence-tracker/presence-tracker.service';
import { AppConfigurationService } from '../../app-configuration/app-configuration.service';

@Injectable()
export class UserPresenceEventHandlerService implements OnModuleInit {
    private readonly logger = new Logger(UserPresenceEventHandlerService.name);

    constructor(
        private readonly client: Client,
        private readonly userPresenceTracker: PresenceTrackerService,
        private readonly configuration: AppConfigurationService,
    ) {}

    onModuleInit(): void {
        if (
            this.configuration.bot.isEnabled &&
            this.configuration.bot.isPresenceTrackerEnabled
        ) {
            this.client.on('presenceUpdate', this.onPresenceUpdate.bind(this));
            this.logger.debug('User presence event handler initialized');
        } else {
            this.logger.debug('User presence event handler is disabled');
        }
    }

    private async onPresenceUpdate(
        oldPresence: Presence | null,
        newPresence: Presence,
    ): Promise<void> {
        const userTag = newPresence.user?.tag;
        const discordUserId = newPresence.userId;
        const discordGuildId = newPresence.guild?.id ?? '';
        const oldStatus = oldPresence?.status ?? 'offline';
        const newStatus = newPresence.status;
        this.logger.debug(
            `${userTag} (${discordUserId}) is now ${newStatus} (was ${oldStatus})`,
        );

        await this.userPresenceTracker.trackUserPresenceUpdate({
            discordUserId,
            discordGuildId,
            oldPresence: oldStatus,
            newPresence: newStatus,
            timestamp: new Date(),
            recordedBy: this.configuration.bot.name,
        });
    }
}
