import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { Client } from 'discord.js';
import { AppConfigurationService } from '../../app-configuration/app-configuration.service';

@Injectable()
export class MetaEventsHandlerService implements OnModuleInit {
    private readonly logger = new Logger(MetaEventsHandlerService.name);

    constructor(
        private readonly client: Client,
        private readonly configuration: AppConfigurationService,
    ) {}

    onModuleInit(): any {
        if (this.configuration.bot.isEnabled) {
            this.client.on('ready', this.onReady.bind(this));
            this.client.on('error', this.onError.bind(this));
            this.client.on('shardError', this.onShardError.bind(this));
            this.client.on('warn', this.onWarn.bind(this));
            this.client.on('debug', this.onDebug.bind(this));
            this.logger.debug('Meta events handler initialized');
        }
    }

    private async onReady(): Promise<void> {
        this.logger.debug('Client is ready');
    }

    private async onError(error: Error): Promise<void> {
        this.logger.error('Client error:', error);
    }

    private async onShardError(error: Error, shardId: number): Promise<void> {
        this.logger.error(`Shard ${shardId} error:`, error);
    }

    private async onWarn(info: string): Promise<void> {
        this.logger.warn(info);
    }

    private async onDebug(info: string): Promise<void> {
        this.logger.debug(info);
    }
}
