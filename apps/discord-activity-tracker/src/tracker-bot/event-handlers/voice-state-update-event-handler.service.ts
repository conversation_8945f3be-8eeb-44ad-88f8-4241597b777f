import { Client, VoiceState } from 'discord.js';
import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { VoiceChannelConnectionTrackerService } from '../../voice-channel-connection-tracker/voice-channel-connection-tracker.service';
import { User } from '../../discord/User';
import { VoiceChannel } from '../../discord/VoiceChannel';
import { AppConfigurationService } from '../../app-configuration/app-configuration.service';

enum VoiceStateAction {
    CONNECTED = 'connected',
    DISCONNECTED = 'disconnected',
    SWITCHED = 'switched',
    OTHER = 'other',
}

@Injectable()
export class VoiceStateUpdateEventHandlerService implements OnModuleInit {
    private readonly logger = new Logger(
        VoiceStateUpdateEventHandlerService.name,
    );

    constructor(
        private readonly client: Client,
        private readonly voiceChannelPresenceTrackerService: VoiceChannelConnectionTrackerService,
        private readonly configuration: AppConfigurationService,
    ) {}

    onModuleInit(): void {
        if (
            this.configuration.bot.isEnabled &&
            this.configuration.bot.isVoiceStateUpdateTrackerEnabled
        ) {
            this.client.on(
                'voiceStateUpdate',
                this.onVoiceStateUpdate.bind(this),
            );
            this.logger.debug('Voice state update event handler initialized');
        } else {
            this.logger.debug('Voice state update event handler is disabled');
        }
    }

    private async onVoiceStateUpdate(
        oldState: VoiceState,
        newState: VoiceState,
    ): Promise<void> {
        switch (this.getVoiceStateAction(oldState, newState)) {
            case VoiceStateAction.CONNECTED:
                await this.onConnected({
                    user: User.fromGuildMember(newState.member),
                    channel: VoiceChannel.fromVoiceBaseChannel(
                        newState.channel,
                    ),
                });
                break;
            case VoiceStateAction.DISCONNECTED:
                await this.onDisconnected({
                    user: User.fromGuildMember(oldState.member),
                    channel: VoiceChannel.fromVoiceBaseChannel(
                        oldState.channel,
                    ),
                });
                break;
            case VoiceStateAction.SWITCHED:
                await this.onSwitched({
                    user: User.fromGuildMember(newState.member),
                    oldChannel: VoiceChannel.fromVoiceBaseChannel(
                        oldState.channel,
                    ),
                    newChannel: VoiceChannel.fromVoiceBaseChannel(
                        newState.channel,
                    ),
                });
        }
    }

    private async onConnected(params: {
        user: User;
        channel: VoiceChannel;
    }): Promise<void> {
        this.logger.debug(
            `User ${params.user} connected to channel ${params.channel}`,
        );
        this.logChannelMembers(params.channel);
        await this.voiceChannelPresenceTrackerService.connected({
            ...params,
            recordedBy: this.configuration.bot.name,
        });
    }

    private async onDisconnected(params: {
        user: User;
        channel: VoiceChannel;
    }): Promise<void> {
        this.logger.debug(
            `User ${params.user} disconnected from channel ${params.channel}`,
        );
        this.logChannelMembers(params.channel);
        await this.voiceChannelPresenceTrackerService.disconnected({
            ...params,
            recordedBy: this.configuration.bot.name,
        });
    }

    private async onSwitched({
        user,
        oldChannel,
        newChannel,
    }: {
        user: User;
        oldChannel: VoiceChannel;
        newChannel: VoiceChannel;
    }): Promise<void> {
        this.logger.debug(
            `User ${user} switched from channel ${oldChannel} to ${newChannel}`,
        );
        await this.onDisconnected({ user, channel: oldChannel });
        await this.onConnected({ user, channel: newChannel });
    }

    private logChannelMembers(channel: VoiceChannel): void {
        this.logger.debug(
            `Channel ${channel} members: ${channel.members.join(', ')}`,
        );
    }

    private getVoiceStateAction(
        oldState: VoiceState,
        newState: VoiceState,
    ): VoiceStateAction | undefined {
        if (!oldState.channel && !!newState.channel) {
            return VoiceStateAction.CONNECTED;
        } else if (!!oldState.channel && !newState.channel) {
            return VoiceStateAction.DISCONNECTED;
        } else if (
            !!oldState.channel &&
            !!newState.channel &&
            oldState.channel.id !== newState.channel.id
        ) {
            return VoiceStateAction.SWITCHED;
        } else {
            return VoiceStateAction.OTHER;
        }
    }
}
