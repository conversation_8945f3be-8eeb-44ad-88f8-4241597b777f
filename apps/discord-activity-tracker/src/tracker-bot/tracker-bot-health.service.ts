import { Injectable, Logger } from '@nestjs/common';
import { TrackerBotService } from './tracker-bot.service';

@Injectable()
export class TrackerBotHealthService {
    private readonly logger = new Logger(TrackerBotHealthService.name);

    constructor(private readonly trackerBot: TrackerBotService) {}

    async isHealthy(): Promise<boolean> {
        if (!this.trackerBot.isEnabled()) {
            return true;
        }

        if (!this.trackerBot.isReady()) {
            this.logger.warn('Bot is not ready');
            return false;
        }

        return true;
    }
}
