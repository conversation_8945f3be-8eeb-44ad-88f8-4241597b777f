import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { AppConfigurationService } from './app-configuration/app-configuration.service';
import { Logger } from '@nestjs/common';

async function bootstrap() {
    const app = await NestFactory.create(AppModule);

    const config = app.get(AppConfigurationService);
    const logger = new Logger('bootstrap');

    await app.listen(config.server.port);

    logger.log(`Server is running on: ${await app.getUrl()}`);
}

bootstrap();
