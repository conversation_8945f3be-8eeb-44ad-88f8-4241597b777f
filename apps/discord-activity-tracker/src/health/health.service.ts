import { Injectable, Logger } from '@nestjs/common';
import { TrackerBotHealthService } from '../tracker-bot/tracker-bot-health.service';

@Injectable()
export class HealthService {
    private readonly logger = new Logger(HealthService.name);

    constructor(
        private readonly trackerBotHealthService: TrackerBotHealthService,
    ) {}

    async isHealthy(): Promise<boolean> {
        const isTrackerBotHealthy =
            await this.trackerBotHealthService.isHealthy();
        if (!isTrackerBotHealthy) {
            this.logger.error('Tracker bot is not healthy');
        }

        return isTrackerBotHealthy;
    }
}
