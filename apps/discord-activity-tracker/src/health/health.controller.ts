import { Controller, Get, ServiceUnavailableException } from '@nestjs/common';
import { HealthService } from './health.service';
import { HealthDto } from './dto/health.dto';

@Controller('health')
export class HealthController {
    constructor(private readonly healthService: HealthService) {}

    @Get()
    async getHealth(): Promise<HealthDto> {
        if (await this.healthService.isHealthy()) {
            return HealthDto.ok();
        }

        throw new ServiceUnavailableException();
    }
}
