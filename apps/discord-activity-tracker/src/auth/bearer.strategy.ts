import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-http-bearer';
import { InjectRepository } from '@nestjs/typeorm';
import { TokenEntity } from './token.entity';
import { Repository } from 'typeorm';

@Injectable()
export class BearerStrategy extends PassportStrategy(Strategy) {
    private readonly logger = new Logger(BearerStrategy.name);

    constructor(
        @InjectRepository(TokenEntity)
        private readonly tokenRepository: Repository<TokenEntity>,
    ) {
        super();
    }

    async validate(token: string): Promise<any> {
        const tokenEntity = await this.tokenRepository.findOne({
            where: { token },
        });

        if (!tokenEntity) {
            this.logger.debug(`Token ${token} not found`);
            throw new UnauthorizedException();
        }

        if (tokenEntity.validUntil < new Date()) {
            this.logger.debug(`Token ${token} expired`);
            throw new UnauthorizedException();
        }

        this.logger.debug(`Client ${tokenEntity.client} authenticated`);

        return { isValid: true };
    }
}
