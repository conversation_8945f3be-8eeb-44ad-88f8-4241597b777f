import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('token')
export class TokenEntity {
    @PrimaryGeneratedColumn()
    id: number;

    @Column({ name: 'token', type: 'varchar', unique: true })
    token: string;

    @Column({ name: 'client', type: 'varchar', unique: true })
    client: string;

    @Column({ name: 'valid_until', type: 'timestamptz' })
    validUntil: Date;
}
