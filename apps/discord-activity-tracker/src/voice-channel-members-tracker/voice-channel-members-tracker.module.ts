import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { VoiceChannelMemberEntity } from './voice-channel-member.entity';
import { VoiceChannelMembersTrackerService } from './voice-channel-members-tracker.service';
import { VoiceChannelPresenceIntervalService } from './voice-channel-presence-interval.service';
import { VoiceChannelPresenceIntervalController } from './voice-channel-presence-interval.controller';
import { VoiceChannelPresenceIntervalRepository } from './voice-channel-presence-interval.repository';

@Module({
    imports: [TypeOrmModule.forFeature([VoiceChannelMemberEntity])],
    providers: [
        VoiceChannelMembersTrackerService,
        VoiceChannelPresenceIntervalService,
        VoiceChannelPresenceIntervalRepository,
    ],
    exports: [VoiceChannelMembersTrackerService],
    controllers: [VoiceChannelPresenceIntervalController],
})
export class VoiceChannelMembersTrackerModule {}
