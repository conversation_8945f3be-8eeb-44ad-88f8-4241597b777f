import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Injectable } from '@nestjs/common';
import { VoiceChannel } from '../discord/VoiceChannel';
import { VoiceChannelMemberEntity } from './voice-channel-member.entity';

@Injectable()
export class VoiceChannelMembersTrackerService {
    constructor(
        @InjectRepository(VoiceChannelMemberEntity)
        private readonly voiceChannelMemberRepository: Repository<VoiceChannelMemberEntity>,
    ) {}

    async track(channels: VoiceChannel[], recordedBy: string): Promise<void> {
        const timestamp = new Date();
        const entities = channels
            .map((channel) => {
                return channel.members.map((member) => {
                    return {
                        timestamp,
                        recordedBy,
                        discordUserId: member.id,
                        discordUserTag: member.tag,
                        discordGuildId: channel.guild.id,
                        discordGuildName: channel.guild.name,
                        channelId: channel.id,
                        channelName: channel.name,
                    };
                });
            })
            .flat();
        if (entities.length > 0) {
            await this.voiceChannelMemberRepository.insert(entities);
        }
    }
}
