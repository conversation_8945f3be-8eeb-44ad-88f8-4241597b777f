import {
    BadRequestException,
    Controller,
    Get,
    Query,
    UseGuards,
    UsePipes,
    ValidationPipe,
} from '@nestjs/common';
import { AuthGuard } from '../auth/auth.guard';
import * as moment from 'moment/moment';
import { Interval } from '../utils/interval';
import { VoiceChannelPresenceIntervalService } from './voice-channel-presence-interval.service';
import { VoiceChannelPresenceIntervalsGetRequestDto } from './dto/voice-channel-presence-intervals-get-request.dto';
import { IntervalsDto } from './dto/intervals.dto';
import { IntervalDto } from './dto/interval.dto';

@UseGuards(AuthGuard)
@Controller()
export class VoiceChannelPresenceIntervalController {
    private static readonly MAX_RANGE_DAYS = 10;

    constructor(
        private readonly voiceChannelPresenceIntervalService: VoiceChannelPresenceIntervalService,
    ) {}

    @Get('/voice-channel-presence-intervals')
    @UsePipes(new ValidationPipe({ transform: true }))
    async getRoundedWeeklyPresence(
        @Query() params: VoiceChannelPresenceIntervalsGetRequestDto,
    ): Promise<IntervalsDto> {
        this.validateRange(params);

        const intervals =
            await this.voiceChannelPresenceIntervalService.getRoundedPresenceIntervals(
                {
                    discordUserId: params.discordUserId,
                    channelIds: params.discordChannelIds,
                    range: Interval.ofDates(params.from, params.to),
                },
            );

        return {
            intervals: intervals.map(IntervalDto.fromDateInterval),
        };
    }

    @Get('/v2/voice-channel-presence-intervals')
    @UsePipes(new ValidationPipe({ transform: true }))
    async getWeeklyPresence(
        @Query() params: VoiceChannelPresenceIntervalsGetRequestDto,
    ): Promise<IntervalsDto> {
        this.validateRange(params);

        const intervals =
            await this.voiceChannelPresenceIntervalService.getPresenceIntervals(
                {
                    discordUserId: params.discordUserId,
                    channelIds: params.discordChannelIds,
                    range: Interval.ofDates(params.from, params.to),
                },
            );

        return {
            intervals: intervals.map(IntervalDto.fromDateInterval),
        };
    }

    private validateRange(params: Interval<Date>) {
        if (
            moment(params.from).isAfter(params.to) ||
            moment(params.to).diff(params.from, 'days') >
                VoiceChannelPresenceIntervalController.MAX_RANGE_DAYS
        ) {
            throw new BadRequestException('Invalid range');
        }
    }
}
