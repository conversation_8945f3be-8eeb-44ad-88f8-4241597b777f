import { <PERSON><PERSON><PERSON>y, <PERSON>Date, <PERSON>NotEmpty, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class VoiceChannelPresenceIntervalsGetRequestDto {
    @IsString()
    @IsNotEmpty()
    discordUserId: string;

    @Transform(({ value }) => value.split(',').map((id: string) => id.trim()))
    @IsArray()
    @IsString({ each: true })
    @IsNotEmpty()
    discordChannelIds: string[];

    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsNotEmpty()
    from: Date;

    @Transform(({ value }) => new Date(value), { toClassOnly: true })
    @IsDate()
    @IsNotEmpty()
    to: Date;
}
