import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Interval } from '../utils/interval';
import { VoiceChannelMemberEntity } from './voice-channel-member.entity';

@Injectable()
export class VoiceChannelPresenceIntervalRepository {
    private static readonly INTERVAL_STEP_MS = 15 * 60 * 1000;

    constructor(
        @InjectRepository(VoiceChannelMemberEntity)
        private readonly voiceChannelMemberRepository: Repository<VoiceChannelMemberEntity>,
    ) {}

    async getRoundedPresenceIntervals(params: {
        discordUserId: string;
        channelIds: string[];
        range: Interval<Date>;
    }): Promise<Interval<Date>[]> {
        const adjustedRange = this.adjustRangeInterval(params.range);
        const intervals = await this.voiceChannelMemberRepository.query(
            `
                WITH intervals AS (
                    SELECT
                        start,
                        start + ($1 * INTERVAL '1 millisecond') AS end
                    FROM generate_series(
                            $2::timestamptz,
                            $3::timestamptz,
                            $1 * INTERVAL '1 millisecond'
                        ) AS start
                    WHERE start < $3::timestamp
                )
                SELECT
                    int.start,
                    int.end
                FROM voice_channel_member vcm
                JOIN intervals int ON vcm.timestamp >= int.start AND vcm.timestamp < int.end
                WHERE vcm.discord_user_id = $4 AND vcm.channel_id = ANY($5)
                GROUP BY int.start, int.end
                ORDER BY int.start
            `,
            [
                VoiceChannelPresenceIntervalRepository.INTERVAL_STEP_MS,
                adjustedRange.from,
                adjustedRange.to,
                params.discordUserId,
                params.channelIds,
            ],
        );

        return intervals.map((interval: { start: Date; end: Date }) =>
            Interval.ofDates(interval.start, interval.end),
        );
    }

    async getPresenceIntervals(params: {
        discordUserId: string;
        channelIds: string[];
        range: Interval<Date>;
    }): Promise<Interval<Date>[]> {
        const intervals = await this.voiceChannelMemberRepository.query(
            `
                WITH ranked_data AS (
                    SELECT DISTINCT ON (date_trunc('minute', timestamp))
                        date_trunc('minute', timestamp) AS rounded_timestamp,
                        date_trunc('minute', timestamp) - INTERVAL '1 minute' * ROW_NUMBER() OVER (ORDER BY date_trunc('minute', timestamp)) AS grp
                    FROM voice_channel_member
                    WHERE timestamp >= $1
                        AND timestamp <= $2
                        AND discord_user_id = $3 
                        AND channel_id = ANY($4)
                    ORDER BY date_trunc('minute', timestamp)
                )
                SELECT 
                    MIN(rounded_timestamp) AS start,
                    MAX(rounded_timestamp) AS end
                FROM ranked_data
                GROUP BY grp
                ORDER BY start
            `,
            [
                params.range.from,
                params.range.to,
                params.discordUserId,
                params.channelIds,
            ],
        );

        return intervals.map((interval: { start: Date; end: Date }) =>
            Interval.ofDates(interval.start, interval.end),
        );
    }

    /**
     * Adjusts the range to the nearest interval step.
     */
    private adjustRangeInterval(range: Interval<Date>): Interval<Date> {
        return Interval.ofDates(
            new Date(
                Math.ceil(
                    range.from.getTime() /
                        VoiceChannelPresenceIntervalRepository.INTERVAL_STEP_MS,
                ) * VoiceChannelPresenceIntervalRepository.INTERVAL_STEP_MS,
            ),
            new Date(
                Math.floor(
                    range.to.getTime() /
                        VoiceChannelPresenceIntervalRepository.INTERVAL_STEP_MS,
                ) * VoiceChannelPresenceIntervalRepository.INTERVAL_STEP_MS,
            ),
        );
    }
}
