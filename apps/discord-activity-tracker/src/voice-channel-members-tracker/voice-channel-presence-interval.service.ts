import { Injectable } from '@nestjs/common';
import { VoiceChannelPresenceIntervalRepository } from './voice-channel-presence-interval.repository';
import { Interval } from '../utils/interval';

@Injectable()
export class VoiceChannelPresenceIntervalService {
    constructor(
        private readonly voiceChannelPresenceIntervalRepository: VoiceChannelPresenceIntervalRepository,
    ) {}

    async getRoundedPresenceIntervals(params: {
        discordUserId: string;
        channelIds: string[];
        range: Interval<Date>;
    }): Promise<Interval<Date>[]> {
        return this.voiceChannelPresenceIntervalRepository.getRoundedPresenceIntervals(
            params,
        );
    }

    async getPresenceIntervals(params: {
        discordUserId: string;
        channelIds: string[];
        range: Interval<Date>;
    }): Promise<Interval<Date>[]> {
        return this.voiceChannelPresenceIntervalRepository.getPresenceIntervals(
            params,
        );
    }
}
