import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';

@Entity('voice_channel_member')
@Index(['channelId', 'timestamp'])
export class VoiceChannelMemberEntity {
    @PrimaryGeneratedColumn({ name: 'id', type: 'bigint' })
    id: bigint;

    @Column({ name: 'discord_user_id', type: 'varchar' })
    discordUserId: string;

    @Column({ name: 'discord_user_tag', type: 'varchar' })
    discordUserTag: string;

    @Column({ name: 'discord_guild_id', type: 'varchar' })
    discordGuildId: string;

    @Column({ name: 'discord_guild_name', type: 'varchar' })
    discordGuildName: string;

    @Column({ name: 'channel_id', type: 'varchar' })
    channelId: string;

    @Column({ name: 'channel_name', type: 'varchar' })
    channelName: string;

    @Index()
    @Column({ name: 'timestamp', type: 'timestamp with time zone' })
    timestamp: Date;

    @Column({ name: 'recorded_by', type: 'varchar' })
    recordedBy: string;
}
