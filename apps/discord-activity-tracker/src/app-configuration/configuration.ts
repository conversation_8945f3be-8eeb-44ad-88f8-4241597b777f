import {
    IsBoolean,
    Is<PERSON><PERSON>ber,
    IsString,
    ValidateIf,
    ValidateNested,
    validateSync,
} from 'class-validator';
import { plainToClass, Type } from 'class-transformer';

export class DatabaseConfiguration {
    @IsString()
    type = 'postgres' as const;

    @IsString()
    host: string;

    @IsNumber()
    port: number;

    @IsString()
    username: string;

    @IsString()
    password: string;

    @IsString()
    database: string;

    @IsBoolean()
    logging: boolean;

    @IsBoolean()
    synchronize: boolean;

    @IsBoolean()
    migrationsRun: boolean;

    @IsBoolean()
    autoLoadEntities: boolean;
}

export class BotConfiguration {
    @IsBoolean()
    isEnabled: boolean;

    @ValidateIf((object) => object.isEnabled)
    @IsString()
    token?: string;

    @ValidateIf((object) => object.isEnabled)
    @IsString()
    name: string;

    @IsBoolean()
    isPresenceTrackerEnabled: boolean;

    @IsBoolean()
    isVoiceStateUpdateTrackerEnabled: boolean;

    @IsBoolean()
    isVoiceChannelMembersTrackerEnabled: boolean;
}

export class ServerConfiguration {
    @IsNumber()
    port: number;
}

export class Configuration {
    @Type(() => DatabaseConfiguration)
    @ValidateNested()
    database: DatabaseConfiguration;

    @Type(() => BotConfiguration)
    @ValidateNested()
    bot: BotConfiguration;

    @Type(() => ServerConfiguration)
    @ValidateNested()
    server: ServerConfiguration;
}

export function loadConfiguration(): Configuration {
    return validateConfiguration({
        database: {
            host: process.env.DB_HOST ?? 'localhost',
            port: Number(process.env.DB_PORT || 5432),
            username: process.env.DB_USERNAME,
            password: process.env.DB_PASSWORD,
            database: process.env.DB_NAME,
            logging: process.env.DB_LOGGING === 'true',
            synchronize: false,
            migrationsRun: false,
            autoLoadEntities: true,
        },
        bot: {
            isEnabled: process.env.BOT_ENABLED === 'true',
            token: process.env.BOT_TOKEN,
            name: process.env.BOT_NAME ?? 'Unknown Bot',
            isPresenceTrackerEnabled:
                process.env.BOT_PRESENCE_TRACKER_ENABLED === 'true',
            isVoiceStateUpdateTrackerEnabled:
                process.env.BOT_VOICE_STATE_UPDATE_TRACKER_ENABLED === 'true',
            isVoiceChannelMembersTrackerEnabled:
                process.env.BOT_VOICE_CHANNEL_MEMBERS_TRACKER_ENABLED ===
                'true',
        },
        server: {
            port: Number(process.env.PORT || 3000),
        },
    });
}

function validateConfiguration(
    records: Record<string, unknown>,
): Configuration {
    const validatedConfiguration = plainToClass(Configuration, records, {
        enableImplicitConversion: true,
    });
    const errors = validateSync(validatedConfiguration, {
        skipMissingProperties: false,
    });

    if (errors.length > 0) {
        throw new Error(errors.toString());
    }

    return validatedConfiguration;
}
