import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
    ServerConfiguration,
    BotConfiguration,
    DatabaseConfiguration,
} from './configuration';

@Injectable()
export class AppConfigurationService {
    readonly database: DatabaseConfiguration;

    readonly bot: BotConfiguration;

    readonly server: ServerConfiguration;

    constructor(configService: ConfigService) {
        this.database = configService.get<DatabaseConfiguration>('database');
        this.bot = configService.get<BotConfiguration>('bot');
        this.server = configService.get<ServerConfiguration>('server');
    }
}
