{"name": "discord-activity-tracker", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "typeorm": "ts-node -r tsconfig-paths/register ./node_modules/typeorm/cli", "migration:show": "npm run typeorm migration:show -- -d ./ormconfig.ts", "migration:generate": "npm run typeorm migration:generate -- -d ./ormconfig.ts", "migration:create": "npm run typeorm migration:create --", "migration:fake": "npm run typeorm migration:run -- -d ./ormconfig.ts --fake", "migration:run": "npm run typeorm migration:run -- -d ./ormconfig.ts", "migration:revert": "npm run typeorm migration:revert -- -d ./ormconfig.ts"}, "dependencies": {"@nestjs/common": "^10.4.16", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.4.4", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.4.15", "@nestjs/schedule": "^4.1.0", "@nestjs/typeorm": "^10.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "discord.js": "^14.18.0", "dotenv": "^16.4.5", "moment": "^2.30.1", "passport-http-bearer": "^1.0.1", "pg": "^8.12.0", "postgres": "^3.4.4", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "typeorm": "^0.3.20"}, "devDependencies": {"@nestjs/cli": "^10.4.5", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/passport-http-bearer": "^1.0.41", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.7.2"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}