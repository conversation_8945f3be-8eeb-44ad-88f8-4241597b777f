// Comprehensive test suite for Enhanced Nord Pool Optimizer
// Based on the proven test scenarios from the old system

class MockNordPoolOptimizer {
  constructor(mockPrices) {
    this.mockPrices = mockPrices;
    this.baseUrl = 'https://dashboard.elering.ee/api/nps/price';
  }

  // Mock the price fetching to use test data
  async fetchTodayPrices() {
    return this.mockPrices;
  }

  getCurrentHour() {
    return 12; // Mock current hour for testing
  }

  getVilniusDate() {
    return new Date();
  }

  validateSlotOptions(options) {
    const { from = 0, to = 23, numberOfHours = 1 } = options;
    if (from < 0 || from > 23) throw new Error('Parameter "from" must be between 0 and 23');
    if (to < 0 || to > 23) throw new Error('Parameter "to" must be between 0 and 23');
    if (to <= from) throw new Error('Parameter "to" must be greater than "from"');
    if (numberOfHours < 1 || numberOfHours > 24) throw new Error('Parameter "numberOfHours" must be between 1 and 24');
    const availableHours = to - from + 1;
    if (numberOfHours > availableHours) {
      throw new Error(`Cannot fit ${numberOfHours} consecutive hours within ${from}-${to} time window (${availableHours} hours available)`);
    }
  }

  async getDailyAveragePrice() {
    const prices = await this.fetchTodayPrices();
    const sum = prices.reduce((total, price) => total + price.price, 0);
    return sum / prices.length;
  }

  async getDailyPriceExtremes() {
    const prices = await this.fetchTodayPrices();
    const cheapest = prices.reduce((min, current) => current.price < min.price ? current : min);
    const expensive = prices.reduce((max, current) => current.price > max.price ? current : max);
    return {
      cheapest: { hour: cheapest.hour, price: cheapest.price },
      expensive: { hour: expensive.hour, price: expensive.price }
    };
  }

  // Copy the enhanced methods from the main optimizer
  analyzeTimeSlot(consecutivePrices, dailyAverage) {
    const prices = consecutivePrices.map(p => p.price);
    const negativeCount = prices.filter(p => p < 0).length;
    const rawAverage = prices.reduce((sum, p) => sum + p, 0) / prices.length;
    const modifiedPrices = prices.map(p => Math.max(0, p));
    const modifiedAverage = modifiedPrices.reduce((sum, p) => sum + p, 0) / modifiedPrices.length;
    const percentAboveDailyAverage = ((rawAverage - dailyAverage) / dailyAverage) * 100;

    return {
      negativeCount,
      rawAverage,
      modifiedAverage,
      percentAboveDailyAverage,
      hasNegativePrices: negativeCount > 0
    };
  }

  findBestTimeSlot(timeSlots) {
    if (timeSlots.length === 0) return null;
    const sortedSlots = timeSlots.sort((a, b) => {
      if (a.negativeCount !== b.negativeCount) {
        return b.negativeCount - a.negativeCount;
      }
      return a.modifiedAverage - b.modifiedAverage;
    });
    return sortedSlots[0];
  }

  isAnySlotWorthRunning(timeSlots, dailyAverage) {
    if (timeSlots.some(slot => slot.hasNegativePrices)) {
      return true;
    }
    const bestSlot = this.findBestTimeSlot(timeSlots);
    if (!bestSlot) return false;
    return bestSlot.percentAboveDailyAverage <= 20;
  }

  async findOptimalTimeSlotAdvanced(options = {}) {
    const { from = 0, to = 23, numberOfHours = 1 } = options;
    this.validateSlotOptions({ from, to, numberOfHours });

    const prices = await this.fetchTodayPrices();
    const filteredPrices = prices.filter(price => price.hour >= from && price.hour <= to);
    const dailyAverage = await this.getDailyAveragePrice();
    const dailyExtremes = await this.getDailyPriceExtremes();

    const timeSlots = [];
    for (let i = 0; i <= filteredPrices.length - numberOfHours; i++) {
      const consecutivePrices = filteredPrices.slice(i, i + numberOfHours);
      const analysis = this.analyzeTimeSlot(consecutivePrices, dailyAverage);
      timeSlots.push({
        startHour: consecutivePrices[0].hour,
        endHour: consecutivePrices[consecutivePrices.length - 1].hour,
        prices: consecutivePrices.map(p => p.price),
        ...analysis
      });
    }

    const bestSlot = this.findBestTimeSlot(timeSlots);
    const isAnySlotOptimal = this.isAnySlotWorthRunning(timeSlots, dailyAverage);

    return {
      bestSlot,
      isOptimal: isAnySlotOptimal && bestSlot !== null,
      allSlots: timeSlots,
      dailyStats: { average: dailyAverage, min: dailyExtremes.cheapest.price, max: dailyExtremes.expensive.price },
      optimalityReason: isAnySlotOptimal ? 'Found optimal time slot' : 'All available time slots are significantly above daily average with no negative prices'
    };
  }

  async isOptimalTimeToStart(options = {}) {
    const analysis = await this.findOptimalTimeSlotAdvanced(options);
    if (!analysis.isOptimal || !analysis.bestSlot) return false;
    const currentHour = this.getCurrentHour();
    return currentHour === analysis.bestSlot.startHour;
  }
}

// Test scenarios adapted from your old system
const testScenarios = [
  {
    name: "Multiple negative prices spread throughout",
    prices: [
      { hour: 0, price: 56.87 }, { hour: 1, price: -1.00 }, { hour: 2, price: -2.00 },
      { hour: 3, price: 15.00 }, { hour: 4, price: -3.00 }, { hour: 5, price: 58.76 },
      { hour: 6, price: 4.90 }, { hour: 7, price: 8.01 }, { hour: 8, price: 25.00 },
      { hour: 9, price: 30.00 }, { hour: 10, price: 35.00 }, { hour: 11, price: 40.00 },
      { hour: 12, price: 45.00 }, { hour: 13, price: 50.00 }, { hour: 14, price: 55.00 },
      { hour: 15, price: 60.00 }, { hour: 16, price: 65.00 }, { hour: 17, price: 70.00 },
      { hour: 18, price: 75.00 }, { hour: 19, price: 80.00 }, { hour: 20, price: 85.00 },
      { hour: 21, price: 90.00 }, { hour: 22, price: 95.00 }, { hour: 23, price: 100.00 }
    ],
    expectedStartHour: 1, // h1-h3 has 2 negative prices
    description: "Should choose h1-h3 with 2 negative prices"
  },
  {
    name: "Negative prices at beginning",
    prices: [
      { hour: 0, price: -5.00 }, { hour: 1, price: -8.00 }, { hour: 2, price: -1.00 },
      { hour: 3, price: 30.00 }, { hour: 4, price: 45.00 }, { hour: 5, price: 58.00 },
      { hour: 6, price: 64.00 }, { hour: 7, price: 70.00 }, { hour: 8, price: 75.00 },
      { hour: 9, price: 80.00 }, { hour: 10, price: 85.00 }, { hour: 11, price: 90.00 },
      { hour: 12, price: 95.00 }, { hour: 13, price: 100.00 }, { hour: 14, price: 105.00 },
      { hour: 15, price: 110.00 }, { hour: 16, price: 115.00 }, { hour: 17, price: 120.00 },
      { hour: 18, price: 125.00 }, { hour: 19, price: 130.00 }, { hour: 20, price: 135.00 },
      { hour: 21, price: 140.00 }, { hour: 22, price: 145.00 }, { hour: 23, price: 150.00 }
    ],
    expectedStartHour: 0, // h0-h2 has 3 negative prices
    description: "Should choose h0-h2 with 3 negative prices"
  },
  {
    name: "All above day average",
    prices: [
      { hour: 0, price: 85.00 }, { hour: 1, price: 86.00 }, { hour: 2, price: 80.00 },
      { hour: 3, price: 75.00 }, { hour: 4, price: 70.00 }, { hour: 5, price: 65.00 },
      { hour: 6, price: 60.00 }, { hour: 7, price: 55.00 }, { hour: 8, price: 50.00 },
      { hour: 9, price: 45.00 }, { hour: 10, price: 40.00 }, { hour: 11, price: 35.00 },
      { hour: 12, price: 30.00 }, { hour: 13, price: 25.00 }, { hour: 14, price: 20.00 },
      { hour: 15, price: 15.00 }, { hour: 16, price: 10.00 }, { hour: 17, price: 5.00 },
      { hour: 18, price: 4.00 }, { hour: 19, price: 3.00 }, { hour: 20, price: 2.00 },
      { hour: 21, price: 1.00 }, { hour: 22, price: 0.50 }, { hour: 23, price: 0.10 }
    ],
    expectedOptimal: false, // Should not be optimal if best available is >20% above daily average
    description: "Should mark as not optimal if all available slots are significantly above daily average"
  }
];

async function runEnhancedTests() {
  console.log('=== Enhanced Nord Pool Optimizer Test Suite ===\n');
  
  let passedTests = 0;
  let totalTests = testScenarios.length;

  for (let i = 0; i < testScenarios.length; i++) {
    const scenario = testScenarios[i];
    console.log(`--- Test ${i + 1}: ${scenario.name} ---`);
    
    try {
      const optimizer = new MockNordPoolOptimizer(scenario.prices);
      const analysis = await optimizer.findOptimalTimeSlotAdvanced({ 
        from: 0, 
        to: 7, // Simulate 8-hour window like old system
        numberOfHours: 3 
      });

      console.log(`Daily average: €${analysis.dailyStats.average.toFixed(2)}/MWh`);
      console.log(`Is optimal: ${analysis.isOptimal}`);
      
      if (analysis.bestSlot) {
        console.log(`Best slot: ${analysis.bestSlot.startHour}:00-${analysis.bestSlot.endHour}:00`);
        console.log(`Negative hours: ${analysis.bestSlot.negativeCount}`);
        console.log(`Modified average: €${analysis.bestSlot.modifiedAverage.toFixed(2)}/MWh`);
        console.log(`% above daily avg: ${analysis.bestSlot.percentAboveDailyAverage.toFixed(1)}%`);
      }

      // Verify results
      let testPassed = false;
      if (scenario.expectedOptimal === false) {
        testPassed = !analysis.isOptimal;
        console.log(`Expected: Not optimal, Got: ${analysis.isOptimal ? 'Optimal' : 'Not optimal'}`);
      } else if (scenario.expectedStartHour !== undefined) {
        testPassed = analysis.isOptimal && analysis.bestSlot && analysis.bestSlot.startHour === scenario.expectedStartHour;
        console.log(`Expected start hour: ${scenario.expectedStartHour}, Got: ${analysis.bestSlot?.startHour || 'none'}`);
      }

      if (testPassed) {
        console.log('✅ PASSED');
        passedTests++;
      } else {
        console.log('❌ FAILED');
      }
      
      console.log(`Description: ${scenario.description}\n`);
      
    } catch (error) {
      console.log(`❌ FAILED: ${error.message}\n`);
    }
  }

  console.log(`=== Test Summary: ${passedTests}/${totalTests} tests passed ===`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! Enhanced logic is working correctly.');
  } else {
    console.log(`⚠️ ${totalTests - passedTests} tests failed. Review needed.`);
  }
}

// Run the tests
runEnhancedTests();
