const { execSync, exec } = require('child_process');
const util = require('util');

const execAsync = util.promisify(exec);

/**
 * Retrieves the current branch name.
 * @return {string} The name of the current Git branch.
 */
function getCurrentBranch() {
  return execSync('git symbolic-ref --short HEAD', { encoding: 'utf-8' }).trim();
}

/**
 * Switches to a specified branch and pulls the latest changes.
 * @param {string} branchName - The name of the branch to switch to.
 * @throws {Error} If the Git operation fails.
 */
function switchToBranch(branchName) {
  execSync(`git checkout ${branchName} && git pull`, { stdio: 'inherit' });
}

/**
 * Creates a new branch based on the current branch.
 * @param {string} branchName - Name of the branch to create.
 * @throws {Error} If the branch creation fails.
 */
function createBranch(branchName) {
  try {
    execSync(`git checkout -b ${branchName}`, { stdio: 'inherit' });
  } catch (error) {
    throw new Error(`Failed to create branch "${branchName}": ${error.message}`);
  }
}

/**
 * Pushes a branch to the remote repository.
 * @param {string} branchName - Name of the branch to push.
 * @throws {Error} If the push fails.
 */
function pushBranch(branchName) {
  try {
    execSync(`git push -u origin ${branchName}`, { stdio: 'inherit' });
  } catch (error) {
    throw new Error(`Failed to push branch "${branchName}" to remote: ${error.message}`);
  }
}

/**
 * Checks if a commit message contains a "[ci skip]" or "[skip ci]" message.
 * @param {string} commit - The commit hash to check.
 * @return {boolean} True if the commit message contains a skip CI message, false otherwise.
 */
function hasSkipCiMessage(commit) {
  try {
    let message = execSync(`git log -1 --format=%B ${commit}`);
    message = message.toString().toLocaleLowerCase();
    return message.includes('[ci skip]') || message.includes('[skip ci]');
  } catch (error) {
    console.warn(`Warning: Could not check commit message for ${commit}: ${error.message}`);
    return false;
  }
}

/**
 * Checks if the branch already exists.
 * @param {string} branchName - Branch name to check.
 * @returns {boolean} True if the branch exists, false otherwise.
 */
function doesBranchExist(branchName) {
  try {
    execSync(`git rev-parse --verify ${branchName}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

/**
 * Ensures that the specified Git branch exists in the repository.
 *
 * @param {string} branch - The name of the branch to verify.
 * @return {string} branch HEAD hash if the branch exists.
 * @throws {Error} If the branch does not exist in the repository.
 */
function ensureBranchExists(branch) {
  try {
    return execSync(`git rev-parse --verify "${branch}"`, {
      encoding: 'utf-8',
      stdio: ['ignore', 'pipe', 'ignore'], // Ignore stderr for cleaner errors
    }).trim();
  } catch {
    throw new Error(`Branch "${branch}" does not exist in the repository.`);
  }
}

/**
 * Checks if a given commit exists in the repository.
 * @param {string} commit
 * @return {boolean}
 */
function checkCommitExists(commit) {
  try {
    execSync(`git cat-file -e ${commit}`, { stdio: 'ignore' });
    return true;
  } catch {
    return false;
  }
}

/**
 * Get the root commit of the repository (first commit).
 *
 * @param {string} [commit='HEAD'] - The commit hash or reference to start from. Defaults to 'HEAD'.
 * @return {string|null} The root commit hash or null if not found.
 */
function getRootCommit(commit = 'HEAD') {
  try {
    const rootCommits = execSync(`git rev-list --max-parents=0 ${commit}`, { encoding: 'utf-8' })
      .trim()
      .split('\n');

    if (rootCommits.length > 1) {
      console.warn(`Warning: multiple root commits found: ${rootCommits.join(', ')}.\nUsing the first one (${rootCommits[0]}).`);
    }

    return rootCommits[0]; // Return the first root commit SHA
  } catch {
    return null; // No root commit available (e.g., shallow clone or invalid input)
  }
}

/**
 * Ensures the specified branch is available locally by fetching it from the remote repository.
 * Does not check out the branch, leaving the current branch untouched.
 *
 * @param {string} branch The branch name to fetch.
 * @throws {Error} If fetching the branch fails.
 */
function fetchBranch(branch) {
  try {
    try {
      // Check if the branch exists locally
      execSync(`git rev-parse --verify ${branch}`, { stdio: 'ignore' });
      console.log(`Branch "${branch}" already exists locally.`);
    } catch {
      // Branch does not exist locally, fetch it from remote
      console.log(`Branch "${branch}" not found locally. Fetching from remote...`);
      execSync(`git fetch origin ${branch}:${branch}`);
      console.log(`Successfully fetched branch "${branch}" from remote.`);
    }
  } catch (error) {
    throw new Error(`Failed to fetch branch "${branch}": ${error.message}`);
  }
}

/**
 * Retrieves all Git tags that match release and release candidate (RC) version patterns.
 * The method filters, processes, sorts the tags, and correctly orders RC versions
 * alongside their respective release versions.
 *
 * @return {string[]} An array of version tags sorted in ascending order. Each tag follows
 * the pattern `v{MAJOR}.{MINOR}.{PATCH}` or `v{MAJOR}.{MINOR}.{PATCH}-rc.{RC_NUMBER}`.
 * @throws {Error} Will throw an error if the tags cannot be retrieved or processed.
 */
function getAllReleaseAndRCTags(){
  try {
    // Get all tags and apply filtering and sorting equivalent to the Bash pipeline
    return execSync(
      `git tag -l | grep -E '^v[0-9]+\\.[0-9]+\\.[0-9]+(-rc\\.[0-9]+)?$' | sed -e '/-rc\\./!s/$/-rc.1000/' | sort -V | sed 's/-rc\\.1000$//'`,
      { encoding: "utf-8" }
    )
      .trim()
      .split('\n');
  } catch (error) {
    throw new Error(`Failed to get all release and RC tags: ${error.message}`);
  }
}

function getAllReleaseTags() {
  try {
    return execSync('git tag -l | grep -E \'^v[0-9]+\\.[0-9]+\\.[0-9]+$\' | sort -V', {encoding: "utf-8"})
      .trim()
      .split('\n');
  } catch (error) {
    throw new Error(`Failed to get all release tags: ${error.message}`);
  }
}

/**
 * Retrieves the commit hash for a given tag.
 * @param {string} tag - The tag name to retrieve the commit hash for.
 * @return {string} The commit hash for the specified tag.
 * @throws {Error} If no tag is provided or the Git command fails.
 */
function getTagHash(tag) {
  if (!tag) {
    throw new Error("No tag version is supplied!");
  }

  try {
    return execSync(`git rev-parse ${tag}^{commit}`, { encoding: 'utf-8' }).trim();
  } catch (error) {
    throw new Error(`Failed to get commit hash for tag "${tag}": ${error.message}`);
  }
}

/**
 * Retrieves the date of the last commit for a given tag.
 * @param {string} tag - The tag name to retrieve the date for.
 * @return {string} The date of the tag's last commit.
 * @throws {Error} If no tag is provided or the Git command fails.
 */
function getTagDate(tag) {
  if (!tag) {
    throw new Error("No tag version is supplied!");
  }

  try {
    return execSync(`git log -1 --pretty=format:"%cd" ${tag}`, { encoding: 'utf-8' }).trim();
  } catch (error) {
    throw new Error(`Failed to get commit date for tag "${tag}": ${error.message}`);
  }
}

/**
 * Retrieves an array of Git commit messages within a specified date range.
 *
 * @param {string} afterDate - The starting date for filtering commit messages. Required.
 * @param {string} [beforeDate] - The optional ending date for filtering commit messages.
 * @return {string[]} An array of commit messages that fall within the specified date range.
 * @throws {Error} Throws an error if `afterDate` is not supplied or if the Git command fails.
 */
function getCommitMessages(afterDate, beforeDate) {
  if (!afterDate) {
    throw new Error("No after date is supplied!");
  }

  try {
    let command = `git log --after="${afterDate}" --pretty=format:"%s"`;

    if (beforeDate) {
      command += ` --before="${beforeDate}"`;
    }

    return execSync(command, { encoding: "utf-8" }).trim()
      .split("\n");
  } catch (error) {
    throw new Error(`Failed to retrieve upcoming release commit messages: ${error.message}`);
  }
}

/**
 * Retrieves a remote tag from the git remote repository.
 *
 * @param {string} tagName - The name of the tag to check.
 * @returns {string} The output of the git command (tag details) if the tag exists, or an empty string if it doesn't.
 * @throws {Error} If the git command fails for any reason.
 */
function getRemoteTag(tagName) {
  try {
    return execSync(`git ls-remote --tags origin ${tagName}`).toString().trim();
  } catch (error) {
    throw new Error(`Error retrieving remote tag "${tagName}": ${error.message}`);
  }
}

/**
 * Creates an annotated Git tag with the specified name and message.
 *
 * @param {string} tagName - The name of the tag to create.
 * @param {string} tagMessage - The message for the annotated tag.
 * @throws {Error} If the git command fails.
 */
function createAnnotatedTag(tagName, tagMessage) {
  try {
    execSync(`git tag -a ${tagName} -m "${tagMessage}"`);
  } catch (error) {
    throw new Error(`Error creating annotated tag "${tagName}": ${error.message}`);
  }
}

/**
 * Pushes a Git tag to the remote repository.
 *
 * @param {string} tagName - The name of the tag to push.
 * @throws {Error} If the git command fails.
 */
function pushTag(tagName) {
  try {
    execSync(`git push origin ${tagName}`);
  } catch (error) {
    throw new Error(`Error pushing tag "${tagName}" to remote: ${error.message}`);
  }
}

/**
 * Checks if the repository has uncommitted changes or untracked files in the working directory.
 *
 * @returns {{hasChanges: boolean, statusMessage: string}} Object containing whether there are uncommitted changes
 *          or untracked files, and the standard git status message if changes exist.
 * @throws {Error} If the git status command fails
 */
function hasUncommittedChanges() {
  try {
    // Get standard git status output (one call)
    const statusMessage = execSync('git status', { encoding: 'utf-8' }).trim();

    // Check for clean working directory phrases
    // Breaking down composite phrases into individual parts for more robust detection
    const cleanPhrases = [
      'nothing to commit',
      'working tree clean',
      'working directory clean',
      'nothing added to commit'
    ];

    // If any of the clean phrases are found, we have no changes
    const hasChanges = !cleanPhrases.some(phrase => statusMessage.includes(phrase));

    // Return empty message if no changes, otherwise return full status
    return {
      hasChanges,
      statusMessage: hasChanges ? statusMessage : ''
    };
  } catch (error) {
    throw new Error(`Failed to check git status: ${error.message}`);
  }
}

/**
 * Retrieves a list of Git commit hashes associated with a specific ticket key from the Git log.
 *
 * @param {string} ticketKey - The key of the ticket to search for in the commit messages.
 * @return {Promise<string[]>} A promise that resolves to an array of commit hashes that match the ticket key.
 *                              If an error occurs, returns an empty array.
 */
async function getCommitsForTicket(ticketKey) {
  try {
    const grepCommand = `git log --all --grep="${ticketKey}" --format="%H"`;
    const { stdout } = await execAsync(grepCommand);
    return stdout.trim().split('\n').filter(Boolean);
  } catch (error) {
    console.error(`Error getting commits for ticket ${ticketKey}:`, error.message);
    return [];
  }
}

/**
 * Finds all pull request merge commits for a ticket (ignores maintenance merges)
 * @param {string} ticketKey - Ticket key to search for (e.g., "PLAT-1234")
 * @returns {Promise<string[]>} Promise resolving to an array of PR merge commit SHAs (empty if none found)
 */
async function findPRMergeCommitsForTicket(ticketKey) {
  try {
    // Find all merge commits for this ticket key
    const { stdout } = await execAsync(`git log --all --grep="${ticketKey}" --merges --pretty=format:"%H %s"`);
    const mergeCommits = stdout.trim().split('\n').filter(Boolean);

    if (mergeCommits.length === 0) return [];

    // Return all "Merge pull request" commits - these are the actual feature merges we want
    return mergeCommits
      .filter(line => line.includes('Merge pull request'))
      .map(line => line.split(' ')[0]); // Extract just the commit SHA
  } catch (err) {
    console.warn(`Could not find merge commits for ticket ${ticketKey}:`, err.message);
    return [];
  }
}

/**
 * Gets net files changed in a commit asynchronously (handles both regular and merge commits).
 * @param {string} commitSha - Commit hash
 * @returns {Promise<string[]>} Promise resolving to an array of file paths
 */
async function getFilesInCommitAsync(commitSha) {
  const { exec } = require('child_process');
  const util = require('util');
  const execAsync = util.promisify(exec);

  try {
    const { stdout: parentsStr } = await execAsync(`git show --pretty=%P -s ${commitSha}`);
    const parents = parentsStr.trim().length > 0 ? parentsStr.trim().split(' ') : [];

    if (parents.length > 1) {
      // Merge commit - show files changed in the branch being merged (parents[1])
      // This matches GitHub's behavior by comparing merge base to the branch that was merged in
      // parents[0] = branch you were on when merging, parents[1] = branch being merged in
      const { stdout: mergeBase } = await execAsync(`git merge-base ${parents[0]} ${parents[1]}`);
      const { stdout: filesOutput } = await execAsync(`git diff --name-only ${mergeBase.trim()} ${parents[1]}`);

      return filesOutput.trim().split('\n').filter(Boolean);
    } else {
      // Regular commit
      const { stdout: filesOutput } = await execAsync(`git diff-tree --no-commit-id --name-only -r ${commitSha}`);
      return filesOutput.trim().split('\n').filter(Boolean);
    }
  } catch (err) {
    console.error(`Error getting files for commit ${commitSha}:`, err.message);
    return [];
  }
}

/**
 * Gets files changed by all PR merge commits for a ticket key
 * @param {string} ticketKey - Ticket key to search for (e.g., "PLAT-1234")
 * @returns {Promise<string[]>} Promise resolving to an array of file paths from all PR merges
 */
async function getFilesFromPRMergeCommits(ticketKey) {
  try {
    const prMergeCommits = await findPRMergeCommitsForTicket(ticketKey);

    if (prMergeCommits.length === 0) {
      return [];
    }

    const allFiles = new Set();

    await Promise.allSettled(prMergeCommits.map(async (commitSha) => {
      const files = await getFilesInCommitAsync(commitSha);
      files.forEach(file => allFiles.add(file));
    }));

    return Array.from(allFiles);
  } catch (err) {
    console.warn(`Error getting files from PR merge commits for ticket ${ticketKey}:`, err.message);
    return [];
  }
}

module.exports = {
  getCurrentBranch,
  switchToBranch,
  hasSkipCiMessage,
  ensureBranchExists,
  doesBranchExist,
  checkCommitExists,
  getRootCommit,
  fetchBranch,
  getAllReleaseAndRCTags,
  getAllReleaseTags,
  getTagHash,
  getTagDate,
  getCommitMessages,
  getRemoteTag,
  createAnnotatedTag,
  pushTag,
  hasUncommittedChanges,
  createBranch,
  pushBranch,
  getCommitsForTicket,
  getFilesInCommitAsync,
  findPRMergeCommitsForTicket,
  getFilesFromPRMergeCommits,
};
