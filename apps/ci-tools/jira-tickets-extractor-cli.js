const yargs = require('yargs');
const { hideBin } = require('yargs/helpers');
const { JiraTicketsExtractor, config: { environments} } = require('./jira-tickets-extractor');

if (require.main === module) {
  const startTime = process.hrtime();

  const argv = yargs(hideBin(process.argv))
    .option('for', {
      alias: 'f',
      type: 'string',
      description: 'Environment for which Jira tickets are retrieved',
      choices: [environments.staging, environments.production],
      demandOption: true,
    })
    .usage('Usage: node $0 --for <environment>')
    .argv;

  const { for: environment } = argv;
  const { ticketCount, jiraKeysString } = new JiraTicketsExtractor(environment).run();

  if (ticketCount) {
    console.log(`Conversion is completed. ${ticketCount} Jira tickets to be added to the new release:`);
    console.log(jiraKeysString);
  } else {
    console.log('No Jira tickets are found to be added to the new release.');
  }

  const endTime = process.hrtime(startTime);
  console.log(`Execution time: ${endTime[0]}s ${endTime[1] / 1000000}ms`);
}

module.exports = JiraTicketsExtractor;
