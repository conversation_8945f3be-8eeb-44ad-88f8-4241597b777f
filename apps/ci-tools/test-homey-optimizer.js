// Test script for HomeyScript Nord Pool Optimizer
// This demonstrates the main functions you'll use in Homey flows

// For testing, we'll copy the optimizer class directly
// In HomeyScript, you would copy the entire NordPoolOptimizer class

class NordPoolOptimizer {
  constructor() {
    this.baseUrl = 'https://dashboard.elering.ee/api/nps/price';
  }

  getVilniusDate() {
    const now = new Date();
    return new Date(now.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
  }

  formatDateForAPI(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  createTodayApiUrl() {
    const today = this.getVilniusDate();
    const start = new Date(new Date(`${this.formatDateForAPI(today)}T00:00:00`).toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
    const end = new Date(new Date(`${this.formatDateForAPI(today)}T23:59:59.999`).toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
    end.setMilliseconds(999);
    return `${this.baseUrl}?start=${start.toISOString()}&end=${end.toISOString()}`;
  }

  async fetchTodayPrices() {
    const url = this.createTodayApiUrl();
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      if (!data.success || !data.data || !data.data.lt) {
        throw new Error('Invalid API response format or no Lithuanian data available');
      }
      return data.data.lt.map(item => {
        const date = new Date(item.timestamp * 1000);
        const vilniusHour = parseInt(date.toLocaleString('lt-LT', {
          timeZone: 'Europe/Vilnius',
          hour: '2-digit',
          hour12: false
        }));
        return { timestamp: item.timestamp, price: item.price, hour: vilniusHour };
      }).sort((a, b) => a.hour - b.hour);
    } catch (error) {
      throw new Error(`Failed to fetch Nord Pool prices: ${error.message}`);
    }
  }

  validateSlotOptions(options) {
    const { from = 0, to = 23, numberOfHours = 1 } = options;
    if (from < 0 || from > 23) throw new Error('Parameter "from" must be between 0 and 23');
    if (to < 0 || to > 23) throw new Error('Parameter "to" must be between 0 and 23');
    if (to <= from) throw new Error('Parameter "to" must be greater than "from"');
    if (numberOfHours < 1 || numberOfHours > 24) throw new Error('Parameter "numberOfHours" must be between 1 and 24');
    const availableHours = to - from + 1;
    if (numberOfHours > availableHours) {
      throw new Error(`Cannot fit ${numberOfHours} consecutive hours within ${from}-${to} time window (${availableHours} hours available)`);
    }
  }

  async findOptimalStartHour(options = {}) {
    const { from = 0, to = 23, numberOfHours = 1 } = options;
    this.validateSlotOptions({ from, to, numberOfHours });
    const prices = await this.fetchTodayPrices();
    if (!prices || prices.length === 0) throw new Error('No price data available for today');
    const filteredPrices = prices.filter(price => price.hour >= from && price.hour <= to);
    if (filteredPrices.length < numberOfHours) {
      throw new Error(`Not enough price data in time window ${from}-${to}. Got ${filteredPrices.length} hours, need ${numberOfHours}`);
    }
    let cheapestSum = Infinity;
    let optimalStartHour = from;
    for (let i = 0; i <= filteredPrices.length - numberOfHours; i++) {
      const consecutivePrices = filteredPrices.slice(i, i + numberOfHours);
      const sum = consecutivePrices.reduce((total, price) => total + price.price, 0);
      if (sum < cheapestSum) {
        cheapestSum = sum;
        optimalStartHour = consecutivePrices[0].hour;
      }
    }
    return optimalStartHour;
  }

  getCurrentHour() {
    const now = this.getVilniusDate();
    return now.getHours();
  }

  async isOptimalTimeNow(options = {}) {
    const optimalStartHour = await this.findOptimalStartHour(options);
    const currentHour = this.getCurrentHour();
    return currentHour === optimalStartHour;
  }

  async getCurrentPrice() {
    const prices = await this.fetchTodayPrices();
    const currentHour = this.getCurrentHour();
    const currentPriceData = prices.find(price => price.hour === currentHour);
    if (!currentPriceData) throw new Error(`No price data available for current hour ${currentHour}`);
    return currentPriceData.price;
  }

  async getDailyPriceExtremes() {
    const prices = await this.fetchTodayPrices();
    if (!prices || prices.length === 0) throw new Error('No price data available');
    const cheapest = prices.reduce((min, current) => current.price < min.price ? current : min);
    const expensive = prices.reduce((max, current) => current.price > max.price ? current : max);
    return {
      cheapest: { hour: cheapest.hour, price: cheapest.price },
      expensive: { hour: expensive.hour, price: expensive.price }
    };
  }

  async getDailyAveragePrice() {
    const prices = await this.fetchTodayPrices();
    if (!prices || prices.length === 0) throw new Error('No price data available');
    const sum = prices.reduce((total, price) => total + price.price, 0);
    return sum / prices.length;
  }
}

async function testOptimizer() {
  console.log('=== Testing HomeyScript Nord Pool Optimizer ===\n');
  
  const optimizer = new NordPoolOptimizer();

  try {
    // Test 1: Basic optimal hour finding
    console.log('1. Finding optimal start hour for 2-hour appliance:');
    const optimalHour = await optimizer.findOptimalStartHour({ numberOfHours: 2 });
    console.log(`   Result: ${optimalHour}:00 (appliance should run from ${optimalHour}:00 to ${optimalHour + 1}:59)`);

    // Test 2: Check if now is optimal
    console.log('\n2. Is current time optimal for 2-hour run?');
    const isOptimalNow = await optimizer.isOptimalTimeNow({ numberOfHours: 2 });
    const currentHour = optimizer.getCurrentHour();
    console.log(`   Current hour: ${currentHour}:00`);
    console.log(`   Is optimal: ${isOptimalNow}`);

    // Test 3: Current price vs average
    console.log('\n3. Current price analysis:');
    const currentPrice = await optimizer.getCurrentPrice();
    const averagePrice = await optimizer.getDailyAveragePrice();
    const isBelowAverage = currentPrice < averagePrice;
    console.log(`   Current price: €${currentPrice.toFixed(2)}/MWh`);
    console.log(`   Daily average: €${averagePrice.toFixed(2)}/MWh`);
    console.log(`   Current is below average: ${isBelowAverage}`);

    // Test 4: Daily extremes
    console.log('\n4. Daily price extremes:');
    const extremes = await optimizer.getDailyPriceExtremes();
    console.log(`   Cheapest: ${extremes.cheapest.hour}:00 (€${extremes.cheapest.price.toFixed(2)}/MWh)`);
    console.log(`   Most expensive: ${extremes.expensive.hour}:00 (€${extremes.expensive.price.toFixed(2)}/MWh)`);

    // Test 5: Daytime optimal hour
    console.log('\n5. Optimal hour during daytime (8:00-18:00):');
    const daytimeOptimal = await optimizer.findOptimalStartHour({ 
      from: 8, 
      to: 18, 
      numberOfHours: 1 
    });
    console.log(`   Result: ${daytimeOptimal}:00`);

    // Test 6: Validation error handling
    console.log('\n6. Testing validation (should show error):');
    try {
      await optimizer.findOptimalStartHour({ from: 20, to: 15, numberOfHours: 1 });
    } catch (error) {
      console.log(`   Expected error: ${error.message}`);
    }

    console.log('\n=== All tests completed successfully! ===');
    console.log('\nFor Homey flows, you can use these patterns:');
    console.log('- Return boolean: await optimizer.isOptimalTimeNow({ numberOfHours: 2 })');
    console.log('- Return number: await optimizer.findOptimalStartHour({ numberOfHours: 3 })');
    console.log('- Compare prices: (await optimizer.getCurrentPrice()) < (await optimizer.getDailyAveragePrice())');

  } catch (error) {
    console.error('Test failed:', error.message);
  }
}

// Run the test
testOptimizer();
