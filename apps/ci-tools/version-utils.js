const fs = require('fs');
const path = require('path');

const { getRemoteTag } = require('./git-utils');

// Path to the central version.json file at the repository root
const VERSION_FILE_PATH = path.join(__dirname, '../../version.json');

/**
 * @typedef {Object} Version
 * @property {string} version - The version string.
 */

/**
 * Gets the current version from the central version.json file
 * @returns {Version} The version object with version string
 * @throws {Error} If the version file cannot be read or parsed
 */
function getVersion() {
  try {
    return JSON.parse(fs.readFileSync(VERSION_FILE_PATH, 'utf8'));
  } catch (error) {
    throw new Error(`Failed to read version file "${VERSION_FILE_PATH}": ${error.message}`);
  }
}

/**
 * Updates the version in the central version.json file
 * @param {string} newVersion - The new version string
 * @throws {Error} If the version file cannot be written
 */
function updateVersion(newVersion) {
  const versionData = {
    version: newVersion
  };

  try {
    fs.writeFileSync(VERSION_FILE_PATH, JSON.stringify(versionData, null, 2), 'utf8');
  } catch (error) {
    throw new Error(`Failed to update version file "${VERSION_FILE_PATH}": ${error.message}`);
  }
}

/**
 * @typedef {Object} ParsedVersion
 * @property {string} type - The type of branch (e.g., release, patch, hotfix).
 * @property {string} version - The version string (e.g., X.Y.Z).
 * @property {number} major - The major version number.
 * @property {number} minor - The minor version number.
 * @property {number} patch - The patch version number.
 */

/**
 * Parse a branch name and extract version information
 * Format: (release|patch|hotfix)/vX.Y.Z
 * @param {string} branchName - Branch name to parse
 * @returns {ParsedVersion|null} Parsed version info or null if invalid format
 */
function parseVersionFromBranch(branchName) {
  const regex = /^(release|patch|hotfix)\/v(\d+)\.(\d+)\.(\d+)$/;
  const match = branchName.match(regex);

  if (!match) {
    return null;
  }

  return {
    type: match[1],
    version: `${match[2]}.${match[3]}.${match[4]}`,
    major: parseInt(match[2], 10),
    minor: parseInt(match[3], 10),
    patch: parseInt(match[4], 10)
  };
}

/**
 * Check if a branch name adheres to the version convention
 * @param {string} branchName - Branch name to check
 * @returns {boolean} Whether the branch name is valid
 */
function isValidVersionBranch(branchName) {
  return parseVersionFromBranch(branchName) !== null;
}

/**
 * Create a release candidate version from a base version
 * @param {string} baseVersion - Base version (X.Y.Z)
 * @param {number} rcNumber - Release candidate number
 * @returns {string} Release candidate version string
 */
function createReleaseCandidate(baseVersion, rcNumber = 0) {
  return `${baseVersion}-rc.${rcNumber}`;
}

/**
 * Increment the rc number in a release candidate version
 * @param {string} rcVersion - Current release candidate version
 * @returns {string|null} Incremented release candidate version or null if invalid format
 */
function incrementReleaseCandidate(rcVersion) {
  const regex = /^(\d+\.\d+\.\d+)-rc\.(\d+)$/;
  const match = rcVersion.match(regex);

  if (!match) {
    return null;
  }

  const baseVersion = match[1];
  const rcNumber = parseInt(match[2], 10);

  return `${baseVersion}-rc.${rcNumber + 1}`;
}

/**
 * Check if a version is a release candidate
 * @param {string} version - Version to check
 * @returns {boolean} Whether the version is a release candidate
 */
function isReleaseCandidate(version) {
  return version.includes('-rc.');
}

/**
 * Extract the base version from a release candidate
 * @param {string} rcVersion - Release candidate version
 * @returns {string} Base version without RC suffix
 */
function getBaseVersionFromRc(rcVersion) {
  const regex = /^(\d+\.\d+\.\d+)-rc\.\d+$/;
  const match = rcVersion.match(regex);

  return match ? match[1] : rcVersion;
}

/**
 * Ensures that a tag does not already exist on the remote repository.
 *
 * @param {string} tagName - The name of the tag to check.
 * @throws {Error} If the tag already exists remotely.
 */
function ensureTagDoesNotExistRemotely(tagName) {
  const remoteTagExistsError = `Tag ${tagName} already exists on remote.`;
  const remoteTags = getRemoteTag(tagName);

  if (remoteTags) {
    throw new Error(remoteTagExistsError);
  }
}


module.exports = {
  getVersion,
  updateVersion,
  parseVersionFromBranch,
  isValidVersionBranch,
  createReleaseCandidate,
  incrementReleaseCandidate,
  isReleaseCandidate,
  getBaseVersionFromRc,
  ensureTagDoesNotExistRemotely,
};
