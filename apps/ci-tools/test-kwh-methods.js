// Test the updated kWh methods

// Mock optimizer with the updated methods
class MockNordPoolOptimizer {
  constructor() {
    this.mockPrices = [
      { hour: 0, price: 45.50 },
      { hour: 1, price: -5.20 },  // Negative price
      { hour: 2, price: 12.30 },
      { hour: 3, price: 25.60 },
      { hour: 4, price: 35.40 }
    ];
  }

  async fetchTodayPrices() {
    return this.mockPrices;
  }

  getCurrentHour() {
    return 1; // Mock current hour
  }

  processPrice(originalPrice) {
    const billingPrice = Math.max(0, originalPrice);
    const originalKwh = originalPrice / 1000;
    const billingKwh = billingPrice / 1000;
    const displayPrice = originalPrice < 0 ? 
      `${billingKwh.toFixed(5)} (${originalKwh.toFixed(5)})` : 
      originalKwh.toFixed(5);
    
    return {
      original: originalPrice,
      billing: billingPrice,
      display: displayPrice,
      isNegative: originalPrice < 0,
      originalKwh: originalKwh,
      billingKwh: billingKwh
    };
  }

  async getCurrentPrice() {
    const prices = await this.fetchTodayPrices();
    const currentHour = this.getCurrentHour();
    const currentPriceData = prices.find(price => price.hour === currentHour);
    return this.processPrice(currentPriceData.price);
  }

  async getCurrentBillingPrice() {
    const priceInfo = await this.getCurrentPrice();
    return priceInfo.billingKwh; // Now returns EUR/kWh
  }

  async getDailyAveragePrice() {
    const prices = await this.fetchTodayPrices();
    const sum = prices.reduce((total, price) => {
      const billingPrice = Math.max(0, price.price);
      return total + billingPrice;
    }, 0);
    const averageMWh = sum / prices.length;
    return averageMWh / 1000; // Convert to EUR/kWh
  }

  async getDailyPriceExtremes() {
    const prices = await this.fetchTodayPrices();
    const cheapest = prices.reduce((min, current) => {
      const currentBilling = Math.max(0, current.price);
      const minBilling = Math.max(0, min.price);
      return currentBilling < minBilling ? current : min;
    });

    const expensive = prices.reduce((max, current) => {
      const currentBilling = Math.max(0, current.price);
      const maxBilling = Math.max(0, max.price);
      return currentBilling > maxBilling ? current : max;
    });

    return {
      cheapest: { 
        hour: cheapest.hour, 
        price: this.processPrice(cheapest.price)
      },
      expensive: { 
        hour: expensive.hour, 
        price: this.processPrice(expensive.price)
      }
    };
  }
}

async function testKwhMethods() {
  console.log('=== Testing Updated kWh Methods ===\n');
  
  const optimizer = new MockNordPoolOptimizer();

  try {
    // Test 1: getCurrentPrice (returns ProcessedPrice)
    console.log('1. getCurrentPrice() - Returns ProcessedPrice:');
    const currentPrice = await optimizer.getCurrentPrice();
    console.log(`   Original: €${currentPrice.original}/MWh (€${currentPrice.originalKwh.toFixed(5)}/kWh)`);
    console.log(`   Billing: €${currentPrice.billing}/MWh (€${currentPrice.billingKwh.toFixed(5)}/kWh)`);
    console.log(`   Display: €${currentPrice.display}/kWh`);
    console.log(`   Is negative: ${currentPrice.isNegative}`);

    // Test 2: getCurrentBillingPrice (returns number in EUR/kWh)
    console.log('\n2. getCurrentBillingPrice() - Returns EUR/kWh:');
    const billingPrice = await optimizer.getCurrentBillingPrice();
    console.log(`   Billing price: €${billingPrice.toFixed(5)}/kWh`);

    // Test 3: getDailyAveragePrice (returns number in EUR/kWh)
    console.log('\n3. getDailyAveragePrice() - Returns EUR/kWh:');
    const averagePrice = await optimizer.getDailyAveragePrice();
    console.log(`   Daily average: €${averagePrice.toFixed(5)}/kWh`);

    // Test 4: Price comparison (both in EUR/kWh)
    console.log('\n4. Price Comparison (both in EUR/kWh):');
    const isBelowAverage = billingPrice < averagePrice;
    console.log(`   Current billing: €${billingPrice.toFixed(5)}/kWh`);
    console.log(`   Daily average: €${averagePrice.toFixed(5)}/kWh`);
    console.log(`   Is current below average: ${isBelowAverage}`);

    // Test 5: Price extremes
    console.log('\n5. Daily Price Extremes:');
    const extremes = await optimizer.getDailyPriceExtremes();
    console.log(`   Cheapest: ${extremes.cheapest.hour}:00 - €${extremes.cheapest.price.display}/kWh`);
    console.log(`   Most expensive: ${extremes.expensive.hour}:00 - €${extremes.expensive.price.display}/kWh`);

    // Test 6: Manual calculation verification
    console.log('\n6. Manual Calculation Verification:');
    console.log('   Mock prices: [45.50, -5.20, 12.30, 25.60, 35.40] EUR/MWh');
    console.log('   Billing prices: [45.50, 0, 12.30, 25.60, 35.40] EUR/MWh (negative = 0)');
    console.log('   Sum: 118.80 EUR/MWh, Average: 23.76 EUR/MWh');
    console.log('   Converted to kWh: 23.76 ÷ 1000 = 0.02376 EUR/kWh');
    console.log(`   Method result: €${averagePrice.toFixed(5)}/kWh ✓`);

    console.log('\n✅ All kWh methods working correctly!');
    console.log('\n📋 Summary:');
    console.log('   - getCurrentPrice(): Returns ProcessedPrice object');
    console.log('   - getCurrentBillingPrice(): Returns number in EUR/kWh');
    console.log('   - getDailyAveragePrice(): Returns number in EUR/kWh');
    console.log('   - All price comparisons now use consistent EUR/kWh units');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testKwhMethods();
