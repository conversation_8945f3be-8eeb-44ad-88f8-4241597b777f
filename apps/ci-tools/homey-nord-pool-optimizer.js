// =========================================================================================
// HomeyScript: Nord Pool Electricity Price Optimizer
// Fetches Lithuanian electricity prices from Elering API and finds optimal time slots
// for running appliances based on cheapest consecutive hours.
// =========================================================================================

// Constants
const ELERING_API_BASE_URL = 'https://dashboard.elering.ee/api/nps/price';
const VILNIUS_TIMEZONE = 'Europe/Vilnius';
const LITHUANIAN_LOCALE = 'lt-LT';
const INTERNAL_LOCALE = 'en-US'; // Used for internal calculations to ensure predictable parsing
const DATE_FORMAT_LOCALE = 'en-CA'; // Used for YYYY-MM-DD date formatting for API URLs
const MIN_HOUR = 0;
const MAX_HOUR = 23;
const MIN_DURATION = 1;
const MAX_DURATION = 24;

/**
 * @typedef {Object} PriceData
 * @property {number} timestamp - Unix timestamp in seconds
 * @property {number} price - Price in EUR/kWh (converted from API's EUR/MWh)
 * @property {number} hour - Hour of the day (0-23) in Vilnius timezone
 */

/**
 * @typedef {Object} ProcessedPrice
 * @property {number} original - Original price in EUR/kWh (can be negative)
 * @property {number} billing - Billing price used by electricity provider in EUR/kWh (negative prices = 0)
 * @property {string} display - Formatted display string in EUR/kWh with 5 decimal places (e.g., "0.00000 (-0.00520)" for negatives)
 * @property {boolean} isNegative - True if original price was negative
 */

/**
 * @typedef {Object} OptimalSlotOptions
 * @property {number} [from=0] - Starting hour (0-23)
 * @property {number} [to=23] - Ending hour (0-23)
 * @property {number} [numberOfHours=1] - Consecutive hours needed (1-24)
 */

class NordPoolOptimizer {
  constructor() {
    this.baseUrl = ELERING_API_BASE_URL;
    this.priceCache = null;
    this.cacheDate = null;
  }

  /**
   * Get current date string in Vilnius timezone (YYYY-MM-DD format)
   * @returns {string} Current date in Vilnius timezone as YYYY-MM-DD string
   */
  getTodayInVilnius() {
    const now = new Date();
    // Using DATE_FORMAT_LOCALE to ensure reliable YYYY-MM-DD format for API URLs
    // Lithuanian locale might return different format that could break API calls
    return new Intl.DateTimeFormat(DATE_FORMAT_LOCALE, {
      timeZone: VILNIUS_TIMEZONE
    }).format(now);
  }

  /**
   * Convert a time in Vilnius timezone to UTC
   * @param {string} dateString - Date string in YYYY-MM-DD format
   * @param {string} timeString - Time string in HH:MM:SS.mmm format
   * @returns {Date} UTC Date object representing the Vilnius time
   */
  convertVilniusTimeToUTC(dateString, timeString) {
    // Use the most reliable cross-platform approach:
    // Create a date representing the time we want in Vilnius, then convert to UTC

    // Step 1: Create a date object for the target time
    const targetDateTime = `${dateString} ${timeString}`;

    // Step 2: Parse this as if it were in Vilnius timezone
    // We'll use the inverse of toLocaleString - create a date that when converted
    // to Vilnius time gives us our target time

    // Create a temporary date to find the offset (use a fixed time to avoid millisecond drift)
    const referenceTime = new Date(`${dateString} 12:00:00`);
    const referenceUTC = referenceTime.getTime();
    // Using INTERNAL_LOCALE for internal timezone calculations to ensure predictable parsing
    const referenceVilnius = new Date(referenceTime.toLocaleString(INTERNAL_LOCALE, { timeZone: VILNIUS_TIMEZONE })).getTime();
    const offsetMs = referenceUTC - referenceVilnius;

    // Create our target time and apply the offset
    const targetLocal = new Date(targetDateTime);
    const targetUTC = new Date(targetLocal.getTime() + offsetMs);

    return targetUTC;
  }

  /**
   * Create API URL for fetching today's prices
   * @returns {string} Complete API URL
   */
  createTodayApiUrl() {
    // Get today's date in Vilnius timezone
    const todayInVilnius = this.getTodayInVilnius();

    // Convert start and end of day from Vilnius timezone to UTC
    const startUTC = this.convertVilniusTimeToUTC(todayInVilnius, '00:00:00.000');
    const endUTC = this.convertVilniusTimeToUTC(todayInVilnius, '23:59:59.999');

    // TODO: comment out after debugging is done
    // console.log('new Date()', new Date());
    // console.log('new Date().toLocaleString()', new Date().toLocaleString());
    // console.log('todayInVilnius', todayInVilnius);
    // console.log('startUTC', startUTC);
    // console.log('endUTC', endUTC);
    // console.log('start iso', startUTC.toISOString());
    // console.log('end iso', endUTC.toISOString());

    return `${this.baseUrl}?start=${startUTC.toISOString()}&end=${endUTC.toISOString()}`;
  }

  /**
   * Fetch Lithuanian electricity prices for today (with caching)
   * @returns {Promise<PriceData[]>} Array of price data for Lithuanian market
   * @throws {Error} If API request fails
   */
  async fetchTodayPrices() {
    const today = this.getTodayInVilnius();

    // Return cached data if available for today
    if (this.priceCache && this.cacheDate === today) {
      return this.priceCache;
    }

    const url = this.createTodayApiUrl();
    console.log("Fetching prices for " + today + " from " + url);

    try {
      const response = await fetch(url);

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();

      if (!data.success) {
        throw new Error('API returned unsuccessful response');
      }

      if (!data.data) {
        throw new Error('API response missing data field');
      }

      if (!data.data.lt) {
        throw new Error('No Lithuanian electricity price data available in API response');
      }

      // Process Lithuanian price data and add hour information
      // Convert from EUR/MWh to EUR/kWh immediately for consistency
      const processedData = data.data.lt.map(item => {
        const date = new Date(item.timestamp * 1000);
        const vilniusHour = parseInt(date.toLocaleString(LITHUANIAN_LOCALE, {
          timeZone: VILNIUS_TIMEZONE,
          hour: '2-digit',
          hour12: false
        }));

        return {
          timestamp: item.timestamp,
          price: item.price / 1000, // Convert EUR/MWh to EUR/kWh
          hour: vilniusHour
        };
      }).sort((a, b) => a.hour - b.hour); // Sort by hour to ensure 0-23 order

      // Cache the processed data
      this.priceCache = processedData;
      this.cacheDate = today;

      return processedData;

    } catch (error) {
      throw new Error(`Failed to fetch Nord Pool prices: ${error.message}`);
    }
  }

  /**
   * Process price according to electricity provider logic (negative prices = 0)
   * Formats EUR/kWh prices with 5 decimal places
   * @param {number} originalPrice - Original price in EUR/kWh (can be negative)
   * @returns {ProcessedPrice} Processed price with billing and display values
   */
  processPrice(originalPrice) {
    const billingPrice = Math.max(0, originalPrice); // Provider treats negative as 0

    // Format display with 5 decimal places
    const displayPrice = originalPrice < 0 ?
      `${billingPrice.toFixed(5)} (${originalPrice.toFixed(5)})` :
      originalPrice.toFixed(5);

    return {
      original: originalPrice,          // EUR/kWh
      billing: billingPrice,           // EUR/kWh (negative = 0)
      display: displayPrice,           // Formatted display string
      isNegative: originalPrice < 0
    };
  }

  /**
   * Validate parameters for finding optimal time slots
   * @param {OptimalSlotOptions} options - Options to validate
   * @throws {Error} If validation fails
   */
  validateSlotOptions(options) {
    const { from = MIN_HOUR, to = MAX_HOUR, numberOfHours = MIN_DURATION } = options;

    // Validate hour ranges
    if (from < MIN_HOUR || from > MAX_HOUR) {
      throw new Error(`Parameter "from" must be between ${MIN_HOUR} and ${MAX_HOUR}`);
    }
    if (to < MIN_HOUR || to > MAX_HOUR) {
      throw new Error(`Parameter "to" must be between ${MIN_HOUR} and ${MAX_HOUR}`);
    }
    if (to <= from) {
      throw new Error('Parameter "to" must be greater than "from"');
    }

    // Validate numberOfHours
    if (numberOfHours < MIN_DURATION || numberOfHours > MAX_DURATION) {
      throw new Error(`Parameter "numberOfHours" must be between ${MIN_DURATION} and ${MAX_DURATION}`);
    }

    // Check if the numberOfHours fits within the time window
    const availableHours = to - from + 1;
    if (numberOfHours > availableHours) {
      throw new Error(`Cannot fit ${numberOfHours} consecutive hours within ${from}-${to} time window (${availableHours} hours available)`);
    }
  }

  /**
   * Find the cheapest consecutive time period from filtered price data
   * Uses billing prices (negative prices treated as 0) for calculations
   * @param {Array} filteredPrices - Array of price objects filtered to time window (prices in EUR/kWh)
   * @param {number} numberOfHours - Number of consecutive hours needed
   * @returns {number} Starting hour of the cheapest consecutive period
   */
  findCheapestConsecutivePeriod(filteredPrices, numberOfHours) {
    let cheapestSum = Infinity;
    let optimalStartHour = filteredPrices[0].hour;

    for (let i = 0; i <= filteredPrices.length - numberOfHours; i++) {
      const consecutivePrices = filteredPrices.slice(i, i + numberOfHours);
      // Use billing prices (negative = 0) for calculations
      const sum = consecutivePrices.reduce((total, price) => {
        const billingPrice = Math.max(0, price.price); // price is already in EUR/kWh
        return total + billingPrice;
      }, 0);

      if (sum < cheapestSum) {
        cheapestSum = sum;
        optimalStartHour = consecutivePrices[0].hour;
      }
    }

    return optimalStartHour;
  }

  /**
   * Get the best start hour for the cheapest consecutive time slot
   * @param {OptimalSlotOptions} [options={}] - Options for finding best slot
   * @returns {Promise<number>} Best start hour (0-23)
   * @throws {Error} If validation fails or no data available
   */
  async getBestStartHour(options = {}) {
    // Set defaults and validate
    const { from = 0, to = 23, numberOfHours = 1 } = options;
    this.validateSlotOptions({ from, to, numberOfHours });

    // Fetch today's prices
    const prices = await this.fetchTodayPrices();
    // TODO: comment out after debugging is done
    console.log(prices);

    if (!prices || prices.length === 0) {
      throw new Error('No price data available for today');
    }

    // Filter prices to the specified time window
    const filteredPrices = prices.filter(price => price.hour >= from && price.hour <= to);

    if (filteredPrices.length < numberOfHours) {
      throw new Error(`Not enough price data in time window ${from}-${to}. Got ${filteredPrices.length} hours, need ${numberOfHours}`);
    }

    // Find and return the cheapest consecutive period start hour
    return this.findCheapestConsecutivePeriod(filteredPrices, numberOfHours);
  }

  /**
   * Check if current hour is the start of the cheapest consecutive time slot
   * @param {OptimalSlotOptions} [options={}] - Options for finding optimal slot
   * @returns {Promise<boolean>} True if current hour is the start of the cheapest period
   * @throws {Error} If validation fails or no data available
   */
  async isOptimalTimeToStart(options = {}) {
    // Get the optimal start hour
    const optimalStartHour = await this.getOptimalStartHour(options);

    // Check if current hour matches the optimal start hour
    const currentHour = this.getCurrentHour();
    return currentHour === optimalStartHour;
  }

  /**
   * Get current hour in Vilnius timezone
   * @returns {number} Current hour (0-23)
   */
  getCurrentHour() {
    const now = new Date();
    const vilniusHour = parseInt(now.toLocaleString(LITHUANIAN_LOCALE, {
      timeZone: VILNIUS_TIMEZONE,
      hour: '2-digit',
      hour12: false
    }));
    return vilniusHour;
  }

  /**
   * Get current price for this hour (returns billing price, but includes original)
   * @returns {Promise<ProcessedPrice>} Current price information with billing and display values
   */
  async getCurrentPrice() {
    const prices = await this.fetchTodayPrices();
    const currentHour = this.getCurrentHour();
    const currentPriceData = prices.find(price => price.hour === currentHour);

    if (!currentPriceData) {
      throw new Error(`No price data available for current hour ${currentHour}`);
    }

    return this.processPrice(currentPriceData.price);
  }

  /**
   * Get current billing price (for backward compatibility)
   * @returns {Promise<number>} Current billing price in EUR/kWh (negative = 0)
   */
  async getCurrentBillingPrice() {
    const priceInfo = await this.getCurrentPrice();
    return priceInfo.billing;
  }

  /**
   * Get the cheapest and most expensive hours of the day (using billing prices)
   * @returns {Promise<{cheapest: {hour: number, price: ProcessedPrice}, expensive: {hour: number, price: ProcessedPrice}}>}
   */
  async getDailyPriceExtremes() {
    const prices = await this.fetchTodayPrices();

    if (!prices || prices.length === 0) {
      throw new Error('No price data available');
    }

    // Find cheapest based on billing price (negative = 0)
    const cheapest = prices.reduce((min, current) => {
      const currentBilling = Math.max(0, current.price);
      const minBilling = Math.max(0, min.price);
      return currentBilling < minBilling ? current : min;
    });

    // Find most expensive based on billing price
    const expensive = prices.reduce((max, current) => {
      const currentBilling = Math.max(0, current.price);
      const maxBilling = Math.max(0, max.price);
      return currentBilling > maxBilling ? current : max;
    });

    return {
      cheapest: {
        hour: cheapest.hour,
        price: this.processPrice(cheapest.price)
      },
      expensive: {
        hour: expensive.hour,
        price: this.processPrice(expensive.price)
      }
    };
  }

  /**
   * Calculate average price for the day using billing prices (negative = 0)
   * @returns {Promise<number>} Average billing price in EUR/kWh
   */
  async getDailyAveragePrice() {
    const prices = await this.fetchTodayPrices();

    if (!prices || prices.length === 0) {
      throw new Error('No price data available');
    }

    // Use billing prices (negative = 0) for average calculation
    // prices are already in EUR/kWh
    const sum = prices.reduce((total, price) => {
      const billingPrice = Math.max(0, price.price);
      return total + billingPrice;
    }, 0);

    return sum / prices.length;
  }
}

// =========================================================================================
// ARGUMENT PARSING FOR HOMEY FLOWS
// =========================================================================================

/**
 * Parse argument string from Homey flow into options object and return mode
 * @param {string} argumentString - Comma-separated arguments: "from,to,numberOfHours,returnOptimalHour"
 * @returns {{options: OptimalSlotOptions, returnOptimalHour: boolean}} Parsed options and return mode
 * @throws {Error} If parsing fails or arguments are invalid
 *
 * Examples:
 * - "2" → { options: { numberOfHours: 2 }, returnOptimalHour: false }
 * - "0,6,2" → { options: { from: 0, to: 6, numberOfHours: 2 }, returnOptimalHour: false }
 * - "8,18,1,true" → { options: { from: 8, to: 18, numberOfHours: 1 }, returnOptimalHour: true }
 * - "" → { options: {}, returnOptimalHour: false } (all defaults)
 */
function parseArguments(argumentString) {
  if (!argumentString || argumentString.trim() === '') {
    return { options: {}, returnOptimalHour: false }; // Use all defaults
  }

  const args = argumentString.split(',').map(arg => arg.trim());
  const options = {};
  let returnOptimalHour = false;

  // Parse arguments in order: from, to, numberOfHours, returnOptimalHour
  // Special case: if only one argument provided, treat it as numberOfHours
  if (args.length === 1 && args[0] !== '') {
    const numberOfHours = parseInt(args[0]);
    if (isNaN(numberOfHours)) {
      throw new Error(`Invalid 'numberOfHours' parameter: "${args[0]}". Must be a number 1-24.`);
    }
    options.numberOfHours = numberOfHours;
  } else {
    // Multiple arguments: parse in order from, to, numberOfHours
    if (args.length >= 1 && args[0] !== '') {
      const from = parseInt(args[0]);
      if (isNaN(from)) {
        throw new Error(`Invalid 'from' parameter: "${args[0]}". Must be a number 0-23.`);
      }
      options.from = from;
    }

    if (args.length >= 2 && args[1] !== '') {
      const to = parseInt(args[1]);
      if (isNaN(to)) {
        throw new Error(`Invalid 'to' parameter: "${args[1]}". Must be a number 0-23.`);
      }
      options.to = to;
    }

    if (args.length >= 3 && args[2] !== '') {
      const numberOfHours = parseInt(args[2]);
      if (isNaN(numberOfHours)) {
        throw new Error(`Invalid 'numberOfHours' parameter: "${args[2]}". Must be a number 1-24.`);
      }
      options.numberOfHours = numberOfHours;
    }
  }

  if (args.length >= 4 && args[3] !== '') {
    const returnOptimalHourStr = args[3].toLowerCase();
    if (returnOptimalHourStr === 'true' || returnOptimalHourStr === '1') {
      returnOptimalHour = true;
    } else if (returnOptimalHourStr === 'false' || returnOptimalHourStr === '0') {
      returnOptimalHour = false;
    } else {
      throw new Error(`Invalid 'returnOptimalHour' parameter: "${args[3]}". Must be 'true', 'false', '1', or '0'.`);
    }
  }

  return { options, returnOptimalHour };
}

/**
 * Main function for Homey script execution with argument support
 * @param {string} [argumentString] - Optional argument string from Homey flow
 * @returns {Promise<boolean|number>} Result based on returnOptimalHour flag
 */
async function runHomeyScript(argumentString) {
  try {
    const optimizer = new NordPoolOptimizer();
    const { options, returnOptimalHour } = parseArguments(argumentString || '');

    console.log(`Nord Pool Optimizer - Arguments: "${argumentString || 'none'}"`, { options, returnOptimalHour });

    let result;
    if (returnOptimalHour) {
      result = await optimizer.getOptimalStartHour(options);
      console.log(`Optimal start hour: ${result}:00`);
    } else {
      result = await optimizer.isOptimalTimeToStart(options);
      console.log(`Should start now: ${result}`);
    }

    return result;
  } catch (error) {
    console.error('Nord Pool Optimizer Error:', error.message);
    throw error;
  }
}

// =========================================================================================
// USAGE EXAMPLES AND EXPORT
// =========================================================================================

/**
 * Main function demonstrating usage
 */
async function demonstrateUsage() {
  const optimizer = new NordPoolOptimizer();

  try {
    console.log('=== Nord Pool Optimizer Demo ===');

    // Example 1: Check if now is the optimal time to start dishwasher (2 hours)
    console.log('\n1. Is now optimal time to start 2-hour appliance?');
    const isOptimalNow = await optimizer.isOptimalTimeToStart({ numberOfHours: 2 });
    const currentHour = optimizer.getCurrentHour();
    console.log(`Current hour: ${currentHour}:00`);
    console.log(`Should start appliance now: ${isOptimalNow} (${(await optimizer.getCurrentBillingPrice())}€/kWh)`);


    // Example 2: Check if now is optimal for washing machine (3 hours)
    console.log('\n2. Is now optimal time to start 3-hour appliance?');
    const isOptimal3Hours = await optimizer.isOptimalTimeToStart({ numberOfHours: 3 });
    console.log(`Should start 3-hour appliance now: ${isOptimal3Hours}`);

    // Example 3: Check daytime optimal (8:00-18:00) for 1-hour appliance
    console.log('\n3. Is now optimal time during daytime (8:00-18:00)?');
    try {
      const isDaytimeOptimal = await optimizer.isOptimalTimeToStart({
        from: 8,
        to: 18,
        numberOfHours: 1
      });
      console.log(`Should start 1-hour appliance now (daytime only): ${isDaytimeOptimal}`);
    } catch (error) {
      console.log(`Note: ${error.message}`);
    }

    // Example 4: Test validation with an invalid range
    console.log('\n4. Testing validation (22:00-06:00 cross-day):');
    try {
      await optimizer.isOptimalTimeToStart({
        from: 22,
        to: 6,
        numberOfHours: 2
      });
    } catch (error) {
      console.log(`Expected error: ${error.message}`);
      console.log('Cross-day scenarios require same-day handling in this version');
    }

    // Example 5: Current price and daily statistics
    console.log('\n5. Current price and daily statistics:');
    const currentPrice = await optimizer.getCurrentPrice();
    const averagePrice = await optimizer.getDailyAveragePrice();
    const extremes = await optimizer.getDailyPriceExtremes();

    console.log(`Current price: €${currentPrice.display}/kWh`);
    console.log(`Daily average (billing): €${averagePrice.toFixed(5)}/kWh`);
    console.log(`Cheapest hour: ${extremes.cheapest.hour}:00 (€${extremes.cheapest.price.display}/kWh)`);
    console.log(`Most expensive hour: ${extremes.expensive.hour}:00 (€${extremes.expensive.price.display}/kWh)`);

    if (currentPrice.isNegative) {
      console.log(`⚡ Current hour has negative price - you get paid to use electricity!`);
    }

    // Example 6: Get a sample of today's prices for debugging
    console.log('\n6. Sample of today\'s prices:');
    const allPrices = await optimizer.fetchTodayPrices();
    allPrices.forEach(price => {
      const processedPrice = optimizer.processPrice(price.price);
      console.log(`${price.hour}:00 - €${processedPrice.display}/kWh`);
    });

    // Example 7: Get the cheapest hour and price
    console.log('\n7. Cheapest hour and price:');
    const cheapest = allPrices.reduce((min, current) => {
      const currentBilling = Math.max(0, current.price);
      const minBilling = Math.max(0, min.price);
      return currentBilling < minBilling ? current : min;
    });
    const processedCheapest = optimizer.processPrice(cheapest.price);
    console.log(`Cheapest hour: ${cheapest.hour}:00 (€${processedCheapest.display}/kWh)`);

  } catch (error) {
    console.error('Error:', error.message);
  }
}

// =========================================================================================
// HOMEY SCRIPT USAGE EXAMPLES
// =========================================================================================

// Export the optimizer class for HomeyScript usage
// Uncomment the line below to run the demo
await demonstrateUsage();

/**
 * HOMEY FLOW EXAMPLES - SIMPLIFIED VERSION:
 *
 * 1. Should I start the dishwasher now? (2 hours needed):
 *    const optimizer = new NordPoolOptimizer();
 *    return await optimizer.isOptimalTimeToStart({ numberOfHours: 2 });
 *
 * 2. Should I start the washing machine now? (3 hours needed):
 *    const optimizer = new NordPoolOptimizer();
 *    return await optimizer.isOptimalTimeToStart({ numberOfHours: 3 });
 *
 * 3. Should I start appliance during daytime only? (8:00-18:00):
 *    const optimizer = new NordPoolOptimizer();
 *    return await optimizer.isOptimalTimeToStart({
 *      from: 8,
 *      to: 18,
 *      numberOfHours: 2
 *    });
 *
 * 4. Is current billing price below average? (for other logic):
 *    const optimizer = new NordPoolOptimizer();
 *    const currentPrice = await optimizer.getCurrentBillingPrice(); // Returns EUR/kWh
 *    const averagePrice = await optimizer.getDailyAveragePrice(); // Returns EUR/kWh
 *    return currentPrice < averagePrice;
 *
 * 5. Display current price with negative handling:
 *    const optimizer = new NordPoolOptimizer();
 *    const priceInfo = await optimizer.getCurrentPrice(); // Returns ProcessedPrice
 *    return `Current price: €${priceInfo.display}/kWh`; // Shows "0.00000 (-0.00520)" for negatives
 *
 * 6. Check if current price is negative (free electricity):
 *    const optimizer = new NordPoolOptimizer();
 *    const priceInfo = await optimizer.getCurrentPrice(); // Returns ProcessedPrice
 *    return priceInfo.isNegative; // Boolean: true if negative price
 *
 * MAIN FUNCTION: isOptimalTimeToStart()
 * - Returns true/false - perfect for Homey flow conditions
 * - Uses billing prices (negative = 0) for all calculations
 * - Just one simple call per appliance type
 * - No need to compare hours manually
 *
 * PRICE HANDLING:
 * - All methods use ProcessedPrice typedef for consistent price handling
 * - Negative prices treated as 0 for calculations (matches provider billing)
 * - Display format in EUR/kWh with 5 decimal places: "0.00000 (-0.00520)"
 * - All prices stored and calculated in EUR/kWh for complete consistency
 */

// =========================================================================================
// MAIN EXECUTION FOR HOMEY SCRIPT
// =========================================================================================

// Check if script is being run with arguments (Homey flow execution)
if (typeof process !== 'undefined' && process.argv && process.argv.length > 2) {
  // Extract argument from command line (Homey passes it as a single argument)
  const argumentString = process.argv[2];

  // Run the script with arguments
  runHomeyScript(argumentString)
    .then(result => {
      console.log('Result:', result);
      process.exit(0);
    })
    .catch(error => {
      console.error('Error:', error.message);
      process.exit(1);
    });
} else {
  // Run demo if no arguments provided (local testing)
  demonstrateUsage()
    .then(() => {
      console.log('\n=== Demo completed successfully ===');
    })
    .catch(error => {
      console.error('Demo failed:', error.message);
    });
}
