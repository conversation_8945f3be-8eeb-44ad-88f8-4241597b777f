// Test kWh conversion and 5 decimal place formatting

// Simple test of the processPrice function
function processPrice(originalPrice) {
  const billingPrice = Math.max(0, originalPrice); // Provider treats negative as 0
  
  // Convert from EUR/MWh to EUR/kWh (divide by 1000)
  const originalKwh = originalPrice / 1000;
  const billingKwh = billingPrice / 1000;
  
  // Format display with 5 decimal places
  const displayPrice = originalPrice < 0 ? 
    `${billingKwh.toFixed(5)} (${originalKwh.toFixed(5)})` : 
    originalKwh.toFixed(5);
  
  return {
    original: originalPrice,           // Keep original MWh values for calculations
    billing: billingPrice,            // Keep billing MWh values for calculations
    display: displayPrice,            // Display in kWh format
    isNegative: originalPrice < 0,
    originalKwh: originalKwh,         // EUR/kWh values for reference
    billingKwh: billingKwh
  };
}

console.log('=== EUR/kWh Conversion Test ===\n');

// Test different price scenarios
const testPrices = [
  { name: 'Negative price', value: -5.20 },
  { name: 'Zero price', value: 0 },
  { name: 'Small positive', value: 12.30 },
  { name: 'Medium price', value: 45.67 },
  { name: 'High price', value: 158.90 },
  { name: 'Very small', value: 0.05 },
  { name: 'Large negative', value: -25.75 }
];

console.log('Price Conversion Results:');
console.log('========================');

testPrices.forEach(test => {
  const processed = processPrice(test.value);
  console.log(`\n${test.name}:`);
  console.log(`  Original: €${test.value}/MWh → €${processed.originalKwh.toFixed(5)}/kWh`);
  console.log(`  Billing:  €${processed.billing}/MWh → €${processed.billingKwh.toFixed(5)}/kWh`);
  console.log(`  Display:  €${processed.display}/kWh`);
  console.log(`  Negative: ${processed.isNegative}`);
});

console.log('\n=== Conversion Verification ===');
console.log('1 MWh = 1000 kWh');
console.log('So EUR/MWh ÷ 1000 = EUR/kWh');
console.log('');
console.log('Examples:');
console.log('  45.67 EUR/MWh ÷ 1000 = 0.04567 EUR/kWh ✓');
console.log('  -5.20 EUR/MWh ÷ 1000 = -0.00520 EUR/kWh ✓');
console.log('  158.90 EUR/MWh ÷ 1000 = 0.15890 EUR/kWh ✓');

console.log('\n=== Display Format Examples ===');
console.log('Positive prices: "0.04567" (just the kWh price)');
console.log('Negative prices: "0.00000 (-0.00520)" (billing price + original in parentheses)');

console.log('\n=== Homey Usage Examples ===');
console.log('const priceInfo = await optimizer.getCurrentPrice();');
console.log('console.log(`Current price: €${priceInfo.display}/kWh`);');
console.log('// Output: "Current price: €0.04567/kWh" or "Current price: €0.00000 (-0.00520)/kWh"');

console.log('\n✅ EUR/kWh conversion with 5 decimal places working correctly!');
