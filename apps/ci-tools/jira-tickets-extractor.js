const { getAllReleaseAndRCTags, getAllReleaseTags, getTagHash, getTagDate, getCommitMessages } = require('./git-utils');
const { formatJiraTicketCount, extractJiraKeysFromCommitMessage } = require('./jira-utils');
const { removeBumpUpMessages } = require('./commit-utils');

// Configuration constants
const config = {
  environments: {
    staging: 'staging',
    production: 'production'
  },
  jiraTicketLimit: 500,
};

class JiraTicketsExtractor {
  #environment;
  #releaseInfo;
  #jiraKeysList = [];
  #jiraKeysString = '';
  #ticketCount = 0;

  constructor(environment) {
    this.#environment = environment;
    this.#releaseInfo = {
      latest: {},
      previous: {}
    };
  }

  get environment() { return this.#environment; }

  get releaseInfo() { return this.#releaseInfo; }

  get jiraKeysList() { return this.#jiraKeysList; }

  get jiraKeysString() { return this.#jiraKeysString; }

  get ticketCount() { return this.#ticketCount; }

  /**
   * Initialize the application with release tags based on environment
   */
  #initialize() {
    let releases;

    switch (this.#environment) {
      case config.environments.staging:
        releases = getAllReleaseAndRCTags();
        break;
      case config.environments.production:
        releases = getAllReleaseTags();
        break;
      default:
        throw new Error(`Handling for ${this.#environment} is not implemented`);
    }

    const latestTwoReleaseTags = releases.slice(-2);
    console.log('Latest two release tags:', JSON.stringify(latestTwoReleaseTags, null, 2));

    // Setup release info
    this.#setupReleaseInfo(latestTwoReleaseTags);
  }

  /**
   * Set up release information for latest and previous releases
   */
  #setupReleaseInfo(latestTwoReleaseTags) {
    // Latest release info
    this.#releaseInfo.latest.tag = latestTwoReleaseTags[1];
    this.#releaseInfo.latest.sha = getTagHash(this.#releaseInfo.latest.tag);
    this.#releaseInfo.latest.date = getTagDate(this.#releaseInfo.latest.tag);

    // Previous release info
    this.#releaseInfo.previous.tag = latestTwoReleaseTags[0];
    this.#releaseInfo.previous.sha = getTagHash(this.#releaseInfo.previous.tag);
    this.#releaseInfo.previous.date = getTagDate(this.#releaseInfo.previous.tag);

    console.log(`Latest release tag: ${this.#releaseInfo.latest.tag} (SHA: ${this.#releaseInfo.latest.sha}) on ${this.#releaseInfo.latest.date}`);
    console.log(`Previous release tag: ${this.#releaseInfo.previous.tag} (SHA: ${this.#releaseInfo.previous.sha}) on ${this.#releaseInfo.previous.date}`);
  }

  /**
   * Get commit messages that include a specific release tag
   */
  #getCommitMessagesWithReleasedJiraKeys(commitMessages, releaseTag) {
    const filteredMessages = commitMessages.filter(message => message.includes(`to ${releaseTag} `));
    console.log(`${releaseTag} commit messages with already released tickets:`,
      JSON.stringify(filteredMessages, null, 2)
    );
    return filteredMessages;
  }

  /**
   * Process commit messages and extract Jira tickets
   */
  #processCommitMessagesAndExtractJiraKeys() {
    // Get upcoming release commit messages
    const upcomingReleaseCommitMessages = getCommitMessages(this.#releaseInfo.latest.date);
    // Remove the last line as typically it's version bump up commit
    upcomingReleaseCommitMessages.pop();
    console.log('Upcoming release commit messages:', JSON.stringify(upcomingReleaseCommitMessages, null, 2));

    // Get latest release commit messages
    const latestReleaseCommitMessages = getCommitMessages(
      this.#releaseInfo.previous.date,
      this.#releaseInfo.latest.date
    );
    console.log('Latest release commit messages:', JSON.stringify(latestReleaseCommitMessages, null, 2));

    // Extract commit messages with released Jira keys
    const latestReleaseCommitMessagesWithReleasedJiraKeys = this.#getCommitMessagesWithReleasedJiraKeys(
      latestReleaseCommitMessages,
      this.#releaseInfo.latest.tag
    );

    const previousReleaseCommitMessagesWithReleasedJiraKeys = this.#getCommitMessagesWithReleasedJiraKeys(
      latestReleaseCommitMessages,
      this.#releaseInfo.previous.tag
    );

    // Process and extract all Jira keys
    const newJiraKeys = this.#extractNewJiraKeys(upcomingReleaseCommitMessages);
    const latestReleaseJiraKeys = this.#extractLatestReleaseJiraKeys(latestReleaseCommitMessages);
    const releasedJiraKeysList = this.#extractAlreadyReleasedJiraKeys(
      latestReleaseCommitMessagesWithReleasedJiraKeys,
      previousReleaseCommitMessagesWithReleasedJiraKeys
    );
    this.#jiraKeysList = this.#filterReleasedJiraKeys(newJiraKeys, latestReleaseJiraKeys, releasedJiraKeysList);
  }

  // Extract new Jira keys
  #extractNewJiraKeys(upcomingReleaseCommitMessages) {
    const newJiraKeys = extractJiraKeysFromCommitMessage(upcomingReleaseCommitMessages.join('\n'));
    const formattedNewJiraKeysCount = formatJiraTicketCount(newJiraKeys, 'new');
    console.log(`${formattedNewJiraKeysCount} to be listed in the new release:`,
      JSON.stringify(newJiraKeys, null, 2)
    );
    return newJiraKeys;
  }

  // Process latest release commit messages and extract Jira keys
  #extractLatestReleaseJiraKeys(latestReleaseCommitMessages) {
    const processedLatestReleaseCommitMessages = removeBumpUpMessages(latestReleaseCommitMessages);
    const latestReleaseJiraKeys = extractJiraKeysFromCommitMessage(processedLatestReleaseCommitMessages.join('\n'));
    const formattedLatestReleaseJiraKeysCount = formatJiraTicketCount(latestReleaseJiraKeys, 'all');
    console.log(`${formattedLatestReleaseJiraKeysCount} listed in the latest release:`,
      JSON.stringify(latestReleaseJiraKeys, null, 2)
    );
    return latestReleaseJiraKeys;
  }

  // Extract already released Jira keys
  #extractAlreadyReleasedJiraKeys(latestReleaseCommitMessagesWithReleasedJiraKeys, previousReleaseCommitMessagesWithReleasedJiraKeys) {
    const latestReleaseReleasedJiraKeys = extractJiraKeysFromCommitMessage(
      latestReleaseCommitMessagesWithReleasedJiraKeys.join('\n')
    );
    const formattedLatestReleaseReleasedJiraKeysCount = formatJiraTicketCount(
      latestReleaseReleasedJiraKeys, 'already released'
    );
    console.log(`${formattedLatestReleaseReleasedJiraKeysCount} noted in latest release commit message:`,
      JSON.stringify(latestReleaseReleasedJiraKeys, null, 2)
    );

    const previousReleaseReleasedJiraKeys = extractJiraKeysFromCommitMessage(
      previousReleaseCommitMessagesWithReleasedJiraKeys.join('\n')
    );
    const formattedPreviousReleaseReleasedJiraKeysCount = formatJiraTicketCount(
      previousReleaseReleasedJiraKeys, 'already released'
    );
    console.log(`${formattedPreviousReleaseReleasedJiraKeysCount} noted in previous release commit message:`,
      JSON.stringify(previousReleaseReleasedJiraKeys, null, 2)
    );

    // Consolidate released Jira keys
    const releasedJiraKeysList = [...latestReleaseReleasedJiraKeys, ...previousReleaseReleasedJiraKeys];
    const formattedReleasedJiraKeysCount = formatJiraTicketCount(releasedJiraKeysList, 'already released');
    console.log(`${formattedReleasedJiraKeysCount} noted in latest and previous release commit messages:`,
      JSON.stringify(releasedJiraKeysList, null, 2)
    );

    return releasedJiraKeysList;
  }

  // Filter out already released Jira keys
  #filterReleasedJiraKeys(newJiraKeys, latestReleaseJiraKeys, releasedJiraKeysList) {
    const remainingLatestReleaseJiraKeys = latestReleaseJiraKeys.filter(
      key => !releasedJiraKeysList.includes(key)
    );
    const formattedRemainingLatestReleaseJiraKeysCount = formatJiraTicketCount(
      remainingLatestReleaseJiraKeys, 'remaining already released'
    );
    console.log(`${formattedRemainingLatestReleaseJiraKeysCount} (except the ones noted as deployed):`,
      JSON.stringify(remainingLatestReleaseJiraKeys, null, 2)
    );

    const remainingUpcomingReleaseJiraKeys = newJiraKeys.filter(
      key => !releasedJiraKeysList.includes(key)
    );
    const formattedRemainingUpcomingReleaseJiraKeysCount = formatJiraTicketCount(
      remainingUpcomingReleaseJiraKeys, 'remaining new'
    );
    console.log(`${formattedRemainingUpcomingReleaseJiraKeysCount} (except the ones noted as deployed):`,
      JSON.stringify(remainingUpcomingReleaseJiraKeys, null, 2)
    );

    // Create final list of Jira keys
    const finalJiraKeysList = [...new Set([
      ...remainingLatestReleaseJiraKeys,
      ...remainingUpcomingReleaseJiraKeys
    ])].sort();

    console.log(`Final list of ${formatJiraTicketCount(finalJiraKeysList)} to be listed in the new release:`,
      JSON.stringify(finalJiraKeysList, null, 2)
    );

    return finalJiraKeysList;
  }

  /**
   * Converts a list of Jira keys into a single comma-separated string.
   * If the list exceeds the Jira API limit, only the last `jiraTicketLimit` keys are included.
   *
   * @param {string[]} finalJiraKeysList - An array of Jira keys to be converted into a string.
   * @return {string} A single line string containing the comma-separated Jira keys.
   */
  convertJiraKeysToString(finalJiraKeysList) {
    if (finalJiraKeysList.length > config.jiraTicketLimit) {
      console.warn(
        `Jira keys list is too long (${finalJiraKeysList.length} keys). ` +
        `Taking the last ${config.jiraTicketLimit} keys due to Jira API limitations.`
      );
      this.#jiraKeysString = finalJiraKeysList.slice(-config.jiraTicketLimit).join(', ');
      this.#ticketCount = config.jiraTicketLimit;
    } else {
      this.#jiraKeysString = finalJiraKeysList.join(', ');
      this.#ticketCount = finalJiraKeysList.length;
    }

    return this.#jiraKeysString;
  }

  /**
   * Executes the main workflow of the `JiraTicketsExtractor` class.
   *
   * This method orchestrates the following steps:
   * 1. Initializes release tags and metadata based on the provided environment.
   * 2. Processes commit messages to extract JIRA ticket keys for the new release.
   * 3. Filters and formats the extracted ticket keys into a comma-separated string.
   *
   * The method updates the internal state of the class, specifically:
   * - `ticketCount`: The number of JIRA tickets found for the new release.
   * - `jiraKeysString`: A formatted string containing the extracted JIRA ticket keys.
   *
   * @returns {JiraTicketsExtractor} Returns the current instance of the class,
   *                                allowing access to extracted JIRA ticket information
   *                                (`ticketCount`, `jiraKeysString`, etc.).
   *
   * @example
   * const extractor = new JiraTicketsExtractor('staging');
   * extractor.run();
   * console.log(`Tickets found: ${extractor.ticketCount}`);
   * console.log(`Formatted JIRA Keys: ${extractor.jiraKeysString}`);
   */

  run() {
    this.#initialize();
    this.#processCommitMessagesAndExtractJiraKeys();
    this.convertJiraKeysToString(this.#jiraKeysList);

    return this;
  }
}

module.exports = {
  JiraTicketsExtractor,
  config
};
