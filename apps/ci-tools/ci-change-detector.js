const axios = require('axios');
const { execSync } = require('child_process');
const path = require('path');
const { hasSkipCiMessage, ensureBranchExists, checkCommitExists, getRootCommit, fetchBranch } = require(
  './git-utils');

const baseApiUrl = 'https://circleci.com/api/v2';
const releaseBranchesRegexp = /^(release|patch|hotfix)\//i;

const headRef = 'HEAD';
const headRefPrevious = 'HEAD~1';

const ignoredWorkflowTitle = 'ignored';
// List of workflows to ignore when given workflow is set to 'ignored'
const setupWorkflows = ['setup-development-workflow', 'setup-staging-workflow', 'setup-production-workflow'];

function isIgnoredWorkflow(workflowName) {
  return workflowName === ignoredWorkflowTitle;
}

function isSetupWorkflow(workflowName) {
  return setupWorkflows.includes(workflowName);
}

/**
 * Validates the project slug format.
 * Ensures it matches the format `{vcs-slug}/{org-name}/{repo-name}`.
 * @param {string} projectSlug
 * @throws {Error} If the project slug is invalid.
 */
function validateProjectSlug(projectSlug) {
  const slugFormat = /^(gh|bb|circleci)\/[\w-]+\/[\w-]+$/;
  if (!slugFormat.test(projectSlug)) {
    throw new Error(
      'Invalid project slug format. Format must be `{vcs-slug}/{org-name}/{repo-name}` (e.g., gh/org/repo or circleci/org-id/project-id).',
    );
  }
}

/**
 * CIChangeDetector class for detecting changes and interacting with CircleCI.
 */
class CIChangeDetector {
  constructor(config) {
    const { token, projectSlug, directory, branch, workflow } = config;

    validateProjectSlug(projectSlug);

    this.projectSlug = projectSlug;
    this.directory = directory;
    this.branch = branch;
    this.workflow = workflow.toLowerCase();

    this.#initializeCircleCiClient(token);
  }

  /**
   * Initializes the CircleCI client with the provided token.
   * @param token
   * @private
   * @returns {AxiosInstance}
   * @throws {Error} If the client initialization fails.
   */
  #initializeCircleCiClient(token) {
    this.client = axios.create({
      baseURL: baseApiUrl,
      headers: {
        'Circle-Token': token,
        Accept: 'application/json',
      },
    });
  }


  /**
   * Validates if a pipeline workflow is valid and successful
   * @param {Object} pipeline The pipeline object to validate
   * @param {Object} successfulWorkflow The workflow to validate
   * @returns {boolean} True if the pipeline is valid, false otherwise
   */
  #isValidPipelineWorkflow(pipeline, successfulWorkflow) {
    if (!successfulWorkflow) return false;

    if (!pipeline.vcs.commit && !checkCommitExists(pipeline.vcs.revision)) {
      console.warn(`Skipping build for commit ${pipeline.vcs.revision} as commit is invalid or missing... It's a result of a force push or shallow clone most likely.`);
      return false;
    }

    const commitMessage = pipeline.vcs.commit?.subject.toLowerCase() || '';
    if (commitMessage.includes('[ci skip]') || commitMessage.includes('[skip ci]')) {
      console.log(`Skipping build for commit ${pipeline.vcs.revision} due to skip CI directive.`);
      return false;
    }

    return true;
  }

  /**
   * Determines the base commit for comparison
   * @param {Object} lastBuild The last successful build info
   * @returns {{baseCommit: string, branchPoint: Object|null}} The base commit and branch point info
   */
  #determineBaseCommit(lastBuild) {
    let baseCommit = lastBuild?.commitSha;
    let branchPoint = null;
    let branchBaseCommit;

    let isCommitValid = checkCommitExists(baseCommit);
    if (!baseCommit || !isCommitValid) {
      console.warn(`Base commit ${baseCommit ? `"${baseCommit}" ` : ''}is missing or invalid. This may indicate a new branch pushed to remote or force push. Falling back...`);

      console.warn('Attempting to find branch point...');
      const defaultBranch = this.getDefaultBranch();
      fetchBranch(defaultBranch);
      branchPoint = this.calculateBranchPoint(defaultBranch);

      if (branchPoint) {
        console.log(`Branch point: ${JSON.stringify(branchPoint, null, 2)}`);
        branchBaseCommit = branchPoint.commitSha;
      }

      if (!branchBaseCommit || !checkCommitExists(branchBaseCommit)) {
        console.warn('Branch point base commit is missing or invalid. Defaulting to root commit.');
        branchBaseCommit = getRootCommit();
      }

      if (!branchBaseCommit && !!lastBuild) {
        throw new Error('No valid commit reference available for comparison.');
      }

      baseCommit = branchBaseCommit;
    }

    return { baseCommit, branchPoint };
  }

  /**
   * Gets the changed files between two commits
   * @param {string} baseCommit Base commit for comparison
   * @param {string} targetCommit Target commit for comparison
   * @returns {string[]} List of changed files
   */
  #getChangedFiles(baseCommit, targetCommit) {
    console.log(`Comparing ${baseCommit}...${targetCommit}`);
    const changedFiles = execSync(
      `git diff --name-only ${baseCommit} ${targetCommit}`,
      { encoding: 'utf-8' },
    )
      .split('\n')
      .filter(Boolean);

    const targetDir = path.resolve(this.directory);
    return changedFiles.filter((file) => path.resolve(file).startsWith(targetDir));
  }

  /**
   * Generates the URL to view a specific workflow in CircleCI.
   * @param {number} pipelineNumber
   * @param {string} workflowId
   * @return {string}
   */
  generateWorkflowUrl(pipelineNumber, workflowId) {
    const normalizedSlug = this.projectSlug.replace(/^gh/, 'github');
    return `https://app.circleci.com/pipelines/${normalizedSlug}/${pipelineNumber}/workflows/${workflowId}`;
  }

  /**
   * Fetch the most recent successful build for the branch and workflow.
   * Filters out builds with "[ci skip]" messages.
   * @return {Promise<null|{pipelineId: string, workflowId: string, workflowName: string, workflowUrl: string, commitSha: string}>} The last successful build or null if not found
   * @throws {Error} If the request fails.
   */
  async getLastSuccessfulBuild() {
    try {
      const response = await this.client.get(
        `/project/${this.projectSlug}/pipeline`,
        { params: { branch: this.branch } },
      );

      for (const pipeline of response.data.items) {
        const workflows = await this.client.get(`/pipeline/${pipeline.id}/workflow`);
        const successfulWorkflow = workflows.data.items.find(
          isIgnoredWorkflow(this.workflow) ? (w) => w.status === 'success' && !isSetupWorkflow(w.name):
            (w) => w.name === this.workflow && w.status === 'success',
        );

        if (successfulWorkflow && this.#isValidPipelineWorkflow(pipeline, successfulWorkflow)) {
          return {
            pipelineId: pipeline.id,
            workflowId: successfulWorkflow.id,
            workflowName: successfulWorkflow.name,
            workflowUrl: this.generateWorkflowUrl(
              successfulWorkflow.pipeline_number,
              successfulWorkflow.id,
            ),
            commitSha: pipeline.vcs.revision,
          };
        }
      }

      console.warn(`No successful "${this.workflow}" workflows found for branch "${this.branch}".`);
      return null;
    } catch (error) {
      throw new Error(`Error fetching the last successful build: ${error.message}`);
    }
  }

  /**
   * Determines the default branch based on the current branch.
   * Defaults to 'main' for release branches and 'develop' for others.
   * @return {string}
   */
  getDefaultBranch() {
    return releaseBranchesRegexp.test(this.branch) || this.branch === 'main' ? 'main' : 'develop';
  }

  /**
   * Computes the merge base (branch point) with the given base branch.
   * @param {string} baseBranch
   * @return {{commitSha: string, baseBranch: string, baseRef: string, isBranchPoint: boolean}|null}
   */
  calculateBranchPoint(baseBranch) {
    try {
      const baseBranchRef = ensureBranchExists(baseBranch);
      const branchPoint = execSync(
        `git merge-base ${this.branch} "${baseBranch}"`,
        { encoding: 'utf-8' },
      ).trim();

      return {
        commitSha: branchPoint,
        isBranchPoint: true,
        baseBranch,
        baseRef: baseBranchRef,
      };
    } catch {
      console.warn(`Failed to compute merge base with ${baseBranch}.`);
      return null;
    }
  }

  /**
   * Detects changes in the current branch since the last successful build.
   * Handles various edge cases like skip CI messages, force pushes, and shallow clones.
   * @return {Promise<{hasChanges: boolean, changes: string[], lastSuccessfulBuild: ({isRootCommit}|null), isFirstRun: boolean, comparedAgainst: string}>} Detection results including changes and comparison metadata.
   */
  async detectChanges() {
    ensureBranchExists(this.branch);

    let targetCommit = headRef;
    if (hasSkipCiMessage(headRef) && this.branch === 'main') {
      console.log(`${headRef} commit contains skip CI message, using ${headRefPrevious} instead.`);
      targetCommit = headRefPrevious;
    }

    const lastBuild = await this.getLastSuccessfulBuild();
    const { baseCommit, branchPoint } = this.#determineBaseCommit(lastBuild);

    try {
      const relevantChanges = this.#getChangedFiles(baseCommit, targetCommit);

      return {
        hasChanges: relevantChanges.length > 0,
        changes: relevantChanges,
        lastSuccessfulBuild: lastBuild,
        isFirstRun: !!branchPoint?.isBranchPoint,
        comparedAgainst: lastBuild
          ? `${baseCommit} (last-successful-build-sha at workflow ${lastBuild.workflowId} for '${lastBuild.workflowName}')`
          : branchPoint ? `${baseCommit} (branch-point of ${branchPoint.baseBranch} at ${branchPoint.baseRef})` : `${baseCommit} (root commit)`,
      };
    } catch (error) {
      throw new Error(`Change detection failed: ${error.message}`);
    }
  }
}

module.exports = CIChangeDetector;
