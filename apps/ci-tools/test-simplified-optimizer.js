// Simple test for the simplified HomeyScript Nord Pool Optimizer
// This shows the main function you'll use in Homey flows

class NordPoolOptimizer {
  constructor() {
    this.baseUrl = 'https://dashboard.elering.ee/api/nps/price';
  }

  getVilniusDate() {
    const now = new Date();
    return new Date(now.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
  }

  formatDateForAPI(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  createTodayApiUrl() {
    const today = this.getVilniusDate();
    const start = new Date(new Date(`${this.formatDateForAPI(today)}T00:00:00`).toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
    const end = new Date(new Date(`${this.formatDateForAPI(today)}T23:59:59.999`).toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
    end.setMilliseconds(999);
    return `${this.baseUrl}?start=${start.toISOString()}&end=${end.toISOString()}`;
  }

  async fetchTodayPrices() {
    const url = this.createTodayApiUrl();
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      if (!data.success || !data.data || !data.data.lt) {
        throw new Error('Invalid API response format or no Lithuanian data available');
      }
      return data.data.lt.map(item => {
        const date = new Date(item.timestamp * 1000);
        const vilniusHour = parseInt(date.toLocaleString('lt-LT', { 
          timeZone: 'Europe/Vilnius', 
          hour: '2-digit', 
          hour12: false 
        }));
        return { timestamp: item.timestamp, price: item.price, hour: vilniusHour };
      }).sort((a, b) => a.hour - b.hour);
    } catch (error) {
      throw new Error(`Failed to fetch Nord Pool prices: ${error.message}`);
    }
  }

  validateSlotOptions(options) {
    const { from = 0, to = 23, numberOfHours = 1 } = options;
    if (from < 0 || from > 23) throw new Error('Parameter "from" must be between 0 and 23');
    if (to < 0 || to > 23) throw new Error('Parameter "to" must be between 0 and 23');
    if (to <= from) throw new Error('Parameter "to" must be greater than "from"');
    if (numberOfHours < 1 || numberOfHours > 24) throw new Error('Parameter "numberOfHours" must be between 1 and 24');
    const availableHours = to - from + 1;
    if (numberOfHours > availableHours) {
      throw new Error(`Cannot fit ${numberOfHours} consecutive hours within ${from}-${to} time window (${availableHours} hours available)`);
    }
  }

  getCurrentHour() {
    const now = this.getVilniusDate();
    return now.getHours();
  }

  async isOptimalTimeToStart(options = {}) {
    const { from = 0, to = 23, numberOfHours = 1 } = options;
    this.validateSlotOptions({ from, to, numberOfHours });
    const prices = await this.fetchTodayPrices();
    if (!prices || prices.length === 0) throw new Error('No price data available for today');
    const filteredPrices = prices.filter(price => price.hour >= from && price.hour <= to);
    if (filteredPrices.length < numberOfHours) {
      throw new Error(`Not enough price data in time window ${from}-${to}. Got ${filteredPrices.length} hours, need ${numberOfHours}`);
    }
    let cheapestSum = Infinity;
    let optimalStartHour = from;
    for (let i = 0; i <= filteredPrices.length - numberOfHours; i++) {
      const consecutivePrices = filteredPrices.slice(i, i + numberOfHours);
      const sum = consecutivePrices.reduce((total, price) => total + price.price, 0);
      if (sum < cheapestSum) {
        cheapestSum = sum;
        optimalStartHour = consecutivePrices[0].hour;
      }
    }
    const currentHour = this.getCurrentHour();
    return currentHour === optimalStartHour;
  }
}

async function testSimplifiedOptimizer() {
  console.log('=== Testing Simplified HomeyScript Optimizer ===\n');
  
  const optimizer = new NordPoolOptimizer();
  const currentHour = optimizer.getCurrentHour();

  try {
    console.log(`Current time: ${currentHour}:00\n`);

    // Test 1: Dishwasher (2 hours)
    console.log('🍽️  DISHWASHER (2 hours needed):');
    const shouldStartDishwasher = await optimizer.isOptimalTimeToStart({ numberOfHours: 2 });
    console.log(`   Should start now: ${shouldStartDishwasher ? '✅ YES' : '❌ NO'}`);

    // Test 2: Washing machine (3 hours)
    console.log('\n👕 WASHING MACHINE (3 hours needed):');
    const shouldStartWashing = await optimizer.isOptimalTimeToStart({ numberOfHours: 3 });
    console.log(`   Should start now: ${shouldStartWashing ? '✅ YES' : '❌ NO'}`);

    // Test 3: Quick appliance (1 hour)
    console.log('\n⚡ QUICK APPLIANCE (1 hour needed):');
    const shouldStartQuick = await optimizer.isOptimalTimeToStart({ numberOfHours: 1 });
    console.log(`   Should start now: ${shouldStartQuick ? '✅ YES' : '❌ NO'}`);

    // Test 4: Daytime only (8-18)
    console.log('\n☀️  DAYTIME ONLY (8:00-18:00, 2 hours):');
    try {
      const shouldStartDaytime = await optimizer.isOptimalTimeToStart({ 
        from: 8, 
        to: 18, 
        numberOfHours: 2 
      });
      console.log(`   Should start now: ${shouldStartDaytime ? '✅ YES' : '❌ NO'}`);
    } catch (error) {
      console.log(`   ⚠️  ${error.message}`);
    }

    // Show what the optimal hours actually are for reference
    console.log('\n📊 FOR REFERENCE - Optimal start hours:');
    const prices = await optimizer.fetchTodayPrices();

    // Find optimal for 1 hour
    let cheapest1h = prices[0];
    prices.forEach(price => {
      if (price.price < cheapest1h.price) cheapest1h = price;
    });
    console.log(`   1-hour appliance: ${cheapest1h.hour}:00 (€${cheapest1h.price.toFixed(2)}/MWh)`);

    // Find optimal for 2 hours
    let cheapest2hSum = Infinity;
    let optimal2hStart = 0;
    for (let i = 0; i <= prices.length - 2; i++) {
      const sum = prices[i].price + prices[i + 1].price;
      if (sum < cheapest2hSum) {
        cheapest2hSum = sum;
        optimal2hStart = prices[i].hour;
      }
    }
    console.log(`   2-hour appliance: ${optimal2hStart}:00 (avg €${(cheapest2hSum/2).toFixed(2)}/MWh)`);

    console.log('\n=== HOMEY FLOW USAGE ===');
    console.log('In your Homey flow, just use:');
    console.log('const optimizer = new NordPoolOptimizer();');
    console.log('return await optimizer.isOptimalTimeToStart({ numberOfHours: 2 });');
    console.log('\nReturns true/false - perfect for flow conditions! 🎯');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testSimplifiedOptimizer();
