// Example demonstrating the ProcessedPrice typedef usage

/**
 * @typedef {Object} ProcessedPrice
 * @property {number} original - Original price from API in EUR/MWh (can be negative)
 * @property {number} billing - Billing price used by electricity provider in EUR/MWh (negative prices = 0)
 * @property {string} display - Formatted display string in EUR/kWh with 5 decimal places (e.g., "0.00000 (-0.00520)" for negatives)
 * @property {boolean} isNegative - True if original price was negative
 * @property {number} originalKwh - Original price converted to EUR/kWh
 * @property {number} billingKwh - Billing price converted to EUR/kWh
 */

// Mock function to demonstrate typedef
function processPrice(originalPrice) {
  const billingPrice = Math.max(0, originalPrice); // Provider treats negative as 0

  // Convert from EUR/MWh to EUR/kWh (divide by 1000)
  const originalKwh = originalPrice / 1000;
  const billingKwh = billingPrice / 1000;

  // Format display with 5 decimal places
  const displayPrice = originalPrice < 0 ?
    `${billingKwh.toFixed(5)} (${originalKwh.toFixed(5)})` :
    originalKwh.toFixed(5);

  return {
    original: originalPrice,           // Keep original MWh values for calculations
    billing: billingPrice,            // Keep billing MWh values for calculations
    display: displayPrice,            // Display in kWh format
    isNegative: originalPrice < 0,
    originalKwh: originalKwh,         // EUR/kWh values for reference
    billingKwh: billingKwh
  };
}

/**
 * Example function that returns ProcessedPrice
 * @returns {ProcessedPrice} Processed price information
 */
function getCurrentPrice() {
  return processPrice(-5.20); // Example negative price
}

/**
 * Example function that uses ProcessedPrice
 * @param {ProcessedPrice} priceInfo - Processed price object
 * @returns {string} Formatted message
 */
function formatPriceMessage(priceInfo) {
  if (priceInfo.isNegative) {
    return `🎉 Free electricity! Market price: €${priceInfo.originalKwh.toFixed(5)}/kWh, You pay: €${priceInfo.billingKwh.toFixed(5)}/kWh`;
  } else {
    return `💰 Current price: €${priceInfo.display}/kWh`;
  }
}

// Usage examples
console.log('=== ProcessedPrice Typedef Examples ===\n');

// Example 1: Get current price
const currentPrice = getCurrentPrice();
console.log('1. ProcessedPrice object structure:');
console.log('   original:', currentPrice.original, '(EUR/MWh)');
console.log('   billing:', currentPrice.billing, '(EUR/MWh)');
console.log('   originalKwh:', currentPrice.originalKwh, '(EUR/kWh)');
console.log('   billingKwh:', currentPrice.billingKwh, '(EUR/kWh)');
console.log('   display:', currentPrice.display, '(formatted for display)');
console.log('   isNegative:', currentPrice.isNegative);

// Example 2: Use in message formatting
console.log('\n2. Formatted message:');
console.log('  ', formatPriceMessage(currentPrice));

// Example 3: Different price scenarios
console.log('\n3. Different price scenarios:');

const scenarios = [
  { name: 'Negative price', value: -8.75 },
  { name: 'Zero price', value: 0 },
  { name: 'Positive price', value: 25.30 },
  { name: 'High price', value: 158.60 }
];

scenarios.forEach(scenario => {
  const processed = processPrice(scenario.value);
  console.log(`   ${scenario.name}: €${processed.display}/kWh (billing: €${processed.billingKwh.toFixed(5)}/kWh)`);
});

// Example 4: Homey flow usage
console.log('\n4. Homey flow usage examples:');
console.log(`
// Basic check for negative prices
const priceInfo = await optimizer.getCurrentPrice(); // Returns ProcessedPrice
if (priceInfo.isNegative) {
  return true; // Start appliance during free electricity
}

// Display price in notification
const priceInfo = await optimizer.getCurrentPrice(); // Returns ProcessedPrice
return \`Current electricity price: €\${priceInfo.display}/kWh\`;

// Compare billing prices
const priceInfo = await optimizer.getCurrentPrice(); // Returns ProcessedPrice
const averagePrice = await optimizer.getDailyAveragePrice(); // Returns number (EUR/MWh)
return priceInfo.billing < averagePrice; // Compare billing price to average
`);

console.log('\n✅ ProcessedPrice typedef provides type safety and clear documentation!');
