const yargs = require('yargs')
const { hideBin } = require('yargs/helpers')
const { processReleaseBranch, createVersionTag, setStableVersion } = require('./repo-version');
const { getVersion } = require('./version-utils');

const usageMessage = `
Version Management Commands:

  update-version --branch=BRANCH   Update version based on release branch name
  set-stable-version --main-branch Set stable version (remove RC suffix)
  create-tag                       Create an annotated git tag based on the current version

Examples:
  Direct usage:
    node repo-version-cli.js update-version --branch="release/v2.3.0"
    node repo-version-cli.js set-stable-version --main-branch
    node repo-version-cli.js create-tag

  Using npm scripts:
    npm run version:update -- --branch="release/v2.3.0"
    npm run version:set-stable -- --main-branch
    npm run version:create-tag

For more details, use --help with any command.
`;

yargs(hideBin(process.argv))
  .usage(usageMessage)
  .command('update-version', 'Update version based on release branch name',
    (yargs) => {
      return yargs.option('branch', {
        alias: 'b',
        describe: 'Branch name to process (format: (release|patch|hotfix)/vX.Y.Z)',
        type: 'string',
        demandOption: true
      });
    },
    (argv) => {
      try {
        // Validate branch name format early
        const branchRegex = /^(release|patch|hotfix)\/v\d+\.\d+\.\d+$/;
        if (!branchRegex.test(argv.branch)) {
          console.error(`Error: Invalid branch name format. Expected: (release|patch|hotfix)/vX.Y.Z, got: ${argv.branch}`);
          process.exit(1); // Exit immediately if branch name is invalid
        }

        const result = processReleaseBranch(argv.branch);
        console.log('Version updated to', result.version);
        process.exit(0);
      } catch (error) {
        console.error('Error:', error.message);
        process.exit(1);
      }
    }
  )
  .command('set-stable-version', 'Set stable version without RC suffix',
    (yargs) => {
      return yargs.option('main-branch', {
        alias: 'm',
        describe: 'Confirm this is being run on the main branch',
        type: 'boolean',
        default: false
      })
    },
    (argv) => {
      try {
        if (!argv.mainBranch) {
          console.error('Error: This command should only be run on the main branch. Use --main-branch to confirm.');
          process.exit(1);
        }

        const currentVersion = getVersion();
        const stableVersion = setStableVersion();
        if (currentVersion.version === stableVersion) {
          console.error('Error: The current version is already stable. It is expected that current version is a release candidate.');
          process.exit(1);
        }
        console.log('Version set to stable:', stableVersion);
        process.exit(0);
      } catch (error) {
        console.error('Error:', error.message);
        process.exit(1);
      }
    }
  )
  .command('create-tag', 'Create an annotated git tag based on the current version', {},
    () => {
      try {
        const tagName = createVersionTag();
        console.log('Created tag:', tagName);
        process.exit(0);
      } catch (error) {
        console.error('Error:', error.message);
        process.exit(1);
      }
    }
  )
  .demandCommand(1, 'No command specified. Use one of: update-version, set-stable-version, or create-tag.')
  .recommendCommands()
  .strict()
  .help(usageMessage)
  .epilogue('For more information, refer to the VERSION_MANAGEMENT.md documentation file.')
  .argv;
