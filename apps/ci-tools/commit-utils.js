/**
 * Removes messages from the array if the commit message starts with "build: Bump up".
 *
 * @param {string[]} commitMessages - An array of commit messages.
 * @returns {string[]} A new array with the filtered commit messages.
 */
function removeBumpUpMessages(commitMessages) {
  if (!Array.isArray(commitMessages)) {
    throw new Error("Invalid input: commitMessages must be an array.");
  }

  return commitMessages.filter(message => !message.startsWith("build: Bump up"));
}

module.exports = {
  removeBumpUpMessages,
}
