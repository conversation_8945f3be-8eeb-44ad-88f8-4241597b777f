// Test negative price handling in the updated optimizer

// Mock optimizer with test data including negative prices
class MockNordPoolOptimizer {
  constructor() {
    this.baseUrl = 'https://dashboard.elering.ee/api/nps/price';
    this.priceCache = null;
    this.cacheDate = null;
    
    // Mock data with negative prices
    this.mockPrices = [
      { hour: 0, price: 45.50 },
      { hour: 1, price: -5.20 },  // Negative price
      { hour: 2, price: -8.75 },  // Negative price
      { hour: 3, price: 12.30 },
      { hour: 4, price: 25.60 },
      { hour: 5, price: 35.40 },
      { hour: 6, price: 42.10 },
      { hour: 7, price: 38.90 },
      { hour: 8, price: 33.20 },
      { hour: 9, price: 28.70 },
      { hour: 10, price: 24.80 },
      { hour: 11, price: 21.50 },
      { hour: 12, price: 18.90 },
      { hour: 13, price: 16.40 },
      { hour: 14, price: 19.20 },
      { hour: 15, price: 22.80 },
      { hour: 16, price: 26.50 },
      { hour: 17, price: 31.70 },
      { hour: 18, price: 37.20 },
      { hour: 19, price: 41.80 },
      { hour: 20, price: 44.60 },
      { hour: 21, price: 46.30 },
      { hour: 22, price: 43.90 },
      { hour: 23, price: 40.20 }
    ];
  }

  async fetchTodayPrices() {
    return this.mockPrices;
  }

  getCurrentHour() {
    return 1; // Mock current hour to be during negative price period
  }

  processPrice(originalPrice) {
    const billingPrice = Math.max(0, originalPrice); // Provider treats negative as 0

    // Convert from EUR/MWh to EUR/kWh (divide by 1000)
    const originalKwh = originalPrice / 1000;
    const billingKwh = billingPrice / 1000;

    // Format display with 5 decimal places
    const displayPrice = originalPrice < 0 ?
      `${billingKwh.toFixed(5)} (${originalKwh.toFixed(5)})` :
      originalKwh.toFixed(5);

    return {
      original: originalPrice,           // Keep original MWh values for calculations
      billing: billingPrice,            // Keep billing MWh values for calculations
      display: displayPrice,            // Display in kWh format
      isNegative: originalPrice < 0,
      originalKwh: originalKwh,         // EUR/kWh values for reference
      billingKwh: billingKwh
    };
  }

  findCheapestConsecutivePeriod(filteredPrices, numberOfHours) {
    let cheapestSum = Infinity;
    let optimalStartHour = filteredPrices[0].hour;

    for (let i = 0; i <= filteredPrices.length - numberOfHours; i++) {
      const consecutivePrices = filteredPrices.slice(i, i + numberOfHours);
      // Use billing prices (negative = 0) for calculations
      const sum = consecutivePrices.reduce((total, price) => {
        const billingPrice = Math.max(0, price.price);
        return total + billingPrice;
      }, 0);

      if (sum < cheapestSum) {
        cheapestSum = sum;
        optimalStartHour = consecutivePrices[0].hour;
      }
    }

    return optimalStartHour;
  }

  async isOptimalTimeToStart(options = {}) {
    const { from = 0, to = 23, numberOfHours = 1 } = options;
    const prices = await this.fetchTodayPrices();
    const filteredPrices = prices.filter(price => price.hour >= from && price.hour <= to);
    const optimalStartHour = this.findCheapestConsecutivePeriod(filteredPrices, numberOfHours);
    const currentHour = this.getCurrentHour();
    return currentHour === optimalStartHour;
  }

  async getCurrentPrice() {
    const prices = await this.fetchTodayPrices();
    const currentHour = this.getCurrentHour();
    const currentPriceData = prices.find(price => price.hour === currentHour);
    return this.processPrice(currentPriceData.price);
  }

  async getDailyAveragePrice() {
    const prices = await this.fetchTodayPrices();
    const sum = prices.reduce((total, price) => {
      const billingPrice = Math.max(0, price.price);
      return total + billingPrice;
    }, 0);
    return sum / prices.length;
  }

  async getDailyPriceExtremes() {
    const prices = await this.fetchTodayPrices();

    const cheapest = prices.reduce((min, current) => {
      const currentBilling = Math.max(0, current.price);
      const minBilling = Math.max(0, min.price);
      return currentBilling < minBilling ? current : min;
    });

    const expensive = prices.reduce((max, current) => {
      const currentBilling = Math.max(0, current.price);
      const maxBilling = Math.max(0, max.price);
      return currentBilling > maxBilling ? current : max;
    });

    return {
      cheapest: { 
        hour: cheapest.hour, 
        price: this.processPrice(cheapest.price)
      },
      expensive: { 
        hour: expensive.hour, 
        price: this.processPrice(expensive.price)
      }
    };
  }
}

async function testNegativePriceHandling() {
  console.log('=== Testing Negative Price Handling ===\n');
  
  const optimizer = new MockNordPoolOptimizer();
  
  try {
    // Test 1: Current price display
    console.log('1. Current Price Display:');
    const currentPrice = await optimizer.getCurrentPrice();
    console.log(`   Original price: €${currentPrice.original}/MWh (€${currentPrice.originalKwh.toFixed(5)}/kWh)`);
    console.log(`   Billing price: €${currentPrice.billing}/MWh (€${currentPrice.billingKwh.toFixed(5)}/kWh)`);
    console.log(`   Display format: €${currentPrice.display}/kWh`);
    console.log(`   Is negative: ${currentPrice.isNegative}`);

    // Test 2: Daily average calculation
    console.log('\n2. Daily Average (using billing prices):');
    const avgPrice = await optimizer.getDailyAveragePrice();
    console.log(`   Average billing price: €${avgPrice.toFixed(2)}/MWh (€${(avgPrice/1000).toFixed(5)}/kWh)`);

    // Test 3: Price extremes
    console.log('\n3. Daily Price Extremes:');
    const extremes = await optimizer.getDailyPriceExtremes();
    console.log(`   Cheapest: ${extremes.cheapest.hour}:00 - €${extremes.cheapest.price.display}/kWh`);
    console.log(`   Most expensive: ${extremes.expensive.hour}:00 - €${extremes.expensive.price.display}/kWh`);

    // Test 4: Optimal time calculation
    console.log('\n4. Optimal Time Calculation:');
    const isOptimal2h = await optimizer.isOptimalTimeToStart({ numberOfHours: 2 });
    const isOptimal3h = await optimizer.isOptimalTimeToStart({ numberOfHours: 3 });
    console.log(`   Current hour (1:00) optimal for 2-hour appliance: ${isOptimal2h}`);
    console.log(`   Current hour (1:00) optimal for 3-hour appliance: ${isOptimal3h}`);

    // Test 5: Show calculation logic
    console.log('\n5. Calculation Logic Demonstration:');
    console.log('   Hours 1-2: Original prices [-5.20, -8.75], Billing prices [0, 0] = Sum: 0');
    console.log('   Hours 2-3: Original prices [-8.75, 12.30], Billing prices [0, 12.30] = Sum: 12.30');
    console.log('   Hours 12-13: Original prices [18.90, 16.40], Billing prices [18.90, 16.40] = Sum: 35.30');
    console.log('   → Hours 1-2 should be optimal (lowest billing sum = 0)');

    console.log('\n✅ All negative price handling tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('   - Negative prices are treated as 0 for all calculations');
    console.log('   - Display shows EUR/kWh with 5 decimal places: "0.00000 (-0.00520)"');
    console.log('   - Internal calculations use EUR/MWh, display converts to EUR/kWh');
    console.log('   - Optimal time selection prioritizes periods with negative prices');
    console.log('   - Daily averages exclude negative price impact');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testNegativePriceHandling();
