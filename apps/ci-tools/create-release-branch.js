const { execSync } = require('child_process');
const {
  getCurrentBranch,
  switchToBranch,
  doesBranchExist,
  hasUncommittedChanges,
  createBranch,
  pushBranch
} = require('./git-utils');
const { getVersion, getBaseVersionFromRc } = require('./version-utils');

/**
 * Generates a new branch name based on the current version and release type.
 *
 * @param {string} releaseType - The type of release: 'release', 'patch', or 'hotfix'.
 * @returns {string} The generated branch name.
 * @throws {Error} If there's an issue generating the branch name.
 */
function generateBranchName(releaseType) {
  try {
    const { version } = getVersion();
    const baseVersion = getBaseVersionFromRc(version);

    // Parse the version components
    const versionParts = baseVersion.split('.');
    if (versionParts.length !== 3) {
      throw new Error(`Invalid version format: ${baseVersion}`);
    }

    const [major, minor, patch] = versionParts.map(Number);

    let newVersion;

    switch (releaseType) {
      case 'release':
        newVersion = `${major}.${minor + 1}.0`;
        break;
      case 'patch':
      case 'hotfix':
        newVersion = `${major}.${minor}.${patch + 1}`;
        break;
      default:
        throw new Error(`Invalid release type: ${releaseType}`);
    }

    return `${releaseType}/v${newVersion}`;
  } catch (error) {
    throw new Error(`Failed to generate branch name: ${error.message}`);
  }
}

/**
 * Creates a release branch based on the specified type and source branch.
 *
 * @param {string} releaseType - The type of release: 'release', 'patch', or 'hotfix'.
 * @param {string} sourceBranch - The source branch to base the release on.
 * @param {boolean} pushToRemote - Whether to push the branch to remote after creation.
 * @returns {Object} Result with new branch info.
 * @throws {Error} If any validation fails, branch creation fails, or there are uncommitted changes in the repository.
 */
function prepareReleaseBranch(releaseType, sourceBranch, pushToRemote = false) {
  console.log(`Creating ${releaseType} branch from ${sourceBranch}...`);

  // Check for uncommitted changes before doing any
  // thing else
  const { hasChanges, statusMessage } = hasUncommittedChanges();

  if (hasChanges) {
    const errorMessage =
      'Branch creation blocked as a precaution - working directory must be clean.\n\n' +
      statusMessage + '\n\n' +
      'Please commit, stash, or discard your changes before creating a new branch.';

    throw new Error(errorMessage);
  }

  // Check if the source branch exists
  if (!doesBranchExist(sourceBranch)) {
    throw new Error(`Branch "${sourceBranch}" does not exist in the repository.`);
  }

  // Get the current branch before switching
  const initialBranch = getCurrentBranch();

  // Switch to source branch and pull latest changes
  if (initialBranch !== sourceBranch) {
    console.log(`Switching to source branch: ${sourceBranch}`);
    switchToBranch(sourceBranch);
  } else {
    console.log(`Already on source branch: ${sourceBranch}`);
    // Still pull the latest changes
    try {
      execSync('git pull', { stdio: 'inherit' });
    } catch (error) {
      throw new Error(`Failed to pull latest changes: ${error.message}`);
    }
  }

  // Generate the new branch name based on the release type
  const newBranchName = generateBranchName(releaseType);

  // Check if the branch already exists using doesBranchExist
  if (doesBranchExist(newBranchName)) {
    throw new Error(
      `Branch "${newBranchName}" already exists. ` +
      'To proceed with this branch:' +
      '\n1. Proceed with the existing branch manually if you are sure about the codebase in there: git checkout ' + newBranchName +
      '\n2. Remove the existing branch and try again: git branch -D ' + newBranchName
    );
  }

  // Create the new branch
  console.log(`Creating new branch: ${newBranchName}`);
  createBranch(newBranchName);

  // Push to remote if requested
  if (pushToRemote) {
    console.log(`Pushing branch "${newBranchName}" to remote...`);
    pushBranch(newBranchName);
  }

  return {
    newBranch: newBranchName,
    sourceVersion: getVersion().version,
    createdFrom: sourceBranch,
    pushed: pushToRemote
  };
}

module.exports = {
  prepareReleaseBranch,
  generateBranchName
};
