// const vilniusTime = new Date(new Date("2025-06-06T00:00:00").toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
// console.log(vilniusTime);
// console.log(vilniusTime.toISOString());

const start = new Date(new Date("2025-06-06T00:00:00").toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
const end = new Date(new Date("2025-06-06T23:59:59.999").toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
end.setMilliseconds(999);
console.log(`https://dashboard.elering.ee/api/nps/price?start=${start.toISOString()}&end=${end.toISOString()}`);

// host: 'dashboard.elering.ee',
//   path: `/api/nps/price?start=${start.toISOString()}&end=${end.toISOString()}`

// =============================================

/**
 * @typedef {Object} PriceData
 * @property {number} timestamp - The Unix timestamp in seconds.
 * @property {number} price - The price at the given timestamp.
 * @property {string} [readableTime] - Optional: The human-readable time string (e.g., "HH:MM:SS").
 * @property {string} [fullDate] - Optional: The full localized date and time string.
 */

/**
 * @type {PriceData[]}
 */
const priceDataArray = [
  { timestamp: 1749157200, price: 102.23 },
  { timestamp: 1749160800, price: 153.15 },
  { timestamp: 1749164400, price: 62.81 },
  { timestamp: 1749168000, price: 58.56 },
  { timestamp: 1749171600, price: 53.97 },
  { timestamp: 1749175200, price: 52.02 },
  { timestamp: 1749178800, price: 65.99 },
  { timestamp: 1749182400, price: 158.6 },
  { timestamp: 1749186000, price: 149.99 },
  { timestamp: 1749189600, price: 61.92 },
  { timestamp: 1749193200, price: 10.87 },
  { timestamp: 1749196800, price: 8.22 },
  { timestamp: 1749200400, price: 4.47 },
  { timestamp: 1749204000, price: 3.5 },
  { timestamp: 1749207600, price: 3.5 },
  { timestamp: 1749211200, price: 3.51 },
  { timestamp: 1749214800, price: 7.29 },
  { timestamp: 1749218400, price: 14 },
  { timestamp: 1749222000, price: 4.9 },
  { timestamp: 1749225600, price: 76.39 },
  { timestamp: 1749229200, price: 144.99 },
  { timestamp: 1749232800, price: 75.36 },
  { timestamp: 1749236400, price: 72.36 },
  { timestamp: 1749240000, price: 70.5 }
];

/**
 * Converts an array of price data objects, adding human-readable time information.
 *
 * @param {PriceData[]} dataArray - The array of price data objects to convert.
 * @returns {PriceData[]} A new array with added readableTime and fullDate properties.
 */
function convertPriceDataTimestamps(dataArray) {
  return dataArray.map(item => {
    // Convert Unix timestamp (seconds) to milliseconds for the Date constructor
    const dateObject = new Date(item.timestamp * 1000);

    // Get hours, minutes, and seconds, padding with a leading zero if necessary
    const hours = dateObject.getHours().toString().padStart(2, '0');
    const minutes = dateObject.getMinutes().toString().padStart(2, '0');
    const seconds = dateObject.getSeconds().toString().padStart(2, '0');

    // Create a human-readable time string in HH:MM:SS format
    const readableTime = `${hours}:${minutes}:${seconds}`;

    // Get a localized string for the full date and time
    const fullDate = dateObject.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' });

    return {
      ...item,
      readableTime: readableTime,
      fullDate: fullDate // Also adding the full localized date string
    };
  });
}

const convertedData = convertPriceDataTimestamps(priceDataArray);

console.log(convertedData);

/*
 Example output for the first item (assuming a US locale and EST timezone for toLocaleString):
 {
 timestamp: 1749157200,
 price: 102.23,
 readableTime: '00:00:00', // This will be based on your system's local timezone
 fullDate: '2025-06-06 00:00:00' // This will also be localized
 }
 */
