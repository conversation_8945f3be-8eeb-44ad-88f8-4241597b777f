const jiraTicketRegex = /PLAT-\d+/g;

/**
 * Extracts unique Jira ticket keys from the given commit message text.
 *
 * @param {string} commitMessage - The commit message from which to extract Jira keys.
 * @return {string[]} An array of unique Jira ticket keys found in the commit message.
 */
function extractJiraKeysFromCommitMessage(commitMessage) {
  if (!commitMessage) {
    console.warn("Warning! No commit messages supplied to extract Jira keys!");
    return [];
  }

  return [...new Set(commitMessage.match(jiraTicketRegex) || [])];
}

/**
 * Formats the number of Jira tickets in a given list into a readable string.
 * Example: "3 Jira tickets", "1 Jira ticket", "3 new Jira tickets".
 *
 * @param {string[]} tickets - An array representing the Jira tickets.
 * @param {string} [prefix] - An optional prefix to add to the formatted string.
 * @return {string} A formatted string indicating the count of Jira tickets.
 */
function formatJiraTicketCount(tickets, prefix) {
  const count = tickets.length;
  const formattedPrefix = prefix ? `${prefix} ` : '';
  const pluralSuffix = count === 1 ? '' : 's';

  return `${count} ${formattedPrefix}Jira ticket${pluralSuffix}`;
}

module.exports = {
  extractJiraKeysFromCommitMessage,
  formatJiraTicketCount,
};
