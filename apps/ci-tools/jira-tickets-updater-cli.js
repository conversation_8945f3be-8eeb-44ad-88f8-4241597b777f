#!/usr/bin/env node

const path = require('path');
const yargs = require('yargs');
const { hideBin } = require('yargs/helpers');
const { updateJiraTickets } = require('./jira-ticket-updater');
const { defaultTicketRegex, validateTicketFormat, validateAppsDirectory } = require('./jira-ticket-updater/ticket-analyzer');

const jiraTickets = [];

const argv = yargs(hideBin(process.argv))
  .usage('Usage: $0 [options]')
  .option('jira-url', {
    alias: 'u',
    type: 'string',
    description: 'Jira API URL (or use JIRA_API_URL env var)'
  })
  .option('jira-token', {
    alias: 'x',
    type: 'string',
    description: 'Jira API token (or use JIRA_API_TOKEN env var)'
  })
  .option('jira-email', {
    alias: 'e',
    type: 'string',
    description: 'Jira account email (or use JIRA_EMAIL env var)'
  })
  .option('tickets', {
    alias: 't',
    type: 'string',
    description: 'Comma-separated list of Jira ticket IDs to update',
    demandOption: true
  })
  .option('fix-version', {
    alias: 'v',
    type: 'string',
    description: 'Fix version to set (defaults to "Release <version>" from version.json)'
  })
  .option('actual-version', {
    type: 'string',
    description: 'Actual versions to set (defaults to version number from version.json)'
  })
  .option('ticket-regex', {
    alias: 'r',
    type: 'string',
    description: `Regex pattern to match ticket IDs in commit messages (default: ${defaultTicketRegex})`
  })
  .option('apps-dir', {
    alias: 'a',
    type: 'string',
    description: 'Path to the apps directory (default: ./apps)',
    default: './apps'
  })
  .option('update-fix-version', {
    type: 'boolean',
    description: 'Update the fix version field',
    default: false
  })
  .option('update-labels', {
    type: 'boolean',
    description: 'Update the labels field with service types',
    default: false
  })
  .option('update-actual-version', {
    type: 'boolean',
    description: 'Update the actual versions custom field',
    default: false
  })
  .option('dry-run', {
    alias: 'd',
    type: 'boolean',
    description: 'Perform a dry run without making changes to Jira',
    default: false
  })
  .option('max-parallel', {
    alias: 'p',
    type: 'number',
    description: 'Maximum number of tickets to process in parallel (or use MAX_PARALLEL_TICKETS env var)',
    default: 10
  })
  .example('$0 --tickets "PLAT-123,PLAT-456" --update-fix-version --update-labels --jira-url https://your-domain.atlassian.net', 'Update tickets with both fix version and labels')
  .example('$0 --tickets "PLAT-123,PLAT-456" --update-actual-version --update-labels', 'Update tickets with actual version and labels')
  .example('$0 --tickets "PLAT-123,PLAT-456" --update-fix-version --fix-version "Release 2.44.2"', 'Update specific tickets with custom fix version only')
  .example('$0 --tickets "PLAT-123,PLAT-456" --update-actual-version --actual-version "2.44.2"', 'Update specific tickets with custom actual version only')
  .example('$0 --tickets "PLAT-123,PLAT-456" --update-labels --dry-run', 'Show what label updates would be made without making changes')
  .example('$0 --tickets "PLAT-123" --update-labels', 'Only update labels, not versions')
  .example('$0 --tickets "PLAT-123,PLAT-456,PLAT-789" --update-fix-version --update-actual-version --update-labels --max-parallel 5', 'Process up to 5 tickets in parallel with all updates')
  .epilogue('For more information, refer to the .circleci/README.md documentation file.')
  .check(argv => {
    if (!argv.updateFixVersion && !argv.updateLabels && !argv.updateActualVersion) {
      throw new Error('At least one of --update-fix-version, --update-actual-version or --update-labels must be explicitly provided');
    }

    if (!argv.jiraUrl && !process.env.JIRA_API_URL) {
      throw new Error('Jira API URL is required (use --jira-url or JIRA_API_URL env var)');
    }

    if (!argv.jiraToken && !process.env.JIRA_API_TOKEN) {
      throw new Error('Jira API token is required (use --jira-token or JIRA_API_TOKEN env var)');
    }

    if (!argv.jiraEmail && !process.env.JIRA_EMAIL) {
      throw new Error('Jira email is required (use --jira-email or JIRA_EMAIL env var)');
    }

    const resolvedAppsDir = path.resolve(argv.appsDir);
    if (!validateAppsDirectory(resolvedAppsDir)) {
      throw new Error(`Apps directory not found at ${resolvedAppsDir}`);
    }

    const ticketRegex = argv.ticketRegex || defaultTicketRegex;
    const allEnteredJiraTickets = argv.tickets.split(',').map(t => t.trim());
    const filteredTickets = [];

    allEnteredJiraTickets.forEach(ticket => {
      if (validateTicketFormat(ticket, ticketRegex)) {
        jiraTickets.push(ticket);
      } else {
        filteredTickets.push(ticket);
      }
    });

    if (filteredTickets.length > 0) {
      console.warn(`The following tickets do not match the required format (${ticketRegex}) and will be excluded: ${filteredTickets.join(', ')}`);
    }

    if (jiraTickets.length === 0) {
      throw new Error(`No tickets matched the required format pattern: ${ticketRegex}`);
    }

    const maxParallel = argv.maxParallel || parseInt(process.env.MAX_PARALLEL_TICKETS) || 10;
    if (maxParallel < 1) {
      throw new Error('Maximum parallel tickets must be at least 1');
    }
    if (maxParallel > 100) {
      throw new Error('Maximum parallel tickets should not exceed 100 to avoid overloading the API');
    }

    return true;
  })
  .help()
  .argv;

// Run the CLI when the script is executed directly
if (require.main === module) {
  // Map CLI args to options format expected by updateJiraTickets
  const options = {
    jiraUrl: argv.jiraUrl || process.env.JIRA_API_URL,
    jiraToken: argv.jiraToken || process.env.JIRA_API_TOKEN,
    jiraEmail: argv.jiraEmail || process.env.JIRA_EMAIL,
    tickets: jiraTickets,
    fixVersion: argv.fixVersion,
    actualVersion: argv.actualVersion,
    ticketRegex: argv.ticketRegex || defaultTicketRegex,
    dryRun: argv.dryRun,
    appsDir: argv.appsDir,
    updateFixVersion: argv.updateFixVersion,
    updateLabels: argv.updateLabels,
    updateActualVersion: argv.updateActualVersion,
    maxParallel: argv.maxParallel || parseInt(process.env.MAX_PARALLEL_TICKETS) || 10
  };

  (async () => {
    try {
      console.log(`Using provided tickets: ${jiraTickets.join(', ')}`);

      const result = await updateJiraTickets(options);

      if (result.success) {
        process.exit(0);
      } else {
        process.exit(1);
      }
    } catch (error) {
      console.error('Unexpected error:', error.message);
      process.exit(1);
    }
  })();
}
