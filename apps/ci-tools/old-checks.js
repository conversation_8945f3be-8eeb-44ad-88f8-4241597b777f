// =====================================================================================================================
// ======================================================= TESTS =======================================================
// =====================================================================================================================

const testData0 = {
  capabilitiesObj: {
    meter_price_h0: { value: 0.05687 },
    meter_price_h1: { value: -0.00100 },
    meter_price_h2: { value: -0.00200 },
    meter_price_h3: { value: 0.01500 },
    meter_price_h4: { value: -0.00300 },
    meter_price_h5: { value: 0.05876 },
    meter_price_h6: { value: 0.00490 },
    meter_price_h7: { value: 0.00801 },
    meter_price_this_day_avg: { value: 0.01900 },
    meter_price_this_day_lowest: { value: -0.00300 },
    hour_this_day_lowest: { value: 16 } // Note: This is 4 hours from now in our 8-hour window
  }
};
// Expected: Should choose hours h1-h3 (3 consecutive hours) which has 2 negative prices
// This is better than h2-h4 (also 2 negative prices) because h3 (0.01500) is lower than h5 (0.05876)

// Test Dataset 1: Multiple negative prices
// This tests the scenario where there are multiple negative prices spread throughout the day
const testData1 = {
  capabilitiesObj: {
    meter_price_h0: { value: 0.05687 },
    meter_price_h1: { value: -0.00100 },
    meter_price_h2: { value: 0.02500 },
    meter_price_h3: { value: -0.00300 },
    meter_price_h4: { value: -0.00200 },
    meter_price_h5: { value: 0.05876 },
    meter_price_h6: { value: 0.00490 },
    meter_price_h7: { value: 0.00801 },
    meter_price_this_day_avg: { value: 0.01900 },
    meter_price_this_day_lowest: { value: -0.00300 },
    hour_this_day_lowest: { value: 15 } // Note: This is 3 hours from now in our 8-hour window
  }
};
// Expected: Should choose hours h1-h3 (3 consecutive hours) which has 2 negative prices

// Test Dataset 2: Negative prices at the beginning
// Tests when the negative prices are clustered at the start of the range
const testData2 = {
  capabilitiesObj: {
    meter_price_h0: { value: -0.00500 },
    meter_price_h1: { value: -0.00800 },
    meter_price_h2: { value: -0.00100 },
    meter_price_h3: { value: 0.03000 },
    meter_price_h4: { value: 0.04500 },
    meter_price_h5: { value: 0.05800 },
    meter_price_h6: { value: 0.06400 },
    meter_price_h7: { value: 0.07000 },
    meter_price_this_day_avg: { value: 0.02000 },
    meter_price_this_day_lowest: { value: -0.00800 },
    hour_this_day_lowest: { value: 13 } // This corresponds to h1 in our window
  }
};
// Expected: Should choose hours h0-h2 (3 consecutive hours) which has 3 negative prices

// Test Dataset 3: Negative prices at the end
// Tests when the negative prices are clustered at the end of the range
const testData3 = {
  capabilitiesObj: {
    meter_price_h0: { value: 0.08500 },
    meter_price_h1: { value: 0.06000 },
    meter_price_h2: { value: 0.03000 },
    meter_price_h3: { value: 0.01000 },
    meter_price_h4: { value: 0.00100 },
    meter_price_h5: { value: -0.00200 },
    meter_price_h6: { value: -0.00400 },
    meter_price_h7: { value: -0.00350 },
    meter_price_this_day_avg: { value: 0.02000 },
    meter_price_this_day_lowest: { value: -0.00400 },
    hour_this_day_lowest: { value: 18 } // This corresponds to h6 in our window
  }
};
// Expected: Should choose hours h5-h7 (3 consecutive hours) which has 3 negative prices

// Test Dataset 4: No negative prices, but some very low prices
// Tests when there are no negative prices, only varying positive prices
const testData4 = {
  capabilitiesObj: {
    meter_price_h0: { value: 0.08500 },
    meter_price_h1: { value: 0.07600 },
    meter_price_h2: { value: 0.06000 },
    meter_price_h3: { value: 0.00900 },
    meter_price_h4: { value: 0.00750 },
    meter_price_h5: { value: 0.00500 },
    meter_price_h6: { value: 0.03500 },
    meter_price_h7: { value: 0.05500 },
    meter_price_this_day_avg: { value: 0.04000 },
    meter_price_this_day_lowest: { value: 0.00500 },
    hour_this_day_lowest: { value: 17 } // This corresponds to h5 in our window
  }
};
// Expected: Should choose hours h3-h5 (3 consecutive hours) which has the lowest average

// Test Dataset 5: Negative prices but lowest hour coming soon
// Tests when there's a better negative price coming soon but not included in best range
const testData5 = {
  capabilitiesObj: {
    meter_price_h0: { value: 0.02500 },
    meter_price_h1: { value: -0.00100 },
    meter_price_h2: { value: -0.00050 },
    meter_price_h3: { value: 0.01000 },
    meter_price_h4: { value: -0.00800 }, // Much better negative price
    meter_price_h5: { value: 0.03000 },
    meter_price_h6: { value: 0.04000 },
    meter_price_h7: { value: 0.05000 },
    meter_price_this_day_avg: { value: 0.02000 },
    meter_price_this_day_lowest: { value: -0.00800 },
    hour_this_day_lowest: { value: 16 } // This corresponds to h4 in our window
  }
};
// Expected: Should prioritize having more negative hours (h1-h3) over having the single best hour (h4)

// Test Dataset 6: Above day average
// Tests when the best available range is still significantly above day average
const testData6 = {
  capabilitiesObj: {
    meter_price_h0: { value: 0.09000 },
    meter_price_h1: { value: 0.08500 },
    meter_price_h2: { value: 0.08000 },
    meter_price_h3: { value: 0.07500 },
    meter_price_h4: { value: 0.07000 },
    meter_price_h5: { value: 0.06500 },
    meter_price_h6: { value: 0.06000 },
    meter_price_h7: { value: 0.05500 },
    meter_price_this_day_avg: { value: 0.04000 },
    meter_price_this_day_lowest: { value: 0.02000 },
    hour_this_day_lowest: { value: 23 } // This is outside our 8-hour window
  }
};
// Expected: Should mark as not optimal since all prices are above day average by >20%

// Test Dataset 7: Mixed case with large negative and positive values
// Tests how algorithm handles extreme price differences
const testData7 = {
  capabilitiesObj: {
    meter_price_h0: { value: 0.15000 },
    meter_price_h1: { value: 0.10000 },
    meter_price_h2: { value: -0.04000 }, // Large negative
    meter_price_h3: { value: 0.12000 },
    meter_price_h4: { value: -0.03000 }, // Large negative
    meter_price_h5: { value: 0.08000 },
    meter_price_h6: { value: 0.07000 },
    meter_price_h7: { value: 0.06000 },
    meter_price_this_day_avg: { value: 0.05000 },
    meter_price_this_day_lowest: { value: -0.04000 },
    hour_this_day_lowest: { value: 14 } // This corresponds to h2 in our window
  }
};
// Expected: Should prioritize h2-h4 because it has 2 negative prices, despite high positive in h3

// Test Dataset 8: 2 Negative Prices vs 3 Hours with Lower Average
// This tests if the algorithm properly prefers 3 consecutive hours with lower positive values
// over 3 hours with 2 negative prices but one very high positive
const testData8 = {
  capabilitiesObj: {
    meter_price_h0: { value: 0.15000 },
    meter_price_h1: { value: 0.10000 },
    meter_price_h2: { value: -0.04000 }, // Large negative
    meter_price_h3: { value: 0.12000 },  // Very high positive
    meter_price_h4: { value: -0.03000 }, // Large negative
    meter_price_h5: { value: 0.01500 },  // Much smaller than h3
    meter_price_h6: { value: 0.00800 },  // Much smaller than h3
    meter_price_h7: { value: 0.01000 },  // Much smaller than h3
    meter_price_this_day_avg: { value: 0.05000 },
    meter_price_this_day_lowest: { value: -0.04000 },
    hour_this_day_lowest: { value: 14 } // This corresponds to h2 in our window
  }
};
// Expected: Should choose h4-h6 (3 consecutive hours with much lower positive values)
// h2-h4: When treating negatives as 0: (0 + 0.12 + 0) ÷ 3 = 0.04
// h4-h6: When treating negatives as 0: (0 + 0.015 + 0.008) ÷ 3 = 0.00767

// Test Dataset 9: Lower positive values vs higher positive values
// Tests if algorithm properly selects range with lower overall average
const testData9 = {
  capabilitiesObj: {
    meter_price_h0: { value: 0.10000 },
    meter_price_h1: { value: -0.04000 }, // Negative
    meter_price_h2: { value: 0.12000 },  // Very high positive
    meter_price_h3: { value: -0.03000 }, // Negative
    meter_price_h4: { value: 0.08000 },  // High positive
    meter_price_h5: { value: 0.01200 },  // Low positive (lower than h3-h5)
    meter_price_h6: { value: 0.00900 },  // Low positive (lower than h3-h5)
    meter_price_h7: { value: 0.01000 },  // Low positive (lower than h3-h5)
    meter_price_this_day_avg: { value: 0.05000 },
    meter_price_this_day_lowest: { value: -0.04000 },
    hour_this_day_lowest: { value: 13 } // This corresponds to h1 in our window
  }
};
// Expected: Should choose h5-h7 (3 consecutive low positive values)
// h3-h5: (-0.03 + 0.08 + 0.012) ÷ 3 = 0.02067
// h5-h7: (0.012 + 0.009 + 0.01) ÷ 3 = 0.01033
// When treating negatives as 0:
// h3-h5: (0 + 0.08 + 0.012) ÷ 3 = 0.03067
// h5-h7: (0.012 + 0.009 + 0.01) ÷ 3 = 0.01033

/**
 * Enhanced testing function with assertions to verify algorithm correctness
 *
 * @param {Function} findOptimalRunTimeForHomey - The function to test
 */
function testAllDatasets(findOptimalRunTimeForHomey) {
  // Run tests with rangeSize = 3
  const rangeSize = 3;

  // Get current time in Vilnius timezone
  const now = new Date();
  const vilniusTime = new Date(now.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
  const currentHour = vilniusTime.getHours();

  // Set up the expected start hours for each test (relative to current hour)
  const expectedRanges = [
    {
      name: "Test 0: Multiple negative prices spread throughout",
      dataset: testData0,
      expectedStartOffset: 1,  // h1-h3 expected
      message: "Should choose h1-h3 with 2 negative prices"
    },
    {
      name: "Test 1: Multiple negative prices different pattern",
      dataset: testData1,
      expectedStartOffset: 1,  // h1-h3 expected
      message: "Should choose h1-h3 with 1 negative price, despite h3-h5 having 2 negative prices"
    },
    {
      name: "Test 2: Negative prices at beginning",
      dataset: testData2,
      expectedStartOffset: 0,  // h0-h2 expected
      message: "Should choose h0-h2 with 3 negative prices"
    },
    {
      name: "Test 3: Negative prices at end",
      dataset: testData3,
      expectedStartOffset: 5,  // h5-h7 expected
      message: "Should choose h5-h7 with 3 negative prices"
    },
    {
      name: "Test 4: No negative prices",
      dataset: testData4,
      expectedStartOffset: 3,  // h3-h5 expected
      message: "Should choose h3-h5 with lowest average"
    },
    {
      name: "Test 5: Negative prices vs single better negative",
      dataset: testData5,
      expectedStartOffset: 1,  // h1-h3 expected
      message: "Should choose h1-h3 with 2 negative prices over h4 with 1 better negative"
    },
    {
      name: "Test 6: All above day average",
      dataset: testData6,
      expectedStartOffset: -1,  // Should not be optimal
      message: "Should mark as not optimal since all prices are above day average by >20%"
    },
    {
      name: "Test 7: Mixed case with extreme values",
      dataset: testData7,
      expectedStartOffset: 2,  // h2-h4 expected
      message: "Should choose h2-h4 with 2 negative prices despite high positive in between"
    },
    {
      name: "Test 8: 2 Negative Prices vs 3 Lower Hours",
      dataset: testData8,
      expectedStartOffset: 4,  // h4-h6 expected
      message: "Should choose h4-h6 with lower modified average (0.00767) vs h2-h4 (0.04000)"
    },
    {
      name: "Test 9: Lower Positive Values vs Higher Avg",
      dataset: testData9,
      expectedStartOffset: 5,  // h5-h7 expected
      message: "Should choose h5-h7 with lower modified average (0.01033) vs h3-h5 (0.03067)"
    }
  ];

  // Run all tests
  let passedTests = 0;
  let totalTests = expectedRanges.length;

  for (let i = 0; i < expectedRanges.length; i++) {
    const test = expectedRanges[i];
    console.log("\n---------------------------------------");
    console.log(test.name);
    console.log("---------------------------------------");

    try {
      // Run the function with test data
      const result = findOptimalRunTimeForHomey(test.dataset, rangeSize);

      // Log the result for debugging
      console.log("Result:", result.isOptimal ? "OPTIMAL" : "NOT OPTIMAL");
      if (result.isOptimal) {
        console.log("Selected range:", `${result.startHour}:00-${result.endHour}:00`);
        console.log("Prices in range:", JSON.stringify(result.prices));
        if (result.negativeHours) {
          console.log("Negative hours in range:", result.negativeHours);
        }
        console.log("Raw average:", result.averagePrice.toFixed(5));
        if (result.modifiedAveragePrice) {
          console.log("Modified average:", result.modifiedAveragePrice.toFixed(5));
        }
        console.log("Start offset:", result.startOffset);
      } else {
        console.log("Reason:", result.optimalityReason);
      }

      // Verify the result
      const expectedOptimal = test.expectedStartOffset !== -1;

      // Check if optimality matches expectation
      if (result.isOptimal !== expectedOptimal) {
        throw new Error(`Optimality check failed: Expected ${expectedOptimal ? "OPTIMAL" : "NOT OPTIMAL"} but got ${result.isOptimal ? "OPTIMAL" : "NOT OPTIMAL"}`);
      }

      // If expected to be optimal, check if the range matches
      if (expectedOptimal) {
        if (result.startOffset !== test.expectedStartOffset) {
          throw new Error(`Range check failed: Expected start offset ${test.expectedStartOffset} (h${(currentHour + test.expectedStartOffset) % 24}) but got ${result.startOffset} (h${(currentHour + result.startOffset) % 24})`);
        }
      }

      // If passed all checks, increment the counter
      passedTests++;
      console.log(`✅ Test ${i} PASSED`);
    } catch (error) {
      console.log(`❌ Test ${i} FAILED: ${error.message}`);
      console.log(`Expected: ${test.message}`);
    }
  }

  // Summary
  console.log("\n---------------------------------------");
  console.log(`Test Summary: ${passedTests}/${totalTests} tests passed`);
  console.log("---------------------------------------");

  if (passedTests === totalTests) {
    console.log("🎉 All tests passed! The algorithm is working correctly.");
  } else {
    console.log(`⚠️ ${totalTests - passedTests} tests failed. Please review the algorithm.`);
  }
}

// To run all tests
testAllDatasets(findOptimalRunTimeForHomey);

// =====================================================================================================================
// =====================================================================================================================
// =====================================================================================================================
