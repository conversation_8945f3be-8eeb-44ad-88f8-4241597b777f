const actualVersionsFieldName = 'Actual versions';

/**
 * Gets the version ID for a specific version name in a project
 * @param {Object} jiraClient - Pre-configured Jira client
 * @param {string} projectKey - Project key (e.g., 'PLAT')
 * @param {string} versionName - Version name to find (e.g., 'Release 2.44.2')
 * @returns {Promise<string>} Version ID
 * @throws {Error} If a version not found or API error
 */
async function getVersionId(jiraClient, projectKey, versionName) {
  console.log(`Getting version ID for "${versionName}" in ${projectKey} project`);

  const versionsData = await jiraClient.get(`/rest/api/3/project/${projectKey}/version`, {
    query: versionName,
    status: 'released,unreleased',
  });

  console.log(`Found ${versionsData.values.length} matching version${versionsData.values.length === 1 ? '' : 's'} for project ${projectKey}`);

  if (versionsData.values.length === 0) {
    throw new Error(`Version "${versionName}" not found in ${projectKey} project. Please create this version in Jira first or use an existing version.`);
  }

  // Find the exact matching version by name
  const exactMatch = versionsData.values.find(v => v.name === versionName);

  if (exactMatch) {
    return exactMatch.id;
  } else {
    // If no exact match is found, show available versions and fail
    console.warn(`No exact match found for version "${versionName}" in project ${projectKey}. Available matching versions:`);
    versionsData.values.forEach(v => console.log(`- ${v.name} (ID: ${v.id})`));
    throw new Error(`Version "${versionName}" not found exactly as specified. Please use the exact version name from the list above.`);
  }
}

/**
 * Gets available bulk edit fields for the specified issues
 * @param {Object} jiraClient - Pre-configured Jira client
 * @param {string[]} issueKeys - Array of issue keys
 * @returns {Promise<Object>} Bulk edit fields response
 */
async function getAvailableBulkFields(jiraClient, issueKeys) {
  console.log('Getting available bulk edit fields...');

  return jiraClient.get('/rest/api/3/bulk/issues/fields', {
    issueIdsOrKeys: issueKeys.join(','),
    maxResults: 50,
  });
}

/**
 * Builds payload for fix version bulk update
 * @param {string[]} tickets - Array of ticket IDs
 * @param {string} versionId - Version ID to add
 * @returns {Object} Bulk update payload
 */
function buildFixVersionPayload(tickets, versionId) {
  return {
    sendBulkNotification: false,
    selectedIssueIdsOrKeys: tickets,
    selectedActions: ['fixVersions'],
    editedFieldsInput: {
      multipleVersionPickerFields: [{
        fieldId: 'fixVersions',
        bulkEditMultiSelectFieldOption: 'ADD',
        versions: [{
          versionId: versionId,
        }],
      }],
    },
  };
}

/**
 * Updates fix versions for multiple tickets using bulk API
 * @param {Object} jiraClient - Pre-configured Jira client
 * @param {string[]} tickets - Array of ticket IDs
 * @param {string} versionId - Version ID to add
 * @returns {Promise<Object>} API response
 */
async function bulkUpdateFixVersions(jiraClient, tickets, versionId) {
  const payload = buildFixVersionPayload(tickets, versionId);
  // Left intentionally for the debugging purposes
  // console.debug('Sending fix version payload:', JSON.stringify(payload, null, 2));

  await jiraClient.post('/rest/api/3/bulk/issues/fields', payload);
}

/**
 * Gets the field ID for the "Actual versions" custom field from bulk fields
 * @param {Object} jiraClient - Pre-configured Jira client
 * @param {string[]} tickets - Array of ticket IDs to check bulk fields for
 * @returns {Promise<string>} Field ID
 */
async function getActualVersionFieldId(jiraClient, tickets) {
  try {
    const { fields } = await getAvailableBulkFields(jiraClient, tickets);

    const actualVersionField = fields.find(field =>
      field.name === actualVersionsFieldName ||
      field.name.toLowerCase() === actualVersionsFieldName.toLowerCase()
    );

    if (!actualVersionField) {
      throw new Error(`${actualVersionsFieldName} field not found in available bulk fields. Please ensure the field exists and is available for bulk operations.`);
    }

    return actualVersionField.id;
  } catch (error) {
    throw new Error(`Failed to get ${actualVersionsFieldName} field ID: ${error.message}`);
  }
}

/**
 * Builds payload for actual versions bulk update (labels field)
 * @param {string[]} tickets - Array of ticket IDs
 * @param {string} version - Version string to add
 * @param {string} fieldId - The actual field ID for the Actual versions field
 * @returns {Object} Bulk update payload
 */
function buildActualVersionPayload(tickets, version, fieldId) {
  return {
    sendBulkNotification: false,
    selectedIssueIdsOrKeys: tickets,
    selectedActions: [fieldId],
    editedFieldsInput: {
      labelsFields: [{
        bulkEditMultiSelectFieldOption: 'ADD',
        fieldId: fieldId,
        labels: [{
          name: version
        }]
      }]
    }
  };
}

/**
 * Updates actual versions custom field for multiple tickets using bulk API
 * @param {Object} jiraClient - Pre-configured Jira client
 * @param {string[]} tickets - Array of ticket IDs
 * @param {string} version - Version string to add
 * @returns {Promise<Object>} API response
 */
async function bulkUpdateActualVersions(jiraClient, tickets, version) {
  const fieldId = await getActualVersionFieldId(jiraClient, tickets);
  const payload = buildActualVersionPayload(tickets, version, fieldId);
  // Left intentionally for the debugging purposes
  // console.debug('Sending actual version payload:', JSON.stringify(payload, null, 2));

  await jiraClient.post('/rest/api/3/bulk/issues/fields', payload);
}

module.exports = {
  actualVersionsFieldName,
  getVersionId,
  getAvailableBulkFields,
  buildFixVersionPayload,
  bulkUpdateFixVersions,
  getActualVersionFieldId,
  buildActualVersionPayload,
  bulkUpdateActualVersions,
};
