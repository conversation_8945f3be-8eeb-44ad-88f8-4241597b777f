/**
 * Main entry point for Jira ticket bulk updater
 *
 * This module orchestrates the process of updating multiple Jira tickets with:
 * - Fix versions (from version.json or custom value)
 * - Service labels (detected from git commit history)
 *
 * Supports dry-run mode, parallel processing, and bulk API operations
 * with automatic fallback to individual updates if bulk operations fail.
 */

const { createJiraClient } = require('./jira-api');
const { buildUpdatePlan, displayUpdatePlan, displayLabelDryRunAnalysis, displayVersionDryRunAnalysis } = require('./update-planner');
const { validateAllOptions } = require('./validators');
const { resolveFixVersion, resolveActualVersion, resolveVersionId } = require('./version-resolver');
const { analyzeServiceLabels, createLabelGroups, filterTicketsWithLabels } = require('./label-analyzer');
const { executeUpdates } = require('./update-executor');
const {
  createNoUpdatesResult,
  createErrorResult,
  createDryRunResult,
  createSuccessR<PERSON>ult,
  formatExecutionTime
} = require('./result-builder');

/**
 * @typedef {Object} UpdateOptions
 * @property {string} jiraUrl - Jira API URL
 * @property {string} jiraToken - Jira API token
 * @property {string} jiraEmail - Jira account email
 * @property {string[]} tickets - Array of Jira ticket IDs
 * @property {string} [fixVersion] - Fix version to set
 * @property {string} [actualVersion] - Actual version to set
 * @property {string} [ticketRegex] - Regex pattern for ticket validation
 * @property {boolean} [dryRun=false] - Whether to perform a dry run
 * @property {string} appsDir - Path to apps directory
 * @property {boolean} [updateFixVersion=false] - Whether to update fix version
 * @property {boolean} [updateLabels=false] - Whether to update labels
 * @property {boolean} [updateActualVersion=false] - Whether to update actual version
 * @property {number} [maxParallel=10] - Maximum number of tickets to process in parallel
 */

/**
 * @typedef {Object} UpdateContext
 * @property {UpdateOptions} options - Original options
 * @property {JiraClient} jiraClient - Jira API client
 * @property {Map<string, string[]>} ticketToLabels - Map of tickets to labels
 * @property {string} [versionId] - Jira version ID
 * @property {Object} [updatePlan] - The update plan
 * @property {boolean} hasUpdates - Whether there are updates to perform
 */

/**
 * Prepares the update context with all necessary data
 * @param {UpdateOptions} options - Update options
 * @returns {Promise<UpdateContext>} Update context
 */
async function prepareUpdateContext(options) {
  const context = {
    options,
    jiraClient: createJiraClient(options.jiraUrl, options.jiraEmail, options.jiraToken),
    ticketToLabels: new Map(),
    hasUpdates: false
  };

  console.log(`Parallel processing limit: ${options.maxParallel}`);

  // Resolve fix version if needed
  if (options.updateFixVersion) {
    const versionInfo = resolveFixVersion(options);
    if (versionInfo) {
      context.fixVersion = versionInfo.version;
      console.log(`Using fix version ${versionInfo.source}: ${versionInfo.version}`);
    }
  }

  // Resolve actual version if needed
  if (options.updateActualVersion) {
    const actualVersionInfo = resolveActualVersion(options);
    if (actualVersionInfo) {
      context.actualVersion = actualVersionInfo.version;
      console.log(`Using actual version ${actualVersionInfo.source}: ${actualVersionInfo.version}`);
    }
  }

  // Analyze labels if needed
  if (options.updateLabels) {
    const analysisResult = await analyzeServiceLabels(
      options.tickets,
      options.appsDir,
      options.maxParallel
    );
    context.ticketToLabels = analysisResult.ticketToLabels;
  }

  // Resolve version ID if we have a fix version
  if (context.fixVersion) {
    const versionIdResult = await resolveVersionId(
      context.jiraClient,
      options.tickets,
      context.fixVersion
    );
    context.versionId = versionIdResult.versionId;
    console.log(`Resolved version ID: ${versionIdResult.versionId}`);
  }

  return context;
}

/**
 * Checks if there are any updates to perform
 * @param {UpdateContext} context - Update context
 * @returns {boolean} True if there are updates to perform
 */
function checkForUpdates(context) {
  const { options, fixVersion, versionId, actualVersion, ticketToLabels } = context;

  // Check fix version updates
  const hasFixVersionUpdate = options.updateFixVersion && fixVersion && versionId;

  // Check actual version updates
  const hasActualVersionUpdate = options.updateActualVersion && actualVersion;

  // Check label updates
  let hasLabelUpdates = false;
  if (options.updateLabels) {
    for (const labels of ticketToLabels.values()) {
      if (labels.length > 0) {
        hasLabelUpdates = true;
        break;
      }
    }
  }

  return hasFixVersionUpdate || hasActualVersionUpdate || hasLabelUpdates;
}

/**
 * Handles dry run execution
 * @param {UpdateContext} context - Update context
 * @returns {Object|null} Result if dry run, null otherwise
 */
function handleDryRun(context) {
  if (!context.options.dryRun) {
    return null;
  }

  // Display version update analysis
  displayVersionDryRunAnalysis({
    updateFixVersion: context.options.updateFixVersion,
    fixVersion: context.fixVersion,
    updateActualVersion: context.options.updateActualVersion,
    actualVersion: context.actualVersion
  }, context.options.tickets);

  // Display label update analysis
  if (context.options.updateLabels) {
    const labelGroups = createLabelGroups(context.options.tickets, context.ticketToLabels);
    displayLabelDryRunAnalysis(labelGroups, context.options.tickets);
  }

  console.log('\nDRY RUN - No changes were made to Jira.');
  return createDryRunResult(context.updatePlan);
}

/**
 * Performs a bulk update of Jira tickets
 * @param {UpdateOptions} options - Options for the update
 * @returns {Promise<Object>} Result of the update operation
 */
async function updateJiraTickets(options) {
  const startTime = process.hrtime();

  try {
    // Validate options
    const validation = validateAllOptions(options);
    if (!validation.valid) {
      throw new Error(validation.error);
    }

    // Prepare update context
    const context = await prepareUpdateContext(options);

    // Check if we have any updates
    if (!checkForUpdates(context)) {
      return createNoUpdatesResult();
    }

    // Build update plan
    const updatePlan = buildUpdatePlan(
      options.tickets,
      context.ticketToLabels,
      {
        updateFixVersion: options.updateFixVersion,
        updateActualVersion: options.updateActualVersion,
        updateLabels: options.updateLabels,
        fixVersion: context.fixVersion,
        actualVersion: context.actualVersion
      }
    );
    context.updatePlan = updatePlan;
    context.hasUpdates = true;

    // Display update plan
    displayUpdatePlan(updatePlan);

    // Handle dry run
    const dryRunResult = handleDryRun(context);
    if (dryRunResult) {
      const time = formatExecutionTime(process.hrtime(startTime));
      console.log(`Execution time: ${time.formatted}`);
      return { ...dryRunResult, executionTimeMs: time.totalMs };
    }

    // Prepare label groups
    const labelGroups = options.updateLabels
      ? createLabelGroups(options.tickets, context.ticketToLabels)
      : [];

    // Execute updates
    const executionResults = await executeUpdates({
      jiraClient: context.jiraClient,
      tickets: options.tickets,
      updateFixVersion: options.updateFixVersion,
      versionId: context.versionId,
      updateActualVersion: options.updateActualVersion,
      actualVersion: context.actualVersion,
      updateLabels: options.updateLabels,
      labelGroups,
      maxParallel: options.maxParallel
    });

    // Build result
    const time = formatExecutionTime(process.hrtime(startTime));
    console.log(`Execution time: ${time.formatted}`);

    return createSuccessResult({
      ticketsUpdated: options.tickets.length,
      updatePlan,
      executionResults,
      updateFixVersion: options.updateFixVersion,
      updateActualVersion: options.updateActualVersion,
      updateLabels: options.updateLabels,
      executionTimeMs: time.totalMs
    });

  } catch (error) {
    console.error('Error updating Jira tickets:', error.message);
    if (error.details) {
      console.error('API error details:', error.details);
    }

    const time = formatExecutionTime(process.hrtime(startTime));
    const result = createErrorResult(error);
    result.executionTimeMs = time.totalMs;

    return result;
  }
}

module.exports = {
  updateJiraTickets
};
