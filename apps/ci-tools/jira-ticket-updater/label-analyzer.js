const { findServiceLabelsForTickets, groupTicketsByLabels } = require('./ticket-analyzer');

/**
 * @typedef {Object} LabelAnalysisResult
 * @property {Map<string, string[]>} ticketToLabels - Map of ticket IDs to their service labels
 * @property {number} ticketsWithLabels - Number of tickets that have labels
 * @property {number} totalLabels - Total number of unique labels found
 */

/**
 * @typedef {Object} LabelGroup
 * @property {string[]} labels - Array of labels for this group
 * @property {string[]} tickets - Array of ticket IDs in this group
 */

/**
 * Analyzes tickets to find service labels based on commit history
 * @param {string[]} tickets - Array of Jira ticket IDs
 * @param {string} appsDir - Path to apps directory
 * @param {number} [maxParallel=10] - Maximum parallel operations
 * @returns {Promise<LabelAnalysisResult>}
 */
async function analyzeServiceLabels(tickets, appsDir, maxParallel = 10) {
  console.log('\nAnalyzing commits to determine service labels...');

  const ticketToLabels = await findServiceLabelsForTickets(tickets, appsDir, maxParallel);

  // Calculate statistics
  let ticketsWithLabels = 0;
  const allLabels = new Set();

  for (const labels of ticketToLabels.values()) {
    if (labels.length > 0) {
      ticketsWithLabels++;
      labels.forEach(label => allLabels.add(label));
    }
  }

  console.log(`Found ${ticketsWithLabels} ticket${ticketsWithLabels !== 1 ? 's' : ''} with service labels (${allLabels.size} unique label${allLabels.size !== 1 ? 's' : ''})`);

  return {
    ticketToLabels,
    ticketsWithLabels,
    totalLabels: allLabels.size
  };
}

/**
 * Groups tickets by their labels for batch processing
 * @param {string[]} tickets - Array of all ticket IDs
 * @param {Map<string, string[]>} ticketToLabels - Map of ticket IDs to labels
 * @returns {Map<string, LabelGroup>} Array of label groups
 */
function createLabelGroups(tickets, ticketToLabels) {
  return groupTicketsByLabels(tickets, ticketToLabels);
}

/**
 * Filters tickets that have labels
 * @param {string[]} tickets - Array of ticket IDs
 * @param {Map<string, string[]>} ticketToLabels - Map of ticket IDs to labels
 * @returns {Object<string, string[]>} Object mapping ticket IDs to their labels
 */
function filterTicketsWithLabels(tickets, ticketToLabels) {
  const ticketsWithLabels = {};

  tickets.forEach(ticket => {
    const labels = ticketToLabels.get(ticket) || [];
    if (labels.length > 0) {
      ticketsWithLabels[ticket] = labels;
    }
  });

  return ticketsWithLabels;
}

/**
 * Checks if any tickets have labels that need updating
 * @param {Map<string, string[]>} ticketToLabels - Map of ticket IDs to labels
 * @returns {boolean} True if at least one ticket has labels
 */
function hasLabelsToUpdate(ticketToLabels) {
  for (const labels of ticketToLabels.values()) {
    if (labels.length > 0) {
      return true;
    }
  }
  return false;
}

/**
 * Creates a summary of label analysis for logging
 * @param {LabelAnalysisResult} analysisResult - Result from analyzeServiceLabels
 * @returns {string} Human-readable summary
 */
function createLabelAnalysisSummary(analysisResult) {
  const { ticketsWithLabels, totalLabels, ticketToLabels } = analysisResult;

  if (ticketsWithLabels === 0) {
    return 'No service labels found for any tickets';
  }

  const labelCounts = {};
  for (const labels of ticketToLabels.values()) {
    labels.forEach(label => {
      labelCounts[label] = (labelCounts[label] || 0) + 1;
    });
  }

  const topLabels = Object.entries(labelCounts)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5)
    .map(([label, count]) => `${label} (${count})`)
    .join(', ');

  return `Found ${ticketsWithLabels} tickets with ${totalLabels} unique labels. Top labels: ${topLabels}`;
}

module.exports = {
  analyzeServiceLabels,
  createLabelGroups,
  filterTicketsWithLabels,
  hasLabelsToUpdate,
  createLabelAnalysisSummary
};
