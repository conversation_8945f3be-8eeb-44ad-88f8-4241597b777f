const { bulkUpdateFixVersions, bulkUpdateActualVersions } = require('./jira-versions');
const { updateLabelsForGroups } = require('./jira-labels');

/**
 * @typedef {Object} VersionUpdateResult
 * @property {boolean} success - Whether the update succeeded
 * @property {Error} [error] - Error if update failed
 * @property {number} [ticketsUpdated] - Number of tickets updated
 */

/**
 * @typedef {Object} LabelUpdateResult
 * @property {number} successCount - Number of successful label updates
 * @property {number} failureCount - Number of failed label updates
 * @property {Object[]} [errors] - Array of errors if any
 */

/**
 * @typedef {Object} UpdateExecutionResult
 * @property {boolean} fixVersionUpdated - Whether fix version was updated
 * @property {VersionUpdateResult} [fixVersionResult] - Fix version update details
 * @property {boolean} actualVersionUpdated - Whether actual version was updated
 * @property {VersionUpdateResult} [actualVersionResult] - Actual version update details
 * @property {LabelUpdateResult} labelResults - Label update results
 */

/**
 * Executes fix version update for tickets
 * @param {Object} jiraClient - Jira API client instance
 * @param {string[]} tickets - Array of ticket IDs to update
 * @param {string} versionId - Jira version ID to set
 * @returns {Promise<VersionUpdateResult>}
 */
async function executeFixVersionUpdate(jiraClient, tickets, versionId) {
  console.log('Updating fix versions...');

  try {
    await bulkUpdateFixVersions(jiraClient, tickets, versionId);
    console.log(`Successfully updated fix version for ${tickets.length} tickets`);

    return {
      success: true,
      ticketsUpdated: tickets.length
    };
  } catch (error) {
    console.error('Error updating fix versions:', error.message);
    if (error.details) {
      console.error('API error details:', JSON.stringify(error.details, null, 2));
    }

    return {
      success: false,
      error,
      ticketsUpdated: 0
    };
  }
}

/**
 * Executes actual version update for tickets
 * @param {Object} jiraClient - Jira API client instance
 * @param {string[]} tickets - Array of ticket IDs to update
 * @param {string} version - Version string to set
 * @returns {Promise<VersionUpdateResult>}
 */
async function executeActualVersionUpdate(jiraClient, tickets, version) {
  console.log('Updating actual versions...');

  try {
    await bulkUpdateActualVersions(jiraClient, tickets, version);
    console.log(`Successfully updated actual version for ${tickets.length} tickets`);

    return {
      success: true,
      ticketsUpdated: tickets.length
    };
  } catch (error) {
    console.error('Error updating actual versions:', error.message);
    if (error.details) {
      console.error('API error details:', JSON.stringify(error.details, null, 2));
    }

    return {
      success: false,
      error,
      ticketsUpdated: 0
    };
  }
}

/**
 * Executes label updates for tickets
 * @param {Object} jiraClient - Jira API client instance
 * @param {Map<string, LabelGroup>} labelGroups - Groups of tickets with same labels
 * @param {number} [maxParallel=10] - Maximum parallel operations
 * @returns {Promise<LabelUpdateResult>}
 */
async function executeLabelUpdate(jiraClient, labelGroups, maxParallel = 10) {
  console.log('\nUpdating labels...');
  console.log(`Found ${labelGroups.size} unique label combinations`);
  console.log(`Processing up to ${maxParallel} label groups in parallel...`);

  try {
    const result = await updateLabelsForGroups(jiraClient, labelGroups, maxParallel);

    if (result.successCount > 0) {
      console.log(`Successfully updated labels for ${result.successCount} ticket group${result.successCount === 1 ? '' : 's'}`);
    }
    if (result.failureCount > 0) {
      console.error(`Failed to update labels for ${result.failureCount} ticket group${result.failureCount === 1 ? '' : 's'}`);
    }

    return result;
  } catch (error) {
    console.error('Error updating labels:', error.message);

    return {
      successCount: 0,
      failureCount: labelGroups.length,
      errors: [error]
    };
  }
}

/**
 * Executes all required updates based on configuration
 * @param {Object} options - Update configuration
 * @param {Object} options.jiraClient - Jira API client
 * @param {string[]} options.tickets - Tickets to update
 * @param {boolean} options.updateFixVersion - Whether to update fix version
 * @param {string} [options.versionId] - Version ID if updating fix version
 * @param {boolean} options.updateActualVersion - Whether to update actual version
 * @param {string} [options.actualVersion] - Version string if updating actual version
 * @param {boolean} options.updateLabels - Whether to update labels
 * @param {Map<string, LabelGroup>} [options.labelGroups] - Label groups if updating labels
 * @param {number} [options.maxParallel] - Maximum parallel operations
 * @returns {Promise<UpdateExecutionResult>}
 */
async function executeUpdates(options) {
  const {
    jiraClient,
    tickets,
    updateFixVersion,
    versionId,
    updateActualVersion,
    actualVersion,
    updateLabels,
    labelGroups,
    maxParallel
  } = options;

  console.log('\nUpdating Jira tickets...');

  const result = {
    fixVersionUpdated: false,
    actualVersionUpdated: false,
    labelResults: { successCount: 0, failureCount: 0 }
  };

  if (updateFixVersion && versionId) {
    const fixVersionResult = await executeFixVersionUpdate(jiraClient, tickets, versionId);
    result.fixVersionUpdated = fixVersionResult.success;
    result.fixVersionResult = fixVersionResult;
  }

  if (updateActualVersion && actualVersion) {
    const actualVersionResult = await executeActualVersionUpdate(jiraClient, tickets, actualVersion);
    result.actualVersionUpdated = actualVersionResult.success;
    result.actualVersionResult = actualVersionResult;
  }

  if (updateLabels && labelGroups && labelGroups.size > 0) {
    result.labelResults = await executeLabelUpdate(jiraClient, labelGroups, maxParallel);
  }

  return result;
}

/**
 * Validates if updates were successful
 * @param {UpdateExecutionResult} result - Execution result
 * @param {Object} expectations - What was expected to be updated
 * @param {boolean} expectations.expectedFixVersionUpdate - Whether fix version was expected to update
 * @param {boolean} expectations.expectedActualVersionUpdate - Whether actual version was expected to update
 * @param {boolean} expectations.expectedLabelUpdate - Whether labels were expected to update
 * @returns {boolean} True if all expected updates succeeded
 */
function validateUpdateSuccess(result, expectations) {
  const { expectedFixVersionUpdate, expectedActualVersionUpdate, expectedLabelUpdate } = expectations;

  // Check fix version update
  if (expectedFixVersionUpdate && !result.fixVersionUpdated) {
    return false;
  }

  // Check actual version update
  if (expectedActualVersionUpdate && !result.actualVersionUpdated) {
    return false;
  }

  // Check label updates
  if (expectedLabelUpdate && result.labelResults.failureCount > 0) {
    return false;
  }

  return true;
}

module.exports = {
  executeFixVersionUpdate,
  executeActualVersionUpdate,
  executeLabelUpdate,
  executeUpdates,
  validateUpdateSuccess
};
