/**
 * Builds payload for bulk labels update
 * @param {string[]} tickets - Array of ticket IDs
 * @param {string[]} labels - Array of label names
 * @returns {Object} Bulk update payload
 */
function buildBulkLabelsPayload(tickets, labels) {
  return {
    sendBulkNotification: false,
    selectedIssueIdsOrKeys: tickets,
    selectedActions: ['labels'],
    editedFieldsInput: {
      labelsFields: [{
        fieldId: 'labels',
        labels: labels.map(label => ({ name: label })),
        bulkEditMultiSelectFieldOption: 'ADD'
      }]
    }
  };
}

/**
 * Builds payload for individual issue label update
 * @param {string[]} labels - Array of label names
 * @returns {Object} Individual update payload
 */
function buildIndividualLabelsPayload(labels) {
  return {
    update: {
      labels: labels.map(label => ({ add: label }))
    }
  };
}

/**
 * Updates a single ticket with labels and handles errors
 * @param {JiraClient} jiraClient - Pre-configured Jira client
 * @param {string} ticket - Single ticket ID
 * @param {string[]} labels - Array of label names
 * @returns {Promise<Object>} Result with success/failure counts
 */
async function updateSingleTicketLabels(jiraClient, ticket, labels) {
  try {
    await updateIssueLabels(jiraClient, ticket, labels);
    return { success: 1, failure: 0 };
  } catch (individualError) {
    console.error(`Failed to update labels for ${ticket}:`, individualError.message);
    if (individualError.details) {
      console.error(`  Error details:`, individualError.details);
    }
    return { success: 0, failure: 1 };
  }
}



/**
 * Updates labels for multiple tickets using bulk API
 * @param {JiraClient} jiraClient - Pre-configured Jira client
 * @param {LabelGroup} labelsGroup
 * @returns {Promise<Object>} API response
 */
async function bulkUpdateLabels(jiraClient, labelsGroup) {
  const payload = buildBulkLabelsPayload(labelsGroup.tickets, labelsGroup.labels);
  return jiraClient.post('/rest/api/3/bulk/issues/fields', payload);
}

/**
 * Updates labels for a single issue
 * @param {JiraClient} jiraClient - Pre-configured Jira client
 * @param {string} ticketId - Single ticket ID
 * @param {string[]} labels - Array of label names
 * @returns {Promise<Object>} API response
 */
async function updateIssueLabels(jiraClient, ticketId, labels) {
  const payload = buildIndividualLabelsPayload(labels);
  return jiraClient.put(`/rest/api/3/issue/${ticketId}`, payload);
}

/**
 * @typedef {Object} LabelGroup
 * @property {string[]} labels - Array of Jira ticket IDs
 * @property {string[]} tickets - Array of Jira ticket IDs
 */

/**
 * High-level function to update labels for grouped tickets
 * @param {JiraClient} jiraClient - Pre-configured Jira client
 * @param {Map<string, LabelGroup>} labelGroups - Map of label groups from groupTicketsByLabels
 * @param {number} [maxParallel=10] - Maximum number of groups to process in parallel
 * @returns {Promise<Object>} Results with success/failure counts
 */
async function updateLabelsForGroups(jiraClient, labelGroups, maxParallel = 10) {
  let labelsSuccessCount = 0;
  let labelsFailureCount = 0;

  const labelGroupEntries = Array.from(labelGroups.entries()).sort((a, b) => a.length - b.length || a[0].localeCompare(b[0]));

  for (let i = 0; i < labelGroupEntries.length; i += maxParallel) {
    const currentBatch = labelGroupEntries.slice(i, i + maxParallel);

    console.log(`Processing label batch ${Math.floor(i/maxParallel) + 1}/${Math.ceil(labelGroupEntries.length/maxParallel)}: ${currentBatch.map(([groupLabel]) => groupLabel ? `[${groupLabel}]` : "<no labels>").join(', ')}`);

    const batchResults = await Promise.allSettled(currentBatch.map(async ([groupLabel, group]) => {
      if (group.labels.length === 0) {
        const ticketInfoMessagePart = group.tickets.length === 1 ? group.tickets[0] : `${group.tickets.length} ticket${group.tickets.length === 1 ? '' : 's'}`;
        console.log(`Skipping ${ticketInfoMessagePart} with no labels`);
        return { success: 0, failure: 0 };
      }


      if (group.tickets.length > 1) {
        try {
          console.log(`Updating ${group.tickets.length} ticket${group.tickets.length === 1 ? '' : 's'} with labels: ${groupLabel}`);
          await bulkUpdateLabels(jiraClient, group);
          return { success: group.tickets.length, failure: 0 };
        } catch (bulkError) {
          console.warn(`Bulk labels update failed for tickets ${group.tickets.join(', ')}, falling back to individual updates...`);
          console.warn('Bulk labels error:', bulkError.message);

          const individualResults = await Promise.allSettled(group.tickets.map(ticket =>
            updateSingleTicketLabels(jiraClient, ticket, group.labels)
          ));

          return individualResults.reduce((acc, result) => {
            if (result.status === 'fulfilled') {
              return {
                success: acc.success + result.value.success,
                failure: acc.failure + result.value.failure
              };
            } else {
              return { success: acc.success, failure: acc.failure + 1 };
            }
          }, { success: 0, failure: 0 });
        }
      } else {
        const ticket = group.tickets[0];
        console.log(`Updating ${ticket} with labels: ${groupLabel}`);
        return updateSingleTicketLabels(jiraClient, ticket, group.labels);
      }
    }));

    batchResults.forEach((result) => {
      if (result.status === 'fulfilled') {
        labelsSuccessCount += result.value.success;
        labelsFailureCount += result.value.failure;
      } else {
        console.error('Unexpected error in batch processing:', result.reason);
        labelsFailureCount += 1;
      }
    });
  }

  return {
    successCount: labelsSuccessCount,
    failureCount: labelsFailureCount
  };
}

module.exports = {
  buildBulkLabelsPayload,
  buildIndividualLabelsPayload,
  bulkUpdateLabels,
  updateIssueLabels,
  updateSingleTicketLabels,
  updateLabelsForGroups,
};
