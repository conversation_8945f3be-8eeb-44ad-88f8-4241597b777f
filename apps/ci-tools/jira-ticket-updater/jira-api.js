const axios = require('axios');

/**
 * Internal HTTP functions with error handling
 */
async function jiraGet(config, endpoint, params = {}) {
  try {
    const response = await axios({
      method: 'get',
      url: `${config.baseURL}${endpoint}`,
      params,
      headers: config.headers,
    });
    return response.data;
  } catch (error) {
    handleJiraError(error);
  }
}

async function jiraPost(config, endpoint, data) {
  try {
    const response = await axios({
      method: 'post',
      url: `${config.baseURL}${endpoint}`,
      data,
      headers: config.headers,
    });
    return response.data;
  } catch (error) {
    handleJiraError(error);
  }
}

async function jiraPut(config, endpoint, data) {
  try {
    const response = await axios({
      method: 'put',
      url: `${config.baseURL}${endpoint}`,
      data,
      headers: config.headers,
    });
    return response.data;
  } catch (error) {
    handleJiraError(error);
  }
}

/**
 * Handles errors that occur during interactions with the Jira API.
 * This method processes API error responses, network issues, or other request-related errors
 * and throws detailed error messages to help with debugging.
 *
 * @param {Object} error - The error object from an Axios request, containing details about the failure.
 * @returns {void}
 * @throws {Error} Throws an error with descriptive details based on the type of issue encountered
 * (API error, network issue, or other request-related error).
 */
function handleJiraError(error) {
  if (error.response) {
    const { status, data } = error.response;
    const message = data?.errorMessages?.join('; ') || data?.message || `HTTP ${status}`;
    const apiError = new Error(`Jira API Error (${status}): ${message}`);
    apiError.status = status;
    apiError.details = data;
    throw apiError;
  } else if (error.request) {
    throw new Error(`Network Error: Unable to reach Jira API at ${error.config?.url}`);
  } else {
    throw new Error(`Request Error: ${error.message}`);
  }
}

/**
 * @typedef {Object} JiraClient
 * @property {function(string, Object=): Promise<any>} get - Performs GET request to Jira API endpoint
 * @property {function(string, Object=): Promise<any>} post - Performs POST request to Jira API endpoint
 * @property {function(string, Object=): Promise<any>} put - Performs PUT request to Jira API endpoint
 */

 /**
 * Factory function that creates pre-configured Jira client
 * @param {string} jiraUrl - Base Jira URL (e.g., 'https://company.atlassian.net')
 * @param {string} jiraEmail - User email for authentication
 * @param {string} jiraToken - API token for authentication
 * @returns {JiraClient} Pre-configured client with get, post, put methods
 */
function createJiraClient(jiraUrl, jiraEmail, jiraToken) {
  const config = {
    baseURL: jiraUrl,
    headers: {
      'Authorization': `Basic ${Buffer.from(`${jiraEmail}:${jiraToken}`).toString('base64')}`,
      'Accept': 'application/json',
      'Content-Type': 'application/json',
    },
  };

  return {
    get: (endpoint, params) => jiraGet(config, endpoint, params),
    post: (endpoint, data) => jiraPost(config, endpoint, data),
    put: (endpoint, data) => jiraPut(config, endpoint, data),
  };
}

module.exports = {
  createJiraClient,
};
