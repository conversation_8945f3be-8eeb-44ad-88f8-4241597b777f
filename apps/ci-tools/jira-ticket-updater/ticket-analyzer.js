const fs = require('fs');
const path = require('path');

const { getCommitsForTicket, getFilesInCommitAsync, getFilesFromPRMergeCommits } = require('../git-utils');

const defaultTicketRegex = '([A-Z][A-Z0-9_]{1,9}-[0-9]+)';

/**
 * Validates that the apps directory exists
 * @param {string} appsDir - Path to the apps directory
 * @returns {boolean} - Whether the apps directory exists
 */
function validateAppsDirectory(appsDir) {
  try {
    return fs.existsSync(appsDir) && fs.statSync(appsDir).isDirectory();
  } catch (error) {
    return false;
  }
}

/**
 * Extracts service types from file paths in commits
 * @param {string[]} filePaths - List of file paths
 * @param {string} appsDir - Path to the apps directory
 * @returns {Set<string>} Set of unique service types
 */
function extractServiceTypes(filePaths, appsDir) {
  if (!Array.isArray(filePaths) || filePaths.length === 0) {
    return new Set();
  }

  const serviceTypes = new Set();
  const appsPath = path.resolve(appsDir);
  const appsDirName = path.basename(appsPath);

  filePaths.forEach(filePath => {
    // Check if the file is in the apps directory
    const normalizedPath = path.normalize(filePath);
    const appsDirPattern = new RegExp(`${appsDirName}/([^/]+)`);
    const match = normalizedPath.match(appsDirPattern);

    if (match && match[1]) {
      serviceTypes.add(match[1]);
    }
  });

  return serviceTypes;
}

/**
 * Validates a ticket ID against a regex pattern
 * @param {string} ticket - Ticket ID to validate
 * @param {string} ticketRegex - Regex pattern for validation
 * @returns {boolean} - Whether the ticket matches the pattern
 */
function validateTicketFormat(ticket, ticketRegex) {
  const regex = new RegExp(`^${ticketRegex}$`);
  return regex.test(ticket);
}

/**
 * Finds service labels for a set of Jira tickets by analyzing commits
 * @param {string[]} jiraTickets - Array of Jira ticket keys
 * @param {string} appsDir - Path to the apps directory
 * @param {number} [parallelLimit=10] - Maximum number of tickets to be processed in parallel
 * @returns {Promise<Map<string, string[]>>} Map of ticket keys to arrays of service labels
 */
async function findServiceLabelsForTickets(jiraTickets, appsDir, parallelLimit = 10) {
  const ticketServiceLabelsMap = new Map();

  jiraTickets.forEach(ticket => {
    ticketServiceLabelsMap.set(ticket, new Set());
  });

  try {
    console.log(`Processing up to ${parallelLimit} tickets in parallel...`);

    // Process tickets in chunks with true parallel processing
    for (let i = 0; i < jiraTickets.length; i += parallelLimit) {
      const currentBatch = jiraTickets.slice(i, i + parallelLimit);
      console.log(`Processing batch ${Math.floor(i/parallelLimit) + 1}/${Math.ceil(jiraTickets.length/parallelLimit)}: ${currentBatch.join(', ')}`);

      // Process each ticket in the current batch in parallel
      await Promise.all(currentBatch.map(async (ticket) => {
        try {
          const ticketServiceLabels = ticketServiceLabelsMap.get(ticket);

          const prFiles = await getFilesFromPRMergeCommits(ticket);

          if (prFiles.length > 0) {
            // Primary strategy: Use files from PR merge commits
            const serviceTypes = extractServiceTypes(prFiles, appsDir);
            console.info(`Service types from PR merge commits for ticket ${ticket}: ${Array.from(serviceTypes).join(', ')}`);

            serviceTypes.forEach(service => {
              ticketServiceLabels.add(service);
            });
          } else {
            // Fallback strategy: Only fetch commits if no PR merge commits found
            const commitShas = await getCommitsForTicket(ticket);

            if (commitShas.length === 0) {
              console.warn(`No commits found for ticket ${ticket}`);
              return;
            }

            console.log(`No PR merge commits found for ticket ${ticket}, processing all ${commitShas.length} individual commit${commitShas.length === 1 ? '' : 's'}`);
            console.log(commitShas);

            await Promise.allSettled(commitShas.map(async (sha) => {
              const files = await getFilesInCommitAsync(sha);
              const serviceTypes = extractServiceTypes(files, appsDir);
              console.info(`Service types in commit ${sha}: ${Array.from(serviceTypes).join(', ')}`);

              serviceTypes.forEach(service => {
                ticketServiceLabels.add(service);
              });
            }));
          }
        } catch (error) {
          console.error(`Error processing ticket ${ticket}:`, error.message);
        }
      }));
    }

    const ticketServiceLabels = new Map();
    ticketServiceLabelsMap.forEach((value, key) => {
      ticketServiceLabels.set(key, Array.from(value).sort());
    });

    return ticketServiceLabels;
  } catch (error) {
    console.error('Error finding service labels:', error.message);
    return new Map();
  }
}


/**
 * Groups tickets by their labels for efficient bulk updates
 * @param {string[]} jiraTickets - Array of Jira ticket IDs
 * @param {Map<string, string[]>} ticketToLabels - Map of ticket IDs to their labels
 * @returns {Map<string, LabelGroup>} Map of label keys to groups of tickets and labels
 */
function groupTicketsByLabels(jiraTickets, ticketToLabels) {
  /** @type {Map<string, LabelGroup>} */
  const labelGroups = new Map();

  jiraTickets.forEach(ticket => {
    const labels = ticketToLabels.get(ticket) || [];
    const labelKey = labels.sort().join(', '); // Create a key from sorted labels

    if (!labelGroups.has(labelKey)) {
      labelGroups.set(labelKey, {
        labels: labels,
        tickets: []
      });
    }
    labelGroups.get(labelKey).tickets.push(ticket);
  });

  return labelGroups;
}

module.exports = {
  validateAppsDirectory,
  validateTicketFormat,
  findServiceLabelsForTickets,
  groupTicketsByLabels,
  defaultTicketRegex,
};
