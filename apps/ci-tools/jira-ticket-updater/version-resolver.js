const { getVersion } = require('../version-utils');
const { getVersionId } = require('./jira-versions');

/**
 * @typedef {Object} VersionInfo
 * @property {string} version - The resolved version string
 * @property {string} source - Source of the version (e.g., 'cli-param', 'version-file')
 */

/**
 * Resolves a version from a version.json file for fix version
 * @returns {VersionInfo}
 * @throws {Error} If a version cannot be read from a file
 */
function resolveFromVersionFile() {
  try {
    const versionData = getVersion();
    return {
      version: `Release ${versionData.version}`,
      source: 'version-file'
    };
  } catch (error) {
    throw new Error(
      `Could not determine version from version.json: ${error.message}. ` +
      'Please provide a fix version using the --fix-version option.'
    );
  }
}

/**
 * Resolves the actual version from a version.json file (just the version number)
 * @returns {VersionInfo}
 * @throws {Error} If a version cannot be read from file
 */
function resolveActualVersionFromFile() {
  try {
    const versionData = getVersion();
    return {
      version: versionData.version,
      source: 'version-file'
    };
  } catch (error) {
    throw new Error(
      `Could not determine version from version.json: ${error.message}. ` +
      'Please provide an actual version using the --actual-version option.'
    );
  }
}

/**
 * Resolves the fix version based on provided options
 * @param {Object} options - Resolution options
 * @param {string} [options.fixVersion] - Explicitly provided fix version
 * @param {boolean} options.updateFixVersion - Whether fix version update is enabled
 * @returns {VersionInfo|null} Resolved version info or null if no version needed
 */
function resolveFixVersion(options) {
  const { fixVersion, updateFixVersion } = options;

  if (!updateFixVersion) {
    return null;
  }

  if (fixVersion) {
    return {
      version: fixVersion,
      source: 'cli-param'
    };
  }

  return resolveFromVersionFile();
}

/**
 * Resolves the actual version based on provided options
 * @param {Object} options - Resolution options
 * @param {string} [options.actualVersion] - Explicitly provided actual version
 * @param {boolean} options.updateActualVersion - Whether actual version update is enabled
 * @returns {VersionInfo|null} Resolved version info or null if no version needed
 */
function resolveActualVersion(options) {
  const { actualVersion, updateActualVersion } = options;

  if (!updateActualVersion) {
    return null;
  }

  if (actualVersion) {
    return {
      version: actualVersion,
      source: 'cli-param'
    };
  }

  return resolveActualVersionFromFile();
}

/**
 * Extracts a project key from a Jira ticket key
 * @param {string} ticketKey - Jira ticket key (e.g., "PROJ-123")
 * @returns {string} Project key (e.g., "PROJ")
 * @throws {Error} If a ticket key format is invalid
 */
function extractProjectKey(ticketKey) {
  const parts = ticketKey.split('-');
  if (parts.length < 2) {
    throw new Error(`Invalid ticket ID format: ${ticketKey}`);
  }
  return parts[0];
}

/**
 * @typedef {Object} VersionIdResult
 * @property {string} versionId - The Jira version ID
 * @property {string} versionName - The version name
 * @property {string} projectKey - The project key
 */

/**
 * Resolves Jira version ID for the given version name
 * @param {Object} jiraClient - Jira API client instance
 * @param {string[]} tickets - Array of ticket IDs
 * @param {string} versionName - Version name to resolve
 * @returns {Promise<VersionIdResult>}
 * @throws {Error} If version ID cannot be resolved
 */
async function resolveVersionId(jiraClient, tickets, versionName) {
  if (!tickets || tickets.length === 0) {
    throw new Error('No tickets provided to extract project key');
  }

  const projectKey = extractProjectKey(tickets[0]);

  try {
    const versionId = await getVersionId(jiraClient, projectKey, versionName);
    return {
      versionId,
      versionName,
      projectKey
    };
  } catch (error) {
    throw new Error(
      `Failed to resolve version ID for "${versionName}" in project ${projectKey}: ${error.message}`
    );
  }
}

module.exports = {
  extractProjectKey,
  resolveFromVersionFile,
  resolveActualVersionFromFile,
  resolveFixVersion,
  resolveActualVersion,
  resolveVersionId,
};
