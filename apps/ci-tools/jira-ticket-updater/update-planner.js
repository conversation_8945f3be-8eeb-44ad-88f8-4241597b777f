/**
 * Builds an update plan for the tickets
 * @param {string[]} jiraTickets - Array of Jira ticket IDs
 * @param {Map<string, string[]>} ticketToLabels - Map of ticket IDs to their labels
 * @param {Object} options - Update options
 * @returns {Object} Update plan object
 */
function buildUpdatePlan(jiraTickets, ticketToLabels, options) {
  const { updateFixVersion, updateActualVersion, updateLabels, fixVersion, actualVersion } = options;
  const updatePlan = {};

  jiraTickets.forEach(ticket => {
    updatePlan[ticket] = {
      fixVersion: updateFixVersion && fixVersion ? fixVersion : null,
      actualVersion: updateActualVersion && actualVersion ? actualVersion : null,
      labels: []
    };

    if (updateLabels && ticketToLabels.get(ticket)) {
      updatePlan[ticket].labels = ticketToLabels.get(ticket) || [];
    }
  });

  return updatePlan;
}

/**
 * Displays the update plan to the console
 * @param {Object} updatePlan - Update plan object
 */
function displayUpdatePlan(updatePlan) {
  console.log('\nUpdate plan:');

  Object.entries(updatePlan).forEach(([ticket, plan]) => {
    console.log(`- ${ticket}:`);
    if (plan.fixVersion) {
      console.log(`  Fix Version: ${plan.fixVersion}`);
    }
    if (plan.actualVersion) {
      console.log(`  Actual Version: ${plan.actualVersion}`);
    }

    if (plan.labels.length > 0) {
      console.log(`  Labels: ${plan.labels.join(', ')}`);
    } else {
      console.log('  Labels: <no labels>');
    }
  });
}

/**
 * Displays dry-run analysis for label updates
 * @param {Map<string, LabelGroup>} labelGroups - Label groups from groupTicketsByLabels
 * @param {string[]} jiraTickets - Array of Jira ticket IDs
 */
function displayLabelDryRunAnalysis(labelGroups, jiraTickets) {
  console.log('\n=== LABEL UPDATE ANALYSIS ===');
  console.log(`Found ${labelGroups.size} unique label combination(s):\n`);

  let groupIndex = 1;
  for (const [groupLabel, group] of labelGroups.entries()) {
    if (group.labels.length === 0) {
      console.log(`Group ${groupIndex}: <no labels>`);
      console.log(`  Tickets: ${group.tickets.join(', ')} (${group.tickets.length} ticket${group.tickets.length === 1 ? '' : 's'})`);
      console.log(`  Action: SKIP - no labels to add`);
    } else {
      console.log(`Group ${groupIndex}: [${groupLabel}]`);
      console.log(`  Tickets: ${group.tickets.join(', ')} (${group.tickets.length} ticket${group.tickets.length === 1 ? '' : 's'})`);
      if (group.tickets.length > 1) {
        console.log(`  Action: BULK UPDATE - one API call for ${group.tickets.length} tickets`);
      } else {
        console.log(`  Action: INDIVIDUAL UPDATE - one API call`);
      }
    }
    groupIndex++;
  }

  displayLabelApiEfficiency(calculateLabelApiEfficiency(labelGroups, jiraTickets));
}

/**
 * Calculates label API call efficiency for label updates
 * @param {Map<string, LabelGroup>} labelGroups - Label groups from groupTicketsByLabels
 * @param {string[]} jiraTickets - Array of Jira ticket IDs
 * @returns {Object} Efficiency metrics
 */
function calculateLabelApiEfficiency(labelGroups, jiraTickets) {
  let bulkCalls = 0;
  let individualCalls = 0;
  let skippedTickets = 0;

  for (const group of labelGroups.values()) {
    if (group.labels.length === 0) {
      skippedTickets += group.tickets.length;
    } else if (group.tickets.length > 1) {
      bulkCalls++;
    } else {
      individualCalls++;
    }
  }

  return {
    totalTickets: jiraTickets.length,
    ticketsToUpdate: jiraTickets.length - skippedTickets,
    skippedTickets,
    bulkCalls,
    individualCalls,
    totalApiCalls: bulkCalls + individualCalls,
  };
}

/**
 * Displays label API efficiency summary
 * @param {Object} efficiency - Efficiency metrics from calculateApiEfficiency
 */
function displayLabelApiEfficiency(efficiency) {
  console.log(`\n=== LABEL API EFFICIENCY SUMMARY ===`);
  console.log(`Total tickets: ${efficiency.totalTickets}`);
  console.log(`Tickets to update: ${efficiency.ticketsToUpdate}`);
  console.log(`Tickets to skip: ${efficiency.skippedTickets}`);
  console.log(`Bulk API calls: ${efficiency.bulkCalls}`);
  console.log(`Individual API calls: ${efficiency.individualCalls}`);
  console.log(`Total API calls needed: ${efficiency.totalApiCalls}`);
}

/**
 * Displays dry-run analysis for version updates
 * @param {Object} options - Version update options
 * @param {boolean} options.updateFixVersion - Whether fix version will be updated
 * @param {string} [options.fixVersion] - Fix version value
 * @param {boolean} options.updateActualVersion - Whether actual version will be updated
 * @param {string} [options.actualVersion] - Actual version value
 * @param {string[]} jiraTickets - Array of Jira ticket IDs
 */
function displayVersionDryRunAnalysis(options, jiraTickets) {
  const { updateFixVersion, fixVersion, updateActualVersion, actualVersion } = options;

  if (!updateFixVersion && !updateActualVersion) {
    return;
  }

  console.log('\n=== VERSION UPDATE ANALYSIS ===');

  if (updateFixVersion && fixVersion) {
    console.log(`Fix Version Update:`);
    console.log(`  Value: ${fixVersion}`);
    console.log(`  Tickets: ${jiraTickets.join(', ')} (${jiraTickets.length} ticket${jiraTickets.length === 1 ? '' : 's'})`);
    console.log(`  Action: BULK UPDATE - one API call for all tickets`);
  }

  if (updateActualVersion && actualVersion) {
    console.log(`Actual Version Update:`);
    console.log(`  Value: ${actualVersion}`);
    console.log(`  Tickets: ${jiraTickets.join(', ')} (${jiraTickets.length} ticket${jiraTickets.length === 1 ? '' : 's'})`);
    console.log(`  Action: BULK UPDATE - one API call for all tickets`);
  }
}

module.exports = {
  buildUpdatePlan,
  displayUpdatePlan,
  displayLabelDryRunAnalysis,
  displayVersionDryRunAnalysis,
  calculateLabelApiEfficiency,
  displayLabelApiEfficiency,
};
