/**
 * @typedef {Object} ValidationResult
 * @property {boolean} valid - Whether validation passed
 * @property {string} [error] - Error message if validation failed
 */

/**
 * Validates that a ticket array is provided and not empty
 * @param {string[]} tickets - Array of Jira ticket IDs
 * @returns {ValidationResult}
 */
function validateTickets(tickets) {
  if (!tickets || !Array.isArray(tickets)) {
    return {
      valid: false,
      error: 'Tickets must be provided as an array. Please provide a comma-separated list of ticket keys to update.'
    };
  }

  if (tickets.length === 0) {
    return {
      valid: false,
      error: 'No tickets provided to update. Please provide at least one ticket key to update.'
    };
  }

  return { valid: true };
}

/**
 * Validates update options configuration
 * @param {Object} options - Update options
 * @param {boolean} options.updateFixVersion - Whether to update fix version
 * @param {string} [options.fixVersion] - Fix version if provided
 * @param {boolean} options.updateActualVersion - Whether to update actual version
 * @param {string} [options.actualVersion] - Actual version if provided
 * @param {boolean} options.updateLabels - Whether to update labels
 * @returns {ValidationResult}
 */
function validateUpdateOptions(options) {
  const { updateFixVersion, updateActualVersion, updateLabels } = options;

  // At least one update type should be enabled
  if (!updateFixVersion && !updateActualVersion && !updateLabels) {
    return {
      valid: false,
      error: 'At least one update type (fix version, actual version, or labels) must be enabled'
    };
  }

  return {
    valid: true,
  };
}

/**
 * Validates Jira connection options
 * @param {Object} options - Connection options
 * @param {string} options.jiraUrl - Jira API URL
 * @param {string} options.jiraToken - Jira API token
 * @param {string} options.jiraEmail - Jira account email
 * @returns {ValidationResult}
 */
function validateConnectionOptions(options) {
  const { jiraUrl, jiraToken, jiraEmail } = options;

  if (!jiraUrl || !jiraToken || !jiraEmail) {
    const missing = [];
    if (!jiraUrl) missing.push('jiraUrl');
    if (!jiraToken) missing.push('jiraToken');
    if (!jiraEmail) missing.push('jiraEmail');

    return {
      valid: false,
      error: `Missing required Jira connection parameters: ${missing.join(', ')}`
    };
  }

  return { valid: true };
}

/**
 * Validates all options for the update operation
 * @param {Object} options - All update options
 * @returns {ValidationResult}
 */
function validateAllOptions(options) {
  // Connection validation
  const connectionValidation = validateConnectionOptions(options);
  if (!connectionValidation.valid) {
    return connectionValidation;
  }

  // Tickets validation
  const ticketsValidation = validateTickets(options.tickets);
  if (!ticketsValidation.valid) {
    return ticketsValidation;
  }

  // Update options validation
  const updateValidation = validateUpdateOptions(options);
  if (!updateValidation.valid) {
    return updateValidation;
  }

  return {
    valid: true,
  };
}

module.exports = {
  validateTickets,
  validateUpdateOptions,
  validateConnectionOptions,
  validateAllOptions
};
