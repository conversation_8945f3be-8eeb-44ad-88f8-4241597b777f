/**
 * @typedef {Object} UpdateResult
 * @property {boolean} success - Whether the update operation succeeded
 * @property {number} ticketsUpdated - Number of tickets that were updated
 * @property {string|null} error - Error message if operation failed
 * @property {*} details - Additional details about the operation
 * @property {boolean} [dryRun] - Whether this was a dry run
 * @property {boolean} [fixVersionUpdated] - Whether fix version was updated
 * @property {Object} [labelResults] - Label update results
 * @property {number} [executionTimeMs] - Execution time in milliseconds
 */

/**
 * @typedef {Object} ExecutionTime
 * @property {number} seconds - Seconds
 * @property {number} milliseconds - Milliseconds
 * @property {number} totalMs - Total time in milliseconds
 * @property {string} formatted - Human-readable format
 */

/**
 * Creates a no-updates-needed result
 * @param {string} [message] - Custom message
 * @returns {UpdateResult}
 */
function createNoUpdatesResult(message = 'No updates to perform. All tickets would remain unchanged.') {
  return {
    success: false,
    ticketsUpdated: 0,
    error: null,
    details: message
  };
}

/**
 * Creates an error result
 * @param {Error|string} error - Error object or message
 * @param {*} [details] - Additional error details
 * @returns {UpdateResult}
 */
function createErrorResult(error, details = null) {
  const errorMessage = error instanceof Error ? error.message : error;

  return {
    success: false,
    ticketsUpdated: 0,
    error: errorMessage,
    details: details || (error instanceof Error ? error.details : null)
  };
}

/**
 * Creates a dry run result
 * @param {Object} updatePlan - The update plan that would be executed
 * @param {number} [executionTimeMs] - Execution time in milliseconds
 * @returns {UpdateResult}
 */
function createDryRunResult(updatePlan, executionTimeMs) {
  const result = {
    success: true,
    ticketsUpdated: 0,
    error: null,
    details: updatePlan,
    dryRun: true
  };

  if (executionTimeMs !== undefined) {
    result.executionTimeMs = executionTimeMs;
  }

  return result;
}

/**
 * Creates a successful update result
 * @param {Object} options - Result options
 * @param {number} options.ticketsUpdated - Number of tickets updated
 * @param {Object} options.updatePlan - The executed update plan
 * @param {Object} options.executionResults - Results from update execution
 * @param {boolean} options.updateFixVersion - Whether fix version was being updated
 * @param {boolean} options.updateLabels - Whether labels were being updated
 * @param {number} [options.executionTimeMs] - Execution time in milliseconds
 * @returns {UpdateResult}
 */
function createSuccessResult(options) {
  const {
    ticketsUpdated,
    updatePlan,
    executionResults,
    updateFixVersion,
    updateLabels,
    executionTimeMs
  } = options;

  const result = {
    success: true,
    ticketsUpdated,
    error: null,
    details: updatePlan
  };

  // Add fix version result if applicable
  if (updateFixVersion) {
    result.fixVersionUpdated = executionResults.fixVersionUpdated || false;
    if (executionResults.fixVersionResult && !executionResults.fixVersionResult.success) {
      result.fixVersionError = executionResults.fixVersionResult.error;
    }
  }

  // Add label results if applicable
  if (updateLabels) {
    result.labelResults = executionResults.labelResults || {
      successCount: 0,
      failureCount: 0
    };
  }

  // Add execution time if provided
  if (executionTimeMs !== undefined) {
    result.executionTimeMs = executionTimeMs;
  }

  return result;
}

/**
 * Formats execution time from process.hrtime() result
 * @param {number[]} hrtime - Result from process.hrtime()
 * @returns {ExecutionTime} Formatted time object
 *
 */
function formatExecutionTime(hrtime) {
  const seconds = hrtime[0];
  const milliseconds = Math.round(hrtime[1] / 1000000);
  const totalMs = seconds * 1000 + milliseconds;

  return {
    seconds,
    milliseconds,
    totalMs,
    formatted: `${seconds}s ${milliseconds}ms`,
  };
}

/**
 * Creates a summary message for the update result
 * @param {UpdateResult} result - The update result
 * @returns {string} Human-readable summary
 */
function createResultSummary(result) {
  if (!result.success) {
    if (result.error) {
      return `Update failed: ${result.error}`;
    }
    return 'No updates were performed';
  }

  if (result.dryRun) {
    return 'DRY RUN completed - no changes were made';
  }

  const parts = [`Updated ${result.ticketsUpdated} tickets`];

  if (result.fixVersionUpdated) {
    parts.push('fix version updated');
  }

  if (result.labelResults) {
    const { successCount, failureCount } = result.labelResults;
    if (successCount > 0) {
      parts.push(`${successCount} label groups updated`);
    }
    if (failureCount > 0) {
      parts.push(`${failureCount} label groups failed`);
    }
  }

  return parts.join(', ');
}

module.exports = {
  createNoUpdatesResult,
  createErrorResult,
  createDryRunResult,
  createSuccessResult,
  formatExecutionTime,
  createResultSummary
};
