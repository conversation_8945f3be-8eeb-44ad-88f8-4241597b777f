// Final comprehensive test for Enhanced Nord Pool Optimizer
// Demonstrates all features adapted from the old proven system

// Copy the enhanced optimizer class for testing
class NordPoolOptimizer {
  constructor() {
    this.baseUrl = 'https://dashboard.elering.ee/api/nps/price';
  }

  getVilniusDate() {
    const now = new Date();
    return new Date(now.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
  }

  formatDateForAPI(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  createTodayApiUrl() {
    const today = this.getVilniusDate();
    const start = new Date(new Date(`${this.formatDateForAPI(today)}T00:00:00`).toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
    const end = new Date(new Date(`${this.formatDateForAPI(today)}T23:59:59.999`).toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
    end.setMilliseconds(999);
    return `${this.baseUrl}?start=${start.toISOString()}&end=${end.toISOString()}`;
  }

  async fetchTodayPrices() {
    const url = this.createTodayApiUrl();
    try {
      const response = await fetch(url);
      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      if (!data.success || !data.data || !data.data.lt) {
        throw new Error('Invalid API response format or no Lithuanian data available');
      }
      return data.data.lt.map(item => {
        const date = new Date(item.timestamp * 1000);
        const vilniusHour = parseInt(date.toLocaleString('lt-LT', {
          timeZone: 'Europe/Vilnius',
          hour: '2-digit',
          hour12: false
        }));
        return { timestamp: item.timestamp, price: item.price, hour: vilniusHour };
      }).sort((a, b) => a.hour - b.hour);
    } catch (error) {
      throw new Error(`Failed to fetch Nord Pool prices: ${error.message}`);
    }
  }

  validateSlotOptions(options) {
    const { from = 0, to = 23, numberOfHours = 1 } = options;
    if (from < 0 || from > 23) throw new Error('Parameter "from" must be between 0 and 23');
    if (to < 0 || to > 23) throw new Error('Parameter "to" must be between 0 and 23');
    if (to <= from) throw new Error('Parameter "to" must be greater than "from"');
    if (numberOfHours < 1 || numberOfHours > 24) throw new Error('Parameter "numberOfHours" must be between 1 and 24');
    const availableHours = to - from + 1;
    if (numberOfHours > availableHours) {
      throw new Error(`Cannot fit ${numberOfHours} consecutive hours within ${from}-${to} time window (${availableHours} hours available)`);
    }
  }

  getCurrentHour() {
    const now = this.getVilniusDate();
    return now.getHours();
  }

  async getCurrentPrice() {
    const prices = await this.fetchTodayPrices();
    const currentHour = this.getCurrentHour();
    const currentPriceData = prices.find(price => price.hour === currentHour);
    if (!currentPriceData) throw new Error(`No price data available for current hour ${currentHour}`);
    return currentPriceData.price;
  }

  async getDailyPriceExtremes() {
    const prices = await this.fetchTodayPrices();
    if (!prices || prices.length === 0) throw new Error('No price data available');
    const cheapest = prices.reduce((min, current) => current.price < min.price ? current : min);
    const expensive = prices.reduce((max, current) => current.price > max.price ? current : max);
    return {
      cheapest: { hour: cheapest.hour, price: cheapest.price },
      expensive: { hour: expensive.hour, price: expensive.price }
    };
  }

  async getDailyAveragePrice() {
    const prices = await this.fetchTodayPrices();
    if (!prices || prices.length === 0) throw new Error('No price data available');
    const sum = prices.reduce((total, price) => total + price.price, 0);
    return sum / prices.length;
  }

  async isCurrentPriceBelowAverage() {
    const currentPrice = await this.getCurrentPrice();
    const averagePrice = await this.getDailyAveragePrice();
    return currentPrice < averagePrice;
  }

  async getNegativePriceHoursCount() {
    const prices = await this.fetchTodayPrices();
    return prices.filter(price => price.price < 0).length;
  }

  async hasNegativePricesToday() {
    const count = await this.getNegativePriceHoursCount();
    return count > 0;
  }

  async getCurrentPriceAnalysis() {
    const currentPrice = await this.getCurrentPrice();
    const averagePrice = await this.getDailyAveragePrice();
    const extremes = await this.getDailyPriceExtremes();
    const percentOfMax = (currentPrice / extremes.expensive.price) * 100;
    const percentOfAverage = (currentPrice / averagePrice) * 100;
    const percentOfRange = ((currentPrice - extremes.cheapest.price) / (extremes.expensive.price - extremes.cheapest.price)) * 100;

    return {
      currentPrice, dailyStats: { average: averagePrice, min: extremes.cheapest.price, max: extremes.expensive.price },
      comparison: { percentOfMax: Math.round(percentOfMax), percentOfAverage: Math.round(percentOfAverage), percentOfRange: Math.round(percentOfRange) },
      isBelowAverage: currentPrice < averagePrice, isNegative: currentPrice < 0
    };
  }

  analyzeTimeSlot(consecutivePrices, dailyAverage) {
    const prices = consecutivePrices.map(p => p.price);
    const negativeCount = prices.filter(p => p < 0).length;
    const rawAverage = prices.reduce((sum, p) => sum + p, 0) / prices.length;
    const modifiedPrices = prices.map(p => Math.max(0, p));
    const modifiedAverage = modifiedPrices.reduce((sum, p) => sum + p, 0) / modifiedPrices.length;
    const percentAboveDailyAverage = ((rawAverage - dailyAverage) / dailyAverage) * 100;
    return { negativeCount, rawAverage, modifiedAverage, percentAboveDailyAverage, hasNegativePrices: negativeCount > 0 };
  }

  findBestTimeSlot(timeSlots) {
    if (timeSlots.length === 0) return null;
    const sortedSlots = timeSlots.sort((a, b) => {
      if (a.negativeCount !== b.negativeCount) return b.negativeCount - a.negativeCount;
      return a.modifiedAverage - b.modifiedAverage;
    });
    return sortedSlots[0];
  }

  isAnySlotWorthRunning(timeSlots, dailyAverage) {
    if (timeSlots.some(slot => slot.hasNegativePrices)) return true;
    const bestSlot = this.findBestTimeSlot(timeSlots);
    if (!bestSlot) return false;
    return bestSlot.percentAboveDailyAverage <= 20;
  }

  async findOptimalTimeSlotAdvanced(options = {}) {
    const { from = 0, to = 23, numberOfHours = 1 } = options;
    this.validateSlotOptions({ from, to, numberOfHours });
    const prices = await this.fetchTodayPrices();
    const filteredPrices = prices.filter(price => price.hour >= from && price.hour <= to);
    const dailyAverage = await this.getDailyAveragePrice();
    const dailyExtremes = await this.getDailyPriceExtremes();

    const timeSlots = [];
    for (let i = 0; i <= filteredPrices.length - numberOfHours; i++) {
      const consecutivePrices = filteredPrices.slice(i, i + numberOfHours);
      const analysis = this.analyzeTimeSlot(consecutivePrices, dailyAverage);
      timeSlots.push({
        startHour: consecutivePrices[0].hour, endHour: consecutivePrices[consecutivePrices.length - 1].hour,
        prices: consecutivePrices.map(p => p.price), ...analysis
      });
    }

    const bestSlot = this.findBestTimeSlot(timeSlots);
    const isAnySlotOptimal = this.isAnySlotWorthRunning(timeSlots, dailyAverage);
    return {
      bestSlot, isOptimal: isAnySlotOptimal && bestSlot !== null, allSlots: timeSlots,
      dailyStats: { average: dailyAverage, min: dailyExtremes.cheapest.price, max: dailyExtremes.expensive.price },
      optimalityReason: isAnySlotOptimal ? 'Found optimal time slot' : 'All available time slots are significantly above daily average with no negative prices'
    };
  }

  async isOptimalTimeToStart(options = {}) {
    const analysis = await this.findOptimalTimeSlotAdvanced(options);
    if (!analysis.isOptimal || !analysis.bestSlot) return false;
    const currentHour = this.getCurrentHour();
    return currentHour === analysis.bestSlot.startHour;
  }
}

async function demonstrateEnhancedFeatures() {
  console.log('=== Enhanced Nord Pool Optimizer - Final Demo ===\n');

  const optimizer = new NordPoolOptimizer();
  const currentHour = optimizer.getCurrentHour();

  try {
    console.log(`🕐 Current time: ${currentHour}:00\n`);

    // 1. Basic appliance checks (simple true/false for Homey flows)
    console.log('=== BASIC APPLIANCE CHECKS ===');
    console.log('🍽️  Dishwasher (2 hours):');
    const dishwasherOptimal = await optimizer.isOptimalTimeToStart({ numberOfHours: 2 });
    console.log(`   Should start now: ${dishwasherOptimal ? '✅ YES' : '❌ NO'}`);

    console.log('\n👕 Washing Machine (3 hours):');
    const washingOptimal = await optimizer.isOptimalTimeToStart({ numberOfHours: 3 });
    console.log(`   Should start now: ${washingOptimal ? '✅ YES' : '❌ NO'}`);

    // 2. Enhanced price analysis
    console.log('\n=== ENHANCED PRICE ANALYSIS ===');
    const priceAnalysis = await optimizer.getCurrentPriceAnalysis();
    console.log(`💰 Current price: €${priceAnalysis.currentPrice.toFixed(2)}/MWh`);
    console.log(`📊 Daily average: €${priceAnalysis.dailyStats.average.toFixed(2)}/MWh`);
    console.log(`📈 Current is ${priceAnalysis.comparison.percentOfAverage}% of daily average`);
    console.log(`🔋 Is negative (free): ${priceAnalysis.isNegative ? '✅ YES' : '❌ NO'}`);
    console.log(`📉 Below average: ${priceAnalysis.isBelowAverage ? '✅ YES' : '❌ NO'}`);

    // 3. Negative price analysis
    console.log('\n=== NEGATIVE PRICE ANALYSIS ===');
    const hasNegative = await optimizer.hasNegativePricesToday();
    const negativeCount = await optimizer.getNegativePriceHoursCount();
    console.log(`⚡ Free electricity available today: ${hasNegative ? '✅ YES' : '❌ NO'}`);
    console.log(`🔢 Hours with negative prices: ${negativeCount}`);

    // 4. Advanced analysis with sophisticated scoring
    console.log('\n=== ADVANCED ANALYSIS (Sophisticated Scoring) ===');
    const analysis = await optimizer.findOptimalTimeSlotAdvanced({ numberOfHours: 2 });
    console.log(`🎯 Any optimal time today: ${analysis.isOptimal ? '✅ YES' : '❌ NO'}`);

    if (analysis.bestSlot) {
      console.log(`⏰ Best time slot: ${analysis.bestSlot.startHour}:00-${analysis.bestSlot.endHour}:00`);
      console.log(`⚡ Negative price hours in slot: ${analysis.bestSlot.negativeCount}`);
      console.log(`💶 Modified average: €${analysis.bestSlot.modifiedAverage.toFixed(2)}/MWh`);
      console.log(`📊 % above daily average: ${analysis.bestSlot.percentAboveDailyAverage.toFixed(1)}%`);
      console.log(`🏆 Score logic: ${analysis.bestSlot.negativeCount > 0 ? 'Prioritized for negative prices' : 'Selected for lowest modified average'}`);
    } else {
      console.log(`❌ ${analysis.optimalityReason}`);
    }

    // 5. Homey flow examples
    console.log('\n=== HOMEY FLOW EXAMPLES ===');
    console.log('For Homey flows, use these simple patterns:');
    console.log('');
    console.log('// Basic appliance check');
    console.log('const optimizer = new NordPoolOptimizer();');
    console.log('return await optimizer.isOptimalTimeToStart({ numberOfHours: 2 });');
    console.log('');
    console.log('// Check if current price is below average');
    console.log('return await optimizer.isCurrentPriceBelowAverage();');
    console.log('');
    console.log('// Check if there\'s free electricity today');
    console.log('return await optimizer.hasNegativePricesToday();');

    console.log('\n🎉 Enhanced optimizer successfully integrates proven logic with 24-hour data!');

  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

// Run the demonstration
demonstrateEnhancedFeatures();
