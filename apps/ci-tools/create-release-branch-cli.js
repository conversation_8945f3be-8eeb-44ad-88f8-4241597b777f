const yargs = require('yargs');
const { hideBin } = require('yargs/helpers');
const { prepareReleaseBranch } = require('./create-release-branch');

const usageMessage = `
Release Branch Creation Commands:

  create-release-branch --type=TYPE --branch=BRANCH [--push]  Create a new branch for release, patch, or hotfix based on specified source branch

Arguments:
  --type, -t      Release type: "release", "patch", or "hotfix" (required)
  --branch, -b    Source branch to base the release on (required)
                  Must match: ^((release|patch|hotfix)/v\\d+\\.\\d+\\.\\d+|develop|main)$
  --push, -p      Push the created branch to remote (optional, default: false)

Examples:
  Direct usage:
    node create-release-branch-cli.js --type=release --branch=develop
    node create-release-branch-cli.js --type=patch --branch=main --push
    node create-release-branch-cli.js --type=hotfix --branch=main

  Using npm scripts:
    npm run create-branch:release -- --branch=develop
    npm run create-branch:patch -- --branch=main
    npm run create-branch:hotfix -- --branch=main

For more details, use --help with any command.
`;

yargs(hideBin(process.argv))
  .usage(usageMessage)
  .command('$0', 'Create a release branch', (yargs) => {
    return yargs
      .option('type', {
        alias: 't',
        describe: 'Release type: "release", "patch", or "hotfix"',
        type: 'string',
        demandOption: true,
        choices: ['release', 'patch', 'hotfix']
      })
      .option('branch', {
        alias: 'b',
        describe: 'Source branch to base the release on (format: (release|patch|hotfix)/vX.Y.Z or develop|main)',
        type: 'string',
        demandOption: true
      })
      .option('push', {
        alias: 'p',
        describe: 'Push the created branch to remote',
        type: 'boolean',
        default: false
      });
  }, (argv) => {
    try {
      // Validate branch name format early
      const branchRegex = /^((release|patch|hotfix)\/v\d+\.\d+\.\d+|develop|main)$/;
      if (!branchRegex.test(argv.branch)) {
        console.error(`Error: Invalid branch name format. Expected: (release|patch|hotfix)/vX.Y.Z or develop|main, got: ${argv.branch}`);
        process.exit(1); // Exit immediately if branch name is invalid
      }

      const result = prepareReleaseBranch(argv.type, argv.branch, argv.push);
      console.log(`Successfully created branch: ${result.newBranch}`);

      if (!argv.push) {
        console.log('Note: Branch created locally only. To push to remote, use --push option.');
      }

      process.exit(0);
    } catch (error) {
      console.error('Error:', error.message);
      process.exit(1);
    }
  })
  .strict()
  .help()
  .epilogue('For more information, refer to the README.md file in the .circleci directory.')
  .argv;
