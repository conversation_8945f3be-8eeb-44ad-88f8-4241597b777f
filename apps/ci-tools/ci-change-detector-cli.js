const yargs = require('yargs');
const { hideBin } = require('yargs/helpers');

const CIChangeDetector = require('./ci-change-detector');

if (require.main === module) {
  const startTime = process.hrtime();

  const argv = yargs(hideBin(process.argv))
      .option('token', {
        alias: 't',
        description: 'CircleCI API token',
        demandOption: true,
      })
      .option('project', {
        alias: 'p',
        description: 'Project slug (e.g., gh/org/repo)',
        demandOption: true,
      })
      .option('workflow', {
        alias: 'w',
        description: 'Specific workflow name to check',
        demandOption: true,
      })
      .option('branch', {
        alias: 'b',
        description: 'Branch to check',
        demandOption: true,
      })
      .option('directory', {
        alias: 'd',
        description: 'Directory to check for changes',
        default: '.',
      })
      .argv;

  const detector = new CIChangeDetector({
    token: argv.token,
    projectSlug: argv.project,
    directory: argv.directory,
    branch: argv.branch,
    workflow: argv.workflow,
  });

  detector
    .detectChanges()
    .then(result => {
      console.log(JSON.stringify(result, null, 2));

      const endTime = process.hrtime(startTime);
      console.log(`Execution time: ${endTime[0]}s ${endTime[1] / 1000000}ms`);

      process.exit(0);
    })
    .catch(error => {
      console.error('Error:', error.message);
      process.exit(1);
    });
}

module.exports = CIChangeDetector;
