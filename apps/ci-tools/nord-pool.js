// =========================================================================================

// NordPool API integration for HomeyScript
// Prices are automatically converted from EUR/MWh to EUR/kWh (divided by 1000)
// 21% VAT is automatically added to all prices
// Negative prices are treated as zero (free electricity)
// All price values use 5 decimal places for precision

/**
 * @typedef {Object} PriceOptions
 * @property {Date|string} [date] - The date to get prices for (defaults to current date)
 * @property {Date|string} [from] - Start date/time for a date range
 * @property {Date|string} [to] - End date/time for a date range
 * @property {{hour?: number, day?: number, week?: number, month?: number}} [maxRange] - Maximum time range limit
 * @property {string} [url] - API endpoint URL (used internally)
 */

const baseUrl = 'https://dataportal-api.nordpoolgroup.com/api';

const config = {
  baseUrl: baseUrl,
  priceUrlHourly: baseUrl + '/DayAheadPrices',
  priceUrlAggregate: baseUrl + '/AggregatePrices'
};

function formatPrice(price) {
  return parseFloat(price.toFixed(5));
}

function formatPriceDisplay(price) {
  if (price <= 0) {
    return "FREE (€0.00000/kWh)";
  }
  return `€${price.toFixed(5)}/kWh`;
}

// Helper functions to replace dayjs functionality
function getVilniusTime(date) {
  if (!date) {
    // Get current time in Vilnius timezone
    const now = new Date();
    const vilniusTime = new Date(now.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
    return vilniusTime;
  }
  // Convert provided date to Vilnius timezone
  const inputDate = new Date(date);
  return new Date(inputDate.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
}

function convertUTCToVilnius(utcDateString) {
  // API returns UTC timestamps, just create Date object and it will be displayed in local time
  return new Date(utcDateString);
}

function formatDate(date) {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
}


function addTime(date, amount, unit) {
  const newDate = new Date(date);
  switch (unit) {
    case 'hour':
      newDate.setHours(newDate.getHours() + amount);
      break;
    case 'day':
      newDate.setDate(newDate.getDate() + amount);
      break;
    case 'week':
      newDate.setDate(newDate.getDate() + (amount * 7));
      break;
    case 'month':
      newDate.setMonth(newDate.getMonth() + amount);
      break;
  }
  return newDate;
}

function subtractTime(date, amount, unit) {
  return addTime(date, -amount, unit);
}

class Prices {
  /**
   * Get price for a specific hour
   * @param {PriceOptions} opts - Options for fetching the price
   * @returns {Promise<{area: string, date: string, value: number}>} The price data for the specified hour
   */
  async at(opts = {}) {
    const now = getVilniusTime();
    const requestedTime = opts.date ? getVilniusTime(opts.date) : now;

    // For the current hour, we want the hour we're currently in (e.g., 03:21 -> 03:00)
    const targetHour = new Date(requestedTime);
    targetHour.setMinutes(0);
    targetHour.setSeconds(0);
    targetHour.setMilliseconds(0);

    const results = await this.getValues({
      ...opts,
      url: config.priceUrlHourly,
      maxRange: { hour: 1 },
      date: requestedTime
    });

    if (results) {
      for (const result of results) {
        const resultTimeVilnius = convertUTCToVilnius(result.date);

        // Compare year, month, day, and hour in Vilnius timezone
        if (
          resultTimeVilnius.getFullYear() === targetHour.getFullYear() &&
          resultTimeVilnius.getMonth() === targetHour.getMonth() &&
          resultTimeVilnius.getDate() === targetHour.getDate() &&
          resultTimeVilnius.getHours() === targetHour.getHours()
        ) {
          return result;
        }
      }
    }
    throw new Error("No results found for " + targetHour.toLocaleString('lt-LT'));
  }

  /**
   * Get all hourly prices for a given day
   * @param {PriceOptions} opts - Options for fetching prices
   * @returns {Promise<Array<{area: string, date: string, value: number}>>} Array of hourly prices
   */
  async hourly(opts = {}) {
    const targetDate = opts.date ? getVilniusTime(opts.date) : getVilniusTime();

    const allPrices = await this.getValues({
      ...opts,
      url: config.priceUrlHourly,
      maxRange: { day: 1 },
      date: targetDate,
      from: null,
      to: null
    });

    // Filter to only include hours from the requested date in Vilnius timezone
    if (!allPrices) return allPrices;

    // Get year, month, day for the target date in Vilnius timezone
    const targetYear = parseInt(targetDate.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius', year: 'numeric' }));
    const targetMonth = parseInt(targetDate.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius', month: 'numeric' }));
    const targetDay = parseInt(targetDate.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius', day: 'numeric' }));

    const filtered = allPrices.filter(price => {
      const priceDate = new Date(price.date);
      const priceYear = parseInt(priceDate.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius', year: 'numeric' }));
      const priceMonth = parseInt(priceDate.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius', month: 'numeric' }));
      const priceDay = parseInt(priceDate.toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius', day: 'numeric' }));

      return priceYear === targetYear && priceMonth === targetMonth && priceDay === targetDay;
    });

    // Sort by time to ensure correct order (00:00 to 23:00)
    return filtered.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
  }

  /**
   * Get the maximum price for a given day
   * @param {PriceOptions} opts - Options for fetching prices
   * @returns {Promise<{area: string, date: string, value: number}|null>} The hour with maximum price
   */
  async dailyMax(opts = {}) {
    const hourlyPrices = await this.hourly(opts);
    if (!hourlyPrices || hourlyPrices.length === 0) {
      return null;
    }
    return hourlyPrices.reduce((max, current) =>
      current.value > max.value ? current : max
    );
  }

  /**
   * Get the minimum price for a given day
   * @param {PriceOptions} opts - Options for fetching prices
   * @returns {Promise<{area: string, date: string, value: number}|null>} The hour with minimum price
   */
  async dailyMin(opts = {}) {
    const hourlyPrices = await this.hourly(opts);
    if (!hourlyPrices || hourlyPrices.length === 0) {
      return null;
    }
    return hourlyPrices.reduce((min, current) =>
      current.value < min.value ? current : min
    );
  }

  /**
   * Calculate the average price for a given day
   * @param {PriceOptions} opts - Options for fetching prices
   * @returns {Promise<{average: number, count: number}|null>} The average price and hour count
   */
  async dailyAverage(opts = {}) {
    const hourlyPrices = await this.hourly(opts);
    if (!hourlyPrices || hourlyPrices.length === 0) {
      return null;
    }
    const sum = hourlyPrices.reduce((total, current) => total + current.value, 0);
    return {
      average: formatPrice(sum / hourlyPrices.length),
      count: hourlyPrices.length
    };
  }

  /**
   * Get hours with free electricity (price <= 0)
   * @param {PriceOptions} opts - Options for fetching prices
   * @returns {Promise<Array<{area: string, date: string, value: number}>>} Array of free hours
   */
  async dailyFreeHours(opts = {}) {
    const hourlyPrices = await this.hourly(opts);
    if (!hourlyPrices || hourlyPrices.length === 0) {
      return [];
    }
    return hourlyPrices.filter(price => price.value <= 0);
  }

  /**
   * Compare current hour price against daily statistics
   * @param {PriceOptions} opts - Options for fetching prices
   * @returns {Promise<Object>} Comparison results with recommendations
   */
  async priceComparison(opts = {}) {
    const currentHour = opts.date ? await this.at(opts) : await this.at();
    const dailyStats = await Promise.all([
      this.dailyMin(opts),
      this.dailyMax(opts),
      this.dailyAverage(opts)
    ]);

    const [minPrice, maxPrice, avgData] = dailyStats;

    if (!minPrice || !maxPrice || !avgData) {
      throw new Error("Unable to get daily statistics for comparison");
    }

    const currentPrice = currentHour.value;
    const percentOfMax = (currentPrice / maxPrice.value) * 100;
    const percentOfAverage = (currentPrice / avgData.average) * 100;
    const percentOfRange = ((currentPrice - minPrice.value) / (maxPrice.value - minPrice.value)) * 100;

    return {
      currentHour,
      dailyStats: {
        min: minPrice,
        max: maxPrice,
        average: avgData.average
      },
      comparison: {
        percentOfMax: Math.round(percentOfMax),
        percentOfAverage: Math.round(percentOfAverage),
        percentOfRange: Math.round(percentOfRange)
      },
      recommendations: this.getPriceRecommendation(percentOfMax, percentOfAverage)
    };
  }

  getPriceRecommendation(percentOfMax, percentOfAverage) {
    // Special case for free electricity
    if (percentOfMax === 0 || percentOfAverage === 0) {
      return {
        category: "free",
        message: "FREE ELECTRICITY! Perfect time to run all appliances!",
        runAppliances: true,
        alertFamily: false
      };
    }

    // Customizable thresholds
    const thresholds = {
      excellent: { maxPercent: 30, avgPercent: 80 },
      good: { maxPercent: 50, avgPercent: 100 },
      fair: { maxPercent: 70, avgPercent: 130 },
      expensive: { maxPercent: 85, avgPercent: 150 }
    };

    if (percentOfMax <= thresholds.excellent.maxPercent && percentOfAverage <= thresholds.excellent.avgPercent) {
      return {
        category: "excellent",
        message: "Excellent time to run any appliances! Very low prices.",
        runAppliances: true,
        alertFamily: false
      };
    } else if (percentOfMax <= thresholds.good.maxPercent && percentOfAverage <= thresholds.good.avgPercent) {
      return {
        category: "good",
        message: "Good time to run appliances. Reasonable prices.",
        runAppliances: true,
        alertFamily: false
      };
    } else if (percentOfMax <= thresholds.fair.maxPercent || percentOfAverage <= thresholds.fair.avgPercent) {
      return {
        category: "fair",
        message: "Fair prices. OK to run essential appliances, but consider waiting for better prices if possible.",
        runAppliances: false,
        alertFamily: false
      };
    } else if (percentOfMax <= thresholds.expensive.maxPercent || percentOfAverage <= thresholds.expensive.avgPercent) {
      return {
        category: "expensive",
        message: "Expensive hour. Avoid running non-essential appliances.",
        runAppliances: false,
        alertFamily: true
      };
    } else {
      return {
        category: "very-expensive",
        message: "Very expensive hour! Minimize electricity usage.",
        runAppliances: false,
        alertFamily: true
      };
    }
  }

  /**
   * Check if it's a good time to run appliances based on price thresholds
   * @param {PriceOptions} opts - Options for fetching prices
   * @param {{maxPercentThreshold?: number, avgPercentThreshold?: number}} customThresholds - Custom threshold values
   * @returns {Promise<{isGoodTime: boolean, recommendation: Object, details: Object}>} Recommendation result
   */
  async isGoodTimeToRun(opts = {}, customThresholds = {}) {
    const comparison = await this.priceComparison(opts);
    const thresholds = {
      maxPercentThreshold: 50, // Default: OK if ≤ 50% of daily max
      avgPercentThreshold: 120, // Default: OK if ≤ 120% of daily average
      ...customThresholds
    };

    const isGood = comparison.comparison.percentOfMax <= thresholds.maxPercentThreshold ||
      comparison.comparison.percentOfAverage <= thresholds.avgPercentThreshold;

    return {
      isGoodTime: isGood,
      recommendation: comparison.recommendations,
      details: comparison
    };
  }

  /**
   * Find the cheapest consecutive hours for running appliances
   * @param {number} numberOfHours - Number of consecutive hours needed
   * @param {PriceOptions} opts - Options for fetching prices
   * @returns {Promise<Object>} The cheapest time range with price details
   */
  async findCheapestConsecutiveHours(numberOfHours, opts = {}) {
    const hourlyPrices = await this.hourly(opts);
    if (!hourlyPrices || hourlyPrices.length < numberOfHours) {
      throw new Error(`Not enough hourly data available. Got ${hourlyPrices?.length || 0} hours, need ${numberOfHours}`);
    }

    let cheapestSum = -1;
    let cheapestRange = [];

    for (let i = 0; i <= hourlyPrices.length - numberOfHours; i++) {
      const range = hourlyPrices.slice(i, i + numberOfHours);
      const sum = range.reduce((total, hour) => total + hour.value, 0);

      if (sum < cheapestSum || cheapestSum === -1) {
        cheapestSum = sum;
        cheapestRange = range;
      }
    }

    return {
      range: cheapestRange,
      averagePrice: formatPrice(cheapestSum / numberOfHours),
      totalCost: formatPrice(cheapestSum),
      startTime: convertUTCToVilnius(cheapestRange[0].date),
      endTime: convertUTCToVilnius(cheapestRange[cheapestRange.length - 1].date)
    };
  }

  /**
   * Internal method to fetch price values from the API
   * @param {PriceOptions} opts - Options for fetching prices
   * @returns {Promise<Array<{area: string, date: string, value: number}>>} Array of price values
   * @private
   */
  async getValues(opts) {
    try {
      let fromTime = opts.from ? getVilniusTime(opts.from) : null;
      let toTime = opts.to ? getVilniusTime(opts.to) : null;
      const maxRangeKey = opts.maxRange ?
        (opts.maxRange.day ? "day" :
          opts.maxRange.hour ? "hour" :
            opts.maxRange.month ? "month" : "week") : undefined;
      const maxRangeValue = opts.maxRange && maxRangeKey ? opts.maxRange[maxRangeKey] : 0;

      // Lithuania area and EUR currency hardcoded
      const area = "LT";
      const currency = "EUR";

      // Time span validation
      if (fromTime && toTime && maxRangeKey && maxRangeValue) {
        const minFromTime = subtractTime(toTime, maxRangeValue, maxRangeKey);
        if (fromTime.getTime() < minFromTime.getTime()) {
          console.log("Time span too long. Setting start time to " + minFromTime.toISOString());
          fromTime = minFromTime;
        }
      }

      // Date calculation logic
      let optsDate;
      if (fromTime && !toTime && !opts.date && maxRangeKey && maxRangeValue) {
        optsDate = addTime(fromTime, maxRangeValue, maxRangeKey);
      } else if ((fromTime || toTime) && !opts.date) {
        optsDate = toTime || fromTime;
      } else if (!opts.date) {
        optsDate = getVilniusTime();
      } else {
        optsDate = getVilniusTime(opts.date);
      }

      // If no from/to times are specified, we don't set them
      // The API will return all 24 hours for the requested date

      console.log("Fetching prices for " + optsDate.toISOString(), formatDate(optsDate));

      const url = `${opts.url}?currency=${currency}&market=DayAhead&deliveryArea=${area}&date=${formatDate(optsDate)}`;

      console.log("Fetching from " + url);

      // Fetch data
      const resp = await fetch(url);
      if (resp.status === 204) {
        console.log("No content available yet for the requested date");
        return;
      }
      if (resp.status !== 200) {
        throw new Error(`Unable to make request to nordpool - status ${resp.status}`);
      }

      const data = await resp.json();

      if (data && data.multiAreaEntries && data.multiAreaEntries.length) {
        const values = [];

        for (const row of data.multiAreaEntries) {
          const date = new Date(row.deliveryStart);

          if (isNaN(date.getTime())) {
            console.log("Invalid date found, skipping entry");
            continue;
          } else if (
            (fromTime && date.getTime() < fromTime.getTime()) ||
            (toTime && date.getTime() >= toTime.getTime())
          ) {
            // Date out of given range
            continue;
          }

          const entry = row.entryPerArea;
          const value = parseFloat(entry[area]);

          if (isNaN(value)) {
            console.log("Invalid price value found, skipping entry");
            continue;
          }

          // Convert from EUR/MWh to EUR/kWh (divide by 1000) and add 21% VAT
          const pricePerKwhWithVAT = (value / 1000) * 1.21;

          values.push({
            area: area,
            date: date.toISOString(),
            value: parseFloat(pricePerKwhWithVAT.toFixed(5)) // 5 decimal places for kWh pricing with VAT
          });
        }

        return values;
      } else {
        console.log("No price data found in API response");
        return [];
      }
    } catch (error) {
      console.error("Error fetching NordPool data:", error.message);
      throw error;
    }
  }
}

// Usage examples:
async function examples() {
  const prices = new Prices();

  try {
    // First, get and log all prices for testing/debugging
    console.log("=== ALL TODAY'S HOURLY PRICES (for debugging) ===");
    const allPrices = await prices.hourly();
    if (allPrices && allPrices.length > 0) {
      allPrices.forEach(price => {
        const priceTimeVilnius = convertUTCToVilnius(price.date);
        console.log(`${priceTimeVilnius.toLocaleString('lt-LT')} - ${formatPriceDisplay(price.value)}`);
      });
    } else {
      console.log("No hourly prices available for today");
    }
    console.log("=== END DEBUG PRICES ===\n");

    // Get current hour price
    console.log("=== Current Hour Price ===");
    const currentPrice = await prices.at();
    const currentTimeVilnius = convertUTCToVilnius(currentPrice.date);
    console.log(`Current price: ${formatPriceDisplay(currentPrice.value)} at ${currentTimeVilnius.toLocaleString('lt-LT')}`);

    // Get all prices for today (summary)
    console.log("=== Today's Price Summary ===");
    const todayPrices = await prices.hourly();
    console.log(`Found ${todayPrices?.length || 0} hourly prices (should be 24 for a complete day)`);
    if (todayPrices?.length > 0) {
      console.log(`Range: ${formatPriceDisplay(Math.min(...todayPrices.map(p => p.value)))} - ${formatPriceDisplay(Math.max(...todayPrices.map(p => p.value)))}`);

      // Verify we have all 24 hours
      const hours = todayPrices.map(p => convertUTCToVilnius(p.date).getHours()).sort((a, b) => a - b);
      const expectedHours = Array.from({length: 24}, (_, i) => i);
      const missingHours = expectedHours.filter(h => !hours.includes(h));

      if (missingHours.length > 0) {
        console.log(`⚠️ Missing hours: ${missingHours.join(', ')}`);
      } else {
        console.log(`✅ Complete 24-hour dataset (00:00 - 23:00)`);
      }
    }

    // Get tomorrow's prices (if available)
    console.log("=== Tomorrow's Hourly Prices ===");
    const tomorrow = addTime(getVilniusTime(), 1, 'day');
    const tomorrowPrices = await prices.hourly({ date: tomorrow });
    console.log(`Found ${tomorrowPrices?.length || 0} hourly prices for tomorrow`);

    // Find cheapest 3 consecutive hours today
    console.log("=== Cheapest 3 Consecutive Hours Today ===");
    const cheapest3Hours = await prices.findCheapestConsecutiveHours(3);
    console.log(`Average price for 3 hours: ${formatPriceDisplay(cheapest3Hours.averagePrice)}`);
    console.log(`Total cost: €${cheapest3Hours.totalCost.toFixed(5)} for 3 kWh`);
    console.log(`Time range: ${cheapest3Hours.startTime.getHours()}:00 - ${cheapest3Hours.endTime.getHours() + 1}:00`);
    console.log(`Start: ${cheapest3Hours.startTime.toLocaleString('lt-LT')}`);
    console.log(`End: ${cheapest3Hours.endTime.toLocaleString('lt-LT')}`);

    // Get daily maximum price
    console.log("=== Today's Maximum Price ===");
    const maxPrice = await prices.dailyMax();
    const maxTimeVilnius = convertUTCToVilnius(maxPrice.date);
    console.log(`Max: ${formatPriceDisplay(maxPrice.value)} at ${maxTimeVilnius.getHours()}:00`);

    // Get daily minimum price
    console.log("=== Today's Minimum Price ===");
    const minPrice = await prices.dailyMin();
    const minTimeVilnius = convertUTCToVilnius(minPrice.date);
    console.log(`Min: ${formatPriceDisplay(minPrice.value)} at ${minTimeVilnius.getHours()}:00`);

    // Get daily average price
    console.log("=== Today's Average Price ===");
    const avgPrice = await prices.dailyAverage();
    console.log(`Average: ${formatPriceDisplay(avgPrice.average)} (${avgPrice.count} hours)`);

    // Check for free electricity hours
    console.log("=== Free Electricity Hours ===");
    const freeHours = await prices.dailyFreeHours();
    if (freeHours.length > 0) {
      console.log(`🎉 Found ${freeHours.length} hours with FREE electricity:`);
      freeHours.forEach(hour => {
        const hourTime = convertUTCToVilnius(hour.date);
        console.log(`  ${hourTime.getHours()}:00 - ${hourTime.getHours() + 1}:00 (${hourTime.toLocaleString('lt-LT')})`);
      });
    } else {
      console.log("No free electricity hours today");
    }

    // Smart price comparison for current hour
    console.log("=== Current Hour Price Analysis ===");
    const priceAnalysis = await prices.priceComparison();
    console.log(`Current: ${formatPriceDisplay(priceAnalysis.currentHour.value)}`);
    console.log(`${priceAnalysis.comparison.percentOfMax}% of daily max, ${priceAnalysis.comparison.percentOfAverage}% of daily average`);
    console.log(`Category: ${priceAnalysis.recommendations.category}`);

    // Simple check: Is it a good time to run appliances?
    console.log("=== Should I Run Appliances Now? ===");
    const shouldRun = await prices.isGoodTimeToRun();
    console.log(`Good time to run appliances: ${shouldRun.isGoodTime}`);
    console.log(`Recommendation: ${shouldRun.recommendation.message}`);
    console.log(`Alert family: ${shouldRun.recommendation.alertFamily}`);

    // Custom thresholds example
    console.log("=== Custom Thresholds (More Strict) ===");
    const strictCheck = await prices.isGoodTimeToRun({}, {
      maxPercentThreshold: 30,  // Only run if ≤ 30% of daily max
      avgPercentThreshold: 90   // Only run if ≤ 90% of daily average
    });
    console.log(`Strict mode - Good time: ${strictCheck.isGoodTime}`);

  } catch (error) {
    console.error("Example failed:", error.message);
  }
}

// Export for HomeyScript usage
// await examples();

const vilniusTime = new Date(new Date("2025-06-06T00:00:00").toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
const start = new Date(new Date("2025-06-06T00:00:00").toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
const end = new Date(new Date("2025-06-06T23:59:59").toLocaleString('lt-LT', { timeZone: 'Europe/Vilnius' }));
console.log(vilniusTime);
console.log(vilniusTime.toISOString());
console.log(`dashboard.elering.ee/api/nps/price?start=${start.toISOString()}&end=${end.toISOString()}`);
console.log(new Date(1749157200));


// host: 'dashboard.elering.ee',
//   path: `/api/nps/price?start=${start.toISOString()}&end=${end.toISOString()}`
