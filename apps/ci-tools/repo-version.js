const gitUtils = require('./git-utils');
const {
  getVersion,
  updateVersion,
  parseVersionFromBranch,
  isValidVersionBranch,
  createReleaseCandidate,
  incrementReleaseCandidate,
  isReleaseCandidate,
  getBaseVersionFromRc,
  ensureTagDoesNotExistRemotely,
} = require('./version-utils');
const { getRemoteTag, createAnnotatedTag, pushTag } = require('./git-utils');

/**
 * Validates and processes a release branch name in CircleCI
 * - Checks if branch name follows the convention (release|patch|hotfix)/vX.Y.Z
 * - Throws error if invalid branch format
 * - Checks if tag exists and fails if it does
 * - Sets version based on branch name or increments RC version
 *
 * @param {string} branchName - Branch name to process
 * @returns {Object} Result with version and branch info
 * @throws {Error} If any validation fails or version file can't be updated
 */
function processReleaseBranch(branchName) {
  console.log(`Processing branch: ${branchName}`);

  // Validate branch name
  if (!isValidVersionBranch(branchName)) {
    throw new Error(`Invalid branch name format. Expected: (release|patch|hotfix)/vX.Y.Z, got: ${branchName}`);
  }

  // Parse version from branch name
  const branchInfo = parseVersionFromBranch(branchName);
  console.log(`Parsed version info:`, JSON.stringify(branchInfo, null, 2));

  // Ensure the tag does not already exist remotely
  ensureTagDoesNotExistRemotely(`v${branchInfo.version}`);

  console.log("Setting RC version...");
  // Get current version from central version file
  const currentVersion = getVersion();
  console.log(`Current version: ${currentVersion.version}`);

  // Set new version based on branch info
  let newVersion;

  if (branchInfo.version === getBaseVersionFromRc(currentVersion.version)) {
    // Same base version, increment RC
    if (isReleaseCandidate(currentVersion.version)) {
      newVersion = incrementReleaseCandidate(currentVersion.version);
    } else {
      newVersion = createReleaseCandidate(currentVersion.version);
    }
  } else {
    // Different version, set to branch version with RC.0
    newVersion = createReleaseCandidate(branchInfo.version);
  }

  // Update central version file
  updateVersion(newVersion);

  return {
    version: newVersion,
    branchInfo,
  };
}

/**
 * Sets the stable version by removing the RC suffix from the current version
 * This should be called when merging to main branch
 *
 * @returns {string} The stable version string
 * @throws {Error} If version file can't be read or updated
 */
function setStableVersion() {
  console.log('Setting stable version...');
  // Get current version
  const currentVersion = getVersion();
  console.log(`Current version: ${currentVersion.version}`);

  // Check if it's already a stable version
  if (!isReleaseCandidate(currentVersion.version)) {
    console.warn('Warning: Version is already stable, no changes needed!');
    return currentVersion.version;
  }

  // Remove RC suffix
  const stableVersion = getBaseVersionFromRc(currentVersion.version);

  // Ensure the tag does not already exist remotely
  ensureTagDoesNotExistRemotely(`v${stableVersion}`);

  // Update version file
  updateVersion(stableVersion);

  return stableVersion;
}

/**
 * Creates an annotated git tag based on the current version
 *
 * @returns {string} The tag name that was created
 * @throws {Error} If tag creation fails
 */
function createVersionTag() {
  const { version } = getVersion();
  const tagName = `v${version}`;
  const tagMessage = `Release ${version}`;

  // Ensure the tag does not already exist remotely
  ensureTagDoesNotExistRemotely(tagName);

  // Create an annotated tag
  createAnnotatedTag(tagName, tagMessage);

  return tagName;
}

module.exports = {
  processReleaseBranch,
  setStableVersion,
  createVersionTag,
};
