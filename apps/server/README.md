# Turing College Server

<!-- Development Env -->

[![CircleCI](https://circleci.com/gh/TuringCollege/server/tree/develop.svg?style=shield&circle-token=****************************************)](https://circleci.com/gh/TuringCollege/server/tree/develop)

REST API built for Turing College Platform using Node.js, Typescript, routing-controllers, Express.js & TypeORM.

## Prerequisites

-   Node.js (>16.x.x)
-   Postgres server running locally
    - You can use this [app](https://postgresapp.com)
    - Or create infra with docker compose `dev-infra $ docker-compose up -d`

## Installation

Use the package manager [npm](https://www.npmjs.com/) to install required packages:

```shell
npm install 
```

## ENV setup

In root directory you can see `.env.example` file which contains minimal configuration 
to start backend. You have two options
- Copy this file and adjust to your environment
```shell
cp .env.example .env
```
- Create symlink to receive updates from GIT
```shell
ln -s .env.example .env
```

After that, generate github private key file, it can contain anything, but
just to be strict with content, you can create it like real with
```shell
ssh-keygen -t rsa -m PEM -f github-private-key.pem -N ""
```

Default `.env.example` fully compatible with docker-compose from `dev-infra`

## Database setup

In order to start with ready for development database, you need
1. Start your Postgres Database
2. Ask one of your colleges recent dump from dev instance
3. With provided `*.sql` file execute command 
```shell
psql --host=localhost --port=5432 --username=root --dbname=turing_college < dump.sql
```
4. Enter `root` password (if you're using docker compose, otherwise the password that you set)

After that you can anonymize dev database and replace credentials/external identifiers with
following query

```postgresql
UPDATE platform_user
SET email = CONCAT('u', id, '@turing.college'), password = '$2b$10$IknHWyQncIHMrJnKyoRxRe2RjxfvPz7sKQAIINJY9ULO522Iir2wS';

DELETE FROM public.discord_profile WHERE discord_nonce IS NOT NULL;
UPDATE public.discord_profile SET discord_user_id = '<YOUR_DISCORD_USER_ID>'
```
Discord user ID can be found [like this](https://www.androidpolice.com/how-to-find-discord-id/).

With this query executed you can login under any user by using
```
Login: 'u<USER_ID_FROM_DB>@turing.college'
Password: 'Test12345678'
```

## Running server in prod-like mode

```bash
npm run build-ts
npm run start:prod
```

## Running server in dev mode

```bash
npm run start:local
```

## Running server in watch mode with reload

```bash
npm run watch
```

## Running tests

Project support both **unit** and **e2e** tests. E2e tests require
running database and server, so they are slower than unit tests, but they allow to
test whole system without isolation.

### ENV setup
Before running tests make sure that `.env.tests` file is present in root directory. You
can use example file `.env.tests.example` which should work out of the box with
no configuration if you're using `dev-infra` docker-compose. 

### E2E tests preparations
If you're want to run e2e tests, you need to have running instance of
server. You run server dedicated for e2e tests with:

```shell
npm run start:local:e2e
```

In another session:

```shell
npm run test:unit
# or
npm run test:e2e
```

You can also run tests from the compiled JS files:

```shell
npm run build
npm run test:unit:ci
# or
npm run test:e2e:ci
```

Using JS files is blazingly fast, but requires compilation 
step to be done before running.


# Writing migrations

In order to change database data or alter columns / tables 
we use migrations. They executed during each application
startup.

To generate migration execute
```shell
npm run db:migration:generate src/migrations/desiredMigrationNameInCamelCase
```
Then in `src/migrations/<NUM_IDENTIFIER>_desiredMigrationNameInCamelCase.ts`
you will find your newly generated migration.

Clear everything that is not intended to be migrated and adjust migration code.

# Elastic Beanstalk Setup

Some notes:

-   Stockholm region is used.

Steps:

1. Go to AWS console, Elastic Beanstalk section.
2. Press _Create a new evironment_.
3. Select _Web server environment_.
4. In the following screen:
    - enter _Application name_: **turing-server**;
    - enter _Environment name_: **turing-college-<env>**, where **env** is **dev**, **staging** or **prod**;
    - enter some meaningful subdomain in the _Domain_ field or leave it blank;
    - select **Node.js** _Platform_, **Node.js 12** _Branch_, **Recommended** _Platform version_;
    - upload zipped source code (on Mac, use **zip -r -X archive_name.zip folder_to_compress** to compress folder without Mac OS related hidden files);
    - Select more options:
        - Select custom configuration preset;
        - Select the maximum of 3 or 4 instances, required instance type (e. g. **t2.micro**);
        - Add Postgres Database (**engine version 12.3**, instance of required size, enter password);
        - Enable rolling updates;
        - Turn on managed updates;
        - Turn on notification for the desired email;
        - Go to load balancers section and:
            - Add listener for HTTPS (443);
            - Configure health check endpoint by editing default process. Enter desired endpoint (e.g. **/health**);
5. Press create app.
6. To assign domain for the environment:

- A certificate should be issued;
- A (alias) record to the EB environment should be created in Route 53 Hosted Zone.

# Code generation

Server allows to generate boilerplate code for domain / persistence entities / repositories / etc.
Use following commands:

| Command (example)                   | Description                                                                                                                           | Scope                  |
|-------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------|------------------------|
| `npm run gen:module user-role`      | Generates new module `UserRoleModule`                                                                                                 | Anywhere               |
| `npm run gen:domain user-role`      | Generates new domain entity with name `UserRole`                                                                                      | Module in current path |
| `npm run gen:entity user-role`      | Generates new persistence entity with name `UserRolePersistenceEntity`                                                                | Module in current path |
| `npm run gen:repo user-role`        | Generates new repository `UserRoleRepository`                                                                                         | Module in current path |
| `npm run gen:controller management` | Generates new controller `UserRoleManagementController`, `index.ts` for module controllers and imports `index.ts` to `Application.ts` | Module in current path |
