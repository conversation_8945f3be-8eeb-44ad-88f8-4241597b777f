{"name": "@turing-college/server", "version": "1.0.0", "description": "", "main": "app.js", "scripts": {"======= Install =======": "", "install-browser": "npx puppeteer browsers install chrome", "======= Start =======": "", "start": "npm run start:prod", "start:local": "ts-node ./src/server.ts", "start:local:e2e": "ENV_FILE_PATH=.env.tests ts-node ./src/server.ts", "start:prod": "node -r source-map-support/register dist/server/src/server.js", "start:prod:debug": "node --inspect -r source-map-support/register dist/server/src/server.js", "======= Build =======": "", "build": "npm run cleanup && npm run build-ts && npm run copy-static-assets", "build-ts": "tsc", "cleanup": "<PERSON><PERSON><PERSON> dist", "copy-static-assets": "npm run copy-email-templates && npm run copy-document-templates && npm run copy-version-json", "copy-version-json": "copyfiles -f \"../../version.json\" \"./dist\"", "copy-email-templates": "copyfiles -E --up 1 \"./src/mailer/templates/**/*\" \"./dist/server/src\"", "copy-document-templates": "copyfiles -E --up 1 \"./src/document/templates/**/*\" \"./dist/server/src\"", "======= Development =======": "", "debug": "nodemon --config nodemon.debug.json", "watch:build": "tsc -w --preserveWatchOutput", "watch:run": "nodemon", "watch:pug": "nodemon --config nodemon.pug.json", "watch": "concurrently -n build,run,assets \"npm run watch:build\" \"npm run watch:run\" \"npm run watch:pug\"", "watch:e2e": "ENV_FILE_PATH=.env.tests npm run watch", "watch:test": "npm run test -- --watchAll", "======= Lint =======": "", "lint": "eslint \"**/*.ts\" --max-warnings=0", "lint:fix": " eslint \"**/*.ts\" --fix", "======= Tests =======": "", "test": "jest", "test:unit": "jest --selectProjects unit --maxWorkers=3", "test:unit:ci": "npm run test:unit -- -c configs/jest/unit/jest.config.unit.ci.js", "test:e2e": "jest --selectProjects e2e --runInBand", "test:e2e:ci": "npm run test:e2e -- -c configs/jest/e2e/jest.config.e2e.ci.js", "======= Type ORM =======": "", "typeorm": "typeorm-ts-node-commonjs -d src/db/createLocalDataSource.ts", "db:schema-hard-sync": "TYPEORM_DROP_SCHEMA=true npm run typeorm schema:sync", "db:migration:fake": "npm run typeorm migration:run -- --fake", "db:migration:show": "npm run typeorm migration:show", "db:migration:generate": "npm run typeorm migration:generate", "db:migration:run": "npm run typeorm migration:run", "db:migration:revert": "npm run typeorm migration:revert", "======= PUG (development) =======": "", "pug:build": "pug src/mailer/templates src/document/templates", "pug:watch": "pug -w src/mailer/templates src/document/templates/", "======= Code generation =======": "", "gen": "hygen", "gen:module": "hygen module new", "gen:domain": "hygen domain-entity new", "gen:entity": "hygen persistence-entity new", "gen:repo": "hygen persistence-entity-repository new", "gen:controller": "hygen controller new", "======= Misc =======": "", "api-schema:diff": "bump diff", "api-schema:generate": "ts-node src/scripts/api-schema/generate-api-schema.ts", "api-schema:git-diff": "sh src/scripts/api-schema/git-diff.sh"}, "author": "Turing College", "license": "ISC", "engines": {"node": "^20.0.0", "npm": "^10.0.0"}, "dependencies": {"@aws-sdk/client-sns": "^3.632.0", "@aws-sdk/client-ssm": "^3.637.0", "@faker-js/faker": "^9.4.0", "@growthbook/growthbook": "^1.1.0", "@keyv/postgres": "^2.1.1", "@octokit/auth-app": "^6.1.3", "@octokit/rest": "^18.12.0", "@segment/analytics-node": "^2.1.2", "@sentry/node": "^8.51.0", "archiver": "^7.0.1", "aws-sdk": "^2.1692.0", "axios": "^1.8.4", "axios-logger": "^2.8.1", "bcrypt": "^5.1.1", "body-parser": "^1.20.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-validator-jsonschema": "^5.0.2", "colors": "^1.4.0", "convict": "^6.2.4", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "cron": "^3.1.6", "cronstrue": "^2.59.0", "date-holidays": "^3.23.21", "decimal.js": "^10.4.3", "dotenv": "^16.4.5", "email-templates": "^12.0.1", "emittery": "^0.13.1", "eventsource": "^2.0.2", "express": "^4.21.2", "express-basic-auth": "^1.2.1", "express-rate-limit": "^7.5.0", "fp-ts": "^2.16.9", "googleapis": "^144.0.0", "ical-generator": "^7.2.0", "jsonwebtoken": "^9.0.2", "keyv": "^5.2.3", "latinize": "^1.0.0", "limiter": "^2.1.0", "mixpanel": "^0.18.0", "moment": "^2.30.1", "moment-timezone": "^0.5.46", "ms": "^2.1.3", "nodemailer": "^6.10.0", "papaparse": "^5.4.1", "passport": "^0.7.0", "passport-github2": "^0.1.12", "passport-google-oauth": "^2.0.0", "pg": "^8.12.0", "pg-boss": "^9.0.3", "postgres-range": "^1.1.4", "preview-email": "^3.1.0", "pug": "^3.0.3", "puppeteer": "^23.2.0", "reflect-metadata": "^0.2.2", "routing-controllers": "^0.11.2", "routing-controllers-openapi": "^5.0.0", "rrule": "^2.8.1", "semver": "^7.6.3", "source-map-support": "^0.5.21", "sqs-consumer": "^10.3.0", "sqs-producer": "^6.0.1", "stdnum": "^1.11.0", "swagger-themes": "^1.4.3", "swagger-ui-express": "^5.0.1", "typedi": "^0.10.0", "typeorm": "^0.3.20", "typeorm-naming-strategies": "^4.1.0", "unzip-stream": "^0.3.4", "uuid": "^10.0.0", "validator": "^13.12.0", "winston": "^3.15.0"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@eslint/js": "^9.14.0", "@types/archiver": "^6.0.2", "@types/bcrypt": "^5.0.2", "@types/convict": "^6.1.6", "@types/cookie-parser": "^1.4.7", "@types/cron": "^2.0.0", "@types/email-templates": "^10.0.4", "@types/eslint__js": "^8.42.3", "@types/eventsource": "^1.1.15", "@types/express": "5.0.0", "@types/jest": "^29.5.13", "@types/jest-expect-message": "^1.1.0", "@types/jsonwebtoken": "^9.0.7", "@types/latinize": "^0.2.16", "@types/luxon": "^3.4.2", "@types/ms": "^0.7.34", "@types/node": "^22.5.4", "@types/nodemailer": "^6.4.17", "@types/papaparse": "^5.3.15", "@types/passport": "^1.0.16", "@types/passport-github2": "^1.2.9", "@types/passport-google-oauth": "^1.0.45", "@types/pug": "^2.0.10", "@types/segment-analytics": "^0.0.38", "@types/semver": "^7.5.8", "@types/shelljs": "^0.8.15", "@types/source-map-support": "^0.5.10", "@types/supertest": "^6.0.2", "@types/swagger-ui-express": "^4.1.7", "@types/unzip-stream": "^0.3.4", "@types/uuid": "^10.0.0", "@types/validator": "^13.12.2", "@typescript-eslint/eslint-plugin": "^8.18.1", "@typescript-eslint/parser": "^8.18.1", "axios-mock-adapter": "^2.0.0", "bump-cli": "^2.9.5", "change-case": "^3.1.0", "concurrently": "^9.1.0", "copyfiles": "^2.4.1", "eslint": "^9.20.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-unused-imports": "^4.1.3", "globals": "^15.11.0", "hygen": "^6.2.11", "jest": "^29.7.0", "jest-config": "^29.7.0", "jest-expect-message": "^1.1.3", "jest-extended": "^4.0.2", "jest-junit": "^16.0.0", "jest-mock-server": "^0.1.0", "node-ical": "^0.20.1", "nodemon": "^3.1.7", "prettier": "^3.3.3", "pug-cli": "^1.0.0-alpha6", "rimraf": "^6.0.1", "shelljs": "^0.8.5", "supertest": "^7.0.0", "ts-jest": "^29.2.4", "ts-mockito": "^2.6.1", "ts-node": "^10.9.2", "typescript": "^5.7.2", "typescript-eslint": "^8.18.1", "wait-for-expect": "^3.0.2", "weak-napi": "^2.0.2"}, "jest-junit": {"outputDirectory": "reports", "outputName": "junit.xml"}}