import typescriptEslint from "@typescript-eslint/eslint-plugin";
import unusedImports from "eslint-plugin-unused-imports";
import globals from "globals";
import tsParser from "@typescript-eslint/parser";
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});

export default [{
    ignores: [
        "configs/**/*.js",
        "**/jest.config.js",
        "dist/*",
        "coverage/*",
        "**/*.d.ts",
        "src/public/",
        "src/types/",
        "**/reports",
        "eslint.config.mjs",
    ],
}, ...compat.extends("plugin:@typescript-eslint/recommended", "plugin:prettier/recommended"), {
    plugins: {
        "@typescript-eslint": typescriptEslint,
        "unused-imports": unusedImports,
    },

    languageOptions: {
        globals: {
            ...globals.node,
        },

        parser: tsParser,
        ecmaVersion: 2018,
        sourceType: "module",

        parserOptions: {
            project: "tsconfig.json",
        },
    },

    rules: {
        quotes: ["error", "single", {
            avoidEscape: true,
        }],

        semi: ["error", "always"],

        "comma-dangle": ["error", {
            arrays: "always-multiline",
            objects: "always-multiline",
            imports: "always-multiline",
            exports: "always-multiline",
            functions: "always-multiline",
        }],

        "@typescript-eslint/no-unused-vars": "off",
        "unused-imports/no-unused-imports": "error",
        "unused-imports/no-unused-vars": ["warn"],
        "@typescript-eslint/no-explicit-any": "off",
        "@typescript-eslint/explicit-function-return-type": "error",

        "@typescript-eslint/naming-convention": ["error", {
            selector: "typeLike",
            format: ["PascalCase"],
        }, {
            selector: "classProperty",
            modifiers: ["static"],
            format: ["UPPER_CASE", "camelCase"],
        }, {
            selector: "classProperty",
            modifiers: ["static", "public"],
            format: ["UPPER_CASE"],
        }, {
            selector: "enumMember",
            format: ["UPPER_CASE"],
        }, {
            selector: "variable",
            modifiers: ["const"],
            format: ["camelCase", "UPPER_CASE"],
        }, {
            selector: "variable",
            modifiers: ["const", "exported"],
            format: ["camelCase", "UPPER_CASE", "PascalCase"],

            filter: {
                regex: ".*Token$",
                match: true,
            },
        }, {
            selector: "objectLiteralProperty",
            format: null,
        }, {
            selector: "function",
            format: ["camelCase", "PascalCase"],
        }, {
            selector: "variable",
            modifiers: ["destructured"],
            format: null,
        }, {
            selector: "import",
            format: null,
        }, {
            selector: "default",
            format: ["camelCase"],
            leadingUnderscore: "allow",
            trailingUnderscore: "forbid",
        }],
    },
}, {
    files: ["src/migrations/*.ts", "**/*.external.ts"],

    rules: {
        "@typescript-eslint/naming-convention": ["error", {
            selector: "typeLike",
            format: ["PascalCase"],
        }, {
            selector: "classProperty",
            modifiers: ["static"],
            format: ["UPPER_CASE", "camelCase"],
        }, {
            selector: "classProperty",
            modifiers: ["static", "public"],
            format: ["UPPER_CASE"],
        }, {
            selector: "enumMember",
            format: ["UPPER_CASE"],
        }, {
            selector: "variable",
            modifiers: ["const"],
            format: ["camelCase", "UPPER_CASE"],
        }, {
            selector: "variable",
            modifiers: ["const", "exported"],
            format: ["camelCase", "UPPER_CASE", "PascalCase"],

            filter: {
                regex: ".*Token$",
                match: true,
            },
        }, {
            selector: "objectLiteralProperty",
            format: null,
        }, {
            selector: "function",
            format: ["camelCase", "PascalCase"],
        }, {
            selector: "variable",
            modifiers: ["destructured"],
            format: null,
        }, {
            selector: "import",
            format: null,
        }, {
            selector: "property",
            format: null,
        }],
    },
}];