const path = require('node:path');
const { kebabCase, pascalCase, snakeCase, camelCase } = require('change-case');
const { existsSync } = require('node:fs');

function root() {
    return __dirname;
}

function currentPath() {
    return process.env.INIT_CWD;
}

function moduleName(name) {
    return pascalCase(name) + 'Module';
}

function moduleIndex(name) {
    return path.join(process.env.INIT_CWD, kebabCase(name), 'infrastructure', 'di', moduleName(name) + '.ts');
}

function persistenceEntityName(name) {
    return pascalCase(name) + 'PersistenceEntity';
}

function persistenceEntityAndRepositoryRoot(name) {
    return path.join(process.env.INIT_CWD, 'infrastructure', 'db', kebabCase(name));
}

function persistenceEntityIndex(name) {
    return path.join(persistenceEntityAndRepositoryRoot(name), persistenceEntityName(name) + '.ts');
}

function persistenceEntityRepositoryName(name) {
    return pascalCase(name) + 'Repository';
}

function persistenceEntityRepositoryIndex(name) {
    return path.join(persistenceEntityAndRepositoryRoot(name), persistenceEntityRepositoryName(name) + '.ts');
}

function coreInfrastructureRoot() {
    return path.join(root(), 'src', 'core', 'infrastructure');
}

function coreInfrastructureDbRoot() {
    return path.join(coreInfrastructureRoot(), 'db');
}

function coreInfrastructureDbRootTypeormPersistenceEntityIndex() {
    return path.join(coreInfrastructureDbRoot(), 'TypeormPersistenceEntity.ts');
}

function coreInfrastructureDbTypeormRepositoryIndex() {
    return path.join(coreInfrastructureDbRoot(), 'TypeormRepository.ts');
}

function coreInfrastructureTransactionManagerIndex() {
    return path.join(coreInfrastructureRoot(), 'TransactionManager.ts');
}

function persistenceEntityTableName(name) {
    return snakeCase(name);
}

function coreDomainRoot() {
    return path.join(root(), 'src', 'core', 'domain');
}

function coreDomainIdIndex() {
    return path.join(coreDomainRoot(), 'value-objects', 'Id.ts');
}

function coreDomainEntityIndex() {
    return path.join(coreDomainRoot(), 'DomainEntity.ts');
}

function entityName(name) {
    return pascalCase(name);
}

function entityIndex(name) {
    return path.join(process.env.INIT_CWD, 'domain', entityName(name) + '.ts');
}

function diIndex() {
    return path.join(root(), 'src', 'di', 'ApplicationLifeCycleController.ts');
}

function importPath(fileToImport, fileToAddImportTo) {
    const relativePath = path.relative(path.dirname(fileToAddImportTo), fileToImport);

    const pathWithoutExtension = path.join(path.dirname(relativePath), path.parse(relativePath).name);

    // If path is just file name, add `./` to make it relative
    if (pathWithoutExtension === path.parse(relativePath).name) {
        return `./${pathWithoutExtension}`;
    }

    return pathWithoutExtension;
}

function utilsRoot() {
    return path.join(root(), 'src', 'utils');
}

function utilsTypesIndex() {
    return path.join(utilsRoot(), 'UtilityTypes.ts');
}

function utilsCreateMapperFromEntity() {
    return path.join(utilsRoot(), 'createMapperFromEntity.ts');
}

function getCurrentModuleName() {
    return path.basename(process.env.INIT_CWD);
}

function getCurrentModuleIndexPath() {
    return path.join(process.env.INIT_CWD, 'infrastructure', 'di', moduleName(getCurrentModuleName()) + '.ts');
}

function controllerName(name) {
    assertInModule();

    return pascalCase(getCurrentModuleName()) + pascalCase(name) + 'Controller';
}

function controllerRoot(name) {
    return path.join(process.env.INIT_CWD, 'controllers', 'http', kebabCase(name));
}

function controllerDtoRoot(name) {
    return path.join(controllerRoot(name), 'dto');
}

function controllerIndex(name) {
    return path.join(controllerRoot(name), controllerName(name) + '.ts');
}

function controllerRoute(name) {
    assertInModule();

    return path.join(kebabCase(getCurrentModuleName()), kebabCase(name));
}

function controllersIndexFileRoot() {
    return path.join(process.env.INIT_CWD, 'controllers', 'http');
}

function controllersIndexFile() {
    return path.join(controllersIndexFileRoot(), 'index.ts');
}

function controllersIndexFileExportVariable() {
    assertInModule();

    return `${camelCase(getCurrentModuleName())}Controllers`;
}

function appIndex() {
    return path.join(root(), 'src', 'app', 'Application.ts');
}

/*
 * Checks that currentPath is root of module
 * */
function assertInModule() {
    if (!existsSync(getCurrentModuleIndexPath())) {
        throw new Error(`Not in the module folder. Expected file ${getCurrentModuleIndexPath()} to exist.`);
    }
}

module.exports = {
    templates: `${__dirname}/_templates`,
    helpers: {
        currentPath,
        root,
        importPath,
        getCurrentModuleIndexPath,
        assertInModule,
        utils: {
            root: utilsRoot,
            types: {
                index: utilsTypesIndex,
            },
            createMapperFromEntity: {
                index: utilsCreateMapperFromEntity,
            },
        },
        app: {
            index: appIndex,
        },
        core: {
            infrastructure: {
                db: {
                    root: coreInfrastructureDbRoot,
                    typeormPersistenceEntity: coreInfrastructureDbRootTypeormPersistenceEntityIndex,
                    typeormRepository: coreInfrastructureDbTypeormRepositoryIndex,
                },
                transactionManager: coreInfrastructureTransactionManagerIndex,
            },
            domain: {
                valueObjects: {
                    id: {
                        index: coreDomainIdIndex,
                    },
                },
                entity: {
                    index: coreDomainEntityIndex,
                },
            },
        },
        module: {
            name: moduleName,
            index: moduleIndex,
        },
        persistenceEntity: {
            root: persistenceEntityAndRepositoryRoot,
            name: persistenceEntityName,
            index: persistenceEntityIndex,
            tableName: persistenceEntityTableName,
        },
        persistenceEntityRepository: {
            root: persistenceEntityAndRepositoryRoot,
            name: persistenceEntityRepositoryName,
            index: persistenceEntityRepositoryIndex,
        },
        controller: {
            name: controllerName,
            index: controllerIndex,
            route: controllerRoute,
            dto: {
                root: controllerDtoRoot,
            },
            indexFile: {
                index: controllersIndexFile,
                root: controllersIndexFileRoot,
                exportVariableName: controllersIndexFileExportVariable,
            },
        },
        entity: {
            name: entityName,
            index: entityIndex,
        },
        di: {
            index: diIndex,
        },
    },
};
