Resources:
  GoogleDriveCloneGithubRepoFilesCommandQueueDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { 'Ref': 'AWSEBEnvironmentName' }
            - "google-drive-clone-github-repo-command"
  GoogleDriveCloneGithubRepoFilesCommandQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { 'Ref': 'AWSEBEnvironmentName' }
            - "google-drive-clone-github-repo-command-dlq"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - GoogleDriveCloneGithubRepoFilesCommandQueueDlq
            - Arn

  GoogleDriveCloneGithubRepoFilesCommandQueueParameters:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { 'Ref': 'AWSEBEnvironmentName' }
            - "/queues/googleDrive/cloneGithubRepoFiles"
      Type: String
      Value: { 'Ref': 'GoogleDriveCloneGithubRepoFilesCommandQueue' }