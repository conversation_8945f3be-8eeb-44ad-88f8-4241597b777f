Resources:
  LearningAssignModuleDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'learning-assign-module-dlq' ] ] }
  LearningAssignModuleQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'learning-assign-module' ] ] }
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn: { 'Fn::GetAtt': [ 'LearningAssignModuleDlq', 'Arn' ] }
  LearningAssignModuleQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: { 'Fn::Join': [ '', [ '/', { 'Ref': 'AWSEBEnvironmentName' }, '/queues/learning/assignModule' ] ] }
      Type: String
      Value: { 'Ref': 'LearningAssignModuleQueue' }
      Description: Assign module queue URL

Outputs:
  LearningAssignModuleQueueUrl:
    Description: Assign module queue URL
    Value: { 'Ref': 'LearningAssignModuleQueue' }
  LearningAssignModuleDlqQueueUrl:
    Description: Assign module DLQ queue URL
    Value: { 'Ref': 'LearningAssignModuleDlq' }

option_settings:
  aws:elasticbeanstalk:application:environment:
    QUEUE_LEARNING_ASSIGN_MODULE: '`{"Ref" : "LearningAssignModuleQueue"}`'
