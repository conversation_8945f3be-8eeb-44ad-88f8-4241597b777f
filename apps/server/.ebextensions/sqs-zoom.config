Resources:
  # Schedule for platform meeting
  ZoomScheduleForPlatformMeetingDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "zoom-schedule-for-platform-meeting-dlq"
  ZoomScheduleForPlatformMeetingQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "zoom-schedule-for-platform-meeting"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - ZoomScheduleForPlatformMeetingDlq
            - Arn
  ZoomScheduleForPlatformMeetingQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/zoom/scheduleForPlatformMeeting"
      Type: String
      Value: { "Ref": "ZoomScheduleForPlatformMeetingQueue" }
      Description: Zoom schedule for platform meeting queue URL

  # Update for platform meeting
  ZoomUpdateForPlatformMeetingDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "zoom-update-for-platform-meeting-dlq"
  ZoomUpdateForPlatformMeetingQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "zoom-update-for-platform-meeting"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - ZoomUpdateForPlatformMeetingDlq
            - Arn
  ZoomUpdateForPlatformMeetingQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/zoom/updateForPlatformMeeting"
      Type: String
      Value: { "Ref": "ZoomUpdateForPlatformMeetingQueue" }
      Description: Zoom update for platform meeting queue URL

  # End meeting
  ZoomEndMeetingDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "zoom-end-meeting-dlq"
  ZoomEndMeetingQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "zoom-end-meeting"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - ZoomEndMeetingDlq
            - Arn
  ZoomEndMeetingQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/zoom/endMeeting"
      Type: String
      Value: { "Ref": "ZoomEndMeetingQueue" }
      Description: Zoom end meeting queue URL

  # Delete meeting
  ZoomDeleteMeetingDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "zoom-delete-meeting-dlq"
  ZoomDeleteMeetingQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "zoom-delete-meeting"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - ZoomDeleteMeetingDlq
            - Arn
  ZoomDeleteMeetingQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/zoom/deleteMeeting"
      Type: String
      Value: { "Ref": "ZoomDeleteMeetingQueue" }
      Description: Zoom delete meeting queue URL

Outputs:
  ZoomScheduleForPlatformMeetingQueueUrl:
    Description: Zoom schedule for meeting queue URL
    Value: { "Ref": "ZoomScheduleForPlatformMeetingQueue" }
  ZoomUpdateForPlatformMeetingQueueUrl:
    Description: Zoom update for meeting queue URL
    Value: { "Ref": "ZoomUpdateForPlatformMeetingQueue" }
  ZoomEndMeetingQueueUrl:
    Description: Zoom schedule for meeting queue URL
    Value: { "Ref": "ZoomEndMeetingQueue" }
  ZoomDeleteMeetingQueueUrl:
    Description: Zoom schedule for meeting queue URL
    Value: { "Ref": "ZoomDeleteMeetingQueue" }
option_settings:
  aws:elasticbeanstalk:application:environment:
    QUEUE_ZOOM_SCHEDULE_FOR_PLATFORM_MEETING: '`{"Ref" : "ZoomScheduleForPlatformMeetingQueue"}`'
    QUEUE_ZOOM_UPDATE_FOR_PLATFORM_MEETING: '`{"Ref" : "ZoomUpdateForPlatformMeetingQueue"}`'
    QUEUE_ZOOM_END_MEETING: '`{"Ref" : "ZoomEndMeetingQueue"}`'
    QUEUE_ZOOM_DELETE_MEETING: '`{"Ref" : "ZoomDeleteMeetingQueue"}`'
