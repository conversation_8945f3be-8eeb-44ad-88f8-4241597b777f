Resources:
  UztDocumentCommandsDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { 'Ref': 'AWSEBEnvironmentName' }
            - "uzt-document-commands-dlq.fifo"
      FifoQueue: true
  UztDocumentCommandsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { 'Ref': 'AWSEBEnvironmentName' }
            - "uzt-document-commands.fifo"
      DelaySeconds: 5
      FifoQueue: true
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - UztDocumentCommandsDlq
            - Arn

  UztDocumentCommandsQueueParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { 'Ref': 'AWSEBEnvironmentName' }
            - "/queues/uztDocument/commandsQueue"
      Type: String
      Value: { 'Ref': 'UztDocumentCommandsQueue' }
      Description: Uzt document commands queue URL


  UztDocumentHandleDocumentEventsQueueDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { 'Ref': 'AWSEBEnvironmentName' }
            - "uzt-documents-handle-document-event-dlq"

  UztDocumentHandleDocumentEventsQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { 'Ref': 'AWSEBEnvironmentName' }
            - "uzt-documents-handle-document-event"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - UztDocumentHandleDocumentEventsQueueDlq
            - Arn

  UztDocumentHandleDocumentEventsQueueParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { 'Ref': 'AWSEBEnvironmentName' }
            - "/queues/uztDocument/handleDocumentEvents"
      Type: String
      Value: { 'Ref': 'UztDocumentHandleDocumentEventsQueue' }
      Description: Uzt document handle document event queue URL

  UztDocumentHandleDocumentEventSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: { 'Ref': 'DocumentEventsTopic' }
      Endpoint: { 'Fn::GetAtt': [ 'UztDocumentHandleDocumentEventsQueue', 'Arn' ] }
      Protocol: 'sqs'
      RawMessageDelivery: true
      RedrivePolicy:
        deadLetterTargetArn: { 'Fn::GetAtt': [ 'UztDocumentHandleDocumentEventsQueueDlq', 'Arn' ] }


Outputs:
  UztDocumentCommandsQueueArn:
    Description: "UZT document commands queue ARN"
    Value: { "Ref": "UztDocumentCommandsQueue" }
  UztDocumentHandleDocumentEventsQueueArn:
    Description: "UZT document events ARN"
    Value: { "Ref": "UztDocumentHandleDocumentEventsQueue" }

option_settings:
  aws:elasticbeanstalk:application:environment:
    QUEUE_UZT_DOCUMENT_COMMANDS: '`{"Ref" : "UztDocumentCommandsQueue"}`'
    QUEUE_UZT_DOCUMENT_HANDLE_DOCUMENT_EVENTS: '`{"Ref" : "UztDocumentHandleDocumentEventsQueue"}`'
