Resources:
  AWSEBAutoScalingGroup:
    Metadata:
      AWS::CloudFormation::Authentication:
        S3Auth:
          type: "s3"
          buckets: ["turingcollege-config"]
          roleName: "aws-elasticbeanstalk-ec2-role"
files:
  "/etc/turing/gh_turing-platform-test.pem":
    mode: "000444"
    owner: root
    group: root
    authentication: "S3Auth"
    source: https://turingcollege-config.s3.eu-north-1.amazonaws.com/gh_turing-platform-test.pem
  "/etc/turing/gh_turing-platform-stage.pem":
    mode: "000444"
    owner: root
    group: root
    authentication: "S3Auth"
    source: https://turingcollege-config.s3.eu-north-1.amazonaws.com/gh_turing-platform-stage.pem
  "/etc/turing/gh_turing-platform-prod.pem":
    mode: "000444"
    owner: root
    group: root
    authentication: "S3Auth"
    source: https://turingcollege-config.s3.eu-north-1.amazonaws.com/gh_turing-platform-prod.pem
