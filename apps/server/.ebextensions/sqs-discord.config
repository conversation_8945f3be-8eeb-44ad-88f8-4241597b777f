Resources:

  # Send message
  DiscordSendMessageDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "discord-send-message-dlq"
  DiscordSendMessage:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "discord-send-message"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - DiscordSendMessageDlq
            - Arn
  DiscordSendMessageQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/discord/sendMessage"
      Type: String
      Value: { "Ref": "DiscordSendMessage" }
      Description: Discord send message queue URL

  # Send direct message
  DiscordSendDirectMessageDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "discord-send-direct-message-dlq"
  DiscordSendDirectMessage:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "discord-send-direct-message"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - DiscordSendDirectMessageDlq
            - Arn
  DiscordSendDirectMessageQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/discord/sendDirectMessage"
      Type: String
      Value: { "Ref": "DiscordSendDirectMessage" }
      Description: Discord send direct message queue URL

  # Update user
  DiscordUpdateUserNickDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "discord-update-user-nick-dlq"
  DiscordUpdateUserNick:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "discord-update-user-nick"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - DiscordUpdateUserNickDlq
            - Arn
  DiscordUpdateUserNickQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/discord/updateUserNick"
      Type: String
      Value: { "Ref": "DiscordUpdateUserNick" }
      Description: Discord update user queue URL

  # Add role to user
  DiscordAddRoleToUserDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "discord-add-role-to-user-dlq"
  DiscordAddRoleToUser:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "discord-add-role-to-user"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - DiscordAddRoleToUserDlq
            - Arn
  DiscordAddRoleToUserQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/discord/addRoleToUser"
      Type: String
      Value: { "Ref": "DiscordAddRoleToUser" }
      Description: Discord add role to user queue URL

  # Remove role from user
  DiscordRemoveRoleFromUserDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "discord-remove-role-from-user-dlq"
  DiscordRemoveRoleFromUser:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "discord-remove-role-from-user"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - DiscordRemoveRoleFromUserDlq
            - Arn
  DiscordRemoveRoleFromUserQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/discord/removeRoleFromUser"
      Type: String
      Value: { "Ref": "DiscordRemoveRoleFromUser" }
      Description: Discord remove role from user queue URL

Outputs:
  # Send message
  DiscordSendMessageQueueUrl:
    Description: Discord send message queue URL
    Value: { "Ref": "DiscordSendMessage" }
  DiscordSendMessageDlqQueueUrl:
    Description: Discord send message DLQ queue URL
    Value: { "Ref": "DiscordSendMessageDlq" }

  # Send direct message
  DiscordSendDirectMessageQueueUrl:
    Description: Discord send direct message queue URL
    Value: { "Ref": "DiscordSendDirectMessage" }
  DiscordSendDirectMessageDlqQueueUrl:
    Description: Discord send direct message DLQ queue URL
    Value: { "Ref": "DiscordSendDirectMessageDlq" }

  # Update user
  DiscordUpdateUserNickQueueUrl:
    Description: Discord update user queue URL
    Value: { "Ref": "DiscordUpdateUserNick" }
  DiscordUpdateUserNickDlqQueueUrl:
    Description: Discord update user DLQ queue URL
    Value: { "Ref": "DiscordUpdateUserNickDlq" }

  # Add role to user
  DiscordAddRoleToUserQueueUrl:
    Description: Discord add role to user queue URL
    Value: { "Ref": "DiscordAddRoleToUser" }
  DiscordAddRoleToUserDlqQueueUrl:
    Description: Discord add role to user DLQ queue URL
    Value: { "Ref": "DiscordAddRoleToUserDlq" }

  # Remove role from user
  DiscordRemoveRoleFromUserQueueUrl:
    Description: Discord remove role from user queue URL
    Value: { "Ref": "DiscordRemoveRoleFromUser" }
  DiscordRemoveRoleFromUserDlqQueueUrl:
    Description: Discord remove role from user DLQ queue URL
    Value: { "Ref": "DiscordRemoveRoleFromUserDlq" }
option_settings:
  aws:elasticbeanstalk:application:environment:
    QUEUE_DISCORD_SEND_MESSAGE: '`{"Ref" : "DiscordSendMessage"}`'
    QUEUE_DISCORD_SEND_DIRECT_MESSAGE: '`{"Ref" : "DiscordSendDirectMessage"}`'
    QUEUE_DISCORD_UPDATE_USER_NICK: '`{"Ref" : "DiscordUpdateUserNick"}`'
    QUEUE_DISCORD_ADD_ROLE_TO_USER: '`{"Ref" : "DiscordAddRoleToUser"}`'
    QUEUE_DISCORD_REMOVE_ROLE_FROM_USER: '`{"Ref" : "DiscordRemoveRoleFromUser"}`'
