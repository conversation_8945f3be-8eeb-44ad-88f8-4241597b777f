Resources:
  # Clone template
  GitHubCloneTemplateDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "github-clone-template-dlq"
  GitHubCloneTemplate:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "github-clone-template"
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - GitHubCloneTemplateDlq
            - Arn
  GitHubCloneTemplateQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/github/cloneTemplate"
      Type: String
      Value: { "Ref": "GitHubCloneTemplate" }
      Description: GitHub clone template queue URL

  # Add collaborator
  GitHubAddCollaboratorDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "github-add-collaborator-dlq"
  GitHubAddCollaborator:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "github-add-collaborator"
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - GitHubAddCollaboratorDlq
            - Arn
  GitHubAddCollaboratorQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/github/addCollaborator"
      Type: String
      Value: { "Ref": "GitHubAddCollaborator" }
      Description: GitHub add collaborator queue URL

  # Remove collaborator
  GitHubRemoveCollaboratorDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "github-remove-collaborator-dlq"
  GitHubRemoveCollaborator:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "github-remove-collaborator"
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - GitHubRemoveCollaboratorDlq
            - Arn
  GitHubRemoveCollaboratorQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/github/removeCollaborator"
      Type: String
      Value: { "Ref": "GitHubRemoveCollaborator" }
      Description: GitHub remove collaborator queue URL

Outputs:
  GitHubCloneTemplateQueueUrl:
    Description: GitHub clone template queue URL
    Value: { "Ref": "GitHubCloneTemplate" }
  GitHubCloneTemplateDlqQueueUrl:
    Description: GitHub clone template DLQ queue URL
    Value: { "Ref": "GitHubCloneTemplateDlq" }
  GitHubAddCollaboratorQueueUrl:
    Description: GitHub add collaborator queue URL
    Value: { "Ref": "GitHubAddCollaborator" }
  GitHubAddCollaboratorDlqQueueUrl:
    Description: GitHub add collaborator DLQ queue URL
    Value: { "Ref": "GitHubAddCollaboratorDlq" }
  GitHubRemoveCollaboratorQueueUrl:
    Description: GitHub remove collaborator queue URL
    Value: { "Ref": "GitHubRemoveCollaborator" }
  GitHubRemoveCollaboratorDlqQueueUrl:
    Description: GitHub remove collaborator DLQ queue URL
    Value: { "Ref": "GitHubRemoveCollaboratorDlq" }
option_settings:
  aws:elasticbeanstalk:application:environment:
    QUEUE_GITHUB_CLONE_TEMPLATE: '`{"Ref" : "GitHubCloneTemplate"}`'
    QUEUE_GITHUB_ADD_COLLABORATOR: '`{"Ref" : "GitHubAddCollaborator"}`'
    QUEUE_GITHUB_REMOVE_COLLABORATOR: '`{"Ref" : "GitHubRemoveCollaborator"}`'
