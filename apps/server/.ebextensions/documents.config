Resources:
  DocumentEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'document-events' ] ] }

  DocumentEventsTopicArnParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: { 'Fn::Join': [ '', [ '/', { 'Ref': 'AWSEBEnvironmentName' }, '/topics/documentEvents' ] ] }
      Type: String
      Value: { 'Ref': 'DocumentEventsTopic' }
      Description: Document events topic ARN

Outputs:
  DocumentEventsTopicTopicArn:
    Description: Document events topic
    Value: { "Ref": "DocumentEventsTopic" }

option_settings:
  aws:elasticbeanstalk:application:environment:
    TOPIC_DOCUMENT_EVENTS: '`{"Ref" : "DocumentEventsTopic"}`'
