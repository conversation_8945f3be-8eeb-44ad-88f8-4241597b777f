Resources:
  SNSPublishToSQSPolicy:
    Type: AWS::SQS::QueuePolicy
    Properties:
      Queues:
        # List all the queues that subscribe to the SNS topics
        - { 'Ref': 'EndorsementStartStagesForStudentQueue' }
        - { 'Ref': 'EndorsementStartStagesForStudentDlq' }
        - { 'Ref': 'IntercomConvertLeadQueue' }
        - { 'Ref': 'IntercomConvertLeadDlq' }
        - { 'Ref': 'RegisterStandupAttendanceQueue' }
        - { 'Ref': 'RegisterStandupAttendanceQueueDlq' }
        - { 'Ref': 'UztDocumentHandleDocumentEventsQueueDlq' }
        - { 'Ref': 'UztDocumentHandleDocumentEventsQueue' }
      PolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service: 'sns.amazonaws.com'
            Action: 'sqs:SendMessage'
            Resource:
              {
                'Fn::Sub':
                  [
                    'arn:aws:sqs:${AWS::Region}:${AWS::AccountId}:${env}-*',
                    { 'env': { 'Ref': 'AWSEBEnvironmentName' } },
                  ],
              }
            Condition:
              ArnEquals:
                aws:SourceArn:
                  {
                    'Fn::Sub':
                      [
                        'arn:aws:sns:${AWS::Region}:${AWS::AccountId}:${env}-*',
                        { 'env': { 'Ref': 'AWSEBEnvironmentName' } },
                      ],
                  }
