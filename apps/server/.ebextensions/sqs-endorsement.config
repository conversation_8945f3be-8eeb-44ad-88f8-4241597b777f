Resources:
  EndorsementStartStagesForStudentDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'endorsement-start-stages-for-student-dlq' ] ] }

  EndorsementStartStagesForStudentQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'endorsement-start-stages-for-student' ] ] }
      DelaySeconds: 5
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn: { 'Fn::GetAtt': [ 'EndorsementStartStagesForStudentDlq', 'Arn' ] }

  EndorsementStartStagesForStudentQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: { 'Fn::Join': [ '', [ '/', { 'Ref': 'AWSEBEnvironmentName' }, '/queues/endorsement/startStagesForStudent' ] ] }
      Type: String
      Value: { 'Ref': 'EndorsementStartStagesForStudentQueue' }
      Description: Start qualified endorsement stages for student queue URL

Outputs:
  EndorsementStartStagesForStudentQueueUrl:
    Description: Start qualified endorsement stages for student queue URL
    Value: { 'Ref': 'EndorsementStartStagesForStudentQueue' }
  EndorsementStartStagesForStudentDlqUrl:
    Description: Start qualified endorsement stages for student DLQ queue URL
    Value: { 'Ref': 'EndorsementStartStagesForStudentDlq' }

option_settings:
  aws:elasticbeanstalk:application:environment:
    QUEUE_ENDORSEMENT_START_STAGES_FOR_STUDENT: '`{"Ref" : "EndorsementStartStagesForStudentQueue"}`'
