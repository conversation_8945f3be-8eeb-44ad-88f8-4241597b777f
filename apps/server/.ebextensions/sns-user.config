Resources:
  # Events: signup_completed
  UserEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'user-events' ] ] }

  UserEventsTopicArnParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: { 'Fn::Join': [ '', [ '/', { 'Ref': 'AWSEBEnvironmentName' }, '/topics/userEvents' ] ] }
      Type: String
      Value: { 'Ref': 'UserEventsTopic' }
      Description: User events topic ARN

  IntercomConvertLeadOnUserSignupCompletedSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: { 'Ref': 'UserEventsTopic' }
      Endpoint: { 'Fn::GetAtt': [ 'IntercomConvertLeadQueue', 'Arn' ] }
      Protocol: 'sqs'
      RawMessageDelivery: true
      FilterPolicy:
        event: [ 'signup_completed' ]
      RedrivePolicy:
        deadLetterTargetArn: { 'Fn::GetAtt': [ 'IntercomConvertLeadDlq', 'Arn' ] }

option_settings:
  aws:elasticbeanstalk:application:environment:
    TOPIC_USER_EVENTS: '`{"Ref" : "UserEventsTopic"}`'
