Resources:
  MeetingEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'meeting-events.fifo' ] ] }
      FifoTopic: true

  MeetingEventsTopicArnParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: { 'Fn::Join': [ '', [ '/', { 'Ref': 'AWSEBEnvironmentName' }, '/topics/meetingEvents' ] ] }
      Type: String
      Value: { 'Ref': 'MeetingEventsTopic' }
      Description: Meeting events topic ARN

  RegisterStandupAttendanceMeetingEventsSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: { 'Ref': 'MeetingEventsTopic' }
      Endpoint: { 'Fn::GetAtt': [ 'RegisterStandupAttendanceQueue', 'Arn' ] }
      Protocol: 'sqs'
      RawMessageDelivery: true
      FilterPolicy:
        event: [ 'attendee_left', 'meeting_ended' ]
      RedrivePolicy:
        deadLetterTargetArn: { 'Fn::GetAtt': [ 'RegisterStandupAttendanceQueueDlq', 'Arn' ] }

option_settings:
  aws:elasticbeanstalk:application:environment:
    TOPIC_MEETING_EVENTS: '`{"Ref" : "MeetingEventsTopic"}`'
