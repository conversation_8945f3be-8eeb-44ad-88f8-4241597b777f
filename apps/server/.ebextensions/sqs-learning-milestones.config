Resources:
  RegisterStandupAttendanceQueueDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "register-standup-attendance-dlq.fifo"
      FifoQueue: true
  RegisterStandupAttendanceQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName:
        Fn::Join:
          - "-"
          - - { "Ref": "AWSEBEnvironmentName" }
            - "register-standup-attendance.fifo"
      DelaySeconds: 5
      FifoQueue: true
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn:
          Fn::GetAtt:
            - RegisterStandupAttendanceQueueDlq
            - Arn
  RegisterStandupAttendanceQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name:
        Fn::Join:
          - ""
          - - "/"
            - { "Ref": "AWSEBEnvironmentName" }
            - "/queues/learningMilestones/registerStandupAttendance"
      Type: String
      Value: { "Ref": "RegisterStandupAttendanceQueue" }
      Description: Register standup attendance queue URL

Outputs:
  RegisterStandupAttendanceQueueUrl:
    Description: Register standup attendance queue URL
    Value: { "Ref": "RegisterStandupAttendanceQueue" }
option_settings:
  aws:elasticbeanstalk:application:environment:
    QUEUE_REGISTER_STANDUP_ATTENDANCE: '`{"Ref" : "RegisterStandupAttendanceQueue"}`'
