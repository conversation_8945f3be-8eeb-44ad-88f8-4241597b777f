Resources:
  # Events: module_completed
  LearningEventsTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'learning-events' ] ] }

  LearningEventsTopicArnParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: { 'Fn::Join': [ '', [ '/', { 'Ref': 'AWSEBEnvironmentName' }, '/topics/learningEvents' ] ] }
      Type: String
      Value: { 'Ref': 'LearningEventsTopic' }
      Description: Learning events topic ARN

  EndorsementStartStagesForStudentOnLearningModuleCompletedSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      TopicArn: { 'Ref': 'LearningEventsTopic' }
      Endpoint: { 'Fn::GetAtt': [ 'EndorsementStartStagesForStudentQueue', 'Arn' ] }
      Protocol: 'sqs'
      RawMessageDelivery: true
      FilterPolicy:
        event: [ 'module_completed' ]
      RedrivePolicy:
        deadLetterTargetArn: { 'Fn::GetAtt': [ 'EndorsementStartStagesForStudentDlq', 'Arn' ] }

option_settings:
  aws:elasticbeanstalk:application:environment:
    TOPIC_LEARNING_EVENTS: '`{"Ref" : "LearningEventsTopic"}`'
