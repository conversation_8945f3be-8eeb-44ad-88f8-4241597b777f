Resources:
  IntercomConvertLeadDlq:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'intercom-convert-lead-dlq' ] ] }
  IntercomConvertLeadQueue:
    Type: AWS::SQS::Queue
    Properties:
      QueueName: { 'Fn::Join': [ '-', [ { 'Ref': 'AWSEBEnvironmentName' }, 'intercom-convert-lead' ] ] }
      DelaySeconds: 60
      RedrivePolicy:
        maxReceiveCount: 5
        deadLetterTargetArn: { 'Fn::GetAtt': [ 'IntercomConvertLeadDlq', 'Arn' ] }
  IntercomConvertLeadQueueUrlParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Name: { 'Fn::Join': [ '', [ '/', { 'Ref': 'AWSEBEnvironmentName' }, '/queues/intercom/convertLead' ] ] }
      Type: String
      Value: { 'Ref': 'IntercomConvertLeadQueue' }
      Description: Convert Intercom lead queue URL

Outputs:
  IntercomConvertLeadQueueUrl:
    Description: Convert Intercom lead queue URL
    Value: { 'Ref': 'IntercomConvertLeadQueue' }
  IntercomConvertLeadDlqQueueUrl:
    Description: Convert Intercom lead DLQ queue URL
    Value: { 'Ref': 'IntercomConvertLeadDlq' }
option_settings:
  aws:elasticbeanstalk:application:environment:
    QUEUE_INTERCOM_CONVERT_LEAD: '`{"Ref" : "IntercomConvertLeadQueue"}`'
