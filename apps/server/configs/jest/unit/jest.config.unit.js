module.exports = {
    ...require('./jest.config.unit.base'),
    transform: {
        '^.+\\.ts?$': [
            'ts-jest',
            {
                tsconfig: '<rootDir>/tsconfig.json',
            },
        ],
    },
    collectCoverageFrom: [
        '<rootDir>/src/**/*.ts',
        '!<rootDir>/src/migrations/**/*.ts',
        '!<rootDir>/src/**/*.test.ts',
    ],
    testMatch: ['<rootDir>/src/**/*.unit.test.ts'],
};
