module.exports = {
    ...require('./jest.config.e2e.base'),
    transform: {
        '^.+\\.ts?$': [
            'ts-jest',
            {
                tsconfig: 'tsconfig.json',
            },
        ],
    },
    testMatch: ['<rootDir>/src/**/*.e2e.test.ts'],
    globalSetup: '<rootDir>/src/test-toolkit/e2e/globalSetup.ts',
    setupFilesAfterEnv: ['jest-extended/all', 'jest-expect-message', '<rootDir>/src/test-toolkit/e2e/setup.ts'],
    /*
     * Allowing to run for long time to allow debugging of running server
     */
    testTimeout: 20_000_000,
    maxWorkers: 1,
};
