import { InvoicePrice } from '../../domain/InvoicePrice';
import { NonFunctionProperties, WithPartial } from '../../../utils/UtilityTypes';

type CreateParams = Omit<WithPartial<NonFunctionProperties<InvoiceProduct>, 'quantity'>, 'total'>;

export class InvoiceProduct {
    private static readonly DEFAULT_QUANTITY = 1;

    readonly title: string;

    readonly quantity: number;

    readonly price: InvoicePrice;

    readonly total: InvoicePrice;

    readonly unitId: string;

    private constructor(params: NonFunctionProperties<InvoiceProduct>) {
        Object.assign(this, params);
    }

    static create(params: CreateParams): InvoiceProduct {
        return new InvoiceProduct({
            ...params,
            quantity: params.quantity ?? InvoiceProduct.DEFAULT_QUANTITY,
            total: params.price.multiply(params.quantity ?? InvoiceProduct.DEFAULT_QUANTITY),
        });
    }
}
