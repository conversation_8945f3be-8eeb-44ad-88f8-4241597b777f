import { InvoiceClient } from './InvoiceClient';
import { InvoiceProduct } from './InvoiceProduct';
import { NonFunctionProperties } from '../../../utils/UtilityTypes';
import { InvoicePrice } from '../../domain/InvoicePrice';

type CreateParams = Omit<NonFunctionProperties<InvoiceMetadata>, 'total'>;

/**
 * A basic class to store the metadata used to generate external invoice.
 * It's serialized and deserialized to and from the database, so it should be kept simple and not contain any business logic.
 */
export class InvoiceMetadata {
    readonly date: Date;

    readonly note: string;

    readonly total: InvoicePrice;

    readonly issuedBy: string;

    readonly bankId: string;

    readonly seriesId: string;

    readonly client: InvoiceClient;

    readonly products: InvoiceProduct[];

    private constructor(params: NonFunctionProperties<InvoiceMetadata>) {
        Object.assign(this, params);
    }

    static create(params: CreateParams): InvoiceMetadata {
        return new InvoiceMetadata({
            ...params,
            total: params.products.reduce((acc, product) => acc.add(product.total), InvoicePrice.fromNumber(0)),
        });
    }
}
