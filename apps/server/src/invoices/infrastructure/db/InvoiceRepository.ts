import TypeormRepository from '../../../core/infrastructure/db/TypeormRepository';
import { Invoice } from '../../domain/Invoice';
import { InvoicePersistenceEntity } from './InvoicePersistenceEntity';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import { InvoiceMapper } from './InvoiceMapper';

export class InvoiceRepository extends TypeormRepository<Invoice, InvoicePersistenceEntity> {
    constructor(tm: TransactionManager) {
        super(tm, new InvoiceMapper(), InvoicePersistenceEntity);
    }
}
