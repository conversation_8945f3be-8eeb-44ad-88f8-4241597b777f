import Consumer from './Consumer';

export default class ConsumerRegistry {
    private readonly consumers: Consumer[] = [];

    constructor() {}

    registerConsumers(newConsumers: Consumer[]): void {
        this.consumers.push(...newConsumers);
    }

    startAll(): void {
        this.consumers.forEach((c) => c.start());
    }

    stopAll(): void {
        this.consumers.forEach((c) => c.stop());
    }
}
