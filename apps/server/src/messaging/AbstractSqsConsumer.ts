import { Consumer as SqsConsumer } from 'sqs-consumer';
import ErrorTracker from '../error-tracking/infrastructure/services/ErrorTracker';
import { Configuration } from '../config/Configuration';
import Logger from '../utils/logger/Logger';
import Consumer from './Consumer';
import { SQSClient } from '@aws-sdk/client-sqs';

/**
 * Simple SQS queue consumer.
 *
 * Required IAM permissions: sqs:ReceiveMessage, sqs:DeleteMessage.
 */
export default abstract class AbstractSqsConsumer<T> implements Consumer {
    private readonly isNoop: boolean;

    private readonly region: string;

    private readonly endpoint: string;

    private readonly waitTimeSeconds: number;

    private sqsConsumer: SqsConsumer;

    protected constructor(
        configuration: Configuration,
        private readonly queueUrl: string,
        private readonly errorTracker: ErrorTracker,
        protected readonly logger: Logger,
    ) {
        this.isNoop = !configuration.aws.sqs.consumer.isEnabled;
        this.region = configuration.aws.region;
        this.waitTimeSeconds = configuration.aws.sqs.consumer.defaultWaitTimeSeconds;
    }

    start(): void {
        if (this.isNoop) {
            this.logger.debug(`Consumer '${this.constructor.name}' is disabled by configuration`);
            return;
        }

        this.logger.debug(`Starting SQS consumer '${this.constructor.name}'`);

        if (!this.sqsConsumer) {
            this.sqsConsumer = SqsConsumer.create({
                region: this.region,
                waitTimeSeconds: this.waitTimeSeconds,
                queueUrl: this.queueUrl,
                sqs: new SQSClient({
                    endpoint: this.endpoint,
                    region: this.region,
                }),
                // @ts-expect-error TS(2345) FIXME: Argument of type 'string | undefined' is not assig... Remove this comment to see the full error message
                handleMessage: (m) => this.onMessage(JSON.parse(m.Body)),
            });
            this.sqsConsumer.on('error', (error) => this.onError(error));
            this.sqsConsumer.on('processing_error', (error) => this.onError(error));
            this.sqsConsumer.on('timeout_error', (error) => this.onError(error));
        }

        this.sqsConsumer.start();
    }

    stop(): void {
        this.logger.debug(`Stopping SQS consumer '${this.constructor.name}'`);
        if (this.sqsConsumer) {
            this.sqsConsumer.stop();
        }
    }

    abstract onMessage(data: T): Promise<void>;

    protected async onError(error: Error): Promise<void> {
        this.logger.warn('SQS consumer error', error);
        this.errorTracker.track(error);
    }
}
