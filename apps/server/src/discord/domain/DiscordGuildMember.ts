import DiscordUserId from './DiscordUserId';
import DiscordNick from './DiscordNick';
import DiscordRoleId from './DiscordRoleId';
import { NonFunctionProperties } from '../../utils/UtilityTypes';

export class DiscordGuildMember {
    readonly id: DiscordUserId;
    readonly nick: DiscordNick;
    readonly roles: DiscordRoleId[];

    constructor(params: NonFunctionProperties<DiscordGuildMember>) {
        this.id = params.id;
        this.nick = params.nick;
        this.roles = params.roles;
    }
}
