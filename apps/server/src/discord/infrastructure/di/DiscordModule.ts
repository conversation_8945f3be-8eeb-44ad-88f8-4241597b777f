import Container from 'typedi';
import BatchFinder from '../../../batches/services/BatchFinder';
import { Configuration } from '../../../config/Configuration';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import ChangeRolesOnStudentBatchChange from '../../controllers/application-event/ChangeRolesOnStudentBatchChange';
import DiscordHttpClient, { DiscordAxiosHttpClient } from '../../services/DiscordHttpClient';
import DiscordMessenger from '../../services/messaging/DiscordMessenger';
import HttpDiscordMessenger from '../../services/messaging/HttpDiscordMessenger';
import SQSDiscordMessenger from '../../services/messaging/SQSDiscordMessenger';
import { DefaultDiscordOnboarding } from '../../services/onboarding/DiscordOnboarding';
import DiscordOnboarding<PERSON>he<PERSON>, {
    DefaultDiscordOnboardingChecker,
} from '../../services/onboarding/DiscordOnboardingChecker';
import HttpDiscordUserManager from '../../services/users/HttpDiscordUserManager';
import SqsDiscordUserManager from '../../services/users/SqsDiscordUserManager';
import {
    DiscordHttpClientToken,
    DiscordOnboardingToken,
    HttpDiscordMessengerToken,
    HttpDiscordUserManagerToken,
    SqsDiscordMessengerToken,
    SqsDiscordUserManagerToken,
    StudentBatchChangedEventSubscriberToken,
} from './tokens';
import { ProfileSetupModule } from '../../../profile-setup-steps/infrastructure/di/ProfileSetupModule';
import { QueueModule } from '../../../queue/infrastructure/di/QueueModule';
import { UserLinkedDiscordAccountChangedEventWorker } from '../../controllers/events/UserLinkedDiscordAccountChangedEventWorker';
import { DiscordRemoveFromGuildCommandWorker } from '../../controllers/commands/DiscordRemoveFromGuildCommandWorker';
import { MentorAssignedCourseModule } from '../../../mentor-assigned-course/infrastructure/di/MentorAssignedCourseModule';
import { DiscordChangeRolesCommandWorker } from '../../controllers/commands/DiscordChangeRolesCommandWorker';
import { DiscordProfileModule } from '../../../discord-profile/infrastructure/di/DiscordProfileModule';
import { UsersAccountsModule } from '../../../users/accounts/infrastructure/di/UsersAccountsModule';
import { UsersStudentsSettingsModule } from '../../../users/students/settings/infrastructure/di/UsersStudentsSettingsModule';
import { ModuleWithControllers } from '../../../di/types/ApplicationModule';
import DiscordOnboardingController from '../../controllers/http/DiscordOnboardingController';
import { LoggingModule } from '../../../logging/infrastructure/di/LoggingModule';

export interface DiscordModuleParams {
    configuration: Configuration;
    loggingModule: LoggingModule;
    tm: TransactionManager;
    batchFinder: BatchFinder;
    usersAccountsModule: UsersAccountsModule;
    usersStudentsSettingsModule: UsersStudentsSettingsModule;
    profileSetupModule: ProfileSetupModule;
    queueModule: QueueModule;
    mentorAssignedCourseModule: MentorAssignedCourseModule;
    discordProfileModule: DiscordProfileModule;
}

export class DiscordModule implements ModuleWithControllers {
    constructor(
        readonly discordMessenger: DiscordMessenger,
        readonly discordOnboardingChecker: DiscordOnboardingChecker,
        readonly discordHttpClient: DiscordHttpClient,
    ) {}

    getName(): string {
        return DiscordModule.name;
    }

    getControllers(): any[] {
        return [DiscordOnboardingController];
    }

    static init({
        configuration,
        loggingModule,
        tm,
        batchFinder,
        usersAccountsModule,
        usersStudentsSettingsModule,
        profileSetupModule,
        queueModule,
        mentorAssignedCourseModule,
        discordProfileModule,
    }: DiscordModuleParams): DiscordModule {
        const httpClient = new DiscordAxiosHttpClient(configuration, loggingModule.loggingService);
        Container.set(DiscordHttpClientToken, httpClient);

        // Messaging
        const sqsMessenger = new SQSDiscordMessenger(configuration, loggingModule.loggingService);
        const httpMessenger = new HttpDiscordMessenger(discordProfileModule.discordProfileRepository, httpClient);
        Container.set(SqsDiscordMessengerToken, sqsMessenger);
        Container.set(HttpDiscordMessengerToken, httpMessenger);

        // Users
        const sqsUserManager = new SqsDiscordUserManager(configuration, loggingModule.loggingService);
        const httpUserManager = new HttpDiscordUserManager(
            configuration,
            usersAccountsModule.userAccountFinder,
            discordProfileModule.discordProfileRepository,
            httpClient,
        );
        Container.set(SqsDiscordUserManagerToken, sqsUserManager);
        Container.set(HttpDiscordUserManagerToken, httpUserManager);

        // Onboarding
        const onboarding = new DefaultDiscordOnboarding(
            configuration,
            loggingModule.loggingService,
            tm,
            usersAccountsModule.userAccountFinder,
            batchFinder,
            usersStudentsSettingsModule.studentSettingsFinder,
            discordProfileModule.discordProfileRepository,
            httpClient,
            profileSetupModule.userOnboardingService,
            queueModule.queueService,
            mentorAssignedCourseModule.mentorAssignedCourseService,
        );
        const onboardingChecker = new DefaultDiscordOnboardingChecker(discordProfileModule.discordProfileRepository);

        queueModule.queueWorkerRegistry.registerWorkers([
            new UserLinkedDiscordAccountChangedEventWorker(queueModule.queueService),
            new DiscordRemoveFromGuildCommandWorker(configuration, httpClient),
            new DiscordChangeRolesCommandWorker(
                httpClient,
                loggingModule.loggingService.createLogger(DiscordChangeRolesCommandWorker.name),
            ),
        ]);

        Container.set(DiscordOnboardingToken, onboarding);

        Container.set(
            StudentBatchChangedEventSubscriberToken,
            new ChangeRolesOnStudentBatchChange(
                configuration,
                batchFinder,
                httpClient,
                discordProfileModule.discordProfileRepository,
            ),
        );

        return new DiscordModule(sqsMessenger, onboardingChecker, httpClient);
    }
}
