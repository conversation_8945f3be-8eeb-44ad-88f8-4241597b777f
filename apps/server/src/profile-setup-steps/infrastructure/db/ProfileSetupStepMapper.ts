import TypeormPersistenceMapper from '../../../core/infrastructure/db/TypeormPersistenceMapper';
import ProfileSetupStepPersistenceEntity from './ProfileSetupStepPersistenceEntity';
import { ProfileSetupStep } from '../../domain/ProfileSetupStep';
import Id from '../../../core/domain/value-objects/Id';

export default class ProfileSetupStepMapper
    implements TypeormPersistenceMapper<ProfileSetupStep, ProfileSetupStepPersistenceEntity>
{
    toDomain(persistence: ProfileSetupStepPersistenceEntity): ProfileSetupStep {
        return new ProfileSetupStep({
            // @ts-expect-error TS(2345) FIXME: Argument of type 'number | undefined' is not assig... Remove this comment to see the full error message
            id: new Id(persistence.id),
            name: persistence.name,
            userId: new Id(persistence.userId),
            isCompleted: persistence.isCompleted,
            position: persistence.position,
            isRequired: persistence.isRequired,
        });
    }

    toPersistence(domain: ProfileSetupStep): ProfileSetupStepPersistenceEntity {
        return new ProfileSetupStepPersistenceEntity({
            id: domain.id?.value,
            name: domain.name,
            userId: domain.userId.value,
            isCompleted: domain.isCompleted,
            position: domain.position,
            isRequired: domain.isRequired,
        });
    }
}
