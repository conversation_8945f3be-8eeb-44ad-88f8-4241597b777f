import TypeormRepository from '../../../core/infrastructure/db/TypeormRepository';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import ProfileSetupStepPersistenceEntity from './ProfileSetupStepPersistenceEntity';
import ProfileSetupStepMapper from './ProfileSetupStepMapper';
import { ProfileSetupStep } from '../../domain/ProfileSetupStep';
import Id from '../../../core/domain/value-objects/Id';
import Transaction from '../../../core/infrastructure/Transaction';
import { ProfileSetupStepName } from '../../domain/ProfileSetupStepName';

export default class ProfileSetupStepRepository extends TypeormRepository<
    ProfileSetupStep,
    ProfileSetupStepPersistenceEntity
> {
    constructor(tm: TransactionManager) {
        super(tm, new ProfileSetupStepMapper(), ProfileSetupStepPersistenceEntity);
    }

    async findByUserId(userId: Id, tx?: Transaction): Promise<ProfileSetupStep[]> {
        return this.getAllWhere({ userId: userId.value }, tx);
    }

    async markStepAsCompleted(userId: Id, step: ProfileSetupStepName, tx?: Transaction): Promise<void> {
        await this.transactionManager.execute({
            action: async (tx) => {
                await tx.entityManager.upsert(
                    ProfileSetupStepPersistenceEntity,
                    {
                        name: step,
                        userId: userId.value,
                        isCompleted: true,
                    },
                    {
                        conflictPaths: ['name', 'userId'],
                        skipUpdateIfNoValuesChanged: true,
                        upsertType: 'on-conflict-do-update',
                    },
                );
            },
            transaction: tx,
            errorMapper: this.errorMapper,
        });
    }

    async markStepAsNotCompleted(userId: Id, step: ProfileSetupStepName, tx?: Transaction): Promise<void> {
        await this.transactionManager.execute({
            action: async (tx) => {
                await tx.entityManager.upsert(
                    ProfileSetupStepPersistenceEntity,
                    {
                        name: step,
                        userId: userId.value,
                        isCompleted: false,
                    },
                    {
                        conflictPaths: ['name', 'userId'],
                        skipUpdateIfNoValuesChanged: true,
                        upsertType: 'on-conflict-do-update',
                    },
                );
            },
            transaction: tx,
            errorMapper: this.errorMapper,
        });
    }

    async deleteByUserId(userId: Id, tx?: Transaction): Promise<void> {
        await this.deleteAllWhere({ userId: userId.value }, tx);
    }

    async findByUserIdAndName(
        userId: Id,
        name: ProfileSetupStepName,
        tx?: Transaction,
    ): Promise<ProfileSetupStep | undefined> {
        return this.getWhere({ name, userId: userId.value }, tx);
    }
}
