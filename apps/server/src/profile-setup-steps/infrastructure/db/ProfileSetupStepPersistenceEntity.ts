import { Column, <PERSON>tity, ManyToOne, RelationId, Unique } from 'typeorm';
import TypeormPersistenceEntity from '../../../core/infrastructure/db/TypeormPersistenceEntity';
import User from '../../../users/shared/infrastructure/db/User';
import { NonFunctionProperties } from '../../../utils/UtilityTypes';
import { ProfileSetupStepName } from '../../domain/ProfileSetupStepName';

@Entity('profile_setup_steps')
@Unique(['name', 'userId'])
export default class ProfileSetupStepPersistenceEntity extends TypeormPersistenceEntity {
    @Column({ name: 'name' })
    name: ProfileSetupStepName;

    @Column({ name: 'user_id' })
    @RelationId((self: ProfileSetupStepPersistenceEntity) => self.user)
    userId: number;

    @Column({ name: 'is_completed' })
    isCompleted: boolean;

    @Column({ name: 'position', nullable: true })
    position: number;

    @Column({ name: 'is_required', default: true })
    isRequired: boolean;

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    private user: never;

    constructor(params: NonFunctionProperties<ProfileSetupStepPersistenceEntity>);
    constructor();

    constructor(params?: NonFunctionProperties<ProfileSetupStepPersistenceEntity>) {
        if (!params) {
            super();
            return;
        }
        super(params.id, params.createdAt, params.updatedAt);
        Object.assign(this, params);
    }
}
