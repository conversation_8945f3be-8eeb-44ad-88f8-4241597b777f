import ProfileSetupStepRepository from '../db/ProfileSetupStepRepository';
import { tokenFor } from '../../../di/helpers/tokenFor';
import { ProfileSetupStepService } from '../../services/ProfileSetupStepService';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import Container from 'typedi';
import { UserOnboardingService } from '../../services/UserOnboardingService';
import { QueueModule } from '../../../queue/infrastructure/di/QueueModule';
import { UserOnboardedEventWorker } from '../../controllers/events/UserOnboardedEventWorker';
import { UsersAccountsModule } from '../../../users/accounts/infrastructure/di/UsersAccountsModule';
import { ModuleWithControllers } from '../../../di/types/ApplicationModule';
import ProfilesSetupController from '../../controllers/http/ProfilesSetupController';
import { AnalyticsModule } from '../../../analytics/infrastructure/di/AnalyticsModule';

interface ProfileSetupInitParams {
    transactionManager: TransactionManager;
    usersAccountsModule: UsersAccountsModule;
    queueModule: QueueModule;
    analyticsModule: AnalyticsModule;
}

export class ProfileSetupModule implements ModuleWithControllers {
    /**
     * Caution: use for test purposes only.
     */
    static readonly PROFILE_SETUP_SETUP_REPOSITORY_TOKEN = tokenFor(ProfileSetupStepRepository);

    private constructor(
        readonly userOnboardingService: UserOnboardingService,
        readonly profileSetupStepRepository: ProfileSetupStepRepository,
        readonly profileSetupStepService: ProfileSetupStepService,
    ) {}

    getName(): string {
        return ProfileSetupModule.name;
    }

    getControllers(): any[] {
        return [ProfilesSetupController];
    }

    static init({
        transactionManager,
        usersAccountsModule,
        queueModule,
        analyticsModule,
    }: ProfileSetupInitParams): ProfileSetupModule {
        const profileSetupStepRepository = new ProfileSetupStepRepository(transactionManager);
        const profileSetupStepService = new ProfileSetupStepService(profileSetupStepRepository, transactionManager);
        const userOnboardingService = new UserOnboardingService(
            usersAccountsModule.userAccountFinder,
            usersAccountsModule.userAccountManager,
            profileSetupStepService,
            queueModule.queueService,
            analyticsModule.analytics,
        );
        const module = new ProfileSetupModule(
            userOnboardingService,
            profileSetupStepRepository,
            profileSetupStepService,
        );

        queueModule.queueWorkerRegistry.registerWorker(new UserOnboardedEventWorker(queueModule.queueService));

        Container.set(UserOnboardingService, module.userOnboardingService);
        Container.set(ProfileSetupModule.PROFILE_SETUP_SETUP_REPOSITORY_TOKEN, module.profileSetupStepRepository);
        Container.set(ProfileSetupStepService, module.profileSetupStepService);

        return module;
    }
}
