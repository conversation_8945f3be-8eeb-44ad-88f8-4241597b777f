import { IsBoolean, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';
import { PROFILE_SETUP_STEP_NAMES, ProfileSetupStepName } from '../../../domain/ProfileSetupStepName';
import { ProfileSetupStep } from '../../../domain/ProfileSetupStep';
import Id from '../../../../core/domain/value-objects/Id';
import { RequireAllExcept } from '../../../../utils/UtilityTypes';
import { IsId } from '../../../../core/controllers/validators/IsId';

export class ProfileSetupStepDto {
    @IsOptional()
    @IsId()
    id: number;

    @IsEnum(PROFILE_SETUP_STEP_NAMES)
    name: ProfileSetupStepName;

    @IsBoolean()
    isRequired: boolean;

    @IsBoolean()
    isCompleted: boolean;

    @IsNumber()
    @IsOptional()
    position: number;

    static fromDomain(step: ProfileSetupStep): ProfileSetupStepDto {
        return {
            id: step.id!.value,
            name: step.name,
            isRequired: step.isRequired,
            isCompleted: step.isCompleted,
            position: step.position,
        };
    }

    static toDomain(step: RequireAllExcept<ProfileSetupStepDto, 'id'>, userId: Id): ProfileSetupStep {
        return new ProfileSetupStep({
            userId,
            id: step.id ? new Id(step.id) : undefined,
            name: step.name,
            isRequired: step.isRequired,
            isCompleted: step.isCompleted,
            position: step.position,
        });
    }
}
