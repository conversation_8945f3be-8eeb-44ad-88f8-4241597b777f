import { IsBoolean, IsEnum, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PROFILE_SETUP_STEP_NAMES, ProfileSetupStepName } from '../../../domain/ProfileSetupStepName';

export class ProfileSetupStepsInfoResponseDto {
    @IsEnum(PROFILE_SETUP_STEP_NAMES, { each: true })
    pending: ProfileSetupStepName[];

    @IsEnum(PROFILE_SETUP_STEP_NAMES, { each: true })
    completed: ProfileSetupStepName[];
}

export class ProfileSetupStepsResponseDto {
    @IsBoolean()
    readonly isCompleted: boolean;

    @ValidateNested()
    @Type(() => ProfileSetupStepsInfoResponseDto)
    readonly steps: ProfileSetupStepsInfoResponseDto;
}
