import { ValidateNested } from 'class-validator';

import { Type } from 'class-transformer';
import { FixNestedArrayJsonSchemaReference } from '../../../../core/controllers/dto/decorators/FixNestedJsonSchemaReference';
import { ProfileSetupStepDto } from './ProfileSetupStepDto';

export class UpdateProfileSetupStepsDto {
    @ValidateNested({ each: true })
    @Type(() => ProfileSetupStepDto)
    @FixNestedArrayJsonSchemaReference(ProfileSetupStepDto)
    steps: ProfileSetupStepDto[];
}
