import { faker } from '@faker-js/faker';
import request from 'supertest';
import Container from 'typedi';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import Id from '../../../core/domain/value-objects/Id';

import { DateString } from '../../../core/domain/value-objects/DateString';
import { CountryCodeIso2 } from '../../../physical-address/domain/types/CountryCodeIso2';
import { PhysicalAddress } from '../../../physical-address/domain/value-objects/PhysicalAddress';
import { IntegrationTestBatch } from '../../../test-toolkit/e2e/entities/IntegrationTestBatch';
import { IntegrationTestCourse } from '../../../test-toolkit/e2e/entities/IntegrationTestCourse';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import NameSanitiser from '../../../users/accounts/services/NameSanitiser';
import { SetStudentProfileSetupComplianceParamsDto } from '../../../users/shared/controllers/http/dto/StudentProfileSetupComplianceParamsDto';
import { EducationLevel } from '../../../users/shared/domain/EducationLevel';
import { EmploymentStatus } from '../../../users/shared/domain/EmploymentStatus';
import { FieldOfStudy } from '../../../users/shared/domain/FieldOfStudy';
import { FuzzyBoolean } from '../../../users/shared/domain/FuzzyBoolean';
import { Gender } from '../../../users/shared/domain/Gender';
import { CountryPersonalCode } from '../../../users/shared/domain/valueObjects/CountryPersonalCode';
import { WorkIndustry } from '../../../users/shared/domain/WorkIndustry';
import { YearsOfExperience } from '../../../users/shared/domain/YearsOfExperience';
import { Role } from '../../../users/shared/infrastructure/db/User';
import { StudentService } from '../../../users/students/management/services/StudentService';
import StudentProfile from '../../../users/students/profile/domain/StudentProfile';
import { StudentProfileFinder } from '../../../users/students/profile/services/StudentProfileFinder';
import { NonFunctionProperties } from '../../../utils/UtilityTypes';
import { CommonProfileSetupStepName } from '../../domain/CommonProfileSetupStepName';
import { ProfileSetupStepName } from '../../domain/ProfileSetupStepName';
import { StudentProfileSetupStepName } from '../../domain/StudentProfileSetupStepName';
import { ProfileSetupStepService } from '../../services/ProfileSetupStepService';
import { Timezone } from '../../../core/domain/types/Timezone';

describe('Student Profile Setup flow', () => {
    let config: Configuration;
    let profileSetupStepService: ProfileSetupStepService;
    let batch: IntegrationTestBatch;
    let course: IntegrationTestCourse;

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        course = await IntegrationTestCourse.create();
        batch = await IntegrationTestBatch.create({ courseId: course.getIdOrThrow() });

        profileSetupStepService = Container.get(ProfileSetupStepService);
    });

    async function expectSteps({
        pending,
        completed,
        student,
    }: {
        pending: ProfileSetupStepName[];
        completed: ProfileSetupStepName[];
        student: IntegrationTestUser;
    }): Promise<void> {
        const res = await request(config.server)
            .get(`/profiles/setup/${student.params.id}/steps`)
            .set('Cookie', `token=${student.getAuthTokenString()}`)
            .send();

        expect(res.status).toBe(200);
        expect(res.body.isCompleted).toBe(pending.length === 0);
        expect(res.body.steps.pending).toMatchObject(pending);
        expect(res.body.steps.completed).toMatchObject(completed);
    }

    async function sendStep({
        stepData,
        student,
        stepName,
    }: {
        stepData: any;
        student: IntegrationTestUser;
        stepName: string;
    }): Promise<void> {
        const url = `/students/${student.params.id}/profile/${stepName}`;
        const res = await request(config.server)
            .post(url)
            .set('Cookie', `token=${student.getAuthTokenString()}`)
            .send(stepData);

        expect(res.status, `Request to ${url} failed`).toBe(204);
    }

    async function getStepInfo({
        student,
        stepName,
    }: {
        student: IntegrationTestUser;
        stepName: string;
    }): Promise<void> {
        const url = `/students/${student.params.id}/profile/${stepName}`;
        const res = await request(config.server).get(url).set('Cookie', `token=${student.getAuthTokenString()}`);

        expect(res.status, `Request to ${url} failed`).toBe(200);

        return res.body;
    }

    it('should work with whole setup flow', async () => {
        const student = await IntegrationTestUser.createFakeStudent();
        await student.createStudentSettings(batch.getIdOrThrow());
        await student.createStudentProfile();
        await student.authorize();

        await profileSetupStepService.createDefaultProfileSetupSteps(new Id(student.params.id), Role.USER, []);

        // Personal info
        const personalInfo = {
            name: NameSanitiser.sanitise(faker.person.firstName()),
            surname: NameSanitiser.sanitise(faker.person.lastName()),
            gender: Gender.FEMALE,
            phoneNumber: faker.phone.number(),
        };

        await expectSteps({
            student,
            pending: [
                StudentProfileSetupStepName.PERSONAL_INFO,
                StudentProfileSetupStepName.ACADEMIC_HISTORY,
                StudentProfileSetupStepName.WORK_RELATED,
                StudentProfileSetupStepName.COMPLIANCE,
                CommonProfileSetupStepName.DISCORD_SETUP,
            ],
            completed: [],
        });

        expect(await getStepInfo({ student, stepName: 'personal-info' })).toMatchObject({
            name: student.params.name,
            surname: student.params.surname,
            gender: null,
            phoneNumber: null,
        });

        await sendStep({
            student,
            stepName: 'personal-info',
            stepData: personalInfo,
        });

        expect(await getStepInfo({ student, stepName: 'personal-info' })).toMatchObject(personalInfo);

        // Academic History
        const academicHistoryInfo = {
            highestEducationLevel: EducationLevel.MASTER_DEGREE,
            fieldOfStudy: FieldOfStudy.OTHER,
            otherFieldOfStudy: 'Some other field',
            subjectOfStudy: faker.lorem.words(3),
            institutionName: faker.company.name(),
            graduationYear: 2025,
        };

        expect(await getStepInfo({ student, stepName: 'academic-history' })).toMatchObject({
            highestEducationLevel: null,
            fieldOfStudy: null,
            subjectOfStudy: null,
            institutionName: null,
            graduationYear: null,
        });

        await expectSteps({
            student,
            pending: [
                StudentProfileSetupStepName.ACADEMIC_HISTORY,
                StudentProfileSetupStepName.WORK_RELATED,
                StudentProfileSetupStepName.COMPLIANCE,
                CommonProfileSetupStepName.DISCORD_SETUP,
            ],
            completed: [StudentProfileSetupStepName.PERSONAL_INFO],
        });

        await sendStep({
            student,
            stepName: 'academic-history',
            stepData: academicHistoryInfo,
        });

        expect(await getStepInfo({ student, stepName: 'academic-history' })).toMatchObject(academicHistoryInfo);

        // Work related

        const workRelatedInfo = {
            employmentStatus: EmploymentStatus.FULL_TIME,
            workIndustry: WorkIndustry.OTHER,
            otherWorkIndustry: 'Other industry',
            companyName: faker.company.name(),
            jobTitle: faker.person.jobTitle(),
            seniorityLevel: YearsOfExperience.TWO_TO_FIVE_YEARS,
            isPlanningToHuntTheJobAfterCourse: FuzzyBoolean.NO,
        };

        await expectSteps({
            student,
            pending: [
                StudentProfileSetupStepName.WORK_RELATED,
                StudentProfileSetupStepName.COMPLIANCE,
                CommonProfileSetupStepName.DISCORD_SETUP,
            ],
            completed: [StudentProfileSetupStepName.PERSONAL_INFO, StudentProfileSetupStepName.ACADEMIC_HISTORY],
        });

        expect(await getStepInfo({ student, stepName: 'work-related' })).toMatchObject({
            employmentStatus: null,
            workIndustry: null,
            companyName: null,
            jobTitle: null,
            seniorityLevel: null,
            isPlanningToHuntTheJobAfterCourse: null,
        });

        await sendStep({
            student,
            stepName: 'work-related',
            stepData: workRelatedInfo,
        });

        expect(await getStepInfo({ student, stepName: 'work-related' })).toMatchObject(workRelatedInfo);

        // Compliance
        const complianceInfo: SetStudentProfileSetupComplianceParamsDto = {
            citizenshipCountry: CountryCodeIso2.LT,
            residenceCountry: CountryCodeIso2.LT,
            residenceFullAddressString: 'Gedimino pr. 17, Vilnius, 01103, Lithuania',
            countryPersonalCode: '33309240064',
            dateOfBirth: DateString.fromDateString('1993-09-24').value,
            timezone: Timezone.EUROPE_VILNIUS,
        };

        await expectSteps({
            student,
            pending: [StudentProfileSetupStepName.COMPLIANCE, CommonProfileSetupStepName.DISCORD_SETUP],
            completed: [
                StudentProfileSetupStepName.PERSONAL_INFO,
                StudentProfileSetupStepName.ACADEMIC_HISTORY,
                StudentProfileSetupStepName.WORK_RELATED,
            ],
        });

        expect(await getStepInfo({ student, stepName: 'compliance' })).toMatchObject({
            citizenshipCountry: null,
            residenceCountry: null,
            countryPersonalCode: null,
            timezone: Timezone.EUROPE_VILNIUS,
        });

        await sendStep({
            student,
            stepName: 'compliance',
            stepData: complianceInfo,
        });

        expect(await getStepInfo({ student, stepName: 'compliance' })).toMatchObject({
            ...complianceInfo,
            dateOfBirth: complianceInfo.dateOfBirth,
        });

        await expectSteps({
            student,
            pending: [CommonProfileSetupStepName.DISCORD_SETUP],
            completed: [
                StudentProfileSetupStepName.PERSONAL_INFO,
                StudentProfileSetupStepName.ACADEMIC_HISTORY,
                StudentProfileSetupStepName.WORK_RELATED,
                StudentProfileSetupStepName.COMPLIANCE,
            ],
        });

        // Check full profile

        const profile = await Container.get(StudentProfileFinder).findByUserId(new Id(student.params.id));

        expect(profile).toMatchObject({
            gender: personalInfo.gender,
            phoneNumber: personalInfo.phoneNumber,
            ...academicHistoryInfo,
            ...workRelatedInfo,
            citizenshipCountry: complianceInfo.citizenshipCountry,
            personalCode: CountryPersonalCode.fromString(
                complianceInfo.countryPersonalCode as string,
                complianceInfo.citizenshipCountry,
            ),
            dateOfBirth: DateString.fromString(complianceInfo.dateOfBirth),
            timezone: complianceInfo.timezone,
            physicalAddress: PhysicalAddress.fromParams({
                full: 'Gedimino pr. 17, Vilnius, 01103, Lithuania',
                countryCode: CountryCodeIso2.LT,
                isVerified: false,
            }),
        } as Partial<NonFunctionProperties<StudentProfile>>);

        const user = await Container.get(StudentService).get(student.params.id);

        expect(user).toMatchObject({
            name: personalInfo.name,
            surname: personalInfo.surname,
        });
    });
});
