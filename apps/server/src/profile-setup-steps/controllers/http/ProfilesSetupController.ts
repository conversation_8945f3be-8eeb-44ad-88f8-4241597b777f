import { Authorized, Body, CurrentUser, Get, JsonController, Param, Put } from 'routing-controllers';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import User, { Role } from '../../../users/shared/infrastructure/db/User';
import responses from '../../../core/controllers/docs/responses';
import { ProfileSetupStepsResponseDto } from './dto/ProfileSetupStepsResponseDto';
import { ProfileSetupStepService } from '../../services/ProfileSetupStepService';
import Id from '../../../core/domain/value-objects/Id';
import { ifStudentAllowOnlySelf } from '../../../core/controllers/ifStudentAllowOnlySelf';
import { GetProfileSetupStepsDto } from './dto/GetProfileSetupStepsDto';
import { ProfileSetupStepDto } from './dto/ProfileSetupStepDto';
import { UpdateProfileSetupStepsDto } from './dto/UpdateProfileSetupStepsDto';
import { ProfileSetupSteps } from '../../domain/ProfileSetupSteps';
import { Objects } from '../../../utils/Objects';

@Service()
@JsonController('/profiles/setup/:userId')
@OpenAPI({
    responses,
    tags: ['Profiles Setup'],
    security: [{ cookieAuth: [] }],
})
export default class ProfilesSetupController {
    constructor(
        @Inject()
        private readonly profileSetupStepService: ProfileSetupStepService,
    ) {}

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER, Role.MENTOR])
    @Get('/steps')
    @ResponseSchema(ProfileSetupStepsResponseDto)
    @OpenAPI({
        description: "Returns student's profile setup steps status",
    })
    async getProfileSetupStepsSummary(
        @Param('userId') userId: number,
        @CurrentUser() user: User,
    ): Promise<ProfileSetupStepsResponseDto> {
        ifStudentAllowOnlySelf(user, userId);

        const steps = await this.profileSetupStepService.getUserProfileSetupSteps(new Id(userId));
        return {
            isCompleted: steps.areAllStepsCompleted(),
            steps: {
                pending: steps.getPendingSteps().map((step) => step.name),
                completed: steps.getCompletedSteps().map((step) => step.name),
            },
        };
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.MENTOR])
    @Get('/steps/verbose')
    @ResponseSchema(GetProfileSetupStepsDto)
    @OpenAPI({
        description: 'Get students profile setup steps with all fields',
    })
    async getProfileSetupStepsForOneUser(@Param('userId') userIdRaw: number): Promise<GetProfileSetupStepsDto> {
        const steps = await this.profileSetupStepService.getUserProfileSetupSteps(new Id(userIdRaw));

        return {
            steps: steps.map(ProfileSetupStepDto.fromDomain),
        };
    }

    @Authorized([Role.ADMIN])
    @Put('/steps')
    @OpenAPI({
        description: "Replaces user's profile setup steps. Omitted steps will be removed. Ids are ignored.",
    })
    async updateProfileSetupSteps(
        @Param('userId') userIdRaw: number,
        @Body() body: UpdateProfileSetupStepsDto,
    ): Promise<void> {
        const userId = new Id(userIdRaw);

        await this.profileSetupStepService.replaceUserSteps({
            userId,
            steps: ProfileSetupSteps.fromArray(
                body.steps.map((dto) => ProfileSetupStepDto.toDomain(Objects.omit(dto, ['id']), userId)),
            ),
        });
    }
}
