import ProfilesSetupController from './ProfilesSetupController';
import { ProfileSetupStepService } from '../../services/ProfileSetupStepService';
import { anything, instance, mock, reset, when } from 'ts-mockito';
import { ProfileSetupSteps } from '../../domain/ProfileSetupSteps';
import { ProfileSetupStep } from '../../domain/ProfileSetupStep';
import { StudentProfileSetupStepName } from '../../domain/StudentProfileSetupStepName';
import { Role } from '../../../users/shared/infrastructure/db/User';
import AuthorizationError from '../../../core/errors/AuthorizationError';
import { CommonProfileSetupStepName } from '../../domain/CommonProfileSetupStepName';
import Id from '../../../core/domain/value-objects/Id';

describe(ProfilesSetupController.name, () => {
    const profileSetupStepServiceMock = mock<ProfileSetupStepService>();

    const profilesSetupController = new ProfilesSetupController(instance(profileSetupStepServiceMock));

    afterEach(() => {
        reset(profileSetupStepServiceMock);
    });

    describe('getProfileSetupSteps', () => {
        it('should return profile setup steps', async () => {
            const userId = new Id(1);
            when(profileSetupStepServiceMock.getUserProfileSetupSteps(anything())).thenResolve(
                ProfileSetupSteps.fromArray([
                    new ProfileSetupStep({
                        name: StudentProfileSetupStepName.PERSONAL_INFO,
                        userId,
                        isCompleted: true,
                        position: 1,
                    }),
                    new ProfileSetupStep({
                        name: StudentProfileSetupStepName.COMPLIANCE,
                        userId,
                        isCompleted: true,
                        position: 2,
                    }),
                    new ProfileSetupStep({
                        name: CommonProfileSetupStepName.DISCORD_SETUP,
                        userId,
                        isCompleted: false,
                        position: 3,
                    }),
                    new ProfileSetupStep({
                        name: StudentProfileSetupStepName.ACADEMIC_HISTORY,
                        userId,
                        isCompleted: false,
                        position: 4,
                    }),
                ]),
            );

            const result = await profilesSetupController.getProfileSetupStepsSummary(1, { id: 1 } as any);

            expect(result).toMatchObject({
                isCompleted: false,
                steps: {
                    pending: [CommonProfileSetupStepName.DISCORD_SETUP, StudentProfileSetupStepName.ACADEMIC_HISTORY],
                    completed: [StudentProfileSetupStepName.PERSONAL_INFO, StudentProfileSetupStepName.COMPLIANCE],
                },
            });
        });

        it('should return completed profile steps if no steps defined for user', async () => {
            when(profileSetupStepServiceMock.getUserProfileSetupSteps(anything())).thenResolve(
                ProfileSetupSteps.fromArray([]),
            );

            const result = await profilesSetupController.getProfileSetupStepsSummary(1, { id: 1 } as any);

            expect(result).toMatchObject({
                isCompleted: true,
                steps: {
                    pending: [],
                    completed: [],
                },
            });
        });

        it('should reject student that tries to get steps of another student', async () => {
            await expect(
                profilesSetupController.getProfileSetupStepsSummary(1, { id: 2, role: Role.USER } as any),
            ).rejects.toBeInstanceOf(AuthorizationError);
        });
    });
});
