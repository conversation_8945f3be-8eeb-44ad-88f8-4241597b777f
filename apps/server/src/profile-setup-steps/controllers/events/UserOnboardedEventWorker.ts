import { Job } from 'pg-boss';
import { AppQueues } from '../../../queue/domain/AppQueues';
import { QueueWorker } from '../../../queue/domain/QueueWorker';
import { QueueService } from '../../../queue/QueueService';
import { EvaluateAndProgressPriedasDocumentCommand } from '../../../uzt/submodules/uzt-document/controllers/commands/dto/EvaluateAndProgressPriedasDocumentCommand';
import { Role } from '../../../users/shared/infrastructure/db/User';
import { SetDefaultScheduleCommand } from '../../../gov/submodules/gov-week-schedule/submodules/default-week-schedule/controllers/command/dto/SetDefaultScheduleCommand';
import { UserOnboardedEvent, UserOnboardedEventDto } from './dto/UserOnboardedEvent';

export class UserOnboardedEventWorker implements QueueWorker {
    readonly queuePattern = AppQueues.schema.user.onboarded.queue;
    readonly options = {
        newJobCheckIntervalSeconds: 1,
    };

    constructor(private readonly queueService: QueueService) {}

    async handler(job: Job<UserOnboardedEventDto>): Promise<void> {
        const event = AppQueues.getMapperByClass(UserOnboardedEvent).deserialize(job.data);

        if (event.role === Role.USER) {
            await this.queueService.commandMany([
                {
                    message: new SetDefaultScheduleCommand({
                        userId: event.userId,
                    }),
                },
                {
                    message: new EvaluateAndProgressPriedasDocumentCommand({
                        userId: event.userId,
                    }),
                },
            ]);
        }
    }
}
