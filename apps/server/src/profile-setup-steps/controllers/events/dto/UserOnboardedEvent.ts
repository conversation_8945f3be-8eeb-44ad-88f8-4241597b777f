import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { Serialized } from '../../../../core/utils/Serialized';
import Id from '../../../../core/domain/value-objects/Id';
import { Role } from '../../../../users/shared/infrastructure/db/User';

export type UserOnboardedEventParams = NonFunctionProperties<UserOnboardedEvent>;
export type UserOnboardedEventDto = Serialized<UserOnboardedEventParams>;

export class UserOnboardedEvent {
    readonly userId: Id;
    readonly role: Role;

    constructor(params: UserOnboardedEventParams) {
        Object.assign(this, params);
    }
}

export class UserOnboardedEventMapper {
    serialize(command: UserOnboardedEvent): UserOnboardedEventDto {
        return {
            userId: command.userId.value,
            role: command.role,
        };
    }

    deserialize(data: UserOnboardedEventDto): UserOnboardedEvent {
        return new UserOnboardedEvent({
            userId: new Id(data.userId),
            role: data.role,
        });
    }
}
