import { Mutable, NonFunctionProperties, RequireAllExcept } from '../../utils/UtilityTypes';
import DomainEntity from '../../core/domain/DomainEntity';
import Id from '../../core/domain/value-objects/Id';
import { ProfileSetupStepName } from './ProfileSetupStepName';

export type ProfileSetupStepParams = Mutable<NonFunctionProperties<ProfileSetupStep>>;

export type ProfileSetupStepConstructorParams = RequireAllExcept<ProfileSetupStepParams, 'isRequired' | 'id'>;

export class ProfileSetupStep extends DomainEntity {
    readonly name: ProfileSetupStepName;
    readonly userId: Id;
    readonly isCompleted: boolean;
    readonly position: number;
    readonly isRequired: boolean;

    constructor(params: ProfileSetupStepConstructorParams) {
        super(params.id);
        this.name = params.name;
        this.userId = params.userId;
        this.isCompleted = params.isCompleted;
        this.position = params.position;
        this.isRequired = params.isRequired ?? true;
    }

    compare(step: ProfileSetupStep): number {
        // Steps without position are always last
        if (step.position === null) {
            return -1;
        }

        return this.position - step.position;
    }
}
