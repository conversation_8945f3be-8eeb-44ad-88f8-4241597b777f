import { ProfileSetupStep } from './ProfileSetupStep';
import { ProfileSetupStatus } from './ProfileSetupStatus';

export class ProfileSetupSteps extends Array<ProfileSetupStep> {
    static fromArray(steps: ProfileSetupStep[]): ProfileSetupSteps {
        return new ProfileSetupSteps(...steps.sort((a, b) => a.compare(b)));
    }

    areAllStepsCompleted(): boolean {
        return !this.some((step) => !step.isCompleted);
    }

    getPendingSteps(): ProfileSetupStep[] {
        return this.filter((step) => !step.isCompleted);
    }

    getCompletedSteps(): ProfileSetupStep[] {
        return this.filter((step) => step.isCompleted);
    }

    getStatus(): ProfileSetupStatus {
        const pendingSteps = this.getPendingSteps();

        if (pendingSteps.length === 0) {
            return ProfileSetupStatus.COMPLETED;
        }

        if (pendingSteps.some((step) => step.isRequired)) {
            return ProfileSetupStatus.HAS_REQUIRED_STEPS;
        }

        return ProfileSetupStatus.HAS_RECOMMENDED_STEPS;
    }
}
