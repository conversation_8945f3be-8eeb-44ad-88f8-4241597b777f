import Id from '../../core/domain/value-objects/Id';
import Transaction from '../../core/infrastructure/Transaction';
import ArgumentValidation from '../../core/utils/validation/ArgumentValidation';
import { QueueService } from '../../queue/QueueService';
import { UserAccountFinder } from '../../users/accounts/services/UserAccountFinder';
import { UserAccountManager } from '../../users/accounts/services/UserAccountManager';
import { Role } from '../../users/shared/infrastructure/db/User';
import { UserOnboardedEvent } from '../controllers/events/dto/UserOnboardedEvent';
import { ProfileSetupStatus } from '../domain/ProfileSetupStatus';
import { ProfileSetupStepName } from '../domain/ProfileSetupStepName';
import { ProfileSetupStepService } from './ProfileSetupStepService';
import Analytics from '../../analytics/Analytics';

export class UserOnboardingService {
    constructor(
        private readonly userAccountFinder: UserAccountFinder,
        private readonly userAccountManager: UserAccountManager,
        private readonly profileSetupStepService: ProfileSetupStepService,
        private readonly queueService: QueueService,
        private readonly analytics: Analytics,
    ) {}

    async completeStep(userId: Id, step: ProfileSetupStepName, tx?: Transaction): Promise<void> {
        const userAccount = await this.userAccountFinder.findByUserId(userId, tx);

        ArgumentValidation.assert.defined(userAccount, `User account with id ${userId} not found`);

        await this.profileSetupStepService.markStepAsCompleted(userId, step, tx);

        // Students are activated by the new onboarding flow
        const shouldActivate =
            userAccount.role !== Role.USER &&
            userAccount.isOnboarding() &&
            (await this.isOnboardingCompleted(userId, tx));

        if (shouldActivate) {
            this.analytics.onboardingCompleted(userId.value);

            await this.userAccountManager.activate({ userId, tx });

            await Transaction.addBeforeCommitActionIfExistOrExecute(async () => {
                await this.queueService.publish({
                    event: new UserOnboardedEvent({ userId, role: userAccount.role }),
                });
            }, tx);
        }
    }

    private async isOnboardingCompleted(userId: Id, tx?: Transaction): Promise<boolean> {
        const profileSetupStatus = await this.profileSetupStepService.getUserProfileStatus(userId, tx);
        return (
            profileSetupStatus === ProfileSetupStatus.COMPLETED ||
            profileSetupStatus === ProfileSetupStatus.HAS_RECOMMENDED_STEPS
        );
    }
}
