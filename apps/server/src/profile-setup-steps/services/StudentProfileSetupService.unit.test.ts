import { ProfileSetupStepService } from './ProfileSetupStepService';
import { StudentProfileSetupStepName } from '../domain/StudentProfileSetupStepName';
import ProfileSetupStepRepository from '../infrastructure/db/ProfileSetupStepRepository';
import TransactionManager from '../../core/infrastructure/TransactionManager';
import { capture, instance, mock, reset, when } from 'ts-mockito';
import { ProfileSetupStep } from '../domain/ProfileSetupStep';
import { CommonProfileSetupStepName } from '../domain/CommonProfileSetupStepName';
import { CourseOnboardingConnection } from '../../education/courses/domain/CourseOnboardingConnection';
import { Role } from '../../users/shared/infrastructure/db/User';
import { MentorProfileSetupStepName } from '../domain/MentorProfileSetupStepName';
import TestObjects from '../../test-toolkit/shared/TestObjects';

describe(ProfileSetupStepService.name, () => {
    let profileSetupStepService: ProfileSetupStepService;

    const profileSetupStepRepositoryMock = mock<ProfileSetupStepRepository>();
    const transactionManagerMock = mock<TransactionManager>();

    beforeAll(async () => {
        profileSetupStepService = new ProfileSetupStepService(
            instance(profileSetupStepRepositoryMock),
            instance(transactionManagerMock),
        );
    });

    beforeEach(() => {
        reset(transactionManagerMock);
        reset(profileSetupStepRepositoryMock);
    });

    describe('createDefaultProfileSetupSteps, student role', () => {
        it('should not github step if no github in onboarding connections', async () => {
            const userId = TestObjects.id();
            when(profileSetupStepRepositoryMock.findByUserId(userId)).thenResolve([]);

            await profileSetupStepService.createDefaultProfileSetupSteps(userId, Role.USER, [], []);
            const [steps] = capture(profileSetupStepRepositoryMock.saveAll).first();

            expect(steps).toMatchObject([
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.PERSONAL_INFO,
                    userId,
                    isCompleted: false,
                    position: 0,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.ACADEMIC_HISTORY,
                    userId,
                    isCompleted: false,
                    position: 1,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.WORK_RELATED,
                    userId,
                    isCompleted: false,
                    position: 2,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.COMPLIANCE,
                    userId,
                    isCompleted: false,
                    position: 3,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.DISCORD_SETUP,
                    userId,
                    isCompleted: false,
                    position: 4,
                }),
            ]);
        });

        it('should create github step if github in onboarding connections ', async () => {
            const userId = TestObjects.id();
            when(profileSetupStepRepositoryMock.findByUserId(userId)).thenResolve([]);
            await profileSetupStepService.createDefaultProfileSetupSteps(
                userId,
                Role.USER,
                [CourseOnboardingConnection.GITHUB_ACCOUNT],
                [],
            );

            const [steps] = capture(profileSetupStepRepositoryMock.saveAll).first();

            expect(steps).toMatchObject([
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.PERSONAL_INFO,
                    userId,
                    isCompleted: false,
                    position: 0,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.ACADEMIC_HISTORY,
                    userId,
                    isCompleted: false,
                    position: 1,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.WORK_RELATED,
                    userId,
                    isCompleted: false,
                    position: 2,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.COMPLIANCE,
                    userId,
                    isCompleted: false,
                    position: 3,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.DISCORD_SETUP,
                    userId,
                    isCompleted: false,
                    position: 4,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.GITHUB_SETUP,
                    userId,
                    isCompleted: false,
                    position: 5,
                }),
            ]);
        });

        it('should create google step if google in onboarding connections ', async () => {
            const userId = TestObjects.id();
            when(profileSetupStepRepositoryMock.findByUserId(userId)).thenResolve([]);
            await profileSetupStepService.createDefaultProfileSetupSteps(
                userId,
                Role.USER,
                [CourseOnboardingConnection.GOOGLE_ACCOUNT],
                [],
            );

            const [steps] = capture(profileSetupStepRepositoryMock.saveAll).first();

            expect(steps).toMatchObject([
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.PERSONAL_INFO,
                    userId,
                    isCompleted: false,
                    position: 0,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.ACADEMIC_HISTORY,
                    userId,
                    isCompleted: false,
                    position: 1,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.WORK_RELATED,
                    userId,
                    isCompleted: false,
                    position: 2,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.COMPLIANCE,
                    userId,
                    isCompleted: false,
                    position: 3,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.DISCORD_SETUP,
                    userId,
                    isCompleted: false,
                    position: 4,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.GOOGLE_SETUP,
                    userId,
                    isCompleted: false,
                    position: 5,
                }),
            ]);
        });

        it('should create both github and google step if both in onboarding connections ', async () => {
            const userId = TestObjects.id();
            when(profileSetupStepRepositoryMock.findByUserId(userId)).thenResolve([]);
            await profileSetupStepService.createDefaultProfileSetupSteps(
                userId,
                Role.USER,
                [CourseOnboardingConnection.GITHUB_ACCOUNT, CourseOnboardingConnection.GOOGLE_ACCOUNT],
                [],
            );

            const [steps] = capture(profileSetupStepRepositoryMock.saveAll).first();

            expect(steps).toMatchObject([
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.PERSONAL_INFO,
                    userId,
                    isCompleted: false,
                    position: 0,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.ACADEMIC_HISTORY,
                    userId,
                    isCompleted: false,
                    position: 1,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.WORK_RELATED,
                    userId,
                    isCompleted: false,
                    position: 2,
                }),
                new ProfileSetupStep({
                    name: StudentProfileSetupStepName.COMPLIANCE,
                    userId,
                    isCompleted: false,
                    position: 3,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.DISCORD_SETUP,
                    userId,
                    isCompleted: false,
                    position: 4,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.GITHUB_SETUP,
                    userId,
                    isCompleted: false,
                    position: 5,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.GOOGLE_SETUP,
                    userId,
                    isCompleted: false,
                    position: 6,
                }),
            ]);
        });
    });

    describe('createDefaultProfileSetupSteps, mentor role', () => {
        it('should not github step if no github in onboarding connections', async () => {
            const userId = TestObjects.id();
            when(profileSetupStepRepositoryMock.findByUserId(userId)).thenResolve([]);
            await profileSetupStepService.createDefaultProfileSetupSteps(userId, Role.MENTOR, [], []);

            const [steps] = capture(profileSetupStepRepositoryMock.saveAll).first();

            expect(steps).toMatchObject([
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.PERSONAL_INFO,
                    userId,
                    isCompleted: false,
                    position: 0,
                }),
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.COMPLIANCE,
                    userId,
                    isCompleted: false,
                    position: 1,
                }),
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.WORK_RELATED,
                    userId,
                    isCompleted: false,
                    position: 2,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.DISCORD_SETUP,
                    userId,
                    isCompleted: false,
                    position: 3,
                }),
            ]);
        });

        it('should create github step if github in onboarding connections ', async () => {
            const userId = TestObjects.id();
            when(profileSetupStepRepositoryMock.findByUserId(userId)).thenResolve([]);
            await profileSetupStepService.createDefaultProfileSetupSteps(
                userId,
                Role.MENTOR,
                [CourseOnboardingConnection.GITHUB_ACCOUNT],
                [],
            );

            const [steps] = capture(profileSetupStepRepositoryMock.saveAll).first();

            expect(steps).toMatchObject([
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.PERSONAL_INFO,
                    userId,
                    isCompleted: false,
                    position: 0,
                }),
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.COMPLIANCE,
                    userId,
                    isCompleted: false,
                    position: 1,
                }),
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.WORK_RELATED,
                    userId,
                    isCompleted: false,
                    position: 2,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.DISCORD_SETUP,
                    userId,
                    isCompleted: false,
                    position: 3,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.GITHUB_SETUP,
                    userId,
                    isCompleted: false,
                    position: 4,
                }),
            ]);
        });

        it('should create google step if google in onboarding connections ', async () => {
            const userId = TestObjects.id();
            when(profileSetupStepRepositoryMock.findByUserId(userId)).thenResolve([]);
            await profileSetupStepService.createDefaultProfileSetupSteps(
                userId,
                Role.MENTOR,
                [CourseOnboardingConnection.GOOGLE_ACCOUNT],
                [],
            );

            const [steps] = capture(profileSetupStepRepositoryMock.saveAll).first();

            expect(steps).toMatchObject([
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.PERSONAL_INFO,
                    userId,
                    isCompleted: false,
                    position: 0,
                }),
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.COMPLIANCE,
                    userId,
                    isCompleted: false,
                    position: 1,
                }),
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.WORK_RELATED,
                    userId,
                    isCompleted: false,
                    position: 2,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.DISCORD_SETUP,
                    userId,
                    isCompleted: false,
                    position: 3,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.GOOGLE_SETUP,
                    userId,
                    isCompleted: false,
                    position: 4,
                }),
            ]);
        });

        it('should create both github and google step if both in onboarding connections ', async () => {
            const userId = TestObjects.id();
            when(profileSetupStepRepositoryMock.findByUserId(userId)).thenResolve([]);
            await profileSetupStepService.createDefaultProfileSetupSteps(
                userId,
                Role.MENTOR,
                [CourseOnboardingConnection.GITHUB_ACCOUNT, CourseOnboardingConnection.GOOGLE_ACCOUNT],
                [],
            );

            const [steps] = capture(profileSetupStepRepositoryMock.saveAll).first();

            expect(steps).toMatchObject([
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.PERSONAL_INFO,
                    userId,
                    isCompleted: false,
                    position: 0,
                }),
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.COMPLIANCE,
                    userId,
                    isCompleted: false,
                    position: 1,
                }),
                new ProfileSetupStep({
                    name: MentorProfileSetupStepName.WORK_RELATED,
                    userId,
                    isCompleted: false,
                    position: 2,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.DISCORD_SETUP,
                    userId,
                    isCompleted: false,
                    position: 3,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.GITHUB_SETUP,
                    userId,
                    isCompleted: false,
                    position: 4,
                }),
                new ProfileSetupStep({
                    name: CommonProfileSetupStepName.GOOGLE_SETUP,
                    userId,
                    isCompleted: false,
                    position: 5,
                }),
            ]);
        });
    });
});
