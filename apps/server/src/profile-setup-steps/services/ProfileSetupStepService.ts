import ProfileSetupStepRepository from '../infrastructure/db/ProfileSetupStepRepository';
import Id from '../../core/domain/value-objects/Id';
import { StudentProfileSetupStepName } from '../domain/StudentProfileSetupStepName';
import Transaction from '../../core/infrastructure/Transaction';
import { ProfileSetupStep } from '../domain/ProfileSetupStep';
import { CommonProfileSetupStepName } from '../domain/CommonProfileSetupStepName';
import { ProfileSetupSteps } from '../domain/ProfileSetupSteps';
import { ProfileSetupStepName } from '../domain/ProfileSetupStepName';
import { ProfileSetupStatus } from '../domain/ProfileSetupStatus';
import TransactionManager from '../../core/infrastructure/TransactionManager';
import { CourseOnboardingConnection } from '../../education/courses/domain/CourseOnboardingConnection';
import { MentorProfileSetupStepName } from '../domain/MentorProfileSetupStepName';
import { Role } from '../../users/shared/infrastructure/db/User';

export class ProfileSetupStepService {
    constructor(
        private readonly repository: ProfileSetupStepRepository,
        private readonly transactionManager: TransactionManager,
    ) {}

    async createDefaultProfileSetupSteps(
        userId: Id,
        role: Role,
        onboardingConnections: CourseOnboardingConnection[],
        notRequiredStepNames: ProfileSetupStepName[] = [],
        tx?: Transaction,
    ): Promise<ProfileSetupStep[]> {
        const steps = await this.generateDefaultProfileSetupSteps(
            userId,
            role,
            onboardingConnections,
            notRequiredStepNames,
        );
        return this.repository.saveAll(steps, tx);
    }

    async getUserProfileSetupSteps(userId: Id, tx?: Transaction): Promise<ProfileSetupSteps> {
        const steps = await this.repository.findByUserId(userId, tx);

        return ProfileSetupSteps.fromArray(steps);
    }

    async getUserProfileStatus(userId: Id, tx?: Transaction): Promise<ProfileSetupStatus> {
        const steps = await this.getUserProfileSetupSteps(userId, tx);

        return steps.getStatus();
    }

    async markStepAsCompleted(userId: Id, step: ProfileSetupStepName, tx?: Transaction): Promise<void> {
        await this.repository.markStepAsCompleted(userId, step, tx);
    }

    async markStepAsNotCompleted(userId: Id, step: ProfileSetupStepName, tx?: Transaction): Promise<void> {
        await this.repository.markStepAsNotCompleted(userId, step, tx);
    }

    async replaceUserSteps({
        userId,
        steps,
        tx: existingTx,
    }: {
        userId: Id;
        steps: ProfileSetupSteps;
        tx?: Transaction;
    }): Promise<void> {
        await this.transactionManager.execute(async (tx) => {
            await this.repository.deleteByUserId(userId, tx);
            await this.repository.saveAll(steps, tx);
        }, existingTx);
    }

    async getByUserIdAndName(
        userId: Id,
        name: ProfileSetupStepName,
        tx?: Transaction,
    ): Promise<ProfileSetupStep | undefined> {
        return this.repository.findByUserIdAndName(userId, name, tx);
    }

    private async generateDefaultProfileSetupSteps(
        userId: Id,
        role: Role,
        onboardingConnections: CourseOnboardingConnection[],
        notRequiredStepNames: ProfileSetupStepName[] = [],
    ): Promise<ProfileSetupSteps> {
        const stepNames = this.getDefaultStepsForRole(role).filter((name) => !notRequiredStepNames.includes(name));

        if (onboardingConnections.includes(CourseOnboardingConnection.GITHUB_ACCOUNT)) {
            stepNames.push(CommonProfileSetupStepName.GITHUB_SETUP);
        }

        if (onboardingConnections.includes(CourseOnboardingConnection.GOOGLE_ACCOUNT)) {
            stepNames.push(CommonProfileSetupStepName.GOOGLE_SETUP);
        }

        const existingProfileSetupSteps = await this.repository.findByUserId(userId);

        return ProfileSetupSteps.fromArray(
            stepNames.map((name, currentIndex) => {
                return new ProfileSetupStep({
                    ...existingProfileSetupSteps.find((step) => step.name === name),
                    name,
                    userId,
                    isCompleted: false,
                    position: currentIndex,
                });
            }),
        );
    }

    private getDefaultStepsForRole(role: Role): ProfileSetupStepName[] {
        switch (role) {
            case Role.USER:
                return [
                    StudentProfileSetupStepName.PERSONAL_INFO,
                    StudentProfileSetupStepName.ACADEMIC_HISTORY,
                    StudentProfileSetupStepName.WORK_RELATED,
                    StudentProfileSetupStepName.COMPLIANCE,
                    CommonProfileSetupStepName.DISCORD_SETUP,
                ];
            case Role.MENTOR:
                return [
                    MentorProfileSetupStepName.PERSONAL_INFO,
                    MentorProfileSetupStepName.COMPLIANCE,
                    MentorProfileSetupStepName.WORK_RELATED,
                    CommonProfileSetupStepName.DISCORD_SETUP,
                ];
            default:
                return [];
        }
    }
}
