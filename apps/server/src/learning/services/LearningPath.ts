import Id from '../../core/domain/value-objects/Id';
import Transaction from '../../core/infrastructure/Transaction';
import RoadmapManager from '../submodules/roadmap/services/RoadmapManager';

export default class LearningPath {
    constructor(private readonly roadmapManager: RoadmapManager) {}

    async beginCourse(courseId: number, studentId: number, tx?: Transaction): Promise<void> {
        await this.roadmapManager.beginCourse(new Id(studentId), new Id(courseId), tx);
    }
}
