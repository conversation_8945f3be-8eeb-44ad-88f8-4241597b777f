import { IsDate, IsEnum, IsInt, IsString } from 'class-validator';
import { AssignedSprintState } from '../../../../roadmap/domain/AssignedSprint';
import { DeadlineSprint } from '../../../services/DeadlineManager';
import { NonFunctionProperties } from '../../../../../../utils/UtilityTypes';

export class DeadlineSprintDTO {
    @IsInt()
    id: number;

    @IsString()
    name: string;

    @IsInt()
    position: number;

    @IsInt()
    duration: number;

    @IsDate()
    deadline: Date;

    @IsEnum(AssignedSprintState)
    state: AssignedSprintState;

    constructor(params: NonFunctionProperties<DeadlineSprintDTO>) {
        Object.assign(this, params);
    }

    static fromDeadlineSprint(deadlineSprint: DeadlineSprint): DeadlineSprintDTO {
        return new DeadlineSprintDTO({
            id: deadlineSprint.id.value,
            name: deadlineSprint.name,
            position: deadlineSprint.position,
            duration: deadlineSprint.duration,
            deadline: deadlineSprint.deadline,
            state: deadlineSprint.state,
        });
    }
}
