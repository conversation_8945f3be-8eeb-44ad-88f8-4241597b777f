import Id from '../../../../../../core/domain/value-objects/Id';
import TypeormPersistenceMapper from '../../../../../../core/infrastructure/db/TypeormPersistenceMapper';
import DeadlineChangeRecord from '../../../domain/audit/DeadlineChangeRecord';
import DeadlineChangeRecordPersistenceEntity from './DeadlineChangeRecordPersistenceEntity';

export default class DeadlineChangeRecordMapper
    implements TypeormPersistenceMapper<DeadlineChangeRecord, DeadlineChangeRecordPersistenceEntity>
{
    toDomain(entity: DeadlineChangeRecordPersistenceEntity): DeadlineChangeRecord {
        return new DeadlineChangeRecord({
            assignedSprintId: new Id(entity.assignedSprintId),
            durationBefore: entity.durationBefore,
            durationAfter: entity.durationAfter,
            creatorId: entity.creatorId ? new Id(entity.creatorId) : undefined,
            extensionRequestId: entity.extensionRequestId ? new Id(entity.extensionRequestId) : undefined,
            id: entity.id ? new Id(entity.id) : undefined,
        });
    }

    toPersistence(record: DeadlineChangeRecord): DeadlineChangeRecordPersistenceEntity {
        return DeadlineChangeRecordPersistenceEntity.fromDeadlineChangeRecord(record);
    }
}
