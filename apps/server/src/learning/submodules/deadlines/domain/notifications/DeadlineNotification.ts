import DomainEntity from '../../../../../core/domain/DomainEntity';
import Id from '../../../../../core/domain/value-objects/Id';
import ArgumentValidation from '../../../../../core/utils/validation/ArgumentValidation';
import DeadlineNotificationType from './DeadlineNotificationType';

export interface DeadlineNotificationProperties {
    assignedSprintId: Id;
    type: DeadlineNotificationType;
    sentAt?: Date;
}

export default class DeadlineNotification extends DomainEntity {
    readonly assignedSprintId: Id;

    readonly type: DeadlineNotificationType;

    readonly sentAt: Date;

    // @ts-expect-error TS(2322) FIXME: Type 'undefined' is not assignable to type 'Id'.
    constructor(properties: DeadlineNotificationProperties, id: Id = undefined) {
        super(id);

        ArgumentValidation.assert.defined(properties.assignedSprintId, 'Assigned sprint ID is required.');
        ArgumentValidation.assert.defined(properties.type, 'Deadline notification type is required.');

        this.type = properties.type;
        this.assignedSprintId = properties.assignedSprintId;
        this.sentAt = properties.sentAt ?? new Date();
    }
}
