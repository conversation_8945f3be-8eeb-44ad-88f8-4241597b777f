import { faker } from '@faker-js/faker';
import Id from '../../../../core/domain/value-objects/Id';
import Transaction from '../../../../core/infrastructure/Transaction';
import ReviewProperties from '../../../../education/sprints/domain/ReviewProperties';
import ReviewEvaluationManager from './ReviewEvaluationManager';
import AssignedSprintPart, { AssignedSprintPartState } from '../../roadmap/domain/AssignedSprintPart';
import CorrectionPersistenceEntity from '../../../../corrections/infrastructure/db/CorrectionPersistenceEntity';
import CoreTransactionManagerMock from '../../../../test-toolkit/unit/deprecated-mocks/CoreTransactionManagerMock';
import CorrectionRepositoryMock from '../../../../test-toolkit/unit/deprecated-mocks/CorrectionRepositoryMock';
import CorrectionServiceMock from '../../../../test-toolkit/unit/deprecated-mocks/CorrectionServiceMock';
import TestObjects from '../../../../test-toolkit/shared/TestObjects';
import { getRandomAssignedSprintPart } from '../../../../test-toolkit/shared/learning';
import { CorrectionStatus } from '../../../../corrections/domain/CorrectionStatus';
import { CorrectionType } from '../../../../corrections/domain/CorrectionType';
import { anything, deepEqual, instance, mock, reset, verify, when } from 'ts-mockito';
import RoadmapManager from '../../roadmap/services/RoadmapManager';

describe('ReviewEvaluationManager', () => {
    const roadmapManager = mock(RoadmapManager);
    const correctionService = new CorrectionServiceMock();
    const correctionRepository = new CorrectionRepositoryMock();
    const reviewEvaluationManager = new ReviewEvaluationManager(
        new CoreTransactionManagerMock(),
        instance(roadmapManager),
        correctionService,
        correctionRepository,
    );

    describe('complete', () => {
        const part = new AssignedSprintPart(
            {
                ...getRandomAssignedSprintPart(),
                state: AssignedSprintPartState.SUBMITTED,
                evaluationProperties: new ReviewProperties({
                    numberOfPeerReviews: 1,
                    numberOfStlReviews: 1,
                }),
            },
            TestObjects.id(),
        );

        beforeEach(() => {
            reset(roadmapManager);
        });

        it('should complete successful mentor review and not complete a part', async () => {
            const feedback = faker.lorem.sentence();
            const correction = {
                id: TestObjects.id().value,
                type: CorrectionType.MENTOR,
                status: CorrectionStatus.SUCCESS,
                assignedSprintPartId: part.getIdOrThrow().value,
            } as CorrectionPersistenceEntity;
            const pastCorrecetions = [
                {
                    id: TestObjects.id().value,
                    type: CorrectionType.STUDENT,
                    status: CorrectionStatus.FAIL,
                    assignedSprintPartId: part.getIdOrThrow().value,
                },
            ] as CorrectionPersistenceEntity[];

            correctionService.submitFeedbackMock.mockResolvedValueOnce(correction);
            correctionRepository.findByAssignedSprintPartMock.mockResolvedValueOnce([...pastCorrecetions, correction]);
            when(roadmapManager.getPart(deepEqual(part.getIdOrThrow()), anything())).thenResolve(part);

            await reviewEvaluationManager.complete(new Id(correction.id), feedback);

            expect(correctionService.submitFeedbackMock).toBeCalledWith(
                correction.id,
                feedback,
                expect.any(Transaction),
            );
            verify(roadmapManager.completePartById(anything(), anything())).never();
        });

        it('should complete successful peer review and not complete a part', async () => {
            const feedback = faker.lorem.sentence();
            const correction = {
                id: TestObjects.id().value,
                type: CorrectionType.STUDENT,
                status: CorrectionStatus.SUCCESS,
                assignedSprintPartId: part.getIdOrThrow().value,
            } as CorrectionPersistenceEntity;
            const pastCorrections = [
                {
                    id: TestObjects.id().value,
                    type: CorrectionType.MENTOR,
                    status: CorrectionStatus.FAIL,
                    assignedSprintPartId: part.getIdOrThrow().value,
                },
            ] as CorrectionPersistenceEntity[];

            correctionService.submitFeedbackMock.mockResolvedValueOnce(correction);
            correctionRepository.findByAssignedSprintPartMock.mockResolvedValueOnce([...pastCorrections, correction]);
            when(roadmapManager.getPart(deepEqual(part.getIdOrThrow()), anything())).thenResolve(part);

            await reviewEvaluationManager.complete(new Id(correction.id), feedback);

            expect(correctionService.submitFeedbackMock).toBeCalledWith(
                correction.id,
                feedback,
                expect.any(Transaction),
            );
            verify(roadmapManager.completePartById(anything(), anything())).never();
        });

        it('should complete successful mentor review and complete a part', async () => {
            const feedback = faker.lorem.sentence();
            const correction = {
                id: TestObjects.id().value,
                type: CorrectionType.MENTOR,
                status: CorrectionStatus.SUCCESS,
                assignedSprintPartId: part.getIdOrThrow().value,
            } as CorrectionPersistenceEntity;
            const pastCorrections = [
                {
                    id: TestObjects.id().value,
                    type: CorrectionType.STUDENT,
                    status: CorrectionStatus.SUCCESS,
                    assignedSprintPartId: part.getIdOrThrow().value,
                },
            ] as CorrectionPersistenceEntity[];

            correctionService.submitFeedbackMock.mockResolvedValueOnce(correction);
            correctionRepository.findByAssignedSprintPartMock.mockResolvedValueOnce([...pastCorrections, correction]);
            when(roadmapManager.getPart(deepEqual(part.getIdOrThrow()), anything())).thenResolve(part);

            await reviewEvaluationManager.complete(new Id(correction.id), feedback);

            expect(correctionService.submitFeedbackMock).toBeCalledWith(
                correction.id,
                feedback,
                expect.any(Transaction),
            );
            verify(roadmapManager.completePartById(deepEqual(part.getIdOrThrow()), anything())).called();
        });

        it('should complete successful peer review and complete a part', async () => {
            const feedback = faker.lorem.sentence();
            const correction = {
                id: TestObjects.id().value,
                type: CorrectionType.STUDENT,
                status: CorrectionStatus.SUCCESS,
                assignedSprintPartId: part.getIdOrThrow().value,
            } as CorrectionPersistenceEntity;
            const pastCorrections = [
                {
                    id: TestObjects.id().value,
                    type: CorrectionType.MENTOR,
                    status: CorrectionStatus.SUCCESS,
                    assignedSprintPartId: part.getIdOrThrow().value,
                },
            ] as CorrectionPersistenceEntity[];

            correctionService.submitFeedbackMock.mockResolvedValueOnce(correction);
            correctionRepository.findByAssignedSprintPartMock.mockResolvedValueOnce([...pastCorrections, correction]);
            when(roadmapManager.getPart(deepEqual(part.getIdOrThrow()), anything())).thenResolve(part);

            await reviewEvaluationManager.complete(new Id(correction.id), feedback);

            expect(correctionService.submitFeedbackMock).toBeCalledWith(
                correction.id,
                feedback,
                expect.any(Transaction),
            );
            verify(roadmapManager.completePartById(deepEqual(part.getIdOrThrow()), anything())).called();
        });

        it('should complete successful peer review with mentor and complete a part', async () => {
            const feedback = faker.lorem.sentence();
            const correction = {
                id: TestObjects.id().value,
                type: CorrectionType.MENTOR,
                status: CorrectionStatus.SUCCESS,
                assignedSprintPartId: part.getIdOrThrow().value,
            } as CorrectionPersistenceEntity;
            const pastCorrections = [
                {
                    id: TestObjects.id().value,
                    type: CorrectionType.MENTOR,
                    status: CorrectionStatus.SUCCESS,
                    assignedSprintPartId: part.getIdOrThrow().value,
                },
            ] as CorrectionPersistenceEntity[];

            correctionService.submitFeedbackMock.mockResolvedValueOnce(correction);
            correctionRepository.findByAssignedSprintPartMock.mockResolvedValueOnce([...pastCorrections, correction]);
            when(roadmapManager.getPart(deepEqual(part.getIdOrThrow()), anything())).thenResolve(part);

            await reviewEvaluationManager.complete(new Id(correction.id), feedback);

            expect(correctionService.submitFeedbackMock).toBeCalledWith(
                correction.id,
                feedback,
                expect.any(Transaction),
            );
            verify(roadmapManager.completePartById(deepEqual(part.getIdOrThrow()), anything())).called();
        });

        it('should complete unsuccessful mentor review', async () => {
            const feedback = faker.lorem.sentence();
            const correction = {
                id: TestObjects.id().value,
                type: CorrectionType.MENTOR,
                status: CorrectionStatus.FAIL,
                assignedSprintPartId: part.getIdOrThrow().value,
            } as CorrectionPersistenceEntity;
            const pastCorrections = [
                {
                    id: TestObjects.id().value,
                    type: CorrectionType.STUDENT,
                    status: CorrectionStatus.SUCCESS,
                    assignedSprintPartId: part.getIdOrThrow().value,
                },
            ] as CorrectionPersistenceEntity[];

            correctionService.submitFeedbackMock.mockResolvedValueOnce(correction);
            correctionRepository.findByAssignedSprintPartMock.mockResolvedValueOnce([...pastCorrections, correction]);
            when(roadmapManager.getPart(anything(), anything())).thenResolve(part);

            await reviewEvaluationManager.complete(new Id(correction.id), feedback);

            expect(correctionService.submitFeedbackMock).toBeCalledWith(
                correction.id,
                feedback,
                expect.any(Transaction),
            );
            verify(roadmapManager.completePartById(anything(), anything())).never();
        });

        it('should complete unsuccessful peer review', async () => {
            const feedback = faker.lorem.sentence();
            const correction = {
                id: TestObjects.id().value,
                type: CorrectionType.STUDENT,
                status: CorrectionStatus.FAIL,
                assignedSprintPartId: part.getIdOrThrow().value,
            } as CorrectionPersistenceEntity;
            const pastCorrections = [
                {
                    id: TestObjects.id().value,
                    type: CorrectionType.MENTOR,
                    status: CorrectionStatus.SUCCESS,
                    assignedSprintPartId: part.getIdOrThrow().value,
                },
            ] as CorrectionPersistenceEntity[];

            correctionService.submitFeedbackMock.mockResolvedValueOnce(correction);
            correctionRepository.findByAssignedSprintPartMock.mockResolvedValueOnce([...pastCorrections, correction]);
            when(roadmapManager.getPart(anything(), anything())).thenResolve(part);

            await reviewEvaluationManager.complete(new Id(correction.id), feedback);

            expect(correctionService.submitFeedbackMock).toBeCalledWith(
                correction.id,
                feedback,
                expect.any(Transaction),
            );
            verify(roadmapManager.completePartById(anything(), anything())).never();
        });
    });
});
