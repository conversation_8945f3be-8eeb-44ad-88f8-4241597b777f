import { Type } from 'class-transformer';
import { IsEnum, IsOptional, ValidateNested } from 'class-validator';
import { EvaluationType } from '../../../../../../education/sprints/domain/EvaluationProperties';
import QuizProperties from '../../../../../../education/sprints/domain/QuizProperties';
import ReviewProperties from '../../../../../../education/sprints/domain/ReviewProperties';
import { EvaluationSummary } from '../../../services/EvaluationManager';
import EvaluationPropertiesDTO from './EvaluationPropertiesDTO';
import QuizSummaryDTO from './QuizSummaryDTO';
import ReviewDTO from './ReviewDTO';
import { FixNestedArrayJsonSchemaReference } from '../../../../../../core/controllers/dto/decorators/FixNestedJsonSchemaReference';

export default class EvaluationDTO {
    @IsEnum(EvaluationType)
    type: EvaluationType;

    @Type(() => EvaluationPropertiesDTO)
    @ValidateNested()
    properties: EvaluationPropertiesDTO;

    @IsOptional()
    @Type(() => QuizSummaryDTO)
    @ValidateNested({ each: true })
    @FixNestedArrayJsonSchemaReference(QuizSummaryDTO)
    quizzes?: QuizSummaryDTO[];

    @IsOptional()
    @Type(() => ReviewDTO)
    @ValidateNested({ each: true })
    @FixNestedArrayJsonSchemaReference(ReviewDTO)
    reviews?: ReviewDTO[];

    static fromSummary(summary: EvaluationSummary): EvaluationDTO {
        if (summary.properties instanceof QuizProperties) {
            const dto = new EvaluationDTO();
            dto.type = summary.properties.type;
            dto.properties = EvaluationPropertiesDTO.fromQuizProperties(summary.properties);
            dto.quizzes = summary.quizzes?.map(QuizSummaryDTO.fromQuiz);

            return dto;
        }

        if (summary.properties instanceof ReviewProperties) {
            const dto = new EvaluationDTO();
            dto.type = summary.properties.type;
            dto.properties = EvaluationPropertiesDTO.fromReviewProperties(summary.properties);
            dto.reviews = summary.reviews?.map((review) => ReviewDTO.fromReview(review));

            return dto;
        }

        throw new Error('Unsupported evaluation type');
    }
}
