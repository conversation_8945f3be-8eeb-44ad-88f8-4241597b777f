import { IsBoolean, IsInt, IsOptional } from 'class-validator';
import QuizProperties from '../../../../../../education/sprints/domain/QuizProperties';
import ReviewProperties from '../../../../../../education/sprints/domain/ReviewProperties';

export default class EvaluationPropertiesDTO {
    @IsOptional()
    @IsInt()
    numberOfQuestions?: number;

    @IsOptional()
    @IsInt()
    numberOfPeerReviews?: number;

    @IsOptional()
    @IsInt()
    numberOfStlReviews?: number;

    @IsOptional()
    @IsBoolean()
    shouldSuggestStlWhenNoPeer?: boolean;

    @IsOptional()
    @IsInt()
    cost?: number;

    static fromQuizProperties(properties: QuizProperties): EvaluationPropertiesDTO {
        const dto = new EvaluationPropertiesDTO();
        dto.numberOfQuestions = properties.numberOfQuestions;

        return dto;
    }

    static fromReviewProperties(properties: ReviewProperties): EvaluationPropertiesDTO {
        const dto = new EvaluationPropertiesDTO();
        dto.numberOfPeerReviews = properties.numberOfPeerReviews;
        dto.numberOfStlReviews = properties.numberOfStlReviews;
        dto.shouldSuggestStlWhenNoPeer = properties.shouldSuggestStlWhenNoPeer;
        dto.cost = properties.cost;

        return dto;
    }
}
