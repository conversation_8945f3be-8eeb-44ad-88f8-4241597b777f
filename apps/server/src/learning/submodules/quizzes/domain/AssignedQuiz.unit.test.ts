import { faker } from '@faker-js/faker';
import moment from 'moment';
import Arrays from '../../../../core/collections/Arrays';
import IllegalArgumentError from '../../../../core/errors/IllegalArgumentError';
import AssignedQuiz, { AssignedQuizState } from './AssignedQuiz';
import AssignedQuizAnswer from './AssignedQuizAnswer';
import AssignedQuizQuestion from './AssignedQuizQuestion';
import TestObjects from '../../../../test-toolkit/shared/TestObjects';

function getRandomQuizAnswer(index: number): AssignedQuizAnswer {
    return new AssignedQuizAnswer(
        {
            isCorrect: index === 0,
            text: faker.lorem.sentence(),
            isSelected: false,
            position: index,
        },
        TestObjects.uniqueId(),
    );
}

function getRandomQuizQuestion(): AssignedQuizQuestion {
    return new AssignedQuizQuestion(
        {
            text: faker.lorem.sentence(),
            answers: Arrays.shuffle(Arrays.stream(faker.number.int({ min: 2, max: 20 })).map(getRandomQuizAnswer)),
            quizQuestionId: TestObjects.id(),
        },
        TestObjects.uniqueId(),
    );
}

function getRandomQuiz(): AssignedQuiz {
    const questions = [getRandomQuizQuestion(), getRandomQuizQuestion()];

    return new AssignedQuiz({
        questions,
        userId: TestObjects.uniqueId(),
        assignedPartId: TestObjects.uniqueId(),
        state: AssignedQuizState.PENDING,
        possibleEvaluation: questions.length,
    });
}

describe('AssignedQuiz', () => {
    describe('begin', () => {
        it('should do nothing if quiz is already in progress', () => {
            const quiz = new AssignedQuiz({
                ...getRandomQuiz(),
                state: AssignedQuizState.IN_PROGRESS,
                endsAt: moment().add(1, 'minutes').toDate(),
                startedAt: moment().subtract(1, 'minutes').toDate(),
            });

            const quizAfter = quiz.begin();

            expect(quizAfter).toEqual(quiz);
        });

        it('should not start if quiz is not pending', () => {
            const quiz = new AssignedQuiz({
                ...getRandomQuiz(),
                state: AssignedQuizState.COMPLETED,
                endsAt: moment().add(1, 'minutes').toDate(),
                startedAt: moment().subtract(1, 'minutes').toDate(),
                completedAt: moment().subtract(1, 'minutes').toDate(),
            });

            expect(() => quiz.begin()).toThrow(new IllegalArgumentError('Quiz is not pending'));
        });

        it('should start quiz if quiz is pending', () => {
            const quiz = getRandomQuiz();

            const quizAfter = quiz.begin();

            expect(quizAfter.isInProgress()).toBeTruthy();
            expect(quizAfter.startedAt).toBeDefined();
            expect(quizAfter.endsAt).toBeDefined();
            expect((quizAfter.startedAt as Date).getTime() + AssignedQuiz.DURATION_MS).toBe(
                (quizAfter.endsAt as Date).getTime(),
            );
        });
    });

    describe('selectAnswer', () => {
        it('should select answer on a question without selected answer', () => {
            const quizBefore = getRandomQuiz().begin();

            const quizAfter = quizBefore.selectAnswer(
                quizBefore.questions[0].getIdOrThrow(),
                quizBefore.questions[0].answers[0].getIdOrThrow(),
            );

            expect(quizAfter.questions[0].answers[0].isSelected).toBeTruthy();
            expect(quizAfter.questions[0].answers.slice(1).every((a) => !a.isSelected)).toBeTruthy();
            expect(quizAfter.questions[1].answers.every((a) => !a.isSelected)).toBeTruthy();
        });

        it('should select answer on a question with selected answer', () => {
            let quizBefore = getRandomQuiz().begin();
            quizBefore = quizBefore.selectAnswer(
                quizBefore.questions[0].getIdOrThrow(),
                quizBefore.questions[0].answers[0].getIdOrThrow(),
            );

            const quizAfter = quizBefore.selectAnswer(
                quizBefore.questions[0].getIdOrThrow(),
                quizBefore.questions[0].answers[1].getIdOrThrow(),
            );

            expect(quizAfter.questions[0].answers[0].isSelected).toBeFalsy();
            expect(quizAfter.questions[0].answers[1].isSelected).toBeTruthy();
        });

        it('should select the same answer twice', () => {
            const quizBefore = getRandomQuiz().begin();

            const quizAfter = quizBefore
                .selectAnswer(quizBefore.questions[0].getIdOrThrow(), quizBefore.questions[0].answers[0].getIdOrThrow())
                .selectAnswer(
                    quizBefore.questions[0].getIdOrThrow(),
                    quizBefore.questions[0].answers[0].getIdOrThrow(),
                );

            expect(quizAfter.questions[0].answers[0].isSelected).toBeTruthy();
        });

        it('should not select non-existing answer', () => {
            const quizBefore = getRandomQuiz().begin();

            expect(() =>
                quizBefore.selectAnswer(quizBefore.questions[0].getIdOrThrow(), TestObjects.uniqueId()),
            ).toThrow(new IllegalArgumentError('Answer not found'));
        });

        it('should not select answer on a completed quiz', () => {
            const quiz = new AssignedQuiz({
                ...getRandomQuiz().begin(),
                state: AssignedQuizState.COMPLETED,
                completedAt: moment().subtract(1, 'minutes').toDate(),
            });

            expect(() =>
                quiz.selectAnswer(quiz.questions[0].getIdOrThrow(), quiz.questions[0].answers[0].getIdOrThrow()),
            ).toThrow(new IllegalArgumentError('Quiz is not in progress'));
        });

        it('should not select answer on an expired quiz', () => {
            const quiz = new AssignedQuiz({
                ...getRandomQuiz().begin(),
                endsAt: moment().subtract(1, 'minutes').toDate(),
            });

            expect(() =>
                quiz.selectAnswer(quiz.questions[0].getIdOrThrow(), quiz.questions[0].answers[0].getIdOrThrow()),
            ).toThrow(new IllegalArgumentError('Quiz time has expired'));
        });
    });

    describe('complete', () => {
        it('should complete quiz with all correct answers', () => {
            const quizBefore = getRandomQuiz().begin();

            const quizAfter = quizBefore
                .selectAnswer(
                    quizBefore.questions[0].getIdOrThrow(),
                    (quizBefore.questions[0].answers.find((a) => a.isCorrect) as AssignedQuizAnswer).getIdOrThrow(),
                )
                .selectAnswer(
                    quizBefore.questions[1].getIdOrThrow(),
                    (quizBefore.questions[1].answers.find((a) => a.isCorrect) as AssignedQuizAnswer).getIdOrThrow(),
                )
                .complete();

            expect(quizAfter.isCompleted()).toBeTruthy();
            expect(quizAfter.completedAt).toBeDefined();
            expect(quizAfter.evaluation).toBe(2);
        });

        it('should complete quiz with all incorrect answers', () => {
            const quizBefore = getRandomQuiz().begin();

            const quizAfter = quizBefore
                .selectAnswer(
                    quizBefore.questions[0].getIdOrThrow(),
                    (quizBefore.questions[0].answers.find((a) => !a.isCorrect) as AssignedQuizAnswer).getIdOrThrow(),
                )
                .selectAnswer(
                    quizBefore.questions[1].getIdOrThrow(),
                    (quizBefore.questions[1].answers.find((a) => !a.isCorrect) as AssignedQuizAnswer).getIdOrThrow(),
                )
                .complete();

            expect(quizAfter.isCompleted()).toBeTruthy();
            expect(quizAfter.completedAt).toBeDefined();
            expect(quizAfter.evaluation).toBe(0);
        });

        it('should complete quiz with mixed answers', () => {
            const quizBefore = getRandomQuiz().begin();

            const quizAfter = quizBefore
                .selectAnswer(
                    quizBefore.questions[0].getIdOrThrow(),
                    (quizBefore.questions[0].answers.find((a) => a.isCorrect) as AssignedQuizAnswer).getIdOrThrow(),
                )
                .selectAnswer(
                    quizBefore.questions[1].getIdOrThrow(),
                    (quizBefore.questions[1].answers.find((a) => !a.isCorrect) as AssignedQuizAnswer).getIdOrThrow(),
                )
                .complete();

            expect(quizAfter.isCompleted()).toBeTruthy();
            expect(quizAfter.completedAt).toBeDefined();
            expect(quizAfter.evaluation).toBe(1);
        });

        it('should not complete quiz with unanswered questions before deadline', () => {
            const quiz = getRandomQuiz().begin();

            expect(() => quiz.complete()).toThrow();
        });

        it('should complete quiz after deadline', () => {
            const quizBefore = new AssignedQuiz({
                ...getRandomQuiz().begin(),
                endsAt: moment().subtract(1, 'minutes').toDate(),
            });

            const quizAfter = quizBefore.complete();

            expect(quizAfter.isCompleted()).toBeTruthy();
            expect(quizAfter.completedAt).toBeDefined();
            expect(quizAfter.evaluation).toBe(0);
        });
    });
});
