import DomainEntity from '../../../../core/domain/DomainEntity';
import Id from '../../../../core/domain/value-objects/Id';
import ArgumentValidation from '../../../../core/utils/validation/ArgumentValidation';
import AssignedQuizAnswer from './AssignedQuizAnswer';

interface AssignedQuizQuestionProperties {
    text: string;
    image?: string;
    answers: ReadonlyArray<AssignedQuizAnswer>;
    quizQuestionId?: Id;
}

export default class AssignedQuizQuestion extends DomainEntity {
    static readonly MIN_ANSWERS = 2;

    static readonly MAX_ANSWERS = 20;

    readonly text: string;

    readonly image?: string;

    readonly answers: ReadonlyArray<AssignedQuizAnswer>;

    readonly quizQuestionId?: Id;

    constructor(properties: AssignedQuizQuestionProperties, id?: Id) {
        super(id);

        const text = properties.text?.trim();
        ArgumentValidation.assert.notEmpty(properties.text?.trim(), 'Question text is required');

        this.text = text;
        this.image = properties.image?.trim();
        ArgumentValidation.assert.between(
            properties.answers?.length,
            AssignedQuizQuestion.MIN_ANSWERS,
            AssignedQuizQuestion.MAX_ANSWERS,
            `Question must have between ${AssignedQuizQuestion.MIN_ANSWERS} and ${AssignedQuizQuestion.MAX_ANSWERS} answers`,
        );
        ArgumentValidation.assert.true(
            properties.answers?.filter((answer) => answer.isCorrect)?.length === 1,
            'Question must have exactly one correct answer',
        );
        this.answers = [...properties.answers];
        this.quizQuestionId = properties.quizQuestionId;
    }

    selectAnswer(answerId: Id): AssignedQuizQuestion {
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        const answer = this.answers.find((a) => a.id.equals(answerId));
        ArgumentValidation.assert.defined(answer, 'Answer not found');

        return new AssignedQuizQuestion(
            {
                ...this,
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                answers: this.answers.map((a) => (a.id.equals(answerId) ? a.select() : a.unselect())),
            },
            this.id,
        );
    }

    hasSelectedAnswer(): boolean {
        return this.answers.some((a) => a.isSelected);
    }

    getSelectedAnswer(): AssignedQuizAnswer | undefined {
        return this.answers.find((a) => a.isSelected);
    }
}
