import Id from '../../../../../core/domain/value-objects/Id';
import DiscordOnboardingChecker from '../../../../../discord/services/onboarding/DiscordOnboardingChecker';
import SinglePreconditionChecker from './SinglePreconditionChecker';

export default class DiscordPreconditionC<PERSON><PERSON> implements SinglePreconditionChecker {
    constructor(private readonly discordOnboardingChecker: DiscordOnboardingChecker) {}

    async isSatisfied(userId: Id): Promise<boolean> {
        return await this.discordOnboardingChecker.isUserAuthorized(userId);
    }
}
