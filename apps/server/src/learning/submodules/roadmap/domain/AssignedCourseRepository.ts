import Id from '../../../../core/domain/value-objects/Id';
import Transaction from '../../../../core/infrastructure/Transaction';
import AssignedCourse from './AssignedCourse';

interface AssignedCourseRepository {
    getByUser(userId: Id, tx?: Transaction): Promise<AssignedCourse | undefined>;

    save(assignedCourse: AssignedCourse, tx?: Transaction): Promise<AssignedCourse>;

    deleteByUser(userId: Id, tx?: Transaction): Promise<void>;

    deleteAll(tx?: Transaction): Promise<void>;
}

export default AssignedCourseRepository;
