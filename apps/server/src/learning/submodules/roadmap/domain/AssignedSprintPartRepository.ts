import Id from '../../../../core/domain/value-objects/Id';
import Transaction from '../../../../core/infrastructure/Transaction';
import AssignedSprintPart from './AssignedSprintPart';

interface AssignedSprintPartRepository {
    get(id: Id, tx?: Transaction): Promise<AssignedSprintPart | undefined>;

    getByIds(ids: Id[], tx?: Transaction): Promise<AssignedSprintPart[]>;

    getByStudentAndSprintPartId(
        studentId: Id,
        sprintPartId: Id,
        tx?: Transaction,
    ): Promise<AssignedSprintPart | undefined>;

    /**
     * Returns sprint part ids, which suggested solution available for user.
     * */
    findPartIdsWithAvailableSuggestedSolutionForUser(userId: Id, tx?: Transaction): Promise<Id[]>;
}

export default AssignedSprintPartRepository;
