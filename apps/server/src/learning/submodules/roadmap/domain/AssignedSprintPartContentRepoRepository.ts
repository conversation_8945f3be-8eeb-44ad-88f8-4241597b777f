import Repository from '../../../../core/domain/Repository';
import Id from '../../../../core/domain/value-objects/Id';
import Transaction from '../../../../core/infrastructure/Transaction';
import AssignedSprintPartContentRepo from './AssignedSprintPartContentRepo';

interface AssignedSprintPartContentRepoRepository extends Repository<AssignedSprintPartContentRepo> {
    getByAssignedSprintPart(assignedSprintPartId: Id, tx?: Transaction): Promise<AssignedSprintPartContentRepo>;
    getByUserId(userId: Id, tx?: Transaction): Promise<AssignedSprintPartContentRepo[]>;
    getPartIdsForTheReposWithWriteAccess(userId: Id, tx?: Transaction): Promise<Id[]>;
}

export default AssignedSprintPartContentRepoRepository;
