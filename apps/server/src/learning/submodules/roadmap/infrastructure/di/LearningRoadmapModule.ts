import Container from 'typedi';
import Announcer from '../../../../../announcements/services/Announcer';
import { Configuration } from '../../../../../config/Configuration';
import TransactionManager from '../../../../../core/infrastructure/TransactionManager';
import { CorrectionsModule } from '../../../../../corrections/infrastructure/di/CorrectionsModule';
import { SprintsModule } from '../../../../../education/sprints/infrastructure/di/SprintsModule';
import { SprintPartReviewerPreconditionsService } from '../../../../../education/sprints/services/SprintPartReviewerPreconditionsService';
import { GoogleProfileModule } from '../../../../../google-account/infrastructure/di/GoogleProfileModule';
import { GoogleDriveModule } from '../../../../../google-drive/infrastracture/di/GoogleDriveModule';
import { KVStoreModule } from '../../../../../kv-store/infrastructure/di/KVStoreModule';
import { LoggingModule } from '../../../../../logging/infrastructure/di/LoggingModule';
import { LoggingService } from '../../../../../logging/services/LoggingService';
import { NotificationManager } from '../../../../../notifications/NotificationManager';
import { QueueModule } from '../../../../../queue/infrastructure/di/QueueModule';
import { UsersAccountsModule } from '../../../../../users/accounts/infrastructure/di/UsersAccountsModule';
import { UsersStudentsSettingsModule } from '../../../../../users/students/settings/infrastructure/di/UsersStudentsSettingsModule';
import LearningEventPublisher from '../../../../services/LearningEventPublisher';
import SprintCompletionCommunityAnnouncementTrigger from '../../controllers/application-events/SprintCompletionCommunityAnnouncementTrigger';
import { GrantAccessToAllSubmissionsCommandWorker } from '../../controllers/commands/GrantAccessToAllSubmissionsCommandWorker';
import { GrantAccessToAllSuggestedSolutionsCommandWorker } from '../../controllers/commands/GrantAccessToAllSuggestedSolutionsCommandWorker';
import { GrantAccessToSubmissionCommandWorker } from '../../controllers/commands/GrantAccessToSubmissionCommandWorker';
import { GrantAccessToSuggestedSolutionCommandWorker } from '../../controllers/commands/GrantAccessToSuggestedSolutionCommandWorker';
import { RefreshPendingPreconditionsCommandWorker } from '../../controllers/commands/RefreshPendingPreconditionsCommandWorker';
import { AssignedSprintPartPrecondition } from '../../domain/AssignedSprintPart';
import { AssignedSprintPartService } from '../../services/AssignedSprintPartService';
import DiscordPreconditionChecker from '../../services/checkers/DiscordPreconditionChecker';
import GithubPreconditionChecker from '../../services/checkers/GithubPreconditionChecker';
import { GoogleProfilePreconditionChecker } from '../../services/checkers/GoogleProfilePreconditionChecker';
import SinglePreconditionChecker from '../../services/checkers/SinglePreconditionChecker';
import NotebookPreviewService from '../../services/NotebookPreviewService';
import PreconditionsChecker from '../../services/PreconditionsChecker';
import RoadmapAccessor from '../../services/RoadmapAccessor';
import RoadmapManager from '../../services/RoadmapManager';
import SprintCompletionCommunityAnnouncer from '../../services/SprintCompletionCommunityAnnouncer';
import { SprintPartLearningMaterialsAccessor } from '../../services/SprintPartLearningMaterialsAccessor';
import { StudentSkillsFinder } from '../../services/StudentSkillsFinder';
import StudentSprintPartAccessor from '../../services/StudentSprintPartAccessor';
import { GithubSubmissionManager } from '../../services/submission/implementations/GithubSubmissionManager';
import { GoogleDriveSubmissionManager } from '../../services/submission/implementations/GoogleDriveSubmissionManager';
import SubmissionManager from '../../services/submission/SubmissionManager';
import AssignedCourseTypeormRepository from '../db/AssignedCourseTypeormRepository';
import AssignedSprintPartContentRepoTypeormRepository from '../db/AssignedSprintPartContentRepoTypeormRepository';
import { AssignedSprintPartGoogleDriveFolderRepository } from '../db/AssignedSprintPartGoogleDriveFolderRepository';
import AssignedSprintPartTypeormRepository from '../db/AssignedSprintPartTypeormRepository';
import AssignedSprintTypeormRepository from '../db/AssignedSprintTypeormRepository';
import { LearningMilestonesModule } from '../../../../../learning-milestones/infrastructure/di/LearningMilestonesModule';
import { ApplicationEventBrokerModule } from '../../../../../events/infrastructure/di/ApplicationEventBrokerModule';
import { GithubModule } from '../../../../../github/infrastructure/di/GithubModule';
import { DiscordModule } from '../../../../../discord/infrastructure/di/DiscordModule';
import { CourseModule } from '../../../../../education/courses/infrastructure/di/CourseModule';
import { ModulesModule } from '../../../../../education/modules/infrastructure/di/ModulesModule';
import { ModuleWithControllers } from '../../../../../di/types/ApplicationModule';
import RoadmapController from '../../controllers/http/RoadmapController';
import StudentSlotsController from '../../controllers/http/StudentSlotsController';
import StudentSprintPartsController from '../../controllers/http/StudentSprintPartsController';
import StudentSprintPartContentController from '../../controllers/http/StudentSprintPartContentController';
import NotebookPreviewController from '../../controllers/http/NotebookPreviewController';
import { AnalyticsModule } from '../../../../../analytics/infrastructure/di/AnalyticsModule';
import { AssignedModuleService } from '../../services/AssignedModuleService';
import AssignedModuleRepository from '../db/AssignedModuleRepository';
import StudentAssignedModuleController from '../../controllers/http/StudentAssignedModuleController';

interface LearningRoadmapModuleInitParams {
    configuration: Configuration;
    loggingModule: LoggingModule;
    transactionManager: TransactionManager;
    usersAccountsModule: UsersAccountsModule;
    courseModule: CourseModule;
    modulesModule: ModulesModule;
    sprintsModule: SprintsModule;
    usersStudentsSettingsModule: UsersStudentsSettingsModule;
    githubModule: GithubModule;
    discordModule: DiscordModule;
    announcer: Announcer;
    analyticsModule: AnalyticsModule;
    googleProfileModule: GoogleProfileModule;
    queueModule: QueueModule;
    learningMilestonesModule: LearningMilestonesModule;
    applicationEventBrokerModule: ApplicationEventBrokerModule;
}

export class LearningRoadmapModule implements ModuleWithControllers {
    constructor(
        readonly roadmapAccessor: RoadmapAccessor,
        readonly roadmapManager: RoadmapManager,
        readonly studentSprintPartAccessor: StudentSprintPartAccessor,
        readonly submissionManager: SubmissionManager,
        readonly studentSkillsFinder: StudentSkillsFinder,
        readonly assignedSprintPartService: AssignedSprintPartService,
        readonly preconditionsChecker: PreconditionsChecker,
        private readonly queueModule: QueueModule,
        private readonly loggingService: LoggingService,
        private readonly transactionManager: TransactionManager,
    ) {}

    static init(params: LearningRoadmapModuleInitParams): LearningRoadmapModule {
        const {
            configuration,
            loggingModule,
            transactionManager,
            usersAccountsModule,
            courseModule,
            modulesModule,
            sprintsModule,
            usersStudentsSettingsModule,
            githubModule,
            discordModule,
            announcer,
            analyticsModule,
            learningMilestonesModule,
            googleProfileModule,
            queueModule,
            applicationEventBrokerModule,
        } = params;
        const assignedCourseRepository = new AssignedCourseTypeormRepository(transactionManager);
        const assignedSprintRepository = new AssignedSprintTypeormRepository(transactionManager);
        const assignedSprintPartRepository = new AssignedSprintPartTypeormRepository(transactionManager);
        const assignedSprintPartContentRepoRepository = new AssignedSprintPartContentRepoTypeormRepository(
            transactionManager,
        );
        const assignedSprintPartGoogleDriveFolderRepository = new AssignedSprintPartGoogleDriveFolderRepository(
            transactionManager,
        );
        const assignedModuleRepository = new AssignedModuleRepository(transactionManager);

        const googleDrive = GoogleDriveModule.init(
            configuration,
            {
                credentials: JSON.parse(atob(configuration.submissions.googleDrive.credentials)),
                scopes: configuration.submissions.googleDrive.scopes.split(','),
            },
            loggingModule.loggingService,
            githubModule.githubFileManager,
            queueModule,
        );

        const kvStoreModule = KVStoreModule.init({
            namespace: 'google-drive',
            configuration,
            loggingService: loggingModule.loggingService,
        });

        const assignedSprintPartService = new AssignedSprintPartService(
            assignedSprintPartRepository,
            sprintsModule.sprintPartFinder,
        );

        const roadmapAccessor = new RoadmapAccessor(
            assignedCourseRepository,
            assignedSprintRepository,
            assignedSprintPartRepository,
        );
        const preconditionChecker = new PreconditionsChecker(
            new Map<AssignedSprintPartPrecondition, SinglePreconditionChecker>([
                [
                    AssignedSprintPartPrecondition.DISCORD,
                    new DiscordPreconditionChecker(discordModule.discordOnboardingChecker),
                ],
                [
                    AssignedSprintPartPrecondition.GITHUB,
                    new GithubPreconditionChecker(githubModule.githubProfileFinder),
                ],
                [
                    AssignedSprintPartPrecondition.GOOGLE_ACCOUNT,
                    new GoogleProfilePreconditionChecker(googleProfileModule.googleProfileService),
                ],
            ]),
        );
        const studentSprintPartAccessor = new StudentSprintPartAccessor(
            roadmapAccessor,
            modulesModule.moduleFinder,
            sprintsModule.sprintPartFinder,
        );
        const githubSubmissionManager = new GithubSubmissionManager(
            configuration,
            usersAccountsModule.userAccountFinder,
            sprintsModule.sprintPartFinder,
            githubModule.githubProfileFinder,
            githubModule.githubRepoManager,
            assignedSprintPartContentRepoRepository,
        );

        const googleDriveSubmissionManager = new GoogleDriveSubmissionManager(
            configuration,
            googleDrive.googleDriveGithubClonerService,
            sprintsModule.sprintPartFinder,
            googleDrive.googleDriveService,
            loggingModule.loggingService.createLogger(GoogleDriveSubmissionManager.name),
            usersAccountsModule.userAccountFinder,
            assignedSprintPartGoogleDriveFolderRepository,
            googleProfileModule.googleProfileService,
            kvStoreModule.kvStoreService,
        );

        const submissionManager = new SubmissionManager(githubSubmissionManager, googleDriveSubmissionManager);

        const materialsCache = KVStoreModule.init({
            namespace: 'learning-materials-cache',
            configuration,
            loggingService: loggingModule.loggingService,
        });

        const sprintPartLearningMaterialsAccessor = new SprintPartLearningMaterialsAccessor(
            sprintsModule.sprintPartFinder,
            githubModule.githubFileManager,
            assignedSprintPartService,
            materialsCache.kvStoreService,
            loggingModule.loggingService.createLogger(SprintPartLearningMaterialsAccessor.name),
        );

        const roadmapManager = new RoadmapManager(
            transactionManager,
            assignedCourseRepository,
            assignedSprintRepository,
            assignedSprintPartRepository,
            courseModule.courseFinder,
            modulesModule.moduleFinder,
            usersAccountsModule.userAccountFinder,
            usersStudentsSettingsModule.studentSettingsFinder,
            preconditionChecker,
            submissionManager,
            new LearningEventPublisher(configuration, applicationEventBrokerModule.integrationEventPublisher),
            applicationEventBrokerModule.applicationEventBroker,
            analyticsModule.analytics,
            learningMilestonesModule.learningMilestonesService,
        );
        const sprintCompletionCommunityAnnouncementTrigger = new SprintCompletionCommunityAnnouncementTrigger(
            loggingModule.loggingService,
            new SprintCompletionCommunityAnnouncer(loggingModule.loggingService, sprintsModule.sprintFinder, announcer),
        );
        const studentSkillsFinder = new StudentSkillsFinder(roadmapAccessor);

        const notebookPreviewService = new NotebookPreviewService(githubModule.githubFileManager);
        const assignedModuleService = new AssignedModuleService(transactionManager, assignedModuleRepository);

        Container.set(RoadmapAccessor, roadmapAccessor);
        Container.set(RoadmapManager, roadmapManager);
        Container.set(StudentSprintPartAccessor, studentSprintPartAccessor);
        Container.set(PreconditionsChecker, preconditionChecker);
        Container.set(SprintPartLearningMaterialsAccessor, sprintPartLearningMaterialsAccessor);
        Container.set(SprintCompletionCommunityAnnouncementTrigger, sprintCompletionCommunityAnnouncementTrigger);
        Container.set(NotebookPreviewService, notebookPreviewService);
        Container.set(SubmissionManager, submissionManager);
        Container.set(AssignedSprintPartService, assignedSprintPartService);
        Container.set(AssignedModuleService, assignedModuleService);
        return new LearningRoadmapModule(
            roadmapAccessor,
            roadmapManager,
            studentSprintPartAccessor,
            submissionManager,
            studentSkillsFinder,
            assignedSprintPartService,
            preconditionChecker,
            queueModule,
            loggingModule.loggingService,
            transactionManager,
        );
    }

    getName(): string {
        return LearningRoadmapModule.name;
    }

    getControllers(): any[] {
        return [
            RoadmapController,
            StudentSlotsController,
            StudentSprintPartsController,
            StudentSprintPartContentController,
            NotebookPreviewController,
            StudentAssignedModuleController,
        ];
    }

    registerWorkers(
        correctionsModule: CorrectionsModule,
        usersAccountsModule: UsersAccountsModule,
        notificationManager: NotificationManager,
        sprintPartReviewerPreconditionsService: SprintPartReviewerPreconditionsService,
    ): void {
        this.queueModule.queueWorkerRegistry.registerWorkers([
            new RefreshPendingPreconditionsCommandWorker(
                this.roadmapManager,
                usersAccountsModule.userAccountFinder,
                notificationManager,
                sprintPartReviewerPreconditionsService,
                this.preconditionsChecker,
                this.transactionManager,
                this.loggingService.createLogger(RefreshPendingPreconditionsCommandWorker.name),
            ),
            new GrantAccessToSubmissionCommandWorker(this.roadmapAccessor, this.submissionManager),
            new GrantAccessToAllSubmissionsCommandWorker(
                this.submissionManager,
                correctionsModule.correctionService,
                this.queueModule.queueService,
                this.loggingService.createLogger(GrantAccessToAllSubmissionsCommandWorker.name),
            ),
            new GrantAccessToAllSuggestedSolutionsCommandWorker(
                this.assignedSprintPartService,
                this.queueModule.queueService,
                this.loggingService.createLogger(GrantAccessToAllSuggestedSolutionsCommandWorker.name),
            ),
            new GrantAccessToSuggestedSolutionCommandWorker(this.submissionManager),
        ]);
    }
}
