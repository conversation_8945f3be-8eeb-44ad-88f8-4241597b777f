import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../../../core/infrastructure/db/TypeormPersistenceEntity';
import AssignedSprintPartPersistenceEntity from './AssignedSprintPartPersistenceEntity';
import AssignedSprintPartGoogleDriveFolder from '../../domain/AssignedSprintPartGoogleDriveFolder';
import Id from '../../../../../core/domain/value-objects/Id';
import { GoogleDriveId } from '../../../../../google-drive/types/GoogleDriveId';

@Entity('assigned_sprint_part_google_drive_folder')
export default class AssignedSprintPartGoogleDriveFolderPersistenceEntity extends TypeormPersistenceEntity {
    @Column({ unique: true })
    @RelationId((self: AssignedSprintPartGoogleDriveFolderPersistenceEntity) => self.assignedSprintPart)
    assignedSprintPartId: number;

    @Column()
    folderId: GoogleDriveId;

    @ManyToOne(() => AssignedSprintPartPersistenceEntity, {
        onDelete: 'CASCADE',
    })
    private assignedSprintPart: AssignedSprintPartPersistenceEntity;

    toDomain(): AssignedSprintPartGoogleDriveFolder {
        return new AssignedSprintPartGoogleDriveFolder(
            new Id(this.assignedSprintPartId),
            this.folderId,
            // @ts-expect-error TS(2345) FIXME: Argument of type 'number | undefined' is not assig... Remove this comment to see the full error message
            new Id(this.id),
        );
    }

    static fromDomain(
        domain: AssignedSprintPartGoogleDriveFolder,
    ): AssignedSprintPartGoogleDriveFolderPersistenceEntity {
        const entity = new AssignedSprintPartGoogleDriveFolderPersistenceEntity();
        entity.id = domain.id?.value;
        entity.assignedSprintPartId = domain.assignedSprintPartId.value;
        entity.folderId = domain.folderId;

        return entity;
    }
}
