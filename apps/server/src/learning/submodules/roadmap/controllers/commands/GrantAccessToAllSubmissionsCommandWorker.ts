import { Job } from 'pg-boss';
import { AppQueues } from '../../../../../queue/domain/AppQueues';
import { QueueWorker } from '../../../../../queue/domain/QueueWorker';

import CorrectionService from '../../../../../corrections/services/CorrectionService';
import { CommandWithOptions, QueueService } from '../../../../../queue/QueueService';
import Logger from '../../../../../utils/logger/Logger';
import SubmissionManager from '../../services/submission/SubmissionManager';
import { StudentSubmissionContentPermission } from '../../services/submission/types/StudentSubmissionContentPermission';
import {
    GrantAccessToAllSubmissionsCommand,
    GrantAccessToAllSubmissionsCommandParams,
} from './dto/GrantAccessToAllSubmissionsCommand';
import { GrantAccessToSubmissionCommand } from './dto/GrantAccessToSubmissionCommand';

export class GrantAccessToAllSubmissionsCommandWorker implements QueueWorker {
    readonly queuePattern = AppQueues.schema.learning.submission.access.grantToAll.wildcard;
    readonly options = {
        newJobCheckIntervalSeconds: 1,
    };

    constructor(
        private readonly submissionManager: SubmissionManager,
        private readonly correctionService: CorrectionService,
        private readonly queueService: QueueService,
        private readonly logger: Logger,
    ) {}

    async handler(job: Job<GrantAccessToAllSubmissionsCommandParams>): Promise<void> {
        const command = AppQueues.getMapperByClass(GrantAccessToAllSubmissionsCommand).deserialize(job.data);

        const readAccessPartIds = await this.correctionService.findPartIdsOfPendingCorrectionsWithEvaluator(
            command.userId,
        );
        const writeAccessPartIds = await this.submissionManager.getPartIdsWithWriteAccessToSubmissionForUser(
            command.userId,
            command.contentType,
        );

        this.logger.debug(
            `Commanding to give access for user ${command.userId} for ` +
                `parts [${readAccessPartIds.join(', ')}] (read) and [${writeAccessPartIds.join(', ')}] (write)`,
        );

        const commands: CommandWithOptions<GrantAccessToSubmissionCommand>[] = [
            ...writeAccessPartIds.map((partId) => {
                return {
                    message: new GrantAccessToSubmissionCommand({
                        assignedSprintPartId: partId,
                        accessType: StudentSubmissionContentPermission.WRITE,
                        userId: command.userId,
                    }),
                    options: {
                        subQueue: command.userId.value,
                    },
                };
            }),
            ...readAccessPartIds.map((partId) => {
                return {
                    message: new GrantAccessToSubmissionCommand({
                        assignedSprintPartId: partId,
                        accessType: StudentSubmissionContentPermission.READ,
                        userId: command.userId,
                    }),
                    options: {
                        subQueue: command.userId.value,
                    },
                };
            }),
        ];

        return this.queueService.commandMany(commands);
    }
}
