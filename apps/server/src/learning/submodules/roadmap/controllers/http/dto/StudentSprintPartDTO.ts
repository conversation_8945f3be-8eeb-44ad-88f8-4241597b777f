import { Type } from 'class-transformer';
import { IsBoolean, IsDate, IsEnum, IsInt, IsOptional, IsString, ValidateNested } from 'class-validator';
import ArgumentValidation from '../../../../../../core/utils/validation/ArgumentValidation';
import { EvaluationType } from '../../../../../../education/sprints/domain/EvaluationProperties';
import SprintPart from '../../../../../../education/sprints/domain/SprintPart';
import AssignedSprintPart, { AssignedSprintPartState } from '../../../domain/AssignedSprintPart';
import StudentSprintPartPreconditionDTO from './StudentSprintPartPreconditionDTO';
import { FixNestedArrayJsonSchemaReference } from '../../../../../../core/controllers/dto/decorators/FixNestedJsonSchemaReference';
import { SprintPartContentType } from '../../../../../../education/sprints/domain/SprintPartContentType';

export default class StudentSprintPartDTO {
    @IsInt()
    id: number;

    @IsString()
    slug: string;

    @IsString()
    name: string;

    @IsString()
    description: string;

    @IsEnum(SprintPartContentType)
    contentType: SprintPartContentType;

    @IsEnum(EvaluationType)
    evaluationType: EvaluationType;

    @IsEnum(AssignedSprintPartState)
    state: AssignedSprintPartState;

    @IsBoolean()
    hasSuggestedSolutionUrl: boolean;

    @IsBoolean()
    hasSubmissionUrl: boolean;

    // Backward compatibility
    @IsBoolean()
    hasSolutionUrl: boolean;

    // Backward compatibility
    @IsBoolean()
    hasGithubUrl: boolean;

    @IsOptional()
    @IsDate()
    startedAt?: Date;

    @IsOptional()
    @IsDate()
    submittedAt?: Date;

    @IsOptional()
    @IsDate()
    completedAt?: Date;

    @Type(() => StudentSprintPartPreconditionDTO)
    @ValidateNested({ each: true })
    @FixNestedArrayJsonSchemaReference(StudentSprintPartPreconditionDTO)
    preconditions: StudentSprintPartPreconditionDTO[];

    static fromSprintPart(part: SprintPart): StudentSprintPartDTO {
        ArgumentValidation.assert.defined(part.id?.value, 'Sprint part ID is required');

        const dto = new StudentSprintPartDTO();
        dto.id = part.id?.value;
        dto.slug = part.slug.value;
        dto.name = part.name.name;
        dto.description = part.description.value;
        dto.contentType = part.contentType;
        dto.evaluationType = part.evaluationProperties.type;
        dto.state = AssignedSprintPartState.PENDING;
        dto.hasSuggestedSolutionUrl = !!part.solutionRepository;
        dto.hasSubmissionUrl = StudentSprintPartDTO.hasSubmission(part);
        dto.preconditions = [];

        // Backward compatibility
        dto.hasSolutionUrl = dto.hasSuggestedSolutionUrl;
        dto.hasGithubUrl = dto.hasSubmissionUrl;

        return dto;
    }

    static fromAssignedSprintPart(
        part: AssignedSprintPart,
        preconditions: StudentSprintPartPreconditionDTO[],
    ): StudentSprintPartDTO {
        const dto = new StudentSprintPartDTO();
        dto.id = part.sprintPartId.value;
        dto.slug = part.slug.value;
        dto.name = part.name.name;
        dto.description = part.description.value;
        dto.contentType = part.contentType;
        dto.evaluationType = part.evaluationProperties.type;
        dto.state = part.state;
        dto.hasSuggestedSolutionUrl = part.hasSolutionUrl;
        dto.hasSubmissionUrl = StudentSprintPartDTO.hasSubmission(part);
        dto.startedAt = part.startedAt;
        dto.submittedAt = part.submittedAt;
        dto.completedAt = part.completedAt;
        dto.preconditions = preconditions;

        // Backward compatibility
        dto.hasSolutionUrl = dto.hasSuggestedSolutionUrl;
        dto.hasGithubUrl = dto.hasSubmissionUrl;

        return dto;
    }

    private static hasSubmission(part: AssignedSprintPart | SprintPart): boolean {
        // We don't want to show the repo URL if the part not assigned
        if (part instanceof SprintPart) {
            return false;
        }

        return part.contentType !== SprintPartContentType.TEMPLATE;
    }
}
