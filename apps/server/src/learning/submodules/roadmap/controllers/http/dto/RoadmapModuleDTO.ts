import { Type } from 'class-transformer';
import { IsDate, IsEnum, IsInt, IsOptional, IsString, ValidateNested } from 'class-validator';
import ArgumentValidation from '../../../../../../core/utils/validation/ArgumentValidation';
import Module from '../../../../../../education/modules/domain/Module';
import AssignedModule, { AssignedModuleState } from '../../../domain/AssignedModule';
import RoadmapSprintDTO from './RoadmapSprintDTO';

export default class RoadmapModuleDTO {
    @IsInt()
    id: number;

    @IsString()
    name: string;

    @IsEnum(AssignedModuleState)
    state: AssignedModuleState;

    @IsOptional()
    @IsDate()
    startedAt?: Date;

    @IsOptional()
    @IsDate()
    completedAt?: Date;

    @Type(() => RoadmapSprintDTO)
    @ValidateNested()
    sprints: RoadmapSprintDTO[];

    static fromModule(module: Module): RoadmapModuleDTO {
        ArgumentValidation.assert.defined(module.id?.value, 'Module ID is required');

        const dto = new RoadmapModuleDTO();
        dto.id = module.id?.value;
        dto.name = module.name.name;
        dto.state = AssignedModuleState.PENDING;
        dto.sprints = module.sprints.map((sprint, i) => RoadmapSprintDTO.fromSprint(sprint, i));

        return dto;
    }

    static fromAssignedModule(module: AssignedModule): RoadmapModuleDTO {
        const dto = new RoadmapModuleDTO();
        dto.id = module.moduleId.value;
        dto.name = module.name.name;
        dto.state = module.state;
        dto.startedAt = module.startedAt;
        dto.completedAt = module.completedAt;
        dto.sprints = module.sprints.map((sprint, i) => RoadmapSprintDTO.fromAssignedSprint(sprint, i));

        return dto;
    }
}
