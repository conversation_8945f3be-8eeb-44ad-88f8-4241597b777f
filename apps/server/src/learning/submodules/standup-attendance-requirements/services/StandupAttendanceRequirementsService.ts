import StandupAttendanceRequirementsRepository from '../infrastructure/db/StandupAttendanceRequirementsRepository';
import Transaction from '../../../../core/infrastructure/Transaction';
import Id from '../../../../core/domain/value-objects/Id';
import { UserStandupAttendanceRequirement } from '../../../../users/students/settings/domain/UserStandupAttendanceRequirement';
import { Week } from '../../../../core/domain/value-objects/Week';

export default class StandupAttendanceRequirementsService {
    constructor(private readonly standupAttendanceRequirementsRepository: StandupAttendanceRequirementsRepository) {}

    getStudentsCurrentStandupRequirements({
        userIds,
        week,
        tx,
    }: {
        userIds?: Id[];
        week: Week;
        tx?: Transaction;
    }): Promise<UserStandupAttendanceRequirement[]> {
        return this.standupAttendanceRequirementsRepository.getStudentsCurrentStandupRequirements({
            tx,
            week,
            userIds,
        });
    }
}
