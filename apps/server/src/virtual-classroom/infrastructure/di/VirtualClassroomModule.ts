import TransactionManager from '../../../core/infrastructure/TransactionManager';
import { VirtualClassroomService } from '../../services/VirtualClassroomService';
import { VirtualClassroomRepository } from '../db/VirtualClassroomRepository';

export class VirtualClassroomModule {
    constructor(readonly virtualClassroomService: VirtualClassroomService) {}

    static init(tm: TransactionManager): VirtualClassroomModule {
        return new VirtualClassroomModule(new VirtualClassroomService(new VirtualClassroomRepository(tm)));
    }
}
