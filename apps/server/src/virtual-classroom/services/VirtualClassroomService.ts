import { VirtualClassroom } from '../domain/VirtualClassroom';
import { VirtualClassroomRepository } from '../infrastructure/db/VirtualClassroomRepository';

export class VirtualClassroomService {
    constructor(private readonly virtualClassroomRepository: VirtualClassroomRepository) {}

    async getVirtualClassrooms(): Promise<VirtualClassroom[]> {
        return await this.virtualClassroomRepository.getAll();
    }
}
