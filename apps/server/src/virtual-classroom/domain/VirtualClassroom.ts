import DomainEntity from '../../core/domain/DomainEntity';
import DiscordChannelId from '../../discord/domain/DiscordChannelId';
import { NonFunctionProperties } from '../../utils/UtilityTypes';

type VirtualClassroomParams = Omit<NonFunctionProperties<VirtualClassroom>, 'domainEvents'>;

export class VirtualClassroom extends DomainEntity {
    readonly name: string;

    readonly discordChannelId: DiscordChannelId;

    constructor(params: VirtualClassroomParams) {
        super(params.id);

        Object.assign(this, params);
    }
}
