import { IsBoolean } from 'class-validator';
import { ReviewSettings } from '../../../domain/ReviewSettings';

export default class ReviewSettingsDTO {
    @IsBoolean()
    isReviewRecorded: boolean;

    static fromReviewSettings(reviewSettings: ReviewSettings): ReviewSettingsDTO {
        const result = new ReviewSettingsDTO();
        result.isReviewRecorded = reviewSettings.isReviewRecorded;
        return result;
    }
}
