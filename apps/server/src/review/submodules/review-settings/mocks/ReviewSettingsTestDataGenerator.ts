import { faker } from '@faker-js/faker';
import { ReviewSettings } from '../domain/ReviewSettings';
import TestObjects from '../../../../test-toolkit/shared/TestObjects';

export class ReviewSettingsTestDataGenerator {
    static getTestReviewSettings = (reviewSetting: Partial<ReviewSettings> = {}): ReviewSettings => {
        return ReviewSettings.fromParams({
            id: TestObjects.id(),
            userId: TestObjects.id(),
            isReviewRecorded: faker.datatype.boolean(),
            ...reviewSetting,
        });
    };
}
