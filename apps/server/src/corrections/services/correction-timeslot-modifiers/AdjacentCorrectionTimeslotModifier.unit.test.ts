import { CorrectionTimeslot } from '../../domain/CorrectionTimeslot';
import TestObjects from '../../../test-toolkit/shared/TestObjects';
import { AdjacentCorrectionTimeslotModifier } from './AdjacentCorrectionTimeslotModifier';

describe('AdjacentCorrectionTimeslotModifier', () => {
    const modifier = new AdjacentCorrectionTimeslotModifier();

    describe('modify', () => {
        it('should adjacent timeslots of the same user', async () => {
            const evaluatorId = TestObjects.id();
            const timeslots = [
                new CorrectionTimeslot({
                    evaluatorId,
                    start: new Date('2021-01-01T10:00:00Z'),
                    end: new Date('2021-01-01T11:00:00Z'),
                }),
                new CorrectionTimeslot({
                    evaluatorId,
                    start: new Date('2021-01-01T11:00:00Z'),
                    end: new Date('2021-01-01T12:00:00Z'),
                }),
            ];

            const result = await modifier.modify({ timeslots });

            expect(result).toEqual([
                new CorrectionTimeslot({
                    evaluatorId,
                    start: new Date('2021-01-01T10:00:00Z'),
                    end: new Date('2021-01-01T12:00:00Z'),
                }),
            ]);
        });

        it('should merge overlapping timeslots of the same user', async () => {
            const evaluatorId = TestObjects.id();
            const timeslots = [
                new CorrectionTimeslot({
                    evaluatorId,
                    start: new Date('2021-01-01T10:00:00Z'),
                    end: new Date('2021-01-01T11:00:00Z'),
                }),
                new CorrectionTimeslot({
                    evaluatorId,
                    start: new Date('2021-01-01T10:30:00Z'),
                    end: new Date('2021-01-01T12:00:00Z'),
                }),
            ];

            const result = await modifier.modify({ timeslots });

            expect(result).toEqual([
                new CorrectionTimeslot({
                    evaluatorId,
                    start: new Date('2021-01-01T10:00:00Z'),
                    end: new Date('2021-01-01T12:00:00Z'),
                }),
            ]);
        });

        it('should not merge non-adjacent timeslots of the same user', async () => {
            const evaluatorId = TestObjects.id();
            const timeslots = [
                new CorrectionTimeslot({
                    evaluatorId,
                    start: new Date('2021-01-01T10:00:00Z'),
                    end: new Date('2021-01-01T11:00:00Z'),
                }),
                new CorrectionTimeslot({
                    evaluatorId,
                    start: new Date('2021-01-01T12:00:00Z'),
                    end: new Date('2021-01-01T13:00:00Z'),
                }),
            ];

            const result = await modifier.modify({ timeslots });

            expect(result).toEqual(timeslots);
        });

        it('should not merge timeslots of different users', async () => {
            const timeslots = [
                new CorrectionTimeslot({
                    evaluatorId: TestObjects.uniqueId(),
                    start: new Date('2021-01-01T10:00:00Z'),
                    end: new Date('2021-01-01T11:00:00Z'),
                }),
                new CorrectionTimeslot({
                    evaluatorId: TestObjects.uniqueId(),
                    start: new Date('2021-01-01T11:00:00Z'),
                    end: new Date('2021-01-01T12:00:00Z'),
                }),
            ];

            const result = await modifier.modify({ timeslots });

            expect(result).toEqual(timeslots);
        });
    });
});
