import { CorrectionTimeslotModifier, CorrectionTimeslotModifierProperties } from './CorrectionTimeslotModifier';
import { CalendarSettingsService } from '../../../calendar-settings/services/CalendarSettingsService';
import { RequireOnly } from '../../../utils/UtilityTypes';
import { CorrectionTimeslot } from '../../domain/CorrectionTimeslot';
import Arrays from '../../../core/collections/Arrays';
import moment from 'moment/moment';
import { CorrectionConfiguration } from '../../../config/Configuration';

export class TimeBeforeCorrectionTimeslotModifier implements CorrectionTimeslotModifier {
    private readonly slotIntervalMinutes: number;

    constructor(
        private readonly calendarSettingsService: CalendarSettingsService,
        private readonly configuration: RequireOnly<CorrectionConfiguration, 'slotInterval'>,
    ) {
        this.slotIntervalMinutes = configuration.slotInterval;
    }

    async modify({
        timeslots,
    }: RequireOnly<CorrectionTimeslotModifierProperties, 'timeslots'>): Promise<CorrectionTimeslot[]> {
        const userIds = Arrays.unique(timeslots.map((t) => t.evaluatorId));
        const settings = await this.calendarSettingsService.getUsersCalendarSettings(userIds);
        const result: CorrectionTimeslot[] = [];
        const now = new Date();

        timeslots.forEach((timeslot) => {
            const userSettings = settings.find((s) => s.userId.equals(timeslot.evaluatorId));
            const startLimit = moment(now)
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                .add(userSettings.timeBeforeReview, 'milliseconds')
                // Take off the first interval, to avoid getting errors after a few seconds when a user selects it,
                // and it already is expired.
                .add(this.slotIntervalMinutes, 'minutes');
            if (startLimit.isBefore(timeslot.start)) {
                result.push(timeslot);
            } else if (startLimit.isBetween(timeslot.start, timeslot.end, undefined, '[]')) {
                result.push(new CorrectionTimeslot({ ...timeslot, start: startLimit.toDate() }));
            }
        });

        return result;
    }
}
