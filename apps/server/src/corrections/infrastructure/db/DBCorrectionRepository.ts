import { Between, Brackets, DataSource, In, <PERSON>Than, Repository } from 'typeorm';
import { DatabaseError } from '../../../core/infrastructure/db/errors/DatabaseError';
import { RepositoryFindOptions } from '../../../core/infrastructure/db/BaseRepository';
import CorrectionRepository from './CorrectionRepository';
import CorrectionPersistenceEntity from './CorrectionPersistenceEntity';
import DBBaseRepository from '../../../core/infrastructure/db/DBBaseRepository';
import Id from '../../../core/domain/value-objects/Id';
import { CorrectionStatus } from '../../domain/CorrectionStatus';

export default class DBCorrectionRepository
    extends DBBaseRepository<CorrectionPersistenceEntity>
    implements CorrectionRepository
{
    protected repository: Repository<CorrectionPersistenceEntity>;

    constructor(datasource: DataSource) {
        super(datasource, CorrectionPersistenceEntity);
        this.repository = datasource.getRepository(CorrectionPersistenceEntity);
    }

    async findByAssignedSprintPart(
        assignedSprintPartId: number,
        options?: RepositoryFindOptions,
    ): Promise<CorrectionPersistenceEntity[]> {
        const em = options?.entityManager || this.entityManager;
        const relations: string[] = options?.relations || [];

        try {
            return em.getRepository(CorrectionPersistenceEntity).find({
                relations,
                where: { assignedSprintPartId },
            });
        } catch (error) {
            throw new DatabaseError(error);
        }
    }

    async findByAnyUserInRange(
        userId: number,
        from?: Date,
        until?: Date,
        options?: RepositoryFindOptions,
    ): Promise<CorrectionPersistenceEntity[]> {
        const em = options?.entityManager || this.entityManager;
        const relations: string[] = options?.relations || [];

        try {
            const repo = em.getRepository(CorrectionPersistenceEntity);
            let queryBuilder = repo.createQueryBuilder('correction').where(
                new Brackets((qb) =>
                    qb
                        .where('correction."evaluator_id" = :userId', {
                            userId,
                        })
                        .orWhere('correction."user_id" = :userId', {
                            userId,
                        }),
                ),
            );

            if (from) {
                queryBuilder = queryBuilder.andWhere('correction."end_date" >= :from', { from });
            }

            if (until) {
                queryBuilder = queryBuilder.andWhere('correction."start_date" <= :until', {
                    until,
                });
            }

            const corrections = await queryBuilder.getMany();
            if (corrections.length === 0) {
                return [];
            }

            return repo.find({
                relations,
                where: { id: In(corrections.map(({ id }) => id)) },
            });
        } catch (error) {
            throw new DatabaseError(error);
        }
    }

    async findByEvaluatorInRange(
        evaluatorId: number,
        from: Date,
        until: Date,
        options?: RepositoryFindOptions,
    ): Promise<CorrectionPersistenceEntity[]> {
        const em = options?.entityManager || this.entityManager;
        const relations: string[] = options?.relations || [];

        try {
            return em.getRepository(CorrectionPersistenceEntity).find({
                relations,
                where: { evaluatorId, startDate: Between(from, until) },
            });
        } catch (error) {
            throw new DatabaseError(error);
        }
    }

    async findByEvaluator(
        evaluatorId: number,
        status?: CorrectionStatus,
        options?: RepositoryFindOptions,
    ): Promise<CorrectionPersistenceEntity[]> {
        const em = options?.entityManager || this.entityManager;
        const relations: string[] = options?.relations || [];

        try {
            return em.getRepository(CorrectionPersistenceEntity).find({
                relations,
                where: { evaluatorId, status },
            });
        } catch (error) {
            throw new DatabaseError(error);
        }
    }

    async findByStudent(
        studentId: number,
        status?: CorrectionStatus,
        options?: RepositoryFindOptions,
    ): Promise<CorrectionPersistenceEntity[]> {
        const em = options?.entityManager || this.entityManager;
        const relations: string[] = options?.relations || [];

        try {
            const repo = em.getRepository(CorrectionPersistenceEntity);
            let queryBuilder = repo
                .createQueryBuilder('correction')
                .where('correction."user_id" = :studentId', { studentId });

            if (status) {
                queryBuilder = queryBuilder.andWhere('correction."status" = :status', { status });
            }

            const corrections = await queryBuilder.getMany();
            if (corrections.length === 0) {
                return [];
            }

            return repo.find({
                relations,
                where: { id: In(corrections.map(({ id }) => id)) },
            });
        } catch (error) {
            throw new DatabaseError(error);
        }
    }

    async findByEvent(
        eventId: number,
        options?: RepositoryFindOptions,
    ): Promise<CorrectionPersistenceEntity | undefined> {
        const em = options?.entityManager || this.entityManager;
        const relations: string[] = options?.relations || [];

        try {
            // @ts-expect-error TS(2322) FIXME: Type 'CorrectionPersistenceEntity | null' is not a... Remove this comment to see the full error message
            return em.getRepository(CorrectionPersistenceEntity).findOne({
                relations,
                where: { eventId },
            });
        } catch (error) {
            throw new DatabaseError(error);
        }
    }

    async findCorrectionsFrom(from: Date, options?: RepositoryFindOptions): Promise<CorrectionPersistenceEntity[]> {
        const em = options?.entityManager || this.entityManager;
        const relations: string[] = options?.relations || [];

        try {
            return em.getRepository(CorrectionPersistenceEntity).find({
                relations,
                where: { startDate: MoreThan(from) },
            });
        } catch (error) {
            throw new DatabaseError(error);
        }
    }

    async findPartIdsOfPendingCorrectionsWithEvaluator(
        evaluatorId: Id,
        options?: RepositoryFindOptions,
    ): Promise<Id[]> {
        const em = options?.entityManager || this.entityManager;

        try {
            const ids: { id: Id }[] = await em.query(
                `
                SELECT DISTINCT assigned_sprint_part_id as id
                FROM correction
                WHERE evaluator_id = $1 AND status = $2
            `,
                [evaluatorId.value, CorrectionStatus.PENDING],
            );

            return ids.map(({ id }) => new Id(id));
        } catch (error) {
            throw new DatabaseError(error);
        }
    }

    findByUserIdAndModuleId(
        userId: Id,
        moduleId: Id,
        options?: RepositoryFindOptions,
    ): Promise<CorrectionPersistenceEntity[]> {
        const em = options?.entityManager || this.entityManager;
        try {
            return em
                .createQueryBuilder(CorrectionPersistenceEntity, 'correction')
                .leftJoin('correction.assignedSprintPart', 'assignedSprintPart')
                .leftJoin('assignedSprintPart.assignedSprint', 'assignedSprint')
                .leftJoin('assignedSprint.assignedModule', 'assignedModule')
                .where('assignedModule.userId = :userId', { userId: userId.value })
                .andWhere('assignedModule.moduleId = :moduleId', { moduleId: moduleId.value })
                .getMany();
        } catch (e) {
            throw new DatabaseError(e);
        }
    }
}
