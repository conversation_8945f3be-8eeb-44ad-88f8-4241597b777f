import moment from 'moment/moment';
import { DataSource, Raw, Repository } from 'typeorm';
import { DatabaseError } from '../../core/infrastructure/db/errors/DatabaseError';
import BaseRepository, { RepositoryFindOptions } from '../../core/infrastructure/db/BaseRepository';
import DBBaseRepository from '../../core/infrastructure/db/DBBaseRepository';
import DailyCorrectionLimitEntity from './model/db/DailyCorrectionLimitEntity';

interface DailyCorrectionLimitRepository extends BaseRepository<DailyCorrectionLimitEntity> {
    findByUserInRange(
        userId: number,
        from: Date,
        until: Date,
        options?: RepositoryFindOptions,
    ): Promise<DailyCorrectionLimitEntity[]>;

    findByUserAndDay(
        userId: number,
        day: Date,
        options?: RepositoryFindOptions,
    ): Promise<DailyCorrectionLimitEntity | undefined>;
}

export class DBDailyCorrectionLimitRepository
    extends DBBaseRepository<DailyCorrectionLimitEntity>
    implements DailyCorrectionLimitRepository
{
    protected repository: Repository<DailyCorrectionLimitEntity>;

    constructor(datasource: DataSource) {
        super(datasource, DailyCorrectionLimitEntity);
        this.repository = datasource.getRepository(DailyCorrectionLimitEntity);
    }

    async findByUserInRange(
        userId: number,
        from: Date,
        until: Date,
        options?: RepositoryFindOptions,
    ): Promise<DailyCorrectionLimitEntity[]> {
        const em = options?.entityManager || this.entityManager;
        const relations: string[] = options?.relations || [];

        try {
            return await em.getRepository(this.type).find({
                relations,
                where: [
                    // Starts in range
                    {
                        userId,
                        day: Raw((d) => `${d} >= :startFrom AND ${d} < :startUntil`, {
                            startFrom: from,
                            startUntil: until,
                        }),
                    },
                    // Ends in range
                    {
                        userId,
                        day: Raw((d) => `${d} > :endFrom AND ${d} <= :endUntil`, {
                            endFrom: moment(from).subtract(1, 'day').toDate(),
                            endUntil: moment(until).subtract(1, 'day').toDate(),
                        }),
                    },
                ],
            });
        } catch (error) {
            throw new DatabaseError(error);
        }
    }

    async findByUserAndDay(
        userId: number,
        day: Date,
        options?: RepositoryFindOptions,
    ): Promise<DailyCorrectionLimitEntity | undefined> {
        const em = options?.entityManager || this.entityManager;
        const relations: string[] = options?.relations || [];

        try {
            // @ts-expect-error TS(2322) FIXME: Type 'DailyCorrectionLimitEntity | null' is not as... Remove this comment to see the full error message
            return await em.getRepository(this.type).findOne({
                relations,
                where: {
                    userId,
                    day,
                },
            });
        } catch (error) {
            throw new DatabaseError(error);
        }
    }
}

export default DailyCorrectionLimitRepository;
