import moment from 'moment';
import DailyCorrectionLimitService, { DefaultDailyCorrectionLimitService } from './DailyCorrectionLimitService';
import CorrectionPersistenceEntity from '../infrastructure/db/CorrectionPersistenceEntity';
import IllegalArgumentError from '../../core/errors/IllegalArgumentError';
import CoreTransactionManagerMock from '../../test-toolkit/unit/deprecated-mocks/CoreTransactionManagerMock';
import CorrectionRepositoryMock from '../../test-toolkit/unit/deprecated-mocks/CorrectionRepositoryMock';
import DailyCorrectionLimitRepositoryMock from '../../test-toolkit/unit/deprecated-mocks/DailyCorrectionLimitRepositoryMock';
import { CorrectionStatus } from '../domain/CorrectionStatus';
import { UserRepository } from '../../users/shared/infrastructure/db/UserRepository';
import { anything, instance, mock, when } from 'ts-mockito';

function correctionInstance(data: Partial<CorrectionPersistenceEntity>): CorrectionPersistenceEntity {
    return Object.setPrototypeOf(data, CorrectionPersistenceEntity.prototype);
}

describe('DefaultDailyCorrectionLimitService', () => {
    let userRepository: UserRepository;
    let correctionRepository: CorrectionRepositoryMock;
    let dailyCorrectionLimitRepository: DailyCorrectionLimitRepositoryMock;
    let dailyCorrectionLimitService: DailyCorrectionLimitService;

    beforeEach(() => {
        userRepository = mock(UserRepository);
        correctionRepository = new CorrectionRepositoryMock();
        dailyCorrectionLimitRepository = new DailyCorrectionLimitRepositoryMock();
        dailyCorrectionLimitService = new DefaultDailyCorrectionLimitService(
            new CoreTransactionManagerMock(),
            instance(userRepository),
            correctionRepository,
            dailyCorrectionLimitRepository,
        );
    });

    describe('updateUserDailyCorrectionLimit', () => {
        test('should not update limit in the past', async () => {
            when(userRepository.exists(anything())).thenResolve(true);

            await expect(
                dailyCorrectionLimitService.updateUserDailyCorrectionLimit(1, {
                    day: moment().subtract(2, 'days').toDate(),
                    limit: 1,
                }),
            ).rejects.toEqual(new IllegalArgumentError('Cannot update limit in the past'));
        });

        test('should update if a limit is the same as a scheduled corrections count', async () => {
            when(userRepository.exists(anything())).thenResolve(true);
            dailyCorrectionLimitRepository.findByUserInRangeMock.mockResolvedValue([]);
            correctionRepository.findByEvaluatorInRangeMock.mockResolvedValue([
                correctionInstance({ id: 1, status: CorrectionStatus.PENDING }),
                correctionInstance({
                    id: 2,
                    status: CorrectionStatus.IN_PROGRESS,
                }),
                correctionInstance({ id: 3, status: CorrectionStatus.SUCCESS }),
                correctionInstance({
                    id: 4,
                    status: CorrectionStatus.STUDENT_CANCELED,
                }),
                correctionInstance({
                    id: 4,
                    status: CorrectionStatus.EVALUATOR_NO_SHOW,
                }),
            ]);

            await expect(
                dailyCorrectionLimitService.updateUserDailyCorrectionLimit(1, {
                    day: new Date(),
                    limit: 3,
                }),
            ).resolves.toBeUndefined();
        });

        test('should not update if a limit is less that a scheduled corrections count', async () => {
            when(userRepository.exists(anything())).thenResolve(true);
            correctionRepository.findByEvaluatorInRangeMock.mockResolvedValue([
                correctionInstance({ id: 1, status: CorrectionStatus.PENDING }),
                correctionInstance({
                    id: 2,
                    status: CorrectionStatus.IN_PROGRESS,
                }),
                correctionInstance({ id: 3, status: CorrectionStatus.SUCCESS }),
            ]);

            await expect(
                dailyCorrectionLimitService.updateUserDailyCorrectionLimit(1, {
                    day: new Date(),
                    limit: 2,
                }),
            ).rejects.toEqual(
                new IllegalArgumentError('Limit cannot be lower than already scheduled corrections count'),
            );
        });
    });
});
