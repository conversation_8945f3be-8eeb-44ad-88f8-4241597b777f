import DiscordUserId from '../../../../discord/domain/DiscordUserId';
import { Change } from '../../../../core/utils/Change';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import Id from '../../../../core/domain/value-objects/Id';

export type RecordDiscordOAuthProfileChangeCommandParams =
    NonFunctionProperties<RecordDiscordOAuthProfileChangeCommand>;

export type RecordDiscordOAuthProfileChangeCommandDto = {
    userId: number;
    change: Change<string | undefined>;
};

export class RecordDiscordOAuthProfileChangeCommand {
    readonly userId: Id;

    readonly change: Change<DiscordUserId | undefined>;

    constructor(params: RecordDiscordOAuthProfileChangeCommandParams) {
        this.userId = params.userId;
        this.change = params.change;
    }
}

export class RecordDiscordOAuthProfileChangeCommandMapper {
    serialize(command: RecordDiscordOAuthProfileChangeCommand): RecordDiscordOAuthProfileChangeCommandDto {
        return {
            userId: command.userId.value,
            change: {
                before: command.change.before ? command.change.before.value : undefined,
                after: command.change.after ? command.change.after.value : undefined,
            },
        };
    }

    deserialize(data: RecordDiscordOAuthProfileChangeCommandDto): RecordDiscordOAuthProfileChangeCommand {
        return new RecordDiscordOAuthProfileChangeCommand({
            userId: new Id(data.userId),
            change: new Change(
                data.change.before ? new DiscordUserId(data.change.before) : undefined,
                data.change.after ? new DiscordUserId(data.change.after) : undefined,
            ),
        });
    }
}
