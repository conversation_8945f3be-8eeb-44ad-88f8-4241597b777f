import TypeormPersistenceMapper from '../../../../core/infrastructure/db/TypeormPersistenceMapper';
import DiscordUserId from '../../../../discord/domain/DiscordUserId';
import DiscordProfilePersistenceEntity from './DiscordProfilePersistenceEntity';
import { DiscordOAuthProfile, DiscordOAuthProfileData } from '../../../domain/DiscordOAuthProfile';
import { OAuthProfile } from '../../../../core/domain/oauth/OAuthProfile';
import Id from '../../../../core/domain/value-objects/Id';
import OAuthNonce from '../../../../core/domain/oauth/OAuthNonce';

export default class DiscordProfileMapper
    implements TypeormPersistenceMapper<DiscordOAuthProfile, DiscordProfilePersistenceEntity>
{
    toDomain(persistence: DiscordProfilePersistenceEntity): DiscordOAuthProfile {
        return new OAuthProfile<DiscordOAuthProfileData>({
            id: persistence.id ? new Id(persistence.id) : undefined,
            data: new DiscordOAuthProfileData(
                persistence.discordUserId ? new DiscordUserId(persistence.discordUserId) : undefined,
            ),
            userId: new Id(persistence.userId),
            completedAt: persistence.authorizationCompletedAt,
            startedAt: persistence.authorizationStartedAt,
            removedAt: persistence.authorizationRemovedAt,
            nonce: persistence.discordNonce ? new OAuthNonce(persistence.discordNonce) : undefined,
        });
    }

    toPersistence(domain: DiscordOAuthProfile): DiscordProfilePersistenceEntity {
        return new DiscordProfilePersistenceEntity({
            id: domain.id?.value,
            userId: domain.userId.value,
            discordUserId: domain.data?.discordUserId?.value,
            authorizationCompletedAt: domain.completedAt,
            authorizationStartedAt: domain.startedAt,
            authorizationRemovedAt: domain.removedAt,
            discordNonce: domain.nonce?.value,
        });
    }
}
