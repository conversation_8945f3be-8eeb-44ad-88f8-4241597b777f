import Id from '../../core/domain/value-objects/Id';
import DiscordUserId from '../../discord/domain/DiscordUserId';
import { DiscordProfileHistoryRecordRepository } from '../infrastructure/db/discord-profile/DiscordProfileHistoryRecordRepository';
import { Change } from '../../core/utils/Change';
import TransactionManager from '../../core/infrastructure/TransactionManager';
import Transaction from '../../core/infrastructure/Transaction';
import { DiscordOAuthProfileHistoryRecord } from '../domain/DiscordOAuthProfileHistoryRecord';

export class DiscordProfileHistoryService {
    constructor(
        private readonly tm: TransactionManager,
        private readonly historyRecordRepository: DiscordProfileHistoryRecordRepository,
    ) {}

    async getProfileHistory(userId: Id): Promise<DiscordOAuthProfileHistoryRecord[]> {
        const records = await this.historyRecordRepository.getByUser(userId);

        return records.sort((a, b) => a.period.from.getTime() - b.period.from.getTime());
    }

    async discordUserIdChanged({
        userId,
        change,
    }: {
        userId: Id;
        change: Change<DiscordUserId | undefined>;
    }): Promise<void> {
        if ((!change.before && !change.after) || change.before?.equals(change.after)) {
            return;
        }

        await this.tm.execute(async (tx) => {
            if (change.before) {
                await this.deactivate(userId, change.before, tx);
            }

            if (change.after) {
                await this.activate(userId, change.after, tx);
            }
        });
    }

    private async activate(userId: Id, discordUserId: DiscordUserId, tx: Transaction): Promise<void> {
        await this.historyRecordRepository.save(
            DiscordOAuthProfileHistoryRecord.createActive(userId, discordUserId),
            tx,
        );
    }

    private async deactivate(userId: Id, discordUserId: DiscordUserId, tx: Transaction): Promise<void> {
        const record = await this.historyRecordRepository.getActive({ userId, discordUserId }, tx);
        if (record) {
            await this.historyRecordRepository.save(record.deactivate(), tx);
        }
    }
}
