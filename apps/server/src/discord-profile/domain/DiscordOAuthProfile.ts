import { OAuthProfile, OAuthProfileData } from '../../core/domain/oauth/OAuthProfile';
import DiscordUserId from '../../discord/domain/DiscordUserId';

export class DiscordOAuthProfileData implements OAuthProfileData<DiscordOAuthProfileData> {
    constructor(readonly discordUserId?: DiscordUserId) {}

    equals(other: DiscordOAuthProfileData): boolean {
        return !!this.discordUserId?.equals(other.discordUserId);
    }

    isSet(): boolean {
        return !!this.discordUserId;
    }
}

export type DiscordOAuthProfile = OAuthProfile<DiscordOAuthProfileData>;
