import { Container } from 'typedi';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import { LoggingModule } from '../../../logging/infrastructure/di/LoggingModule';
import Consumer from '../../../messaging/Consumer';
import { IntercomLeadManagerToken } from '../../infrastructure/di/tokens';
import ConvertLeadConsumer from './ConvertLeadConsumer';
import { ErrorTrackingModule } from '../../../error-tracking/infrastructure/di/ErrorTrackingModule';

export default (): Consumer[] => [
    new ConvertLeadConsumer(
        Container.get(ConfigurationModule.CONFIGURATION_TOKEN),
        Container.get(LoggingModule.LOGGING_SERVICE_TOKEN),
        Container.get(IntercomLeadManagerToken),
        Container.get(ErrorTrackingModule.ERROR_TRACKER_TOKEN),
    ),
];
