import { Inject, Service } from 'typedi';
import { Authorized, Body, JsonController, Post } from 'routing-controllers';
import { PhysicalAddressService } from '../../services/PhysicalAddressService';
import { VerifyAddressRequestDto } from './dto/VerifyAddressRequestDto';
import { PhysicalAddressServiceToken } from '../../infrastructure/di/tokens';
import { Role } from '../../../users/shared/infrastructure/db/User';
import { VerifyAddressResponseDto } from './dto/VerifyAddressResponseDto';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import responses from '../../../core/controllers/docs/responses';

@Service()
@JsonController('/physical-address')
export class PhysicalAddressController {
    constructor(
        @Inject(PhysicalAddressServiceToken)
        private readonly physicalAddressService: PhysicalAddressService,
    ) {}

    @Authorized([Role.USER, Role.ADMIN, Role.STAFF, Role.MENTOR])
    @ResponseSchema(VerifyAddressResponseDto)
    @Post('/verify')
    @OpenAPI({
        responses,
        summary: 'Verifies address and returns helpful hints about how to fix it.',
        security: [{ cookieAuth: [] }],
    })
    async verify(@Body() dto: VerifyAddressRequestDto): Promise<VerifyAddressResponseDto> {
        return this.physicalAddressService.verifyAddressVerbose(dto.address, dto.countryCode);
    }
}
