import { DiscordServerRepository } from '../infrastructure/db/DiscordServerRepository';
import { DiscordServer } from '../domain/DiscordServer';
import Transaction from '../../../../core/infrastructure/Transaction';
import Id from '../../../../core/domain/value-objects/Id';

export class DiscordServerService {
    constructor(private readonly discordServerRepository: DiscordServerRepository) {}

    getAll(tx?: Transaction): Promise<DiscordServer[]> {
        return this.discordServerRepository.getAll(tx);
    }

    getById(id: Id, tx?: Transaction): Promise<DiscordServer | undefined> {
        return this.discordServerRepository.get(id, tx);
    }

    save(discordServer: DiscordServer, tx?: Transaction): Promise<DiscordServer> {
        return this.discordServerRepository.save(discordServer, tx);
    }
}
