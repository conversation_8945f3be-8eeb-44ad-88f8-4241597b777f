import Id from '../../../../../../core/domain/value-objects/Id';
import { NonFunctionProperties } from '../../../../../../utils/UtilityTypes';

export type SyncDiscordServersInfoToPlatformCommandDto = {
    discordServerId: number | 'all';
};

export class SyncDiscordServersInfoToPlatformCommand {
    readonly discordServerId: Id | 'all';

    constructor(params: NonFunctionProperties<SyncDiscordServersInfoToPlatformCommand>) {
        this.discordServerId = params.discordServerId;
    }
}

export class SyncDiscordServersInfoToPlatformCommandMapper {
    serialize(command: SyncDiscordServersInfoToPlatformCommand): object {
        return {
            discordServerId: command.discordServerId === 'all' ? 'all' : command.discordServerId.value,
        };
    }

    deserialize(dto: SyncDiscordServersInfoToPlatformCommandDto): SyncDiscordServersInfoToPlatformCommand {
        return new SyncDiscordServersInfoToPlatformCommand({
            discordServerId: dto.discordServerId === 'all' ? 'all' : new Id(dto.discordServerId),
        });
    }
}
