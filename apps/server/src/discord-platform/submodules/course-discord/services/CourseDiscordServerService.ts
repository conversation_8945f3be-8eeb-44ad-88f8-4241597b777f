import { CourseDiscordServerRepository } from '../infrastructure/db/course-discord-server/CourseDiscordServerRepository';
import Id from '../../../../core/domain/value-objects/Id';
import { CourseDiscordServer } from '../domain/CourseDiscordServer';
import Transaction from '../../../../core/infrastructure/Transaction';

export class CourseDiscordServerService {
    constructor(private readonly courseDiscordServerRepository: CourseDiscordServerRepository) {}

    getByCourseId(courseId: Id, tx?: Transaction): Promise<CourseDiscordServer | undefined> {
        return this.courseDiscordServerRepository.getByCourseId(courseId, tx);
    }

    save(courseDiscordServer: CourseDiscordServer, tx?: Transaction): Promise<CourseDiscordServer> {
        return this.courseDiscordServerRepository.save(courseDiscordServer, tx);
    }
}
