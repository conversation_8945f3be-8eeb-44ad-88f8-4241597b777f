import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, <PERSON>lationId, Unique } from 'typeorm';
import TypeormPersistenceEntity from '../../../core/infrastructure/db/TypeormPersistenceEntity';
import User from '../../../users/shared/infrastructure/db/User';
import { NonFunctionProperties } from '../../../utils/UtilityTypes';
import { GoogleOAuthProfile, GoogleOAuthProfileData } from '../../domain/GoogleOAuthProfile';
import Id from '../../../core/domain/value-objects/Id';
import Email from '../../../core/domain/value-objects/Email';
import { OAuthProfile } from '../../../core/domain/oauth/OAuthProfile';
import OAuthNonce from '../../../core/domain/oauth/OAuthNonce';

@Entity('google_profile')
@Unique(GoogleProfilePersistenceEntity.UNIQUE_EMAIL_INDEX_NAME, ['email'])
@Unique(GoogleProfilePersistenceEntity.UNIQUE_USER_ID_INDEX_NAME, ['userId'])
export default class GoogleProfilePersistenceEntity extends TypeormPersistenceEntity {
    static readonly UNIQUE_EMAIL_INDEX_NAME = 'google_profile_unique_email';
    static readonly UNIQUE_USER_ID_INDEX_NAME = 'google_profile_unique_user_id';

    @Column({ name: 'user_id' })
    @RelationId((self: GoogleProfilePersistenceEntity) => self.user)
    userId: number;

    @Column({ name: 'email', nullable: true })
    email: string;

    @Column({ name: 'nonce', nullable: true })
    nonce: string;

    @Column({ name: 'started_at', type: 'timestamptz', nullable: true })
    startedAt: Date;

    @Column({ name: 'completed_at', type: 'timestamptz', nullable: true })
    completedAt: Date;

    @Column({ name: 'removed_at', type: 'timestamptz', nullable: true })
    removedAt: Date;

    @OneToOne(() => User, (user) => user.id, { onDelete: 'CASCADE' })
    @JoinColumn()
    private user: never;

    constructor(params?: NonFunctionProperties<GoogleProfilePersistenceEntity>) {
        if (!params) {
            super();
            return;
        }
        super(params.id, params.createdAt, params.updatedAt);
        Object.assign(this, params);
    }

    static fromDomain(domain: GoogleOAuthProfile): GoogleProfilePersistenceEntity {
        return new GoogleProfilePersistenceEntity({
            id: domain.id?.value,
            userId: domain.userId.value,
            // @ts-expect-error TS(2322) FIXME: Type 'string | undefined' is not assignable to typ... Remove this comment to see the full error message
            email: domain.data?.email?.value,
            // @ts-expect-error TS(2322) FIXME: Type 'string | undefined' is not assignable to typ... Remove this comment to see the full error message
            nonce: domain.nonce?.value,
            // @ts-expect-error TS(2322) FIXME: Type 'Date | undefined' is not assignable to type ... Remove this comment to see the full error message
            startedAt: domain.startedAt,
            // @ts-expect-error TS(2322) FIXME: Type 'Date | undefined' is not assignable to type ... Remove this comment to see the full error message
            completedAt: domain.completedAt,
            // @ts-expect-error TS(2322) FIXME: Type 'Date | undefined' is not assignable to type ... Remove this comment to see the full error message
            removedAt: domain.removedAt,
        });
    }

    toDomain(): GoogleOAuthProfile {
        return new OAuthProfile<GoogleOAuthProfileData>({
            // @ts-expect-error TS(2345) FIXME: Argument of type 'Email | undefined' is not assign... Remove this comment to see the full error message
            data: new GoogleOAuthProfileData(this.email ? new Email(this.email) : undefined),
            // @ts-expect-error TS(2345) FIXME: Argument of type 'number | undefined' is not assig... Remove this comment to see the full error message
            id: new Id(this.id),
            userId: new Id(this.userId),
            nonce: this.nonce ? new OAuthNonce(this.nonce) : undefined,
            startedAt: this.startedAt,
            completedAt: this.completedAt,
            removedAt: this.removedAt,
        });
    }
}
