import BatchFinder from '../../batches/services/BatchFinder';
import Email from '../../core/domain/value-objects/Email';
import Id from '../../core/domain/value-objects/Id';
import ImageString from '../../core/domain/value-objects/ImageString';
import FullName from '../../core/domain/value-objects/user/FullName';
import IllegalStateError from '../../core/errors/IllegalStateError';
import StateValidation from '../../core/utils/validation/StateValidation';
import Correction from '../../corrections/domain/Correction';
import CorrectionFinder from '../../corrections/services/CorrectionFinder';
import PhoneNumber from '../../endorsement/domain/hiring-profile/PhoneNumber';
import LinkedInUrl from '../../endorsement/domain/social-media/LinkedInUrl';
import HiringProfileManager from '../../endorsement/services/HiringProfileManager';
import SocialMediaManager from '../../endorsement/services/SocialMediaManager';
import { StudentSkillsFinder } from '../../learning/submodules/roadmap/services/StudentSkillsFinder';
import SkillCategory from '../../learning/submodules/skills/domain/SkillCategory';
import { UserAccountFinder } from '../../users/accounts/services/UserAccountFinder';
import { MentorProfileFinder } from '../../users/mentors/services/MentorProfileFinder';
import ResumeStudentSettingsHelper from '../../users/students/settings/services/ResumeStudentSettingsHelper';
import { StudentSettingsFinder } from '../../users/students/settings/services/StudentSettingsFinder';
import ResumePublicId from '../domain/ResumePublicId';
import ResumeSettings from '../domain/ResumeSettings';
import ResumeSettingsRepository from '../domain/ResumeSettingsRepository';
import Resume from './dto/Resume';
import ResumeAccountExecutive from './dto/ResumeAccountExecutive';
import ResumeBatchMentor from './dto/ResumeBatchMentor';
import ResumeCorrections from './dto/ResumeCorrections';
import ResumeEvaluator from './dto/ResumeEvaluator';
import ResumeProfile from './dto/ResumeProfile';
import ResumeSkillCategory from './dto/ResumeSkillCategory';

export default class ResumeManager {
    constructor(
        private readonly resumeSettingsRepository: ResumeSettingsRepository,
        private readonly hiringProfileManager: HiringProfileManager,
        private readonly socialMediaManager: SocialMediaManager,
        private readonly studentSkillsFinder: StudentSkillsFinder,
        private readonly correctionFinder: CorrectionFinder,
        private readonly userAccountFinder: UserAccountFinder,
        private readonly batchFinder: BatchFinder,
        private readonly studentSettingsFinder: StudentSettingsFinder,
        private readonly mentorProfileFinder: MentorProfileFinder,
        private readonly resumeStudentProfileHelper: ResumeStudentSettingsHelper,
    ) {}

    async getUserResume(userId: Id): Promise<Resume | undefined> {
        const settings = await this.resumeSettingsRepository.getByUser(userId);
        if (!settings) {
            return undefined;
        }

        return this.getResume(settings);
    }

    async getPublicResume(publicId: ResumePublicId): Promise<Resume | undefined> {
        const settings = await this.resumeSettingsRepository.getByPublicId(publicId);
        if (!settings || !settings.isConsentGiven || !settings.isEnabled) {
            return undefined;
        }

        return this.getResume(settings);
    }

    private async getResume(settings: ResumeSettings): Promise<Resume> {
        const profile = await this.getResumeProfile(settings.userId);
        const skills = await this.getResumeSkills(settings.userId);
        const corrections = await this.getResumeCorrections(settings.userId);
        const batchMentors = await this.getResumeBatchMentors(settings.userId);
        const accountExecutive = this.getResumeAccountExecutive();

        return new Resume(profile, skills, corrections, batchMentors, accountExecutive);
    }

    private async getResumeProfile(userId: Id): Promise<ResumeProfile> {
        const hiringProfile = await this.hiringProfileManager.get(userId);
        StateValidation.isValid(!!hiringProfile, 'Hiring profile not available');

        const socialMedia = await this.socialMediaManager.get(userId);
        StateValidation.isValid(!!socialMedia, 'Social media profile not available');

        const account = await this.userAccountFinder.findByUserId(userId);
        if (!account?.isComplete()) {
            throw new IllegalStateError('User not found');
        }

        const wasJtl = await this.resumeStudentProfileHelper.wasJtl(userId);

        return new ResumeProfile(
            // @ts-expect-error TS(2345) FIXME: Argument of type 'FullName | undefined' is not ass... Remove this comment to see the full error message
            account.name,
            account.email,
            wasJtl,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            hiringProfile.phoneNumber,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            socialMedia.github.url,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            socialMedia.linkedIn.url,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            hiringProfile.languages,
        );
    }

    private async getResumeSkills(userId: Id): Promise<ResumeSkillCategory[]> {
        const skills = await this.studentSkillsFinder.findStudentSkills(userId);
        const uniqueCategories: SkillCategory[] = skills.reduce(
            // @ts-expect-error TS(2339) FIXME: Property 'id' does not exist on type 'never'.
            (res, skill) => (res.some((c) => c.id.equals(skill.category.id)) ? res : [...res, skill.category]),
            [],
        );

        return uniqueCategories
            .sort((a, b) => a.order.compareTo(b.order))
            .map(
                (category) =>
                    new ResumeSkillCategory(
                        category,
                        skills.filter((skill) => skill.category.id?.equals(category.id)),
                    ),
            );
    }

    private async getResumeCorrections(userId: Id): Promise<ResumeCorrections> {
        const given = await this.correctionFinder.findEvaluatorCorrections(userId);
        const received = await this.correctionFinder.findStudentCorrections(userId);
        const lastReceived = received.sort((a, b) => b.time.compareTo(a.time))[0];
        const evaluators = await this.getEvaluators(received);

        return new ResumeCorrections(given.length, received.length, lastReceived?.time.start, evaluators);
    }

    private async getEvaluators(receivedCorrections: Correction[]): Promise<ResumeEvaluator[]> {
        const evaluatorIds = receivedCorrections
            .map(({ evaluatorId }) => evaluatorId)
            // @ts-expect-error TS(2339) FIXME: Property 'equals' does not exist on type 'never'.
            .reduce((ids, evaluatorId) => (ids.some((id) => id.equals(evaluatorId)) ? ids : [...ids, evaluatorId]), []);
        if (evaluatorIds.length === 0) {
            return [];
        }

        const allAccounts = await this.userAccountFinder.findByUserIds(evaluatorIds);
        const completeAccounts = allAccounts.filter((account) => account.isComplete());
        const mentorProfiles = await this.mentorProfileFinder.findByUserIds(evaluatorIds);

        return completeAccounts.map(
            (account) =>
                new ResumeEvaluator(
                    // @ts-expect-error TS(2345) FIXME: Argument of type 'FullName | undefined' is not ass... Remove this comment to see the full error message
                    account.name,
                    account.role,
                    account.image,
                    mentorProfiles.find((p) => p.userId.equals(account.id))?.employmentPosition,
                ),
        );
    }

    private async getResumeBatchMentors(userId: Id): Promise<ResumeBatchMentor[]> {
        const studentProfile = await this.studentSettingsFinder.findByUserId(userId);
        if (!studentProfile) {
            return [];
        }

        const batch = await this.batchFinder.findById(studentProfile.batchId);
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        if (batch.mentors.length === 0) {
            return [];
        }

        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        const allAccounts = await this.userAccountFinder.findByUserIds(batch.mentors);
        const completeAccounts = allAccounts.filter((account) => account.isComplete());
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        const mentorProfiles = await this.mentorProfileFinder.findByUserIds(batch.mentors);

        return completeAccounts.map(
            (account) =>
                new ResumeBatchMentor(
                    // @ts-expect-error TS(2345) FIXME: Argument of type 'FullName | undefined' is not ass... Remove this comment to see the full error message
                    account.name,
                    account.image,
                    mentorProfiles.find((p) => p.userId.equals(account.id))?.employmentPosition,
                ),
        );
    }

    private getResumeAccountExecutive(): ResumeAccountExecutive {
        return new ResumeAccountExecutive(
            new FullName('Elena', 'Karmazaitė'),
            new Email('<EMAIL>'),
            new PhoneNumber('+***********'),
            new LinkedInUrl('https://www.linkedin.com/in/elena-karmazait%c4%97-b38a15199'),
            new ImageString(
                'https://res.cloudinary.com/turingcollege/image/upload/w_150,h_150,q_100/TuringCollege/idgm53x2xplrdm4f5yk2.jpg',
            ),
        );
    }
}
