import { IsBoolean, IsString } from 'class-validator';
import ResumeSettings from '../../../domain/ResumeSettings';

export default class ResumeSettingsDTO {
    @IsString()
    publicId: string;

    @IsBoolean()
    isEnabled: boolean;

    @IsBoolean()
    isConsentGiven: boolean;

    constructor(publicId: string, isEnabled: boolean, isConsentGiven: boolean) {
        this.publicId = publicId;
        this.isEnabled = isEnabled;
        this.isConsentGiven = isConsentGiven;
    }

    static fromResumeSettings(resumeSettings: ResumeSettings): ResumeSettingsDTO {
        return new ResumeSettingsDTO(
            resumeSettings.publicId.value,
            resumeSettings.isEnabled,
            resumeSettings.isConsentGiven,
        );
    }
}
