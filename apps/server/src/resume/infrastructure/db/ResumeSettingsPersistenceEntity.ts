import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>To<PERSON>ne, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../core/infrastructure/db/TypeormPersistenceEntity';
import User from '../../../users/shared/infrastructure/db/User';

@Entity('student_resume_settings')
export default class ResumeSettingsPersistenceEntity extends TypeormPersistenceEntity {
    @Column({ unique: true })
    publicId: string;

    @OneToOne(() => User, { onDelete: 'CASCADE' })
    @JoinColumn()
    private user: User;

    @Column()
    @RelationId((self: ResumeSettingsPersistenceEntity) => self.user)
    userId: number;

    @Column({ default: false })
    isEnabled: boolean;

    @Column({ default: false })
    isConsentGiven: boolean;

    @Column({ type: 'timestamptz', nullable: true })
    consentGivenAt?: Date;
}
