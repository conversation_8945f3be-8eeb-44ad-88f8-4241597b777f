import { GoogleDrivePermission } from './GoogleDrivePermission';
import Email from '../../core/domain/value-objects/Email';
import { GOOGLE_DRIVE_PERMISSION_RANKING } from './GoogleDrivePermissionRanking';

export class GoogleDrivePermissions {
    constructor(readonly permissions: GoogleDrivePermission[]) {}

    forUser(email: Email): GoogleDrivePermissions {
        return new GoogleDrivePermissions(this.permissions.filter((permission) => permission.email?.equals(email)));
    }

    highestPermission(): GoogleDrivePermission | null {
        return this.permissions.reduce(
            (highestPermission: GoogleDrivePermission, permission: GoogleDrivePermission) => {
                if (!highestPermission) {
                    return permission;
                }

                if (
                    GOOGLE_DRIVE_PERMISSION_RANKING[highestPermission.role] <
                    GOOGLE_DRIVE_PERMISSION_RANKING[permission.role]
                ) {
                    return permission;
                }

                return highestPermission;
            },
            null,
        );
    }
}
