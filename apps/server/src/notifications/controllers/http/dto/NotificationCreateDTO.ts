import { IsEnum, IsString, Min<PERSON>ength } from 'class-validator';
import { NotificationType } from '../../../domain/Notification';
import { IsId } from '../../../../core/controllers/validators/IsId';

export default class NotificationCreateDTO {
    @IsId()
    userId: number;

    @IsEnum(NotificationType)
    type: NotificationType;

    @IsString()
    @MinLength(1)
    title: string;

    @IsString()
    @MinLength(1)
    description: string;
}
