import request from 'supertest';
import Container from 'typedi';
import { faker } from '@faker-js/faker';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import Id from '../../../core/domain/value-objects/Id';
import { TransactionManagerToken } from '../../../core/infrastructure/di/tokens';
import NotificationDTO from './dto/NotificationDTO';
import Notification, { NotificationType } from '../../domain/Notification';
import { NotificationManager } from '../../NotificationManager';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { <PERSON>ie } from '../../../auth/domain/Cookie';
import ImpersonationDoNothingDTO from '../../../auth/controllers/http/dto/ImpersonationDoNothingDTO';
import { NotificationRepository } from '../../infrastructure/db/NotificationRepository';
import NotificationSnoozeDTO from './dto/NotificationSnoozeDTO';

describe('Notifications controller IT', () => {
    let config: Configuration;
    let notificationRepository: NotificationRepository;
    let notificationsManager: NotificationManager;
    let student: IntegrationTestUser;
    let anotherStudent: IntegrationTestUser;
    let notifications: Notification[] = [];

    async function cleanup(): Promise<void> {
        await Promise.all(
            notifications.map((notification) => notificationRepository.delete(notification.getIdOrThrow())),
        );
        notifications = [];
    }

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        notificationRepository = new NotificationRepository(Container.get(TransactionManagerToken));
        notificationsManager = Container.get(NotificationManager);
        student = await IntegrationTestUser.createFakeStudent();
        anotherStudent = await IntegrationTestUser.createFakeStudent();
        await student.authorize();
        await anotherStudent.authorize();
    });

    afterEach(async () => {
        await cleanup();
    });

    describe('GET /notifications', () => {
        it('should get current user notifications', async () => {
            notifications = [
                await notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 1',
                    description: 'Test description 1',
                }),
                await notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 2',
                    description: 'Test description 2',
                }),
                await notificationsManager.createNotification({
                    userId: new Id(anotherStudent.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 3',
                    description: 'Test description 3',
                }),
            ];

            const res = await request(config.server)
                .get('/notifications')
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send()
                .expect(200);

            expect(res.body).toEqual(
                expect.arrayContaining([
                    {
                        ...NotificationDTO.fromNotification(notifications[0]),
                        createdAt: notifications[0].createdAt.toISOString(),
                    },
                    {
                        ...NotificationDTO.fromNotification(notifications[1]),
                        createdAt: notifications[1].createdAt.toISOString(),
                    },
                ]),
            );
        });

        it('should get current user acknowledged notifications', async () => {
            notifications = [
                await notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 1',
                    description: 'Test description 1',
                }),
                await notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 2',
                    description: 'Test description 2',
                }),
                await notificationsManager.createNotification({
                    userId: new Id(anotherStudent.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 3',
                    description: 'Test description 3',
                }),
            ];

            await notificationsManager.acknowledgeNotification(notifications[1].id as Id);

            const res = await request(config.server)
                .get('/notifications?isAck=true')
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send()
                .expect(200);

            expect(res.body).toEqual([
                {
                    ...NotificationDTO.fromNotification(notifications[1]),
                    createdAt: notifications[1].createdAt.toISOString(),
                    isAck: true,
                },
            ]);
        });

        it('should get current user unacknowledged notifications', async () => {
            notifications = [
                await notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 1',
                    description: 'Test description 1',
                }),
                await notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 2',
                    description: 'Test description 2',
                }),
                await notificationsManager.createNotification({
                    userId: new Id(anotherStudent.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 3',
                    description: 'Test description 3',
                }),
            ];

            await notificationsManager.acknowledgeNotification(notifications[1].id as Id);

            const res = await request(config.server)
                .get('/notifications?isAck=false')
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send()
                .expect(200);

            expect(res.body).toEqual([
                {
                    ...NotificationDTO.fromNotification(notifications[0]),
                    createdAt: notifications[0].createdAt.toISOString(),
                },
            ]);
        });
    });

    describe('PUT /notifications/:id/ack', () => {
        it('should do nothing when impersonating', async () => {
            notifications = await Promise.all([
                notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 1',
                    description: 'Test description 1',
                }),
            ]);

            const res = await request(config.server)
                .put(`/notifications/${notifications[0].id?.value}/ack`)
                .set(
                    'Cookie',
                    `${Cookie.TOKEN}=${student.getAuthTokenString()};${Cookie.IMPERSONATE}=${faker.internet.jwt()}`,
                )
                .send()
                .expect(200);
            expect(res.body).toEqual(new ImpersonationDoNothingDTO());
        });
    });

    describe('PUT /notifications/:id/snooze', () => {
        it('should snooze notification', async () => {
            notifications = await Promise.all([
                notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 1',
                    description: 'Test description 1',
                    snoozeLimit: 1,
                    snoozeCounter: 0,
                }),
            ]);

            await request(config.server)
                .put(`/notifications/${notifications[0].id?.value}/snooze`)
                .set('Cookie', `${Cookie.TOKEN}=${student.getAuthTokenString()}`)
                .send()
                .expect(204);
        });

        it('should snooze notification with custom duration', async () => {
            notifications = await Promise.all([
                notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 1',
                    description: 'Test description 1',
                    snoozeLimit: 1,
                    snoozeCounter: 0,
                }),
            ]);
            const data: NotificationSnoozeDTO = {
                snoozeInMinutes: 3 * 24 * 60,
            };

            await request(config.server)
                .put(`/notifications/${notifications[0].getIdOrThrow().value}/snooze`)
                .set('Cookie', `${Cookie.TOKEN}=${student.getAuthTokenString()}`)
                .send(data)
                .expect(204);
        });

        it('should not allow to snooze notification when limit reached', async () => {
            notifications = await Promise.all([
                notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 1',
                    description: 'Test description 1',
                    snoozeLimit: 1,
                    snoozeCounter: 1,
                }),
            ]);

            await request(config.server)
                .put(`/notifications/${notifications[0].id?.value}/snooze`)
                .set('Cookie', `${Cookie.TOKEN}=${student.getAuthTokenString()}`)
                .send()
                .expect(400);
        });

        it('should do nothing when impersonating', async () => {
            notifications = await Promise.all([
                notificationsManager.createNotification({
                    userId: new Id(student.id as Id),
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: 'Test title 1',
                    description: 'Test description 1',
                }),
            ]);

            const res = await request(config.server)
                .put(`/notifications/${notifications[0].id?.value}/snooze`)
                .set(
                    'Cookie',
                    `${Cookie.TOKEN}=${student.getAuthTokenString()};${Cookie.IMPERSONATE}=${faker.internet.jwt()}`,
                )
                .send()
                .expect(200);
            expect(res.body).toEqual(new ImpersonationDoNothingDTO());
        });
    });
});
