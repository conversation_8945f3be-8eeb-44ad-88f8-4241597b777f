import axios from 'axios';
import { Configuration } from '../../config/Configuration';
import Id from '../../core/domain/value-objects/Id';
import ApplicationError from '../../core/errors/ApplicationError';
import httpLogErrorMessage from '../../http/httpLogErrorMessage';
import { LoggingService } from '../../logging/services/LoggingService';
import Logger from '../../utils/logger/Logger';
import City from '../domain/City';
import Country from '../domain/Country';
import CountryCode from '../domain/value-objects/CountryCode';
import LocationName from '../domain/value-objects/LocatioinName';
import { CountryCodeIso2 } from '../../physical-address/domain/types/CountryCodeIso2';
import { KVStoreService } from '../../kv-store/KVStoreService';
import { Duration } from '../../core/domain/value-objects/Duration';

interface ResponseCountry {
    id: number;
    name: string;
    iso2: CountryCodeIso2;
}

interface ResponseCity {
    id: number;
    name: string;
}

export default class CountriesService {
    private readonly apiUrl: string;

    private readonly apiKey: string;

    private readonly logger: Logger;

    private readonly cacheTtl = new Duration(14, 'days').asMilliseconds();

    constructor(
        configuration: Configuration,
        loggingService: LoggingService,
        private readonly kvStore: KVStoreService,
    ) {
        this.apiUrl = configuration.countries.apiUrl;
        this.apiKey = configuration.countries.apiKey;
        this.logger = loggingService.createLogger(CountriesService.name);
    }

    async getCountries(): Promise<Country[]> {
        const countries: ResponseCountry[] = await this.apiGetRequest('/countries');

        return countries.map(this.toCountry);
    }

    async getCities(countryCode: CountryCode): Promise<City[]> {
        const cities: ResponseCity[] = await this.apiGetRequest(`/countries/${countryCode.value}/cities`);

        return cities.map(this.toCity);
    }

    private toCountry(responseCountry: ResponseCountry): Country {
        return new Country(
            new Id(responseCountry.id),
            new LocationName(responseCountry.name),
            new CountryCode(responseCountry.iso2),
        );
    }

    private toCity(responseCity: ResponseCity): City {
        return new City(new Id(responseCity.id), new LocationName(responseCity.name));
    }

    private async apiGetRequest<DataType>(path: string): Promise<DataType> {
        const cache = await this.kvStore.get<DataType>(path);

        if (cache) {
            return cache;
        }

        try {
            const res = await axios.get(`${this.apiUrl}${path}`, {
                headers: {
                    'X-CSCAPI-KEY': this.apiKey,
                },
            });

            if (!cache) {
                await this.kvStore.set(path, res.data, this.cacheTtl);
            }

            return res.data;
        } catch (e) {
            this.logError('Countries request failed', e);
            throw new ApplicationError('Countries request failed', e);
        }
    }

    private logError(baseMessage: string, error: any): void {
        if (error.isAxiosError) {
            this.logger.warn(httpLogErrorMessage(baseMessage, error));
        } else {
            this.logger.error(httpLogErrorMessage(baseMessage, error));
        }
    }
}
