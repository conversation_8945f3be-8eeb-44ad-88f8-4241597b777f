import StudentSettings from '../../users/students/settings/domain/StudentSettings';
import TestObjects from './TestObjects';
import { faker } from '@faker-js/faker';
import { StudentSource } from '../../users/students/settings/domain/StudentSource';
import StudentType from '../../users/students/settings/domain/StudentType';

export const getTestStudentSettings = (studentSettings: Partial<StudentSettings> = {}): StudentSettings => {
    return new StudentSettings({
        id: TestObjects.id(),
        batchId: TestObjects.id(),
        studentId: TestObjects.id(),
        source: faker.helpers.enumValue(StudentSource),
        endDate: faker.date.future(),
        isEndorsementEnabled: faker.datatype.boolean(),
        studentType: faker.helpers.arrayElement([
            (): StudentType => StudentType.regular(),
            (): StudentType => StudentType.jtl(),
            (): StudentType => StudentType.trial(),
        ])(),
        ...studentSettings,
    });
};
