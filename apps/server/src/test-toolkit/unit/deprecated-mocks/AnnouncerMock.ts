import Announcement from '../../../announcements/domain/Announcement';
import AnnouncementCategory from '../../../announcements/domain/AnnouncementCategory';
import AnnouncementBuilder from '../../../announcements/services/AnnouncementBuilder';
import Announcer from '../../../announcements/services/Announcer';

export default class AnnouncerMock implements Announcer {
    readonly sendAnnouncementWithGifMock = jest.fn();

    readonly newAnnouncementMock = jest.fn();

    sendAnnouncementWithGif(announcement: Announcement): Promise<void> {
        return this.sendAnnouncementWithGifMock(announcement);
    }

    newAnnouncement(category: AnnouncementCategory): AnnouncementBuilder {
        return this.newAnnouncementMock(category);
    }
}
