import ApplicationEvent from '../../../events/application/ApplicationEvent';
import ApplicationEventBroker from '../../../events/application/ApplicationEventBroker';
import ApplicationEventSubscriber from '../../../events/application/ApplicationEventSubscriber';

export default class EventBrokerMock implements ApplicationEventBroker {
    readonly publishMock = jest.fn();

    readonly publishAllMock = jest.fn();

    readonly subscribeMock = jest.fn();

    readonly unsubscribeAllMock = jest.fn();

    publish<E extends ApplicationEvent<any>>(event: E): Promise<void> {
        return this.publishMock(event);
    }

    publishAll(events: ApplicationEvent<any>[]): Promise<void> {
        return this.publishAllMock(events);
    }

    subscribe<E extends ApplicationEvent<any>>(name: string, subscriber: ApplicationEventSubscriber<E>): void {
        this.subscribeMock(name, subscriber);
    }

    unsubscribeAll(): void {
        this.unsubscribeAllMock();
    }
}
