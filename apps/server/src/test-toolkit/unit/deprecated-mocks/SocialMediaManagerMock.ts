import Id from '../../../core/domain/value-objects/Id';
import SocialMedia from '../../../endorsement/domain/social-media/SocialMedia';
import SocialMediaManager, { SocialMediaUpdateProperties } from '../../../endorsement/services/SocialMediaManager';

export default class SocialMediaManagerMock implements SocialMediaManager {
    readonly getMock = jest.fn();

    readonly updateMock = jest.fn();

    get(userId: Id): Promise<SocialMedia | undefined> {
        return this.getMock(userId);
    }

    update(userId: Id, properties: SocialMediaUpdateProperties): Promise<SocialMedia | undefined> {
        return this.updateMock(userId, properties);
    }
}
