import { MockServer } from 'jest-mock-server';
import { Configuration } from '../../../config/Configuration';
import Url from '../../../core/domain/value-objects/Url';
import GithubUsername from '../../../github/domain/GithubUsername';
import Numbers from '../../../utils/Numbers';

export class GithubMockServer extends MockServer {
    private readonly apiUrl: Url;

    private readonly orgName: string;

    private readonly installationId: string;

    constructor(configuration: Configuration) {
        super({
            host: new Url(configuration.github.apiUrl).hostname,
            port: new Url(configuration.github.apiUrl).port,
        });

        this.apiUrl = new Url(configuration.github.apiUrl);
        this.orgName = configuration.github.orgName;
        this.installationId = configuration.github.installationId;
    }

    mockTokenInstallation(status: number = 200, body?: any): void {
        this.post(
            this.apiUrl.addPath(`/app/installations/${this.installationId}/access_tokens`).pathname,
        ).mockImplementation((ctx) => {
            ctx.status = status;
            ctx.body = body ?? {
                token: 'test',
            };
        });
    }

    mockGetUser(githubUsername: GithubUsername, status: number = 200, body?: any): void {
        this.get(this.apiUrl.addPath(`/users/${githubUsername.value}`).pathname).mockImplementation((ctx) => {
            ctx.status = status;
            ctx.body = body ?? {
                id: Numbers.randomInt(),
            };
        });
    }

    mockInvitations(status: number = 200, body?: any): void {
        this.post(this.apiUrl.addPath(`/orgs/${this.orgName}/invitations`).pathname).mockImplementation((ctx) => {
            ctx.status = status;
            ctx.body = body;
        });
    }

    mockAddCollaborator(githubUsername: GithubUsername, status: number = 200): void {
        this.post(
            this.apiUrl.addPath(`/orgs/${this.orgName}/outside_collaborators/${githubUsername.value}`).pathname,
        ).mockImplementationOnce((ctx) => {
            ctx.status = status;
        });
    }

    mockGetMember(githubUsername: GithubUsername, status: number = 200): void {
        this.get(
            this.apiUrl.addPath(`/orgs/${this.orgName}/members/${githubUsername.value}`).pathname,
        ).mockImplementation((ctx) => {
            ctx.status = status;
        });
    }
}
