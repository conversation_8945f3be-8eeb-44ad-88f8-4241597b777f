import { IntegrationTestEntity } from './IntegrationTestEntity';
import Container from 'typedi';

import { RequireOnly } from '../../../utils/UtilityTypes';

import {
    CostTrackingWorklogEntry,
    CostTrackingWorklogEntryParams,
} from '../../../cost-tracking/submodules/cost-tracking-worklog-entry/domain/CostTrackingWorklogEntry';
import { CostTrackingWorklogEntryModule } from '../../../cost-tracking/submodules/cost-tracking-worklog-entry/infrastructure/di/CostTrackingWorklogEntryModule';
import { faker } from '@faker-js/faker';
import { CostTrackingWorklogEntryActivityType } from '../../../cost-tracking/submodules/cost-tracking-worklog-entry/domain/CostTrackingWorklogEntryActivityType';
import { CostTrackingWorklogEntryType } from '../../../cost-tracking/submodules/cost-tracking-worklog-entry/domain/CostTrackingWorklogEntryType';
import Id from '../../../core/domain/value-objects/Id';
import { Duration } from '../../../core/domain/value-objects/Duration';

export class IntegrationTestCostTrackingWorklogEntry extends IntegrationTestEntity<CostTrackingWorklogEntry> {
    static async create(
        params: RequireOnly<CostTrackingWorklogEntryParams, 'worklogId'>,
    ): Promise<IntegrationTestCostTrackingWorklogEntry> {
        const service = Container.get(CostTrackingWorklogEntryModule.COST_TRACKING_WORKLOG_ENTRY_SERVICE_TOKEN);

        const entity = await service.save(
            CostTrackingWorklogEntry.fromParams({
                description: faker.lorem.sentence(),
                timestamp: faker.date.recent(),
                type: CostTrackingWorklogEntryType.MANUAL,
                programName: faker.lorem.word(),
                activityType: faker.helpers.arrayElement(
                    Object.values(CostTrackingWorklogEntryActivityType).filter(
                        (v) =>
                            v !== CostTrackingWorklogEntryActivityType.REVIEW &&
                            v !== CostTrackingWorklogEntryActivityType.OTHER,
                    ),
                ),
                activityTime: params.activityTime || Duration.fromSeconds(faker.number.int({ min: 1, max: 6000 })),
                additionalTime: params.additionalTime || Duration.fromSeconds(faker.number.int({ min: 1, max: 6000 })),
                ...params,
            }),
        );

        return new IntegrationTestCostTrackingWorklogEntry(entity);
    }

    static async get(entryId: Id): Promise<IntegrationTestCostTrackingWorklogEntry | undefined> {
        const entry = await Container.get(CostTrackingWorklogEntryModule.COST_TRACKING_WORKLOG_ENTRY_SERVICE_TOKEN).get(
            entryId,
        );

        if (!entry) {
            return undefined;
        }

        return new IntegrationTestCostTrackingWorklogEntry(entry);
    }
}
