import Container from 'typedi';
import { TransactionManagerToken } from '../../../core/infrastructure/di/tokens';
import { IntegrationTestEntity } from './IntegrationTestEntity';
import { UztDocument, UztDocumentParams } from '../../../uzt/submodules/uzt-document/domain/documents/UztDocument';
import { UztDocumentRepository } from '../../../uzt/submodules/uzt-document/infrastructure/db/uzt-document/UztDocumentRepository';
import { UztDocumentFactory } from '../../../uzt/submodules/uzt-document/domain/documents/UztDocumentFactory';
import { UztDocumentState } from '../../../uzt/submodules/uzt-document/domain/documents/UztDocumentWorkflow';
import { DocumentState } from '../../../document/domain/DocumentWorkflow';
import Id from '../../../core/domain/value-objects/Id';
import { UztDocumentType } from '../../../uzt/submodules/uzt-document/domain/documents/UztDocumentType';
import { IntegrationTestDocument } from './IntegrationTestDocument';
import { DigitalDocument } from '../../../document/domain/DigitalDocument';
import { faker } from '@faker-js/faker';
import { DocumentTemplate } from '../../../document/domain/DocumentTemplate';

export interface CreateWithDocumentParams {
    uztDocumentState: UztDocumentState;
    documentState: DocumentState;
    userId: Id;
    type: UztDocumentType;
    month: Date;
    deletedAt?: Date;
}

export class IntegrationTestUztDocument extends IntegrationTestEntity<UztDocument> {
    static async create(params: UztDocumentParams): Promise<IntegrationTestUztDocument> {
        const repo = new UztDocumentRepository(Container.get(TransactionManagerToken));

        const entity = await repo.save(UztDocumentFactory.fromParams(params));

        return new IntegrationTestUztDocument(entity);
    }

    static async createWithDocument({
        uztDocumentState,
        documentState,
        userId,
        type,
        month,
        deletedAt,
    }: CreateWithDocumentParams): Promise<IntegrationTestUztDocument> {
        const document = await IntegrationTestDocument.create(
            new DigitalDocument({ state: documentState, template: faker.helpers.enumValue(DocumentTemplate) }),
        );

        return IntegrationTestUztDocument.create(
            UztDocumentFactory.fromParams({
                userId,
                type,
                month,
                document: document.params,
                state: uztDocumentState,
                deletedAt,
            }),
        );
    }

    static deleteAll(): Promise<void> {
        const repo = new UztDocumentRepository(Container.get(TransactionManagerToken));
        return repo.deleteAll();
    }
}
