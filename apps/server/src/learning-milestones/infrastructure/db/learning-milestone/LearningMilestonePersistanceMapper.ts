import TypeormPersistenceMapper from '../../../../core/infrastructure/db/TypeormPersistenceMapper';
import { LearningMilestone } from '../../../domain/LearningMilestone';
import Id from '../../../../core/domain/value-objects/Id';
import LearningMilestonePersistenceEntity from './LearningMilestonePersistenceEntity';
import { DateRange } from '../../../../core/domain/value-objects/DateRange';

export default class LearningMilestonePersistenceMapper
    implements TypeormPersistenceMapper<LearningMilestone, LearningMilestonePersistenceEntity>
{
    toDomain(persistence: LearningMilestonePersistenceEntity): LearningMilestone {
        return LearningMilestone.fromParams({
            id: persistence.id ? new Id(persistence.id) : undefined,
            period: DateRange.deserialize(persistence.period),
            progress: persistence.progress,
            target: persistence.target,
            type: persistence.type,
            userId: new Id(persistence.userId),
            explanationId: persistence?.explanationId ? new Id(persistence.explanationId) : undefined,
            exceptionId: persistence?.exceptionId ? new Id(persistence.exceptionId) : undefined,
        });
    }

    toPersistence(domain: LearningMilestone): LearningMilestonePersistenceEntity {
        return new LearningMilestonePersistenceEntity({
            id: domain.id?.value,
            period: domain.period.serialize(),
            periodFrom: domain.period.from,
            periodTo: domain.period.to,
            progress: domain.progress,
            target: domain.target,
            type: domain.type,
            userId: domain.userId.value,
            explanationId: domain.explanationId?.value,
            exceptionId: domain.exceptionId?.value,
        });
    }
}
