import { In, Raw } from 'typeorm';
import { DateRange } from '../../../../core/domain/value-objects/DateRange';
import Id from '../../../../core/domain/value-objects/Id';
import { Ordering } from '../../../../core/domain/value-objects/Ordering';
import { Pagination, PaginationResult } from '../../../../core/domain/value-objects/Pagination';
import Transaction from '../../../../core/infrastructure/Transaction';
import TransactionManager from '../../../../core/infrastructure/TransactionManager';
import TypeormRepository from '../../../../core/infrastructure/db/TypeormRepository';
import { LearningMilestone } from '../../../domain/LearningMilestone';
import { LearningMilestoneType } from '../../../domain/types/LearningMilestoneType';
import { LearningMilestoneStreak } from '../../../domain/LearningMilestoneStreak';
import { LearningStateOption } from '../../../../users/students/settings/domain/LearningState';
import StudentSettingsPersistenceEntity from '../../../../users/students/settings/db/StudentSettingsPersistenceEntity';
import LearningMilestonePersistenceEntity from './LearningMilestonePersistenceEntity';
import LearningMilestonePersistenceMapper from './LearningMilestonePersistanceMapper';
import { LearningMilestoneGlobalException } from '../../../domain/LearningMilestoneGlobalException';
import { TypeormDateRangeHelper } from '../../../../core/infrastructure/db/TypeormDateRangeHelper';
import { UserStandupAttendanceRequirementChange } from '../../../../users/students/settings/domain/UserStandupAttendanceRequirementChange';
import ArgumentValidation from '../../../../core/utils/validation/ArgumentValidation';

export default class LearningMilestoneRepository extends TypeormRepository<
    LearningMilestone,
    LearningMilestonePersistenceEntity
> {
    private readonly entity = LearningMilestonePersistenceEntity;

    constructor(tm: TransactionManager) {
        super(tm, new LearningMilestonePersistenceMapper(), LearningMilestonePersistenceEntity);
    }

    async getForUserByIds({
        milestonesIds,
        tx,
        userId,
    }: {
        milestonesIds: Id[];
        userId: Id;
        tx?: Transaction;
    }): Promise<LearningMilestone[]> {
        return this.getAllWhere(
            {
                id: In(milestonesIds.map((id) => id.value)),
                userId: userId.value,
            },
            tx,
        );
    }

    getForUserWithinRange({
        type,
        userId,
        range,
        tx,
    }: {
        type: LearningMilestoneType;
        userId: Id;
        range: DateRange;
        tx?: Transaction;
    }): Promise<LearningMilestone[]> {
        return this.getAllWhere(
            {
                type,
                userId: userId.value,
                period: TypeormDateRangeHelper.intersects(range),
            },
            tx,
        );
    }

    async getByIds({ milestonesIds, tx }: { milestonesIds: Id[]; tx?: Transaction }): Promise<LearningMilestone[]> {
        return this.getAllWhere(
            {
                id: In(milestonesIds.map((id) => id.value)),
            },
            tx,
        );
    }

    async insertOrDoNothing(learningMilestones: LearningMilestone[], tx?: Transaction): Promise<void> {
        await this.transactionManager.execute(async (transaction) => {
            await transaction.entityManager
                .createQueryBuilder()
                .insert()
                .into(this.entity)
                .values(learningMilestones.map((milestone) => this.mapper.toPersistence(milestone)))
                .orIgnore()
                .execute();
        }, tx);
    }

    async setMilestoneTargetWithinRange({
        userId,
        range,
        newTarget,
        type,
        tx,
    }: {
        range: DateRange;
        userId: Id;
        newTarget: number;
        type: LearningMilestoneType;
        tx?: Transaction;
    }): Promise<void> {
        await this.transactionManager.execute(async (transaction) => {
            await transaction.entityManager
                .createQueryBuilder()
                .update(this.entity)
                .set({ target: newTarget })
                .where({
                    userId: userId.value,
                    period: TypeormDateRangeHelper.intersects(range),
                    type,
                })
                .execute();
        }, tx);
    }

    async getUserLearningMilestones({
        userId,
        range,
        pagination,
    }: {
        userId: Id;
        range: DateRange;
        pagination?: Pagination;
    }): Promise<PaginationResult<LearningMilestone>> {
        let filterQuery = {};
        if (range && range.hasFiniteBound()) {
            filterQuery = {
                period: Raw(
                    (period) => {
                        return `${period} && :range`;
                    },
                    {
                        range: range.serialize(),
                    },
                ),
            };
        }

        return this.getAllWhereAdvanced({
            where: {
                userId: userId.value,
                ...filterQuery,
            },
            pagination,
            order: Ordering.fromParams({
                field: 'period',
                direction: -1,
            }),
        });
    }

    async incrementMilestoneProgress({
        userIds,
        period,
        milestoneType,
        increment = 1,
        tx,
    }: {
        userIds: Id[];
        period: DateRange;
        milestoneType: LearningMilestoneType;
        increment?: number;
        tx?: Transaction;
    }): Promise<boolean> {
        ArgumentValidation.assert.notEmpty(userIds);

        return this.transactionManager.execute(async (transaction) => {
            const result = await transaction.entityManager.increment(
                this.entity,
                {
                    userId: userIds.length === 1 ? userIds[0].value : In(userIds.map((id) => id.value)),
                    period: period.serialize(),
                    type: milestoneType,
                },
                'progress',
                increment,
            );

            return result.affected !== 0;
        }, tx);
    }

    async findLatestUnmetMilestoneStreak({
        range,
        milestoneType,
    }: {
        range: DateRange;
        milestoneType: LearningMilestoneType;
    }): Promise<LearningMilestoneStreak[]> {
        return this.transactionManager.execute(async (transaction) => {
            const result = await transaction.entityManager.query(
                `
                with met_or_explained as (
                    SELECT
                        ${this.entity.USER_ID_COLUMN_NAME},
                        MAX(lower(${this.entity.PERIOD_COLUMN_NAME}))
                    FROM
                        ${this.entity.TABLE_NAME}
                    WHERE
                        (
                            ${this.entity.EXPLANATION_ID_COLUMN_NAME} IS NOT NULL OR
                            ${this.entity.PROGRESS_COLUMN_NAME} >=
                                ${this.entity.TARGET_COLUMN_NAME}
                        )
                        AND ${this.entity.PERIOD_COLUMN_NAME} && $1
                        AND ${this.entity.TYPE_COLUMN_NAME} = $2
                    GROUP BY
                        ${this.entity.USER_ID_COLUMN_NAME}
                )
                
                SELECT DISTINCT ON (${this.entity.TABLE_NAME}.${this.entity.USER_ID_COLUMN_NAME})
                    ${this.entity.TABLE_NAME}.${this.entity.USER_ID_COLUMN_NAME},
                    count(${this.entity.TABLE_NAME}.${this.entity.USER_ID_COLUMN_NAME}) over user_id_win as unmet_streak,
                    last_value(${this.entity.TARGET_COLUMN_NAME}) over (
                        PARTITION BY ${this.entity.TABLE_NAME}.${this.entity.USER_ID_COLUMN_NAME}
                        ORDER BY ${this.entity.PERIOD_COLUMN_NAME}
                    ) as last_week_target,
                    last_value(${this.entity.TABLE_NAME}.id) over user_id_win as last_week_milestone_id
                FROM ${this.entity.TABLE_NAME}
                LEFT JOIN met_or_explained
                    ON ${this.entity.TABLE_NAME}.${this.entity.USER_ID_COLUMN_NAME} =
                        met_or_explained.${this.entity.USER_ID_COLUMN_NAME}
                JOIN ${StudentSettingsPersistenceEntity.TABLE_NAME}
                    ON ${StudentSettingsPersistenceEntity.TABLE_NAME}.${StudentSettingsPersistenceEntity.STUDENT_ID_COLUMN} =
                        ${this.entity.TABLE_NAME}.${this.entity.USER_ID_COLUMN_NAME}
                WHERE
                    ${this.entity.PERIOD_COLUMN_NAME} && $1
                    AND ${this.entity.EXCEPTION_ID_COLUMN_NAME} IS NULL
                    AND ${this.entity.TYPE_COLUMN_NAME} = $2
                    AND ${StudentSettingsPersistenceEntity.TABLE_NAME}.${StudentSettingsPersistenceEntity.LEARNING_STATE_COLUMN} = $3
                    AND (lower(${this.entity.PERIOD_COLUMN_NAME}) > max OR max IS NULL)
                WINDOW user_id_win as (
                    PARTITION BY ${this.entity.TABLE_NAME}.${this.entity.USER_ID_COLUMN_NAME}
                )
                `,
                [range.serialize(), milestoneType, LearningStateOption.IN_PROGRESS],
            );

            return result.map((row: any) => {
                return LearningMilestoneStreak.fromParams({
                    userId: new Id(row.user_id),
                    streak: Math.min(+row.unmet_streak, 3),
                    lastWeek: {
                        target: row.last_week_target,
                        milestoneId: new Id(row.last_week_milestone_id),
                    },
                });
            });
        });
    }

    async applyExceptionToMilestones({
        exception,
        tx,
    }: {
        exception: LearningMilestoneGlobalException;
        tx: Transaction;
    }): Promise<void> {
        await this.transactionManager.execute(async (transaction) => {
            await transaction.entityManager.update(
                LearningMilestonePersistenceEntity,
                {
                    period: TypeormDateRangeHelper.intersects(exception.period),
                },
                {
                    // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                    exceptionId: exception.id.value,
                },
            );
        }, tx);
    }

    async unlinkExceptionFromMilestones({
        exception,
        tx,
    }: {
        exception: LearningMilestoneGlobalException;
        tx: Transaction;
    }): Promise<void> {
        await this.transactionManager.execute(async (transaction) => {
            await transaction.entityManager.update(
                LearningMilestonePersistenceEntity,
                {
                    // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                    exceptionId: exception.id.value,
                },
                {
                    // @ts-expect-error TS(2322) FIXME: Type 'null' is not assignable to type '(() => stri... Remove this comment to see the full error message
                    exceptionId: null,
                },
            );
        }, tx);
    }

    async removeAllWithinRangeForUser({
        userId,
        range,
        tx,
    }: {
        userId: Id;
        range: DateRange;
        tx: Transaction;
    }): Promise<void> {
        return this.transactionManager.execute(async (transaction) => {
            await transaction.entityManager.delete(this.entity, {
                userId: userId.value,
                period: TypeormDateRangeHelper.intersects(range),
            });
        }, tx);
    }

    async getRequirementsChangesWithinRange({
        range,
        userIds = [],
        tx,
    }: {
        range: DateRange;
        userIds?: Id[];
        tx?: Transaction;
    }): Promise<UserStandupAttendanceRequirementChange[]> {
        let userIdsClause = '';

        if (userIds.length) {
            userIdsClause = `and ${this.entity.USER_ID_COLUMN_NAME} in (${userIds.map((id) => id.value).join(',')})`;
        }

        return this.transactionManager.execute(async (transaction) => {
            const result = await transaction.entityManager.query(
                `
                    with milestones_with_lag as (
                        select
                            ${this.entity.USER_ID_COLUMN_NAME},
                            ${this.entity.TARGET_COLUMN_NAME} as new_target,
                            LAG(${this.entity.TARGET_COLUMN_NAME}) over (
                                partition by ${this.entity.USER_ID_COLUMN_NAME}
                                order by ${this.entity.PERIOD_COLUMN_NAME}
                            ) as old_target
                        from ${this.entity.TABLE_NAME}
                        where
                           ${this.entity.PERIOD_COLUMN_NAME} && $1
                           ${userIdsClause}
                    )
                    select * from milestones_with_lag
                    where new_target != old_target      
                `,
                [range.serialize()],
            );

            return result.map((row: any) => {
                return UserStandupAttendanceRequirementChange.fromParams({
                    userId: new Id(row.user_id),
                    target: {
                        before: row.old_target,
                        after: row.new_target,
                    },
                });
            });
        }, tx);
    }
}
