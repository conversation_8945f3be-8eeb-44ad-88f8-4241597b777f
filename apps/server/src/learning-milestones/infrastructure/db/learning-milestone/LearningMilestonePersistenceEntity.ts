import { Column, Entity, Exclusion, ManyToOne, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../../core/infrastructure/db/TypeormPersistenceEntity';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { LearningMilestoneType } from '../../../domain/types/LearningMilestoneType';
import User from '../../../../users/shared/infrastructure/db/User';
import UnmetLearningMilestoneExplanationPersistenceEntity from '../unmet-learning-milestone-explanation/UnmetLearningMilestoneExplanationPersistenceEntity';
import LearningMilestoneGlobalExceptionPersistenceEntity from '../learning-milestone-global-exception/LearningMilestoneGlobalExceptionPersistenceEntity';

@Entity(LearningMilestonePersistenceEntity.TABLE_NAME)
@Exclusion(
    LearningMilestonePersistenceEntity.GIST_UNIQUE_EXCLUSION_NAME,
    'USING gist ("user_id" WITH =, "type" WITH =, "period" WITH &&)',
)
export default class LearningMilestonePersistenceEntity extends TypeormPersistenceEntity {
    static TABLE_NAME = 'learning_milestone';

    static GIST_UNIQUE_EXCLUSION_NAME = 'no_overlapping_periods_exclusion';

    static TYPE_COLUMN_NAME = 'type';

    static PERIOD_COLUMN_NAME = 'period';

    static PERIOD_FROM_COLUMN_NAME = 'period_from';

    static PERIOD_TO_COLUMN_NAME = 'period_to';

    static USER_ID_COLUMN_NAME = 'user_id';

    static EXPLANATION_ID_COLUMN_NAME = 'explanation_id';

    static EXCEPTION_ID_COLUMN_NAME = 'exception_id';

    static TARGET_COLUMN_NAME = 'target';

    static PROGRESS_COLUMN_NAME = 'progress';

    @Column({
        name: LearningMilestonePersistenceEntity.TYPE_COLUMN_NAME,
        default: LearningMilestoneType.STANDUP_ATTENDANCE,
        type: 'enum',
        enum: LearningMilestoneType,
    })
    type: LearningMilestoneType;

    @Column({ name: LearningMilestonePersistenceEntity.PERIOD_COLUMN_NAME, type: 'tstzrange' })
    period: string;

    @Column({ name: LearningMilestonePersistenceEntity.USER_ID_COLUMN_NAME })
    @RelationId((self: LearningMilestonePersistenceEntity) => self.user)
    userId: number;

    @Column({ name: LearningMilestonePersistenceEntity.EXPLANATION_ID_COLUMN_NAME, nullable: true })
    @RelationId((self: LearningMilestonePersistenceEntity) => self.explanation)
    explanationId?: number;

    @Column({ name: LearningMilestonePersistenceEntity.EXCEPTION_ID_COLUMN_NAME, nullable: true })
    @RelationId((self: LearningMilestonePersistenceEntity) => self.exception)
    exceptionId?: number;

    @Column({ name: LearningMilestonePersistenceEntity.TARGET_COLUMN_NAME })
    target: number;

    @Column({ name: LearningMilestonePersistenceEntity.PROGRESS_COLUMN_NAME })
    progress: number;

    @Column({
        name: LearningMilestonePersistenceEntity.PERIOD_FROM_COLUMN_NAME,
        type: 'timestamptz',
        nullable: true,
    })
    periodFrom: Date;

    @Column({
        name: LearningMilestonePersistenceEntity.PERIOD_TO_COLUMN_NAME,
        type: 'timestamptz',
        nullable: true,
    })
    periodTo: Date;

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    private user: never;

    @ManyToOne(() => UnmetLearningMilestoneExplanationPersistenceEntity, { onDelete: 'SET NULL', nullable: true })
    private explanation?: never;

    @ManyToOne(() => LearningMilestoneGlobalExceptionPersistenceEntity, { onDelete: 'SET NULL', nullable: true })
    private exception?: never;

    constructor(params: NonFunctionProperties<LearningMilestonePersistenceEntity>);
    constructor();

    constructor(params?: NonFunctionProperties<LearningMilestonePersistenceEntity>) {
        if (!params) {
            super();
            return;
        }
        super(params.id, params.createdAt, params.updatedAt);
        Object.assign(this, params);
    }
}
