import { Column, Entity, Index, ManyToOne, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../../core/infrastructure/db/TypeormPersistenceEntity';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import User from '../../../../users/shared/infrastructure/db/User';
import { RegisteredStandupAttendance } from '../../../domain/RegisteredStandupAttendance';
import Id from '../../../../core/domain/value-objects/Id';
import { Duration } from '../../../../core/domain/value-objects/Duration';
import ZoomId from '../../../../meetings/domain/value-objects/ZoomId';
import MeetingPersistenceEntity from '../../../../meetings/infrastructure/db/MeetingPersistenceEntity';

@Entity('registered_standup_attendance')
@Index(['userId', 'occurrenceId'], { unique: true })
export default class RegisteredStandupAttendancePersistenceEntity extends TypeormPersistenceEntity {
    @Column({ name: 'user_id' })
    @RelationId((self: RegisteredStandupAttendancePersistenceEntity) => self.user)
    userId: number;

    @Index()
    @Column({ name: 'occurrence_id' })
    occurrenceId: string;

    @Column({ name: 'meeting_id' })
    @RelationId((self: RegisteredStandupAttendancePersistenceEntity) => self.meeting)
    meetingId: number;

    @Column({ name: 'duration', type: 'integer' })
    duration: number;

    @Column({ name: 'timestamp', type: 'timestamptz' })
    timestamp: Date;

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    private user: never;

    @ManyToOne(() => MeetingPersistenceEntity, { onDelete: 'CASCADE' })
    private meeting: never;

    constructor(params: NonFunctionProperties<RegisteredStandupAttendancePersistenceEntity>);
    constructor();

    constructor(params?: NonFunctionProperties<RegisteredStandupAttendancePersistenceEntity>) {
        if (!params) {
            super();
            return;
        }
        super(params.id, params.createdAt, params.updatedAt);
        Object.assign(this, params);
    }

    toDomain(): RegisteredStandupAttendance {
        return new RegisteredStandupAttendance({
            id: this.id ? new Id(this.id) : undefined,
            userId: new Id(this.userId),
            occurrenceId: new ZoomId(this.occurrenceId),
            meetingId: new Id(this.meetingId),
            duration: Duration.fromSeconds(this.duration),
            timestamp: this.timestamp,
        });
    }

    static fromDomain(domain: RegisteredStandupAttendance): RegisteredStandupAttendancePersistenceEntity {
        return new RegisteredStandupAttendancePersistenceEntity({
            id: domain.id?.value,
            userId: domain.userId.value,
            occurrenceId: domain.occurrenceId.value,
            meetingId: domain.meetingId.value,
            duration: domain.duration.asSeconds(),
            timestamp: domain.timestamp,
        });
    }
}
