import TransactionManager from '../../../../core/infrastructure/TransactionManager';
import TypeormRepository from '../../../../core/infrastructure/db/TypeormRepository';
import RegisteredStandupAttendancePersistenceEntity from './RegisteredStandupAttendancePersistenceEntity';
import { createMapperFromEntity } from '../../../../utils/createMapperFromEntity';
import { RegisteredStandupAttendance } from '../../../domain/RegisteredStandupAttendance';
import ZoomId from '../../../../meetings/domain/value-objects/ZoomId';

export class RegisteredStandupAttendanceRepository extends TypeormRepository<
    RegisteredStandupAttendance,
    RegisteredStandupAttendancePersistenceEntity
> {
    constructor(tm: TransactionManager) {
        super(
            tm,
            createMapperFromEntity(RegisteredStandupAttendancePersistenceEntity),
            RegisteredStandupAttendancePersistenceEntity,
        );
    }

    getByOccurrenceId(occurrenceId: ZoomId): Promise<RegisteredStandupAttendance[]> {
        return this.getAllWhere({ occurrenceId: occurrenceId.value });
    }
}
