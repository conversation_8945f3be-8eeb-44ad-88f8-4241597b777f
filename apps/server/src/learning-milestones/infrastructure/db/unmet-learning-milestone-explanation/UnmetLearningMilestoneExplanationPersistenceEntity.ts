import { Column, <PERSON>tity, ManyToOne, OneToMany, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../../core/infrastructure/db/TypeormPersistenceEntity';
import User from '../../../../users/shared/infrastructure/db/User';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { UnmetLearningMilestoneExplanationSituation } from '../../../domain/types/UnmetLearningMilestoneExplanationSituation';
import LearningMilestonePersistenceEntity from '../learning-milestone/LearningMilestonePersistenceEntity';

@Entity(UnmetLearningMilestoneExplanationPersistenceEntity.TABLE_NAME)
export default class UnmetLearningMilestoneExplanationPersistenceEntity extends TypeormPersistenceEntity {
    static TABLE_NAME = 'unmet_learning_milestone_explanation';

    @Column({
        name: 'situation',
        type: 'enum',
        enum: UnmetLearningMilestoneExplanationSituation,
    })
    situation: UnmetLearningMilestoneExplanationSituation;

    @Column({
        name: 'reasoning',
    })
    reasoning: string;

    @Column({ name: 'user_id' })
    @RelationId((self: UnmetLearningMilestoneExplanationPersistenceEntity) => self.user)
    userId: number;

    @OneToMany(() => LearningMilestonePersistenceEntity, (milestone) => milestone.explanationId)
    private milestones: never;

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    private user: never;

    constructor(params: NonFunctionProperties<UnmetLearningMilestoneExplanationPersistenceEntity>);
    constructor();

    constructor(params?: NonFunctionProperties<UnmetLearningMilestoneExplanationPersistenceEntity>) {
        if (!params) {
            super();
            return;
        }
        super(params.id, params.createdAt, params.updatedAt);
        Object.assign(this, params);
    }
}
