import Id from '../../../../core/domain/value-objects/Id';
import TypeormPersistenceMapper from '../../../../core/infrastructure/db/TypeormPersistenceMapper';
import { UnmetLearningMilestoneExplanation } from '../../../domain/UnmetLearningMilestoneExplanation';
import UnmetLearningMilestoneExplanationPersistenceEntity from './UnmetLearningMilestoneExplanationPersistenceEntity';

export default class UnmetLearningMilestoneExplanationPersistenceMapper
    implements
        TypeormPersistenceMapper<UnmetLearningMilestoneExplanation, UnmetLearningMilestoneExplanationPersistenceEntity>
{
    toDomain(persistence: UnmetLearningMilestoneExplanationPersistenceEntity): UnmetLearningMilestoneExplanation {
        return UnmetLearningMilestoneExplanation.fromParams({
            // @ts-expect-error TS(2345) FIXME: Argument of type 'number | undefined' is not assig... Remove this comment to see the full error message
            id: new Id(persistence.id),
            reasoning: persistence.reasoning,
            situation: persistence.situation,
            userId: new Id(persistence.userId),
        });
    }

    toPersistence(domain: UnmetLearningMilestoneExplanation): UnmetLearningMilestoneExplanationPersistenceEntity {
        return new UnmetLearningMilestoneExplanationPersistenceEntity({
            id: domain.id?.value,
            reasoning: domain.reasoning,
            situation: domain.situation,
            userId: domain.userId.value,
        });
    }
}
