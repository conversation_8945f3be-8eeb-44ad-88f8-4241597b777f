import TypeormRepository from '../../../../core/infrastructure/db/TypeormRepository';
import TransactionManager from '../../../../core/infrastructure/TransactionManager';
import { UnmetLearningMilestoneExplanation } from '../../../domain/UnmetLearningMilestoneExplanation';
import UnmetLearningMilestoneExplanationPersistenceEntity from './UnmetLearningMilestoneExplanationPersistenceEntity';
import UnmetLearningMilestoneExplanationPersistenceMapper from './UnmetLearningMilestoneExplanationPersistenceMapper';

export default class UnmetLearningMilestoneExplanationRepository extends TypeormRepository<
    UnmetLearningMilestoneExplanation,
    UnmetLearningMilestoneExplanationPersistenceEntity
> {
    constructor(tm: TransactionManager) {
        super(
            tm,
            new UnmetLearningMilestoneExplanationPersistenceMapper(),
            UnmetLearningMilestoneExplanationPersistenceEntity,
        );
    }
}
