import TypeormPersistenceMapper from '../../../../core/infrastructure/db/TypeormPersistenceMapper';
import Id from '../../../../core/domain/value-objects/Id';
import { DateRange } from '../../../../core/domain/value-objects/DateRange';
import LearningMilestoneGlobalExceptionPersistenceEntity from './LearningMilestoneGlobalExceptionPersistenceEntity';
import { LearningMilestoneGlobalException } from '../../../domain/LearningMilestoneGlobalException';

export default class LearningMilestoneGlobalExceptionPersistenceMapper
    implements
        TypeormPersistenceMapper<LearningMilestoneGlobalException, LearningMilestoneGlobalExceptionPersistenceEntity>
{
    toDomain(persistence: LearningMilestoneGlobalExceptionPersistenceEntity): LearningMilestoneGlobalException {
        return LearningMilestoneGlobalException.fromParams({
            id: persistence.id ? new Id(persistence.id) : undefined,
            period: DateRange.deserialize(persistence.period),
            name: persistence.name,
        });
    }

    toPersistence(domain: LearningMilestoneGlobalException): LearningMilestoneGlobalExceptionPersistenceEntity {
        return new LearningMilestoneGlobalExceptionPersistenceEntity({
            id: domain.id?.value,
            period: domain.period.serialize(),
            periodFrom: domain.period.from,
            periodTo: domain.period.to,
            name: domain.name,
        });
    }
}
