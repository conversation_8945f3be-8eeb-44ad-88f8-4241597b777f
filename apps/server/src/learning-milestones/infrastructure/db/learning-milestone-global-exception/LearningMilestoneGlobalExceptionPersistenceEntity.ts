import { Column, Entity, Exclusion, OneToMany } from 'typeorm';
import TypeormPersistenceEntity from '../../../../core/infrastructure/db/TypeormPersistenceEntity';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import LearningMilestonePersistenceEntity from '../learning-milestone/LearningMilestonePersistenceEntity';

@Entity(LearningMilestoneGlobalExceptionPersistenceEntity.TABLE_NAME)
@Exclusion(
    LearningMilestoneGlobalExceptionPersistenceEntity.GIST_UNIQUE_EXCLUSION_NAME,
    `USING gist (${LearningMilestoneGlobalExceptionPersistenceEntity.PERIOD_COLUMN_NAME} WITH &&)`,
)
export default class LearningMilestoneGlobalExceptionPersistenceEntity extends TypeormPersistenceEntity {
    static TABLE_NAME = 'learning_milestone_global_exception';

    static GIST_UNIQUE_EXCLUSION_NAME = 'lm_global_explanation_no_overlap_periods_excl';

    static NAME_COLUMN_NAME = 'name';

    static PERIOD_COLUMN_NAME = 'period';

    static PERIOD_FROM_COLUMN_NAME = 'period_from';

    static PERIOD_TO_COLUMN_NAME = 'period_to';

    @Column({ name: LearningMilestoneGlobalExceptionPersistenceEntity.NAME_COLUMN_NAME })
    name: string;

    @Column({ name: LearningMilestoneGlobalExceptionPersistenceEntity.PERIOD_COLUMN_NAME, type: 'tstzrange' })
    period: string;

    @Column({
        name: LearningMilestoneGlobalExceptionPersistenceEntity.PERIOD_FROM_COLUMN_NAME,
        type: 'timestamptz',
        nullable: true,
    })
    periodFrom: Date;

    @Column({
        name: LearningMilestoneGlobalExceptionPersistenceEntity.PERIOD_TO_COLUMN_NAME,
        type: 'timestamptz',
        nullable: true,
    })
    periodTo: Date;

    @OneToMany(() => LearningMilestonePersistenceEntity, (milestone) => milestone.exceptionId)
    private milestones: never;

    constructor(params: NonFunctionProperties<LearningMilestoneGlobalExceptionPersistenceEntity>);
    constructor();

    constructor(params?: NonFunctionProperties<LearningMilestoneGlobalExceptionPersistenceEntity>) {
        if (!params) {
            super();
            return;
        }
        super(params.id, params.createdAt, params.updatedAt);
        Object.assign(this, params);
    }
}
