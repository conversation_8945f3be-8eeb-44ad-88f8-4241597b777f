import { DatabaseError } from '../../../../core/infrastructure/db/errors/DatabaseError';
import TransactionManager from '../../../../core/infrastructure/TransactionManager';
import TypeormRepository from '../../../../core/infrastructure/db/TypeormRepository';
import LearningMilestoneGlobalExceptionPersistenceMapper from './LearningMilestoneGlobalExceptionPersistanceMapper';
import { LearningMilestoneGlobalException } from '../../../domain/LearningMilestoneGlobalException';
import LearningMilestoneGlobalExceptionPersistenceEntity from './LearningMilestoneGlobalExceptionPersistenceEntity';
import { Pagination, PaginationResult } from '../../../../core/domain/value-objects/Pagination';
import { Ordering } from '../../../../core/domain/value-objects/Ordering';
import Transaction from '../../../../core/infrastructure/Transaction';
import IllegalStateError from '../../../../core/errors/IllegalStateError';
import { Week } from '../../../../core/domain/value-objects/Week';
import { TypeormDateRangeHelper } from '../../../../core/infrastructure/db/TypeormDateRangeHelper';
import { DateRange } from '../../../../core/domain/value-objects/DateRange';

export default class LearningMilestoneGlobalExceptionRepository extends TypeormRepository<
    LearningMilestoneGlobalException,
    LearningMilestoneGlobalExceptionPersistenceEntity
> {
    constructor(tm: TransactionManager) {
        super(
            tm,
            new LearningMilestoneGlobalExceptionPersistenceMapper(),
            LearningMilestoneGlobalExceptionPersistenceEntity,
        );
    }

    async saveException({
        exception,
        tx,
    }: {
        exception: LearningMilestoneGlobalException;
        tx?: Transaction;
    }): Promise<LearningMilestoneGlobalException> {
        return this.transactionManager.execute(async (transaction) => {
            let createdException;
            try {
                createdException = await transaction.entityManager.save(
                    LearningMilestoneGlobalExceptionPersistenceEntity,
                    this.mapper.toPersistence(exception),
                );
            } catch (e) {
                if (e?.constraint === LearningMilestoneGlobalExceptionPersistenceEntity.GIST_UNIQUE_EXCLUSION_NAME) {
                    throw new IllegalStateError('Exception for this period already exists');
                }
                throw new DatabaseError(e);
            }

            return this.mapper.toDomain(createdException);
        }, tx);
    }

    async getWithPagination({
        pagination,
    }: {
        pagination?: Pagination;
    }): Promise<PaginationResult<LearningMilestoneGlobalException>> {
        return this.getAllWhereAdvanced({
            where: {},
            pagination,
            order: Ordering.fromParams({
                field: 'period',
                direction: -1,
            }),
        });
    }

    async getForWeek({
        week,
        tx,
    }: {
        week: Week;
        tx?: Transaction;
    }): Promise<LearningMilestoneGlobalException | undefined> {
        return this.getWhere(
            {
                period: TypeormDateRangeHelper.intersects(week.range),
            },
            tx,
        );
    }

    getAllWithinRange({
        range,
        tx,
    }: {
        range: DateRange;
        tx?: Transaction;
    }): Promise<LearningMilestoneGlobalException[]> {
        return this.getAllWhere(
            {
                period: TypeormDateRangeHelper.intersects(range),
            },
            tx,
        );
    }
}
