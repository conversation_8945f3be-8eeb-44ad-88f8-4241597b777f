import Container from 'typedi';
import { Configuration } from '../../../config/Configuration';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import CreateWeeklyStandupAttendanceMilestonesCronJob from '../../controllers/cron/CreateWeeklyStandupAttendanceMilestonesCronJob';
import {
    LearningMilestoneGlobalExceptionRepositoryToken,
    LearningMilestonesGlobalExceptionsFinderToken,
    LearningMilestonesGlobalExceptionsServiceToken,
    LearningMilestonesServiceToken,
    RegisteredStandupAttendanceRepositoryToken,
    StandupAttendanceChangedRequirementsNotifierServiceToken,
    StandupAttendanceMilestonesNotifierServiceToken,
    StandupAttendanceTrackerServiceToken,
    UnmetLearningMilestonesExplanationsServiceToken,
} from './tokens';
import { LearningMilestonesService } from '../../services/LearningMilestonesService';
import LearningMilestoneRepository from '../db/learning-milestone/LearningMilestoneRepository';
import UnmetLearningMilestoneExplanationRepository from '../db/unmet-learning-milestone-explanation/UnmetLearningMilestoneExplanationRepository';
import NotifyUsersAboutUnmetMilestonesByPlatformCronJob from '../../controllers/cron/NotifyUsersAboutUnmetMilestonesByPlatformCronJob';
import { NotificationManager } from '../../../notifications/NotificationManager';
import Mailer from '../../../mailer/Mailer';
import { StandupAttendanceUnmetMilestonesNotifierService } from '../../services/StandupAttendanceUnmetMilestonesNotifierService';
import NotifyUsersAboutUnmetMilestonesByEmailCronJob from '../../controllers/cron/NotifyUsersAboutUnmetMilestonesByEmailCronJob';
import { UnmetLearningMilestonesExplanationsService } from '../../services/UnmetLearningMilestonesExplanationsService';
import { LearningMilestonesGlobalExceptionsService } from '../../services/LearningMilestonesGlobalExceptionsService';
import LearningMilestoneGlobalExceptionRepository from '../db/learning-milestone-global-exception/LearningMilestoneGlobalExceptionRepository';
import { LearningMilestonesGlobalExceptionsFinder } from '../../services/LearningMilestonesGlobalExceptionsFinder';
import { StandupAttendanceChangedRequirementsNotifierService } from '../../services/StandupAttendanceChangedRequirementsNotifierService';
import NotifyStandupAttendanceRequirementsChangedByEmailCronJob from '../../controllers/cron/NotifyStandupAttendanceRequirementsChangedByEmailCronJob';
import NotifyStandupAttendanceRequirementsChangedByPlatformCronJob from '../../controllers/cron/NotifyStandupAttendanceRequirementsChangedByPlatformCronJob';
import { StandupAttendanceTrackerService } from '../../services/StandupAttendanceTrackerService';
import { RegisteredStandupAttendanceRepository } from '../db/registered-standup-attendance/RegisteredStandupAttendanceRepository';
import { LearningStandupAttendanceRequirementsModule } from '../../../learning/submodules/standup-attendance-requirements/infrastructure/di/LearningStandupAttendanceRequirementsModule';
import { UsersAccountsModule } from '../../../users/accounts/infrastructure/di/UsersAccountsModule';
import { CalendarModule } from '../../../calendar/infrastructure/di/CalendarModule';
import { MeetingModule } from '../../../meetings/infrastructure/di/MeetingModule';
import { LoggingModule } from '../../../logging/infrastructure/di/LoggingModule';
import CronScheduler from '../../../cron/CronScheduler';
import { ModuleOnStart, ModuleWithControllers } from '../../../di/types/ApplicationModule';
import CronPattern from '../../../cron/CronPattern';
import { CronModule } from '../../../cron/infrastructure/di/CronModule';
import { LearningMilestonesUserController } from '../../controllers/http/milestones/LearningMilestonesUserController';
import { LearningMilestonesController } from '../../controllers/http/milestones/LearningMilestonesController';
import { LearningMilestonesGlobalExceptionsController } from '../../controllers/http/exceptions/LearningMilestonesGlobalExceptionsController';
import { LearningMilestonesExplanationsController } from '../../controllers/http/explanations/LearningMilestonesExplanationsController';

export interface LearningMilestonesParams {
    configuration: Configuration;
    loggingModule: LoggingModule;
    transactionManager: TransactionManager;
    learningStandupAttendanceRequirementsModule: LearningStandupAttendanceRequirementsModule;
    calendarModule: CalendarModule;
    notificationManager: NotificationManager;
    mailer: Mailer;
    usersAccountsModule: UsersAccountsModule;
    meetingModule: MeetingModule;
    cronModule: CronModule;
}

export class LearningMilestonesModule implements ModuleOnStart, ModuleWithControllers {
    constructor(
        private readonly cronScheduler: CronScheduler,
        private readonly configuration: Configuration,
        private readonly createLearningGoalsCronJob: CreateWeeklyStandupAttendanceMilestonesCronJob,
        private readonly notifyUsersAboutUnmetMilestonesByEmailCronJob: NotifyUsersAboutUnmetMilestonesByEmailCronJob,
        private readonly notifyUsersAboutUnmetMilestonesByPlatformCronJob: NotifyUsersAboutUnmetMilestonesByPlatformCronJob,
        private readonly notifyStandupAttendanceRequirementsChangedByEmailCronJob: NotifyStandupAttendanceRequirementsChangedByEmailCronJob,
        private readonly notifyStandupAttendanceRequirementsChangedByPlatformCronJob: NotifyStandupAttendanceRequirementsChangedByPlatformCronJob,
        readonly learningMilestonesService: LearningMilestonesService,
    ) {}

    moduleOnStart(): void | Promise<void> {
        this.cronScheduler.schedule(
            CronPattern.fromString(this.configuration.cron.createWeeklyStandupAttendanceMilestones),
            this.createLearningGoalsCronJob,
        );
        this.cronScheduler.schedule(
            CronPattern.fromString(this.configuration.cron.notifyUsersAboutUnmetMilestonesByPlatform),
            this.notifyUsersAboutUnmetMilestonesByPlatformCronJob,
        );
        this.cronScheduler.schedule(
            CronPattern.fromString(this.configuration.cron.notifyUsersAboutUnmetMilestonesByEmail),
            this.notifyUsersAboutUnmetMilestonesByEmailCronJob,
        );
        this.cronScheduler.schedule(
            CronPattern.fromString(this.configuration.cron.notifyStandupAttendanceRequirementsChangedByEmail),
            this.notifyStandupAttendanceRequirementsChangedByEmailCronJob,
        );
        this.cronScheduler.schedule(
            CronPattern.fromString(this.configuration.cron.notifyStandupAttendanceRequirementsChangedByPlatform),
            this.notifyStandupAttendanceRequirementsChangedByPlatformCronJob,
        );
    }

    getName(): string {
        return LearningMilestonesModule.name;
    }

    getControllers(): any[] {
        return [
            LearningMilestonesUserController,
            LearningMilestonesController,
            LearningMilestonesGlobalExceptionsController,
            LearningMilestonesExplanationsController,
        ];
    }

    static init({
        configuration,
        loggingModule,
        transactionManager,
        learningStandupAttendanceRequirementsModule,
        calendarModule,
        notificationManager,
        mailer,
        usersAccountsModule,
        meetingModule,
        cronModule,
    }: LearningMilestonesParams): LearningMilestonesModule {
        const learningMilestoneRepository = new LearningMilestoneRepository(transactionManager);
        const registeredStandupAttendanceRepository = new RegisteredStandupAttendanceRepository(transactionManager);
        const unmetLearningMilestoneExplanationRepository = new UnmetLearningMilestoneExplanationRepository(
            transactionManager,
        );
        const learningMilestoneGlobalExceptionRepository = new LearningMilestoneGlobalExceptionRepository(
            transactionManager,
        );

        const learningMilestonesGlobalExceptionsFinder = new LearningMilestonesGlobalExceptionsFinder(
            learningMilestoneGlobalExceptionRepository,
        );

        const standupAttendanceChangedRequirementsNotifierService =
            new StandupAttendanceChangedRequirementsNotifierService(
                configuration,
                loggingModule.loggingService,
                notificationManager,
                mailer,
                usersAccountsModule.userAccountFinder,
            );

        const learningMilestonesService = new LearningMilestonesService(
            loggingModule.loggingService,
            learningMilestoneRepository,
            learningStandupAttendanceRequirementsModule.standupAttendanceRequirementsService,
            learningMilestonesGlobalExceptionsFinder,
            standupAttendanceChangedRequirementsNotifierService,
        );

        const standupAttendanceTrackingService = new StandupAttendanceTrackerService(
            configuration,
            calendarModule.calendarEventService,
            meetingModule.axiosZoomClient,
            learningMilestoneRepository,
            registeredStandupAttendanceRepository,
            transactionManager,
            loggingModule.loggingService.createLogger(StandupAttendanceTrackerService.name),
            meetingModule.meetingAttendeeRepository,
        );

        const unmetLearningMilestonesExplanationsService = new UnmetLearningMilestonesExplanationsService(
            unmetLearningMilestoneExplanationRepository,
            transactionManager,
            learningMilestonesService,
        );

        const standupAttendanceMilestonesNotifierService = new StandupAttendanceUnmetMilestonesNotifierService(
            configuration,
            transactionManager,
            learningMilestonesService,
            notificationManager,
            mailer,
            usersAccountsModule.userAccountFinder,
        );

        const learningMilestonesGlobalExceptionsService = new LearningMilestonesGlobalExceptionsService(
            learningMilestoneGlobalExceptionRepository,
            transactionManager,
            learningMilestonesService,
        );

        const createLearningGoalsCronJob = new CreateWeeklyStandupAttendanceMilestonesCronJob(
            loggingModule.loggingService,
            learningMilestonesService,
        );
        const notifyUsersAboutUnmetMilestonesByPlatformCronJob = new NotifyUsersAboutUnmetMilestonesByPlatformCronJob(
            loggingModule.loggingService,
            standupAttendanceMilestonesNotifierService,
        );
        const notifyUsersAboutUnmetMilestonesByEmailCronJob = new NotifyUsersAboutUnmetMilestonesByEmailCronJob(
            loggingModule.loggingService,
            standupAttendanceMilestonesNotifierService,
        );

        const notifyStandupAttendanceRequirementsChangedByEmailCronJob =
            new NotifyStandupAttendanceRequirementsChangedByEmailCronJob(
                loggingModule.loggingService,
                learningMilestonesService,
            );

        const notifyStandupAttendanceRequirementsChangedByPlatformCronJob =
            new NotifyStandupAttendanceRequirementsChangedByPlatformCronJob(
                loggingModule.loggingService,
                learningMilestonesService,
            );

        Container.set(LearningMilestoneGlobalExceptionRepositoryToken, learningMilestoneGlobalExceptionRepository);
        Container.set(LearningMilestonesGlobalExceptionsServiceToken, learningMilestonesGlobalExceptionsService);
        Container.set(LearningMilestonesGlobalExceptionsFinderToken, learningMilestonesGlobalExceptionsFinder);
        Container.set(UnmetLearningMilestonesExplanationsServiceToken, unmetLearningMilestonesExplanationsService);
        Container.set(LearningMilestonesServiceToken, learningMilestonesService);
        Container.set(StandupAttendanceMilestonesNotifierServiceToken, standupAttendanceMilestonesNotifierService);
        Container.set(
            StandupAttendanceChangedRequirementsNotifierServiceToken,
            standupAttendanceChangedRequirementsNotifierService,
        );

        Container.set(StandupAttendanceTrackerServiceToken, standupAttendanceTrackingService);
        Container.set(RegisteredStandupAttendanceRepositoryToken, registeredStandupAttendanceRepository);

        return new LearningMilestonesModule(
            cronModule.cronScheduler,
            configuration,
            createLearningGoalsCronJob,
            notifyUsersAboutUnmetMilestonesByEmailCronJob,
            notifyUsersAboutUnmetMilestonesByPlatformCronJob,
            notifyStandupAttendanceRequirementsChangedByEmailCronJob,
            notifyStandupAttendanceRequirementsChangedByPlatformCronJob,
            learningMilestonesService,
        );
    }
}
