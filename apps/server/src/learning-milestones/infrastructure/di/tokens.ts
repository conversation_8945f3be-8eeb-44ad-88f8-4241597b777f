import { Token } from 'typedi';
import CreateWeeklyStandupAttendanceMilestonesCronJob from '../../controllers/cron/CreateWeeklyStandupAttendanceMilestonesCronJob';
import { LearningMilestonesService } from '../../services/LearningMilestonesService';
import { StandupAttendanceUnmetMilestonesNotifierService } from '../../services/StandupAttendanceUnmetMilestonesNotifierService';
import { UnmetLearningMilestonesExplanationsService } from '../../services/UnmetLearningMilestonesExplanationsService';
import { LearningMilestonesGlobalExceptionsService } from '../../services/LearningMilestonesGlobalExceptionsService';
import { LearningMilestonesGlobalExceptionsFinder } from '../../services/LearningMilestonesGlobalExceptionsFinder';
import LearningMilestoneGlobalExceptionRepository from '../db/learning-milestone-global-exception/LearningMilestoneGlobalExceptionRepository';
import { StandupAttendanceChangedRequirementsNotifierService } from '../../services/StandupAttendanceChangedRequirementsNotifierService';
import { tokenFor } from '../../../di/helpers/tokenFor';
import { StandupAttendanceTrackerService } from '../../services/StandupAttendanceTrackerService';
import { RegisteredStandupAttendanceRepository } from '../db/registered-standup-attendance/RegisteredStandupAttendanceRepository';

export const CreateWeeklyStandupAttendanceMilestonesCronJobToken =
    new Token<CreateWeeklyStandupAttendanceMilestonesCronJob>('CreateWeeklyStandupAttendanceMilestonesCronJob');

export const LearningMilestonesServiceToken = new Token<LearningMilestonesService>('LearningMilestonesService');
export const UnmetLearningMilestonesExplanationsServiceToken = new Token<UnmetLearningMilestonesExplanationsService>(
    'UnmetLearningMilestonesExplanationsService',
);
export const LearningMilestonesGlobalExceptionsServiceToken = new Token<LearningMilestonesGlobalExceptionsService>(
    'LearningMilestonesGlobalExceptionsService',
);

/**
 * @deprecated can only be used in tests
 */
export const LearningMilestoneGlobalExceptionRepositoryToken = new Token<LearningMilestoneGlobalExceptionRepository>(
    'LearningMilestoneGlobalExceptionRepository',
);
export const StandupAttendanceMilestonesNotifierServiceToken =
    new Token<StandupAttendanceUnmetMilestonesNotifierService>('StandupAttendanceMilestonesNotifierService');
export const StandupAttendanceChangedRequirementsNotifierServiceToken =
    new Token<StandupAttendanceChangedRequirementsNotifierService>(
        'StandupAttendanceChangedRequirementsNotifierService',
    );
export const LearningMilestonesGlobalExceptionsFinderToken = new Token<LearningMilestonesGlobalExceptionsFinder>(
    'LearningMilestonesGlobalExceptionsFinder',
);

export const StandupAttendanceTrackerServiceToken = tokenFor(StandupAttendanceTrackerService);
/**
 * @deprecated can only be used in tests
 */
export const RegisteredStandupAttendanceRepositoryToken = tokenFor(RegisteredStandupAttendanceRepository);
