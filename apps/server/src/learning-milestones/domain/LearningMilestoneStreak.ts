import { Mutable, NonFunctionProperties } from '../../utils/UtilityTypes';
import Id from '../../core/domain/value-objects/Id';

export type LearningMilestoneStreakParams = Mutable<NonFunctionProperties<LearningMilestoneStreak>>;

export class LearningMilestoneStreak {
    readonly userId: Id;
    readonly streak: number;
    readonly lastWeek: {
        target: number;
        milestoneId: Id;
    };

    private constructor(params: LearningMilestoneStreakParams) {
        this.userId = params.userId;
        this.streak = params.streak;
        this.lastWeek = params.lastWeek;
    }

    static fromParams(params: LearningMilestoneStreakParams): LearningMilestoneStreak {
        return new LearningMilestoneStreak(params);
    }
}
