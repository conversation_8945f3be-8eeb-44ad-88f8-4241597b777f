import DomainEntity from '../../core/domain/DomainEntity';
import Id from '../../core/domain/value-objects/Id';
import { Mutable, NonFunctionProperties } from '../../utils/UtilityTypes';
import { UnmetLearningMilestoneExplanationSituation } from './types/UnmetLearningMilestoneExplanationSituation';

export type UnmetLearningMilestoneExplanationParams = Mutable<NonFunctionProperties<UnmetLearningMilestoneExplanation>>;

export class UnmetLearningMilestoneExplanation extends DomainEntity {
    readonly situation: UnmetLearningMilestoneExplanationSituation;
    readonly reasoning: string;
    readonly userId: Id;

    private constructor(params: UnmetLearningMilestoneExplanationParams) {
        super(params.id);
        this.situation = params.situation;
        this.reasoning = params.reasoning;
        this.userId = params.userId;
    }

    static fromParams(params: UnmetLearningMilestoneExplanationParams): UnmetLearningMilestoneExplanation {
        return new UnmetLearningMilestoneExplanation(params);
    }
}
