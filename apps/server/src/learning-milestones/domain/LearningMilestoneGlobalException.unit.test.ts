import { LearningMilestoneGlobalException } from './LearningMilestoneGlobalException';
import { Week } from '../../core/domain/value-objects/Week';
import IllegalStateError from '../../core/errors/IllegalStateError';
import { LearningMilestonesConstants } from './LearningMilestonesConstants';

describe(LearningMilestoneGlobalException.name, () => {
    const name = 'test';

    describe('create', () => {
        it('should not allow to create exception for past week', () => {
            expect(() =>
                LearningMilestoneGlobalException.create({
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-2).range,
                    name,
                }),
            ).toThrow(IllegalStateError);
        });

        it('should not allow to create exception for this week', () => {
            expect(() =>
                LearningMilestoneGlobalException.create({
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).range,
                    name,
                }),
            ).toThrow(IllegalStateError);
        });

        it('should allow to create exception for next week', () => {
            expect(() =>
                LearningMilestoneGlobalException.create({
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).next().range,
                    name,
                }),
            ).not.toThrow(IllegalStateError);
        });

        it(`should allow to create exception for week in future before ${LearningMilestoneGlobalException.MAX_EXTENSION_WEEKS_AHEAD} + 1 weeks`, () => {
            expect(() =>
                LearningMilestoneGlobalException.create({
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(
                        LearningMilestoneGlobalException.MAX_EXTENSION_WEEKS_AHEAD + 1,
                    ).range,
                    name,
                }),
            ).not.toThrow(IllegalStateError);
        });

        it(`should not allow to create exception after ${LearningMilestoneGlobalException.MAX_EXTENSION_WEEKS_AHEAD} + 1 weeks`, () => {
            expect(() =>
                LearningMilestoneGlobalException.create({
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(
                        LearningMilestoneGlobalException.MAX_EXTENSION_WEEKS_AHEAD + 2,
                    ).range,
                    name,
                }),
            ).toThrow(IllegalStateError);
        });
    });
});
