import Id from '../../core/domain/value-objects/Id';
import { NonFunctionProperties } from '../../utils/UtilityTypes';
import DomainEntity from '../../core/domain/DomainEntity';
import { Duration } from '../../core/domain/value-objects/Duration';
import ZoomId from '../../meetings/domain/value-objects/ZoomId';

export type RegisteredStandupAttendanceParams = NonFunctionProperties<RegisteredStandupAttendance>;

export class RegisteredStandupAttendance extends DomainEntity {
    readonly userId: Id;
    readonly occurrenceId: ZoomId;
    readonly meetingId: Id;
    readonly duration: Duration;
    readonly timestamp: Date;

    constructor(params: RegisteredStandupAttendanceParams) {
        super(params.id);
        this.userId = params.userId;
        this.occurrenceId = params.occurrenceId;
        this.meetingId = params.meetingId;
        this.duration = params.duration;
        this.timestamp = params.timestamp;
    }

    updateDuration(duration: Duration): RegisteredStandupAttendance {
        if (duration.equals(this.duration)) {
            return this;
        }

        return new RegisteredStandupAttendance({
            ...this,
            duration,
        });
    }
}
