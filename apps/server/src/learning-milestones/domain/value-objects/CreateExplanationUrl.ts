import Id from '../../../core/domain/value-objects/Id';
import Url from '../../../core/domain/value-objects/Url';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';

export default class CreateExplanationUrl extends Url {
    private static readonly PATH = '/settings/standups';

    private static readonly QUERY_PARAM = 'milestoneId';

    static fromIds(clientUrl: Url, milestoneIds: Id[]): Url {
        ArgumentValidation.assert.defined(milestoneIds, 'Milestones IDs are required');

        return milestoneIds.reduce(
            (url, milestoneId) => url.addQuery(CreateExplanationUrl.QUERY_PARAM, milestoneId.value.toString()),
            clientUrl.addPath(CreateExplanationUrl.PATH),
        );
    }
}
