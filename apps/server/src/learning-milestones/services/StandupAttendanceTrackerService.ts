import CalendarEventCategory from '../../calendar/domain/CalendarEventCategory';
import { Week } from '../../core/domain/value-objects/Week';
import { LearningMilestoneType } from '../domain/types/LearningMilestoneType';
import { CalendarEventService } from '../../calendar/services/CalendarEventService';
import ZoomClient from '../../meetings/services/zoom/ZoomClient';
import { Duration } from '../../core/domain/value-objects/Duration';
import { Configuration } from '../../config/Configuration';
import LearningMilestoneRepository from '../infrastructure/db/learning-milestone/LearningMilestoneRepository';
import { RegisteredStandupAttendanceRepository } from '../infrastructure/db/registered-standup-attendance/RegisteredStandupAttendanceRepository';
import TransactionManager from '../../core/infrastructure/TransactionManager';
import { RegisteredStandupAttendance } from '../domain/RegisteredStandupAttendance';
import Id from '../../core/domain/value-objects/Id';
import Logger from '../../utils/logger/Logger';
import ZoomId from '../../meetings/domain/value-objects/ZoomId';
import MeetingAttendeeRepository from '../../meetings/domain/MeetingAttendeeRepository';
import MeetingAttendee from '../../meetings/domain/MeetingAttendee';
import { LearningMilestonesConstants } from '../domain/LearningMilestonesConstants';

export class StandupAttendanceTrackerService {
    private readonly requiredStandupAttendanceDuration: Duration;

    constructor(
        configuration: Configuration,
        private readonly calendarEventService: CalendarEventService,
        private readonly zoomClient: ZoomClient,
        private readonly learningMilestoneRepository: LearningMilestoneRepository,
        private readonly registeredStandupAttendanceRepository: RegisteredStandupAttendanceRepository,
        private readonly transactionManager: TransactionManager,
        private readonly logger: Logger,
        private readonly meetingAttendeeRepository: MeetingAttendeeRepository,
    ) {
        this.requiredStandupAttendanceDuration = new Duration(
            configuration.learningMilestones.standup.attendanceTimeRequirement,
        );
    }

    async registerStandupAttendanceWhenMeetingEnded({
        meetingId,
        occurrenceId,
        timestamp,
    }: {
        meetingId: Id;
        occurrenceId: ZoomId;
        timestamp: Date;
    }): Promise<void> {
        this.logger.debug(`Registering standup attendance for meeting ${meetingId.value}.`);

        const calendarEvent = await this.calendarEventService.getByMeetingId(meetingId);

        if (!calendarEvent || calendarEvent.category !== CalendarEventCategory.STANDUP) {
            this.logger.debug(`Event ${meetingId.value} is not a standup. Skipping.`);

            return;
        }

        const participants = await this.zoomClient.getPastMeetingParticipants(occurrenceId);

        this.logger.debug(`Found ${participants.length} participants for meeting ${meetingId.value}.`);

        if (!participants.length) {
            return;
        }

        const groupedParticipants = participants.getParticipantsGroupedRegistrantId();

        if (!groupedParticipants.size) {
            this.logger.debug(`No participants found for meeting ${meetingId.value}.`);

            return;
        }

        const attendees = await this.meetingAttendeeRepository.getManyByZoom(
            Array.from(groupedParticipants.keys()).map((registrantId) => new ZoomId(registrantId)),
        );

        const registeredAttendances = await this.registeredStandupAttendanceRepository.getByOccurrenceId(occurrenceId);
        const newUsersPassedThreshold: Id[] = [];

        const updatedAttendances = attendees.map((attendee: MeetingAttendee) => {
            const existing = registeredAttendances.find((attendance) => attendance.userId.equals(attendee.userId));
            const durationBefore = existing?.duration ?? Duration.fromSeconds(0);
            const durationAfter = groupedParticipants.get(attendee.zoomId.value) ?? Duration.fromSeconds(0);

            if (
                durationBefore.isLessThan(this.requiredStandupAttendanceDuration) &&
                durationAfter.isGreaterThanOrEqual(this.requiredStandupAttendanceDuration)
            ) {
                newUsersPassedThreshold.push(attendee.userId!);
            }

            if (!existing) {
                return new RegisteredStandupAttendance({
                    userId: attendee.userId!,
                    occurrenceId: occurrenceId,
                    meetingId: meetingId,
                    duration: durationAfter,
                    timestamp: timestamp,
                });
            }

            return existing.updateDuration(durationAfter);
        });

        this.logger.debug(`Updating ${updatedAttendances.length} attendance records.`);

        await this.transactionManager.execute(async (tx) => {
            await this.registeredStandupAttendanceRepository.saveAll(updatedAttendances, tx);

            if (!newUsersPassedThreshold.length) {
                return;
            }

            await this.learningMilestoneRepository.incrementMilestoneProgress({
                period: Week.fromDate(timestamp, LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).range,
                userIds: newUsersPassedThreshold,
                milestoneType: LearningMilestoneType.STANDUP_ATTENDANCE,
                tx,
            });
        });
    }
}
