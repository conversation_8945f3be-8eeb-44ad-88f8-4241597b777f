import TransactionManager from '../../core/infrastructure/TransactionManager';
import LearningMilestoneGlobalExceptionRepository from '../infrastructure/db/learning-milestone-global-exception/LearningMilestoneGlobalExceptionRepository';
import {
    LearningMilestoneGlobalException,
    LearningMilestoneGlobalExceptionParams,
} from '../domain/LearningMilestoneGlobalException';
import Transaction from '../../core/infrastructure/Transaction';
import { LearningMilestonesService } from './LearningMilestonesService';
import Id from '../../core/domain/value-objects/Id';

export class LearningMilestonesGlobalExceptionsService {
    constructor(
        private readonly learningMilestoneGlobalExceptionRepository: LearningMilestoneGlobalExceptionRepository,
        private readonly transactionManager: TransactionManager,
        private readonly learningMilestonesService: LearningMilestonesService,
    ) {}

    createException({
        exceptionParams,
        tx,
    }: {
        exceptionParams: LearningMilestoneGlobalExceptionParams;
        tx?: Transaction;
    }): Promise<LearningMilestoneGlobalException> {
        const newException = LearningMilestoneGlobalException.create({
            name: exceptionParams.name,
            period: exceptionParams.period,
        });

        return this.transactionManager.execute(async (transaction) => {
            const createdException = await this.learningMilestoneGlobalExceptionRepository.saveException({
                exception: newException,
                tx,
            });

            await this.learningMilestonesService.applyExceptionToMilestones({
                exception: createdException,
                tx: transaction,
            });

            return createdException;
        }, tx);
    }

    deleteException(id: Id, tx?: Transaction): Promise<void> {
        return this.learningMilestoneGlobalExceptionRepository.delete(id, tx);
    }

    updateException({
        id,
        exceptionParams,
        tx,
    }: {
        id: Id;
        exceptionParams: LearningMilestoneGlobalExceptionParams;
        tx?: Transaction;
    }): Promise<LearningMilestoneGlobalException> {
        return this.transactionManager.execute(async (transaction) => {
            const exception = await this.learningMilestoneGlobalExceptionRepository.get(id, tx);

            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            const updatedException = exception.update({
                name: exceptionParams.name,
                period: exceptionParams.period,
            });

            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            if (!exception.period.equals(updatedException.period)) {
                await this.learningMilestonesService.unlinkExceptionFromMilestones({
                    // @ts-expect-error TS(2322) FIXME: Type 'LearningMilestoneGlobalException | undefined... Remove this comment to see the full error message
                    exception: exception,
                    tx: transaction,
                });
                await this.learningMilestonesService.applyExceptionToMilestones({
                    exception: updatedException,
                    tx: transaction,
                });
            }

            await this.learningMilestoneGlobalExceptionRepository.saveException({
                exception: updatedException,
                tx: transaction,
            });

            return updatedException;
        }, tx);
    }
}
