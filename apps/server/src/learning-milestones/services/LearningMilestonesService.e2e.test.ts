import Container from 'typedi';
import { Week } from '../../core/domain/value-objects/Week';
import { LearningMilestone } from '../domain/LearningMilestone';
import { LearningMilestoneStreak } from '../domain/LearningMilestoneStreak';
import { LearningMilestoneType } from '../domain/types/LearningMilestoneType';
import { LearningMilestonesServiceToken } from '../infrastructure/di/tokens';
import { LearningMilestonesService } from './LearningMilestonesService';
import Arrays from '../../utils/Arrays';
import { RequireOnly } from '../../utils/UtilityTypes';
import { IntegrationTestBatch } from '../../test-toolkit/e2e/entities/IntegrationTestBatch';
import { IntegrationTestCourse } from '../../test-toolkit/e2e/entities/IntegrationTestCourse';
import { IntegrationTestLearningMilestone } from '../../test-toolkit/e2e/entities/IntegrationTestLearningMilestone';
import { IntegrationTestUnmetLearningMilestoneExplanation } from '../../test-toolkit/e2e/entities/IntegrationTestUnmetLearningMilestoneExplanation';
import { IntegrationTestUser } from '../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestLearningMilestoneGlobalException } from '../../test-toolkit/e2e/entities/IntegrationTestLearningMilestoneGlobalException';
import { UserStandupAttendanceRequirementChange } from '../../users/students/settings/domain/UserStandupAttendanceRequirementChange';
import { LearningMilestonesConstants } from '../domain/LearningMilestonesConstants';

describe(LearningMilestonesService.name, () => {
    let learningMilestonesService: LearningMilestonesService;
    let batch: IntegrationTestBatch;
    let course: IntegrationTestCourse;

    async function createStudentWithSettings(): Promise<IntegrationTestUser> {
        const student = await IntegrationTestUser.createFakeStudent();
        await student.createStudentSettings(batch.getIdOrThrow());

        return student;
    }

    async function createMilestones(
        user: IntegrationTestUser,
        milestones: RequireOnly<LearningMilestone, 'period'>[],
    ): Promise<IntegrationTestLearningMilestone[]> {
        return Promise.all(
            milestones.map((milestone) => {
                return IntegrationTestLearningMilestone.create({
                    userId: user.getIdOrThrow(),
                    type: LearningMilestoneType.STANDUP_ATTENDANCE,
                    period: milestone.period,
                    target: milestone.target ?? 1,
                    progress: milestone.progress ?? 1,
                    explanationId: milestone.explanationId,
                    exceptionId: milestone.exceptionId,
                });
            }),
        );
    }

    beforeAll(async () => {
        learningMilestonesService = Container.get(LearningMilestonesServiceToken);

        course = await IntegrationTestCourse.create();
        batch = await IntegrationTestBatch.create({
            courseId: course.getIdOrThrow(),
        });
    });

    describe('getUsersUnmetMilestonesStreakForLastThreeWeeks', () => {
        const lastFiveWeeks = Arrays.stream(4).reduce(
            (weeks) => {
                const lastWeek = weeks[weeks.length - 1];
                return [...weeks, lastWeek.jump(-1)];
            },
            [Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE)],
        );

        function findUserStreak(
            streaks: LearningMilestoneStreak[],
            user: IntegrationTestUser,
        ): LearningMilestoneStreak | undefined {
            return streaks.find((streak) => streak.userId.equals(user.id));
        }

        it('should find correct users according to milestones', async () => {
            const noMilestonesUser = await createStudentWithSettings();
            await createMilestones(noMilestonesUser, []);

            const allMetMilestonesUser = await createStudentWithSettings();
            await createMilestones(
                allMetMilestonesUser,
                lastFiveWeeks.map((week) => {
                    return {
                        period: week.range,
                        target: 1,
                        progress: 1,
                    };
                }),
            );

            const allUnmetMilestonesUser = await createStudentWithSettings();
            await createMilestones(
                allUnmetMilestonesUser,
                lastFiveWeeks.map((week) => {
                    return {
                        period: week.range,
                        target: 3,
                        progress: 0,
                    };
                }),
            );

            const twoWeeksAgoException = await IntegrationTestLearningMilestoneGlobalException.create({
                name: 'Test exception',
                period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-2).range,
            });
            const threeWeeksAgoException = await IntegrationTestLearningMilestoneGlobalException.create({
                name: 'Test exception',
                period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-3).range,
            });
            const fourWeeksAgoException = await IntegrationTestLearningMilestoneGlobalException.create({
                name: 'Test exception',
                period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-4).range,
            });

            const allUnmetMilestonesButWithExceptionUser = await createStudentWithSettings();

            await createMilestones(
                allUnmetMilestonesButWithExceptionUser,
                lastFiveWeeks.map((week, i) => {
                    return {
                        period: week.range,
                        target: 3,
                        progress: 0,
                        exceptionId: i === 3 ? threeWeeksAgoException.getIdOrThrow() : undefined,
                    };
                }),
            );

            const lastWeekSuccessAfterBadStreak = await createStudentWithSettings();
            await createMilestones(
                lastWeekSuccessAfterBadStreak,
                lastFiveWeeks.map((week, i) => {
                    return {
                        period: week.range,
                        target: 1,
                        progress: i === 1 ? 1 : 0,
                    };
                }),
            );

            const lastWeekFailed = await createStudentWithSettings();
            await createMilestones(
                lastWeekFailed,
                lastFiveWeeks.map((week, i) => {
                    return {
                        period: week.range,
                        target: 1,
                        progress: i === 1 ? 0 : 1,
                    };
                }),
            );

            const lastWeekFailedButExplained = await createStudentWithSettings();
            const explanation = await IntegrationTestUnmetLearningMilestoneExplanation.create({
                userId: lastWeekFailedButExplained.getIdOrThrow(),
            });
            await createMilestones(
                lastWeekFailedButExplained,
                lastFiveWeeks.map((week, i) => {
                    return {
                        period: week.range,
                        target: 1,
                        progress: i === 1 ? 0 : 1,
                        explanationId: i === 1 ? explanation.getIdOrThrow() : undefined,
                    };
                }),
            );

            const lastTwoWeeksFailed = await createStudentWithSettings();
            await createMilestones(
                lastTwoWeeksFailed,
                lastFiveWeeks.map((week, i) => {
                    return {
                        period: week.range,
                        target: 6,
                        progress: [1, 2].includes(i) ? 0 : 6,
                    };
                }),
            );

            const lastThreeWeeksFailed = await createStudentWithSettings();
            await createMilestones(
                lastThreeWeeksFailed,
                lastFiveWeeks.map((week, i) => {
                    return {
                        period: week.range,
                        target: 9,
                        progress: [1, 2, 3].includes(i) ? 0 : 9,
                    };
                }),
            );

            const lastFourWeeksFailed = await createStudentWithSettings();
            await createMilestones(
                lastFourWeeksFailed,
                lastFiveWeeks.map((week, i) => {
                    return {
                        period: week.range,
                        target: 8,
                        progress: [1, 2, 3, 4].includes(i) ? 0 : 8,
                    };
                }),
            );

            // 1 failed + 3 exceptions + 3 failed = streak of 3 (minimized to 3)
            const threeWeekExceptionUser = await createStudentWithSettings();
            await createMilestones(threeWeekExceptionUser, [
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).range,
                    target: 8,
                    progress: 0,
                },
                // Last week failed
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-1).range,
                    target: 8,
                    progress: 0,
                },
                // 3 exceptions
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-2).range,
                    target: 8,
                    progress: 0,
                    exceptionId: twoWeeksAgoException.getIdOrThrow(),
                },
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-3).range,
                    target: 8,
                    progress: 0,
                    exceptionId: threeWeeksAgoException.getIdOrThrow(),
                },
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-4).range,
                    target: 8,
                    progress: 0,
                    exceptionId: fourWeeksAgoException.getIdOrThrow(),
                },
                // 3 failed
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-5).range,
                    target: 8,
                    progress: 0,
                },
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-6).range,
                    target: 8,
                    progress: 0,
                },
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-7).range,
                    target: 8,
                    progress: 0,
                },
            ]);

            // 2 failed + 1 exception + 1 failed = streak of 3
            const oneWeekExceptionUser = await createStudentWithSettings();
            await createMilestones(oneWeekExceptionUser, [
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).range,
                    target: 8,
                    progress: 0,
                },
                // 2 failed
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-1).range,
                    target: 8,
                    progress: 0,
                },
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-2).range,
                    target: 8,
                    progress: 0,
                },
                // 1 exception
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-3).range,
                    target: 8,
                    progress: 0,
                    exceptionId: threeWeeksAgoException.getIdOrThrow(),
                },
                // 1 failed
                {
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-5).range,
                    target: 8,
                    progress: 0,
                },
            ]);

            const streaks = await learningMilestonesService.getUsersUnmetMilestonesStreakForLastThreeWeeks();

            expect(findUserStreak(streaks, noMilestonesUser)).toBeUndefined();
            expect(findUserStreak(streaks, allMetMilestonesUser)).toBeUndefined();
            expect(findUserStreak(streaks, allUnmetMilestonesUser)).toMatchObject({
                streak: 3,
                lastWeek: {
                    target: 3,
                },
            });
            expect(findUserStreak(streaks, lastWeekSuccessAfterBadStreak)).toBeUndefined();
            expect(findUserStreak(streaks, lastWeekFailedButExplained)).toBeUndefined();
            expect(findUserStreak(streaks, lastWeekFailed)).toMatchObject({
                streak: 1,
                lastWeek: {
                    target: 1,
                },
            });
            expect(findUserStreak(streaks, lastTwoWeeksFailed)).toMatchObject({
                streak: 2,
                lastWeek: {
                    target: 6,
                },
            });
            expect(findUserStreak(streaks, lastThreeWeeksFailed)).toMatchObject({
                streak: 3,
                lastWeek: {
                    target: 9,
                },
            });
            expect(findUserStreak(streaks, lastFourWeeksFailed)).toMatchObject({
                streak: 3,
                lastWeek: {
                    target: 8,
                },
            });
            expect(findUserStreak(streaks, allUnmetMilestonesButWithExceptionUser)).toMatchObject({
                streak: 3,
                lastWeek: {
                    target: 3,
                },
            });
            expect(findUserStreak(streaks, threeWeekExceptionUser)).toMatchObject({
                streak: 3,
                lastWeek: {
                    target: 8,
                },
            });
            expect(findUserStreak(streaks, oneWeekExceptionUser)).toMatchObject({
                streak: 3,
                lastWeek: {
                    target: 8,
                },
            });
        });
    });

    describe('getRequirementsChangesSinceLastWeek', () => {
        function findUserChange(
            changes: UserStandupAttendanceRequirementChange[],
            user: IntegrationTestUser,
        ): UserStandupAttendanceRequirementChange | undefined {
            return changes.find((changes) => changes.userId.equals(user.id));
        }

        it('should find correct users according to milestones', async () => {
            const twoWeeksAgo = Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-2);
            const lastWeek = twoWeeksAgo.next();
            const thisWeek = lastWeek.next();
            const nextWeek = thisWeek.next();

            const noMilestonesUser = await IntegrationTestUser.createFakeStudent();

            const noChangesUser = await IntegrationTestUser.createFakeStudent();
            await createMilestones(
                noChangesUser,
                [lastWeek, thisWeek, nextWeek].map((week) => {
                    return {
                        period: week.range,
                        target: 1,
                    };
                }),
            );

            const changesOnNextWeek = await IntegrationTestUser.createFakeStudent();
            await createMilestones(
                changesOnNextWeek,
                [lastWeek, thisWeek, nextWeek].map((week) => {
                    return {
                        period: week.range,
                        target: week.equals(nextWeek) ? 1 : 0,
                    };
                }),
            );

            const changesOnThisWeek = await IntegrationTestUser.createFakeStudent();
            await createMilestones(
                changesOnThisWeek,
                [lastWeek, thisWeek, nextWeek].map((week) => {
                    return {
                        period: week.range,
                        target: week.equals(thisWeek) ? 1 : 0,
                    };
                }),
            );

            const changesTwoWeeksBefore = await IntegrationTestUser.createFakeStudent();
            await createMilestones(
                changesTwoWeeksBefore,
                [twoWeeksAgo, lastWeek, thisWeek, nextWeek].map((week) => {
                    return {
                        period: week.range,
                        target: week.equals(twoWeeksAgo) ? 1 : 0,
                    };
                }),
            );

            const singleMilestoneLastWeek = await IntegrationTestUser.createFakeStudent();
            await createMilestones(singleMilestoneLastWeek, [
                {
                    period: lastWeek.range,
                    target: 1,
                },
            ]);

            const singleMilestoneThisWeek = await IntegrationTestUser.createFakeStudent();
            await createMilestones(singleMilestoneThisWeek, [
                {
                    period: thisWeek.range,
                    target: 1,
                },
            ]);

            const changes = await learningMilestonesService.getRequirementsChangesSinceLastWeek();

            expect(findUserChange(changes, noMilestonesUser)).toBeUndefined();
            expect(findUserChange(changes, noChangesUser)).toBeUndefined();
            expect(findUserChange(changes, changesOnNextWeek)).toBeUndefined();
            expect(findUserChange(changes, changesOnThisWeek)).toMatchObject({
                userId: expect.anything(),
                target: {
                    before: 0,
                    after: 1,
                },
            });
            expect(findUserChange(changes, changesTwoWeeksBefore)).toBeUndefined();
            expect(findUserChange(changes, singleMilestoneLastWeek)).toBeUndefined();
            expect(findUserChange(changes, singleMilestoneThisWeek)).toBeUndefined();
        });
    });
});
