import { Configuration } from '../../config/Configuration';
import Url from '../../core/domain/value-objects/Url';
import Transaction from '../../core/infrastructure/Transaction';
import { LoggingService } from '../../logging/services/LoggingService';
import Mailer from '../../mailer/Mailer';
import { MailType } from '../../mailer/types/TemplateParams';
import { NotificationType } from '../../notifications/domain/Notification';
import { NotificationManager } from '../../notifications/NotificationManager';
import { UserStandupAttendanceRequirementChange } from '../../users/students/settings/domain/UserStandupAttendanceRequirementChange';
import Logger from '../../utils/logger/Logger';
import CreateExplanationUrl from '../domain/value-objects/CreateExplanationUrl';
import { UserAccountFinder } from '../../users/accounts/services/UserAccountFinder';

export class StandupAttendanceChangedRequirementsNotifierService {
    private readonly logger: Logger;

    private readonly clientUrl: Url;

    constructor(
        configuration: Configuration,
        loggingService: LoggingService,
        private readonly notificationManager: NotificationManager,
        private readonly mailer: Mailer,
        private readonly userAccountFinder: UserAccountFinder,
    ) {
        this.logger = loggingService.createLogger(StandupAttendanceChangedRequirementsNotifierService.name);
        this.clientUrl = new Url(configuration.client.url);
    }

    async notifyUsersAboutChangedRequirementsByEmail(changes: UserStandupAttendanceRequirementChange[]): Promise<void> {
        const emailNotifications = changes.map((change) => {
            return this.notifyUserAboutChangedRequirementsByEmail(change);
        });

        await Promise.all(emailNotifications);
    }

    async notifyUsersAboutChangedRequirementsByPlatform(
        changes: UserStandupAttendanceRequirementChange[],
    ): Promise<void> {
        const platformNotifications = changes.map((change) => {
            return this.notifyUserAboutChangedRequirementsByPlatform(change);
        });

        await Promise.all(platformNotifications);
    }

    async notifyUserAboutChangedRequirements({
        change,
        tx,
    }: {
        change: UserStandupAttendanceRequirementChange;
        tx?: Transaction;
    }): Promise<void> {
        await this.notifyUserAboutChangedRequirementsByPlatform(change, tx);
        await this.notifyUserAboutChangedRequirementsByEmail(change);
    }

    async notifyUserAboutChangedRequirementsByPlatform(
        change: UserStandupAttendanceRequirementChange,
        tx?: Transaction,
    ): Promise<void> {
        const user = await this.userAccountFinder.findByUserId(change.userId);
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        if (!user.isStudent() || !user.isActive()) {
            return;
        }

        this.logger.debug(
            `Creating platform notification about standup attendance requirement change ${JSON.stringify(change)}`,
        );

        await this.notificationManager.acknowledgeUserNotificationsByTypes({
            userId: change.userId,
            types: [NotificationType.STANDUP_ATTENDANCE_REQUIREMENT_CHANGED],
            tx,
        });
        await this.notificationManager.createNotification(
            {
                userId: change.userId,
                type: NotificationType.STANDUP_ATTENDANCE_REQUIREMENT_CHANGED,
                title: 'New standup attendance requirement',
                description: 'New standup attendance requirement',
                payload: {
                    newRequirement: change.target.after,
                },
            },
            tx,
        );
    }

    async notifyUserAboutChangedRequirementsByEmail(change: UserStandupAttendanceRequirementChange): Promise<void> {
        const student = await this.userAccountFinder.findByUserId(change.userId);
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        if (!student.isStudent() || !student.isActive()) {
            return;
        }

        this.logger.debug(`Sending email about standup attendance requirement change ${JSON.stringify(change)}`);

        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        return this.mailer.send(student.email.value, {
            type: MailType.STANDUP_ATTENDANCE_REQUIREMENT_CHANGED_NOTIFICATION,
            receiver: {
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                firstName: student.name.firstName,
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                lastName: student.name.lastName,
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                username: student.username.value,
            },
            requirement: change.target.after,
            addExplanationLink: CreateExplanationUrl.fromIds(this.clientUrl, []).value,
        });
    }
}
