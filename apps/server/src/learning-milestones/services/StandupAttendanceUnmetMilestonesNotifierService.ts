import { Configuration } from '../../config/Configuration';
import Url from '../../core/domain/value-objects/Url';
import TransactionManager from '../../core/infrastructure/TransactionManager';
import { NotificationType } from '../../notifications/domain/Notification';
import Mailer from '../../mailer/Mailer';
import { LearningMilestonesService } from './LearningMilestonesService';
import { NotificationManager } from '../../notifications/NotificationManager';
import { LearningMilestoneStreak } from '../domain/LearningMilestoneStreak';
import CreateExplanationUrl from '../domain/value-objects/CreateExplanationUrl';
import { MailType } from '../../mailer/types/TemplateParams';
import { UserAccountFinder } from '../../users/accounts/services/UserAccountFinder';

export class StandupAttendanceUnmetMilestonesNotifierService {
    private readonly clientUrl: Url;

    constructor(
        configuration: Configuration,
        private readonly tm: TransactionManager,
        private readonly learningMilestonesService: LearningMilestonesService,
        private readonly notificationManager: NotificationManager,
        private readonly mailer: Mailer,
        private readonly userAccountFinder: UserAccountFinder,
    ) {
        this.clientUrl = new Url(configuration.client.url);
    }

    async notifyUsersAboutUnmetMilestonesByPlatform(): Promise<void> {
        const unmetStreaks = await this.learningMilestonesService.getUsersUnmetMilestonesStreakForLastThreeWeeks();

        const platformNotifications = unmetStreaks.map((streak) => {
            return this.notifyUserAboutUnmetMilestonesByPlatform(streak);
        });

        await Promise.all(platformNotifications);
    }

    async notifyUsersAboutUnmetMilestonesByEmail(): Promise<void> {
        const unmetStreaks = await this.learningMilestonesService.getUsersUnmetMilestonesStreakForLastThreeWeeks();

        const emailNotifications = unmetStreaks.map((streak) => {
            return this.notifyUserAboutUnmetMilestonesByEmail(streak);
        });

        await Promise.all(emailNotifications);
    }

    private async notifyUserAboutUnmetMilestonesByPlatform(streak: LearningMilestoneStreak): Promise<void> {
        const student = await this.userAccountFinder.findByUserId(streak.userId);
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        if (!student.isStudent() || !student.isActive()) {
            return;
        }

        await this.tm.execute(async (tx) => {
            await this.notificationManager.acknowledgeUserNotificationsByTypes({
                userId: streak.userId,
                types: [NotificationType.UNMET_STANDUP_ATTENDANCE_REQUIREMENT],
                tx,
            });
            await this.notificationManager.createNotification(
                {
                    userId: streak.userId,
                    type: NotificationType.UNMET_STANDUP_ATTENDANCE_REQUIREMENT,
                    title: 'You missed stand-ups last week',
                    description: 'Please, provide explanations',
                    payload: {
                        streak: streak.streak,
                        lastWeek: {
                            target: streak.lastWeek.target,
                            milestoneId: streak.lastWeek.milestoneId.value,
                        },
                    },
                },
                tx,
            );
        });
    }

    private async notifyUserAboutUnmetMilestonesByEmail(streak: LearningMilestoneStreak): Promise<void> {
        if (streak.streak < 2) {
            return;
        }

        const student = await this.userAccountFinder.findByUserId(streak.userId);
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        if (!student.isStudent() || !student.isActive()) {
            return;
        }

        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        await this.mailer.send(student.email.value, {
            type: MailType.STANDUP_ATTENDANCE_UNMET_NOTIFICATION,
            receiver: {
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                firstName: student.name.firstName,
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                lastName: student.name.lastName,
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                username: student.username.value,
            },
            streak: streak.streak,
            requirement: streak.lastWeek.target,
            addExplanationLink: CreateExplanationUrl.fromIds(this.clientUrl, [streak.lastWeek.milestoneId]).value,
        });
    }
}
