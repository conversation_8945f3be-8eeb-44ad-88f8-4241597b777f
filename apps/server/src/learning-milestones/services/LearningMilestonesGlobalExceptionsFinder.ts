import { Pagination, PaginationResult } from '../../core/domain/value-objects/Pagination';
import LearningMilestoneGlobalExceptionRepository from '../infrastructure/db/learning-milestone-global-exception/LearningMilestoneGlobalExceptionRepository';
import { LearningMilestoneGlobalException } from '../domain/LearningMilestoneGlobalException';
import Transaction from '../../core/infrastructure/Transaction';
import { Week } from '../../core/domain/value-objects/Week';
import Id from '../../core/domain/value-objects/Id';
import { DateRange } from '../../core/domain/value-objects/DateRange';

export class LearningMilestonesGlobalExceptionsFinder {
    constructor(
        private readonly learningMilestoneGlobalExceptionRepository: LearningMilestoneGlobalExceptionRepository,
    ) {}

    async getForWeek({ week, tx }: { week: Week; tx?: Transaction }): Promise<LearningMilestoneGlobalException> {
        // @ts-expect-error TS(2322) FIXME: Type 'LearningMilestoneGlobalException | undefined... Remove this comment to see the full error message
        return this.learningMilestoneGlobalExceptionRepository.getForWeek({
            week,
            tx,
        });
    }

    async getWithPagination({
        pagination,
    }: {
        pagination?: Pagination;
    }): Promise<PaginationResult<LearningMilestoneGlobalException>> {
        return this.learningMilestoneGlobalExceptionRepository.getWithPagination({
            pagination,
        });
    }

    getById(id: Id): Promise<LearningMilestoneGlobalException> {
        // @ts-expect-error TS(2322) FIXME: Type 'Promise<LearningMilestoneGlobalException | u... Remove this comment to see the full error message
        return this.learningMilestoneGlobalExceptionRepository.get(id);
    }

    async getEmptyWeekSlotsForException(): Promise<Week[]> {
        const possiblePeriodValues = LearningMilestoneGlobalException.getPossiblePeriodValues();

        const exceptionsWithinRange = await this.learningMilestoneGlobalExceptionRepository.getAllWithinRange({
            range: DateRange.fromDatesInclusive(
                possiblePeriodValues[0].range.from,
                possiblePeriodValues[possiblePeriodValues.length - 1].range.to,
            ),
        });

        return possiblePeriodValues.filter((week) => {
            return !exceptionsWithinRange.some((exception) => {
                return exception.period.equals(week.range);
            });
        });
    }
}
