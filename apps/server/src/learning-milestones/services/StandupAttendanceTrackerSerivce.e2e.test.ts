import { faker } from '@faker-js/faker';
import moment from 'moment/moment';
import Container from 'typedi';
import CalendarEventCategory from '../../calendar/domain/CalendarEventCategory';
import CalendarEventDescription from '../../calendar/domain/CalendarEventDescription';
import CalendarEventSummary from '../../calendar/domain/CalendarEventSummary';
import { ConfigurationModule } from '../../config/infrastructure/di/ConfigurationModule';
import { Pagination } from '../../core/domain/value-objects/Pagination';
import { Week } from '../../core/domain/value-objects/Week';
import { LearningMilestoneType } from '../domain/types/LearningMilestoneType';
import {
    LearningMilestonesServiceToken,
    RegisteredStandupAttendanceRepositoryToken,
    StandupAttendanceTrackerServiceToken,
} from '../infrastructure/di/tokens';
import { LearningMilestonesService } from './LearningMilestonesService';
import ZoomId from '../../meetings/domain/value-objects/ZoomId';
import { MeetingStateIntegrationEventType } from '../../meetings/events/MeetingStateIntegrationEventType';
import { IntegrationTestCalendarEvent } from '../../test-toolkit/e2e/entities/IntegrationTestCalendarEvent';
import { IntegrationTestLearningMilestone } from '../../test-toolkit/e2e/entities/IntegrationTestLearningMilestone';
import { IntegrationTestMeeting } from '../../test-toolkit/e2e/entities/IntegrationTestMeeting';
import { IntegrationTestMeetingAttendee } from '../../test-toolkit/e2e/entities/IntegrationTestMeetingAttendee';
import { IntegrationTestMeetingHost } from '../../test-toolkit/e2e/entities/IntegrationTestMeetingHost';
import { IntegrationTestUser } from '../../test-toolkit/e2e/entities/IntegrationTestUser';
import { Configuration } from '../../config/Configuration';
import { StandupAttendanceTrackerService } from './StandupAttendanceTrackerService';
import { MeetingStateIntegrationEvent } from '../../meetings/events/MeetingStateIntegrationEvent';
import { ZoomMockServer } from '../../test-toolkit/e2e/mock-servers/zoom.mock-server';
import { PastZoomMeetingParticipant } from '../../meetings/services/zoom/types/PastMeetingParticipant.external';
import { RegisteredStandupAttendanceRepository } from '../infrastructure/db/registered-standup-attendance/RegisteredStandupAttendanceRepository';
import { Duration } from '../../core/domain/value-objects/Duration';
import TestObjects from '../../test-toolkit/shared/TestObjects';
import { LearningMilestonesConstants } from '../domain/LearningMilestonesConstants';

describe(LearningMilestonesService.name, () => {
    let configuration: Configuration;
    let standupAttendanceTrackerService: StandupAttendanceTrackerService;
    let learningMilestoneService: LearningMilestonesService;
    let registeredStandupAttendanceRepository: RegisteredStandupAttendanceRepository;
    let requiredDuration: moment.Duration;
    let zoomMockServer: ZoomMockServer;

    beforeAll(async () => {
        configuration = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        learningMilestoneService = Container.get(LearningMilestonesServiceToken);
        standupAttendanceTrackerService = Container.get(StandupAttendanceTrackerServiceToken);
        registeredStandupAttendanceRepository = Container.get(RegisteredStandupAttendanceRepositoryToken);
        requiredDuration = moment.duration(configuration.learningMilestones.standup.attendanceTimeRequirement);
        zoomMockServer = new ZoomMockServer(configuration);
        await zoomMockServer.start();
    });

    beforeEach(() => {
        zoomMockServer.reset();
        zoomMockServer.mockGetOAuthToken();
    });

    afterAll(() => {
        zoomMockServer.stop();
    });

    describe('registerStandupAttendanceWhenMeetingEnded', () => {
        async function prepareTestData(): Promise<{
            occurrenceId: ZoomId;
            meeting: IntegrationTestMeeting;
            student: IntegrationTestUser;
            learningMilestone: IntegrationTestLearningMilestone;
            week: Week;
        }> {
            const meetingHost = await IntegrationTestMeetingHost.create();
            const meeting = await IntegrationTestMeeting.create(meetingHost.id!);
            await IntegrationTestCalendarEvent.create({
                category: CalendarEventCategory.STANDUP,
                summary: new CalendarEventSummary('Standup event'),
                description: new CalendarEventDescription('Standup event'),
                isAutoMeeting: true,
                autoMeetingId: meeting.params.id,
            });

            const week = Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE);
            const student = await IntegrationTestUser.createFakeStudent();
            const learningMilestone = await IntegrationTestLearningMilestone.create({
                userId: student.id!,
                type: LearningMilestoneType.STANDUP_ATTENDANCE,
                period: week.range,
                target: 1,
            });

            const occurrenceId = new ZoomId(faker.string.uuid());

            return {
                occurrenceId,
                meeting,
                student,
                week,
                learningMilestone,
            };
        }

        async function verifyMilestone({
            student,
            week,
            target,
            progress,
            learningMilestone,
        }: {
            student: IntegrationTestUser;
            week: Week;
            target: number;
            progress: number;
            learningMilestone: IntegrationTestLearningMilestone;
        }): Promise<void> {
            const userMilestones = await learningMilestoneService.getUserLearningMilestones({
                userId: student.id!,
                range: week.range,
                pagination: Pagination.fromParams({ page: 1, pageSize: 10 }),
            });

            expect(userMilestones.totalRows).toBe(1);
            expect(userMilestones.rows[0]).toMatchObject({
                id: expect.objectContaining(learningMilestone.id),
                userId: expect.objectContaining(student.id),
                type: LearningMilestoneType.STANDUP_ATTENDANCE,
                period: expect.objectContaining(week.range),
                progress,
                target,
            });
        }

        async function verifyAttendanceRecord({
            occurrenceId,
            student,
            duration,
        }: {
            occurrenceId: ZoomId;
            student: IntegrationTestUser;
            duration: Duration;
        }): Promise<void> {
            const attendanceRecords = await registeredStandupAttendanceRepository.getByOccurrenceId(occurrenceId);

            expect(attendanceRecords).toHaveLength(1);
            expect(attendanceRecords[0]).toMatchObject({
                occurrenceId: occurrenceId,
                userId: student.id!,
            });
            expect(attendanceRecords[0].duration.asSeconds()).toBe(duration.asSeconds());
        }

        it('should ignore non standup events', async () => {
            const meetingHost = await IntegrationTestMeetingHost.create();
            const meeting = await IntegrationTestMeeting.create(meetingHost.id!);
            await IntegrationTestCalendarEvent.create({
                category: CalendarEventCategory.MEETING,
                summary: new CalendarEventSummary('Non standup event'),
                description: new CalendarEventDescription('Non standup event'),
                isAutoMeeting: true,
                autoMeetingId: meeting.params.id,
            });
            const occurrenceId = new ZoomId(faker.string.uuid());

            const week = Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE);
            const student = await IntegrationTestUser.createFakeStudent();
            const learningMilestone = await IntegrationTestLearningMilestone.create({
                userId: student.id!,
                type: LearningMilestoneType.STANDUP_ATTENDANCE,
                period: week.range,
                target: 1,
            });

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: meeting.params.id!,
                    occurrenceId,
                    timestamp: new Date(),
                }),
            );

            await verifyMilestone({
                student,
                week,
                target: 1,
                progress: 0,
                learningMilestone,
            });

            const attendanceRecords = await registeredStandupAttendanceRepository.getByOccurrenceId(occurrenceId);

            expect(attendanceRecords).toHaveLength(0);
        });

        it('should track learner attendance less than threshold, but not mark as attended', async () => {
            const { occurrenceId, meeting, student, week, learningMilestone } = await prepareTestData();
            const zoomId = TestObjects.zoomId();

            zoomMockServer.mockPastMeetingParticipants(occurrenceId, 200, {
                page_count: 1,
                page_size: 1,
                next_page_token: '',
                total_records: 1,
                participants: [
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        registrant_id: zoomId.value,
                        duration: 60,
                    } as PastZoomMeetingParticipant,
                ],
            });

            await IntegrationTestMeetingAttendee.create(meeting.params.id!, student.id!, zoomId);

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: meeting.params.id!,
                    occurrenceId,
                    timestamp: new Date(),
                }),
            );

            await verifyMilestone({
                student,
                week,
                target: 1,
                progress: 0,
                learningMilestone,
            });

            await verifyAttendanceRecord({
                occurrenceId,
                student: student,
                duration: Duration.fromSeconds(60),
            });
        });

        it('should track learner attendance equal to threshold and mark as attended', async () => {
            const { occurrenceId, meeting, student, week, learningMilestone } = await prepareTestData();
            const zoomId = TestObjects.zoomId();

            zoomMockServer.mockPastMeetingParticipants(occurrenceId, 200, {
                page_count: 1,
                page_size: 1,
                next_page_token: '',
                total_records: 1,
                participants: [
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        registrant_id: zoomId.value,
                        duration: requiredDuration.asSeconds(),
                    } as PastZoomMeetingParticipant,
                ],
            });

            await IntegrationTestMeetingAttendee.create(meeting.params.id!, student.id!, zoomId);

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: meeting.params.id!,
                    occurrenceId,
                    timestamp: new Date(),
                }),
            );

            await verifyMilestone({
                student,
                week,
                target: 1,
                progress: 1,
                learningMilestone,
            });

            await verifyAttendanceRecord({
                occurrenceId,
                student: student,
                duration: new Duration(requiredDuration),
            });
        });

        it('should track learner attendance greater than threshold and mark as attended', async () => {
            const { occurrenceId, meeting, student, week, learningMilestone } = await prepareTestData();
            const duration = requiredDuration.clone().add({
                minute: 5,
            });
            const zoomId = TestObjects.zoomId();

            zoomMockServer.mockPastMeetingParticipants(occurrenceId, 200, {
                page_count: 1,
                page_size: 1,
                next_page_token: '',
                total_records: 1,
                participants: [
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        registrant_id: zoomId.value,
                        duration: duration.asSeconds(),
                    } as PastZoomMeetingParticipant,
                ],
            });

            await IntegrationTestMeetingAttendee.create(meeting.params.id!, student.id!, zoomId);

            await verifyMilestone({
                student,
                week,
                target: 1,
                progress: 0,
                learningMilestone,
            });

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: meeting.params.id!,
                    occurrenceId,
                    timestamp: new Date(),
                }),
            );

            await verifyMilestone({
                student,
                week,
                target: 1,
                progress: 1,
                learningMilestone,
            });

            await verifyAttendanceRecord({
                occurrenceId,
                student,
                duration: new Duration(duration),
            });
        });

        it('should track learner attendance once and mark as attended once when called two times and zoom returns same data', async () => {
            const { occurrenceId, meeting, student, week, learningMilestone } = await prepareTestData();
            const duration = requiredDuration.clone().add({
                minute: 5,
            });
            const zoomId = TestObjects.zoomId();

            const data = {
                page_count: 1,
                page_size: 1,
                next_page_token: '',
                total_records: 1,
                participants: [
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        registrant_id: zoomId.value,
                        duration: duration.asSeconds(),
                    } as PastZoomMeetingParticipant,
                ],
            };

            await IntegrationTestMeetingAttendee.create(meeting.params.id!, student.id!, zoomId);

            zoomMockServer.mockPastMeetingParticipants(occurrenceId, 200, data);

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: meeting.params.id!,
                    occurrenceId,
                    timestamp: new Date(),
                }),
            );

            zoomMockServer.mockPastMeetingParticipants(occurrenceId, 200, data);

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: meeting.params.id!,
                    occurrenceId,
                    timestamp: new Date(),
                }),
            );

            await verifyMilestone({
                student,
                week,
                target: 1,
                progress: 1,
                learningMilestone,
            });

            await verifyAttendanceRecord({
                occurrenceId,
                student,
                duration: new Duration(duration),
            });
        });

        it('should update learner attendance record and mark as attended when duration changed', async () => {
            const { occurrenceId, meeting, student, week, learningMilestone } = await prepareTestData();
            const zoomId = TestObjects.zoomId();

            zoomMockServer.mockPastMeetingParticipants(occurrenceId, 200, {
                page_count: 1,
                page_size: 1,
                next_page_token: '',
                total_records: 1,
                participants: [
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        duration: 15,
                        registrant_id: zoomId.value,
                    } as PastZoomMeetingParticipant,
                ],
            });

            await IntegrationTestMeetingAttendee.create(meeting.params.id!, student.id!, zoomId);

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: meeting.params.id!,
                    occurrenceId,
                    timestamp: new Date(),
                }),
            );

            zoomMockServer.mockPastMeetingParticipants(occurrenceId, 200, {
                page_count: 1,
                page_size: 1,
                next_page_token: '',
                total_records: 1,
                participants: [
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        duration: 15,
                        registrant_id: zoomId.value,
                    } as PastZoomMeetingParticipant,
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        duration: requiredDuration.asSeconds(),
                        registrant_id: zoomId.value,
                    } as PastZoomMeetingParticipant,
                ],
            });

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: meeting.params.id!,
                    occurrenceId,
                    timestamp: new Date(),
                }),
            );

            await verifyMilestone({
                student,
                week,
                target: 1,
                progress: 1,
                learningMilestone,
            });

            await verifyAttendanceRecord({
                occurrenceId,
                student,
                duration: new Duration(requiredDuration.clone().add({ second: 15 })),
            });
        });

        it('should sum up attendances length', async () => {
            const { occurrenceId, meeting, student, week, learningMilestone } = await prepareTestData();
            const zoomId = TestObjects.zoomId();

            zoomMockServer.mockPastMeetingParticipants(occurrenceId, 200, {
                page_count: 1,
                page_size: 1,
                next_page_token: '',
                total_records: 1,
                participants: [
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        duration: 15,
                        registrant_id: zoomId.value,
                    } as PastZoomMeetingParticipant,
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        duration: 30,
                        registrant_id: zoomId.value,
                    } as PastZoomMeetingParticipant,
                    {
                        user_id: '',
                        name: 'TTTT BBBB | test',
                        user_email: faker.internet.email(),
                        duration: 30,
                        registrant_id: TestObjects.zoomId().value,
                    } as PastZoomMeetingParticipant,
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        duration: requiredDuration.asSeconds() + 15,
                        registrant_id: zoomId.value,
                    } as PastZoomMeetingParticipant,
                    {
                        user_id: '',
                        name: 'SSS Test | test',
                        user_email: faker.internet.email(),
                        duration: 30,
                        registrant_id: TestObjects.zoomId().value,
                    } as PastZoomMeetingParticipant,
                ],
            });

            await IntegrationTestMeetingAttendee.create(meeting.params.id!, student.id!, zoomId);

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: meeting.params.id!,
                    occurrenceId,
                    timestamp: new Date(),
                }),
            );

            await verifyMilestone({
                student,
                week,
                target: 1,
                progress: 1,
                learningMilestone,
            });

            await verifyAttendanceRecord({
                occurrenceId,
                student,
                duration: new Duration(requiredDuration.clone().add({ second: 15 + 30 + 15 })),
            });
        });

        it('should count two occurrences separately', async () => {
            const { occurrenceId, meeting, student, week, learningMilestone } = await prepareTestData();
            const occurrenceParticipantIdA = TestObjects.zoomId();

            zoomMockServer.mockPastMeetingParticipants(occurrenceId, 200, {
                page_count: 1,
                page_size: 1,
                next_page_token: '',
                total_records: 1,
                participants: [
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        duration: requiredDuration.asSeconds(),
                        registrant_id: occurrenceParticipantIdA.value,
                    } as PastZoomMeetingParticipant,
                ],
            });

            await IntegrationTestMeetingAttendee.create(meeting.params.id!, student.id!, occurrenceParticipantIdA);

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: meeting.params.id!,
                    occurrenceId,
                    timestamp: new Date(),
                }),
            );

            const anotherOccurrenceId = new ZoomId(faker.string.uuid());
            const meetingHost = await IntegrationTestMeetingHost.create();
            const anotherMeeting = await IntegrationTestMeeting.create(meetingHost.id!);
            await IntegrationTestCalendarEvent.create({
                category: CalendarEventCategory.STANDUP,
                summary: new CalendarEventSummary('Standup event'),
                description: new CalendarEventDescription('Standup event'),
                isAutoMeeting: true,
                autoMeetingId: anotherMeeting.params.id,
            });
            const occurrenceParticipantIdB = TestObjects.zoomId();

            zoomMockServer.mockPastMeetingParticipants(anotherOccurrenceId, 200, {
                page_count: 1,
                page_size: 1,
                next_page_token: '',
                total_records: 1,
                participants: [
                    {
                        user_id: '',
                        name: 'Test Test | test',
                        user_email: student.params.email,
                        duration: requiredDuration.clone().add({ second: 15 }).asSeconds(),
                        registrant_id: occurrenceParticipantIdB.value,
                    } as PastZoomMeetingParticipant,
                ],
            });

            await IntegrationTestMeetingAttendee.create(meeting.params.id!, student.id!, occurrenceParticipantIdB);

            await standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(
                new MeetingStateIntegrationEvent({
                    topic: configuration.topics.meetingEvents,
                    eventType: MeetingStateIntegrationEventType.ENDED,
                    meetingId: anotherMeeting.params.id!,
                    occurrenceId: anotherOccurrenceId,
                    timestamp: new Date(),
                }),
            );

            await verifyMilestone({
                student,
                week,
                target: 1,
                progress: 2,
                learningMilestone,
            });

            await verifyAttendanceRecord({
                occurrenceId,
                student,
                duration: new Duration(requiredDuration),
            });
            await verifyAttendanceRecord({
                occurrenceId: anotherOccurrenceId,
                student,
                duration: new Duration(requiredDuration.clone().add({ second: 15 })),
            });
        });
    });
});
