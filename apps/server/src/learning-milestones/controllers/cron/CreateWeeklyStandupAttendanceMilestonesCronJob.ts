import CronJob from '../../../cron/CronJob';
import { LoggingService } from '../../../logging/services/LoggingService';
import { LearningMilestonesService } from '../../services/LearningMilestonesService';

export default class CreateWeeklyStandupAttendanceMilestonesCronJob extends CronJob {
    constructor(
        loggingService: LoggingService,
        private readonly learningMilestonesService: LearningMilestonesService,
    ) {
        super(
            'create-weekly-standup-attendance-milestones',
            loggingService.createLogger(CreateWeeklyStandupAttendanceMilestonesCronJob.name),
        );
    }

    protected async executeJob(): Promise<void> {
        await this.learningMilestonesService.createWeeklyStandupAttendanceGoalsForThreeWeeks();
    }
}
