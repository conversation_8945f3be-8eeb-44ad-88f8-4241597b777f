import CronJob from '../../../cron/CronJob';
import { LoggingService } from '../../../logging/services/LoggingService';
import { LearningMilestonesService } from '../../services/LearningMilestonesService';

export default class NotifyStandupAttendanceRequirementsChangedByPlatformCronJob extends CronJob {
    constructor(
        loggingService: LoggingService,
        private readonly learningMilestonesService: LearningMilestonesService,
    ) {
        super(
            'notify-standup-attendance-requirements-changed-by-platform-cron-job',
            loggingService.createLogger(NotifyStandupAttendanceRequirementsChangedByPlatformCronJob.name),
        );
    }

    protected async executeJob(): Promise<void> {
        await this.learningMilestonesService.notifyUsersWithChangedRequirementsSinceLastWeekByPlatform();
    }
}
