import CronJob from '../../../cron/CronJob';
import { LoggingService } from '../../../logging/services/LoggingService';
import { StandupAttendanceUnmetMilestonesNotifierService } from '../../services/StandupAttendanceUnmetMilestonesNotifierService';

export default class NotifyUsersAboutUnmetMilestonesByEmailCronJob extends CronJob {
    constructor(
        loggingService: LoggingService,
        private readonly standupAttendanceMilestonesNotifierService: StandupAttendanceUnmetMilestonesNotifierService,
    ) {
        super(
            'notify-users-about-unmet-milestones-by-email-cron-job',
            loggingService.createLogger(NotifyUsersAboutUnmetMilestonesByEmailCronJob.name),
        );
    }

    protected async executeJob(): Promise<void> {
        await this.standupAttendanceMilestonesNotifierService.notifyUsersAboutUnmetMilestonesByEmail();
    }
}
