import CronJob from '../../../cron/CronJob';
import { LoggingService } from '../../../logging/services/LoggingService';
import { StandupAttendanceUnmetMilestonesNotifierService } from '../../services/StandupAttendanceUnmetMilestonesNotifierService';

export default class NotifyUsersAboutUnmetMilestonesByPlatformCronJob extends CronJob {
    constructor(
        loggingService: LoggingService,
        private readonly standupAttendanceMilestonesNotifierService: StandupAttendanceUnmetMilestonesNotifierService,
    ) {
        super(
            'notify-users-about-unmet-milestones-by-platform-cron-job',
            loggingService.createLogger(NotifyUsersAboutUnmetMilestonesByPlatformCronJob.name),
        );
    }

    protected async executeJob(): Promise<void> {
        await this.standupAttendanceMilestonesNotifierService.notifyUsersAboutUnmetMilestonesByPlatform();
    }
}
