import { WeekSlotDto } from './WeekSlotDto';
import { Type } from 'class-transformer';
import { FixNestedArrayJsonSchemaReference } from '../../../../../core/controllers/dto/decorators/FixNestedJsonSchemaReference';
import { ValidateNested } from 'class-validator';

export class GetEmptyWeekSlotsForException {
    @Type(() => WeekSlotDto)
    @FixNestedArrayJsonSchemaReference(WeekSlotDto)
    @ValidateNested({ each: true })
    slots: WeekSlotDto[];
}
