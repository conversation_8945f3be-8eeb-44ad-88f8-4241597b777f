import { IsDateString, IsInt, IsString } from 'class-validator';
import { Mutable, NonFunctionProperties } from '../../../../../utils/UtilityTypes';
import { LearningMilestoneGlobalException } from '../../../../domain/LearningMilestoneGlobalException';

export type LearningMilestoneGlobalExceptionDtoParams = Mutable<
    NonFunctionProperties<LearningMilestonesGlobalExceptionDto>
>;

export class LearningMilestonesGlobalExceptionDto {
    @IsInt()
    id: number;

    @IsString()
    name: string;

    @IsDateString()
    periodStart: string;

    @IsDateString()
    periodEnd: string;

    constructor(params: LearningMilestoneGlobalExceptionDtoParams) {
        Object.assign(this, params);
    }

    static fromDomain(domain?: LearningMilestoneGlobalException): LearningMilestonesGlobalExceptionDto | null {
        if (!domain) {
            return null;
        }

        return new LearningMilestonesGlobalExceptionDto({
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            id: domain.id.value,
            name: domain.name,
            periodEnd: domain.period.to.toISOString(),
            periodStart: domain.period.from.toISOString(),
        });
    }
}
