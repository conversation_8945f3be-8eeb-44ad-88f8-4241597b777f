import { LearningMilestonesGlobalExceptionDto } from './GlobalExceptionDto';
import { Type } from 'class-transformer';
import { FixNestedArrayJsonSchemaReference } from '../../../../../core/controllers/dto/decorators/FixNestedJsonSchemaReference';
import { ValidateNested } from 'class-validator';

export class GetOneGlobalExceptionDto {
    @Type(() => LearningMilestonesGlobalExceptionDto)
    @FixNestedArrayJsonSchemaReference(LearningMilestonesGlobalExceptionDto)
    @ValidateNested()
    exception: LearningMilestonesGlobalExceptionDto;
}
