import { Authorized, CurrentUser, Get, <PERSON>sonController, Param, QueryParams } from 'routing-controllers';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import responses from '../../../../core/controllers/docs/responses';
import { ifStudentAllowOnlySelf } from '../../../../core/controllers/ifStudentAllowOnlySelf';
import { DateRange } from '../../../../core/domain/value-objects/DateRange';
import Id from '../../../../core/domain/value-objects/Id';
import User, { Role } from '../../../../users/shared/infrastructure/db/User';
import { LearningMilestonesServiceToken } from '../../../infrastructure/di/tokens';
import { LearningMilestonesService } from '../../../services/LearningMilestonesService';
import { GetAllLearningMilestonesRequestDto } from './dto/GetAllLearningMilestonesRequestDto';
import { LearningMilestoneDto } from './dto/LearningMilestoneDto';
import { QueryLearningMilestonesByUserResponseDto } from './dto/QueryLearningMilestonesByUserResponseDto';
import { GetOneLearningMilestoneByIdResponseDto } from './dto/GetOneLearningMilestoneByIdResponseDto';
import { GetManyLearningMilestonesByIdResponseDto } from './dto/GetManyLearningMilestonesByIdResponseDto';
import { GetManyLearningMilestonesByIdRequestDto } from './dto/GetManyLearningMilestonesByIdRequestDto';
import NotFoundError from '../../../../core/errors/NotFoundError';
import { LEARNING_MILESTONES_OPEN_API_CONFIGURATION } from '../learningMilestonesOpenApiConfiguration';

@Service()
@OpenAPI(LEARNING_MILESTONES_OPEN_API_CONFIGURATION)
@JsonController('/students')
export class LearningMilestonesUserController {
    constructor(
        @Inject(LearningMilestonesServiceToken)
        private readonly learningMilestonesService: LearningMilestonesService,
    ) {}

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Get('/:userId/learning-milestones/query')
    @ResponseSchema(QueryLearningMilestonesByUserResponseDto)
    @OpenAPI({
        responses,
        summary: 'Get learning milestones by user',
        security: [{ cookieAuth: [] }],
    })
    async queryAllUserLearningMilestones(
        @Param('userId') userId: number,
        @CurrentUser() user: User,
        @QueryParams() options: GetAllLearningMilestonesRequestDto,
    ): Promise<QueryLearningMilestonesByUserResponseDto> {
        ifStudentAllowOnlySelf(user, userId);

        const range = DateRange.fromDatesInclusive(options.from, options.to);
        const result = await this.learningMilestonesService.getUserLearningMilestones({
            userId: new Id(userId),
            range,
            pagination: options,
        });

        return new QueryLearningMilestonesByUserResponseDto({
            totalRows: result.totalRows,
            // @ts-expect-error TS(2322) FIXME: Type '(LearningMilestoneDto | null)[]' is not assi... Remove this comment to see the full error message
            rows: result.rows.map(LearningMilestoneDto.fromDomain),
        });
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Get('/:userId/learning-milestones/:milestoneId')
    @ResponseSchema(GetOneLearningMilestoneByIdResponseDto)
    @OpenAPI({
        responses,
        summary: 'Get one learning milestone by ID',
        security: [{ cookieAuth: [] }],
    })
    async getMilestoneById(
        @Param('userId') userId: number,
        @Param('milestoneId') milestoneId: number,
        @CurrentUser() user: User,
    ): Promise<GetOneLearningMilestoneByIdResponseDto> {
        ifStudentAllowOnlySelf(user, userId);

        const [milestone] = await this.learningMilestonesService.getUserLearningMilestonesByIds({
            milestonesIds: [new Id(milestoneId)],
            userId: new Id(userId),
        });

        if (!milestone) {
            throw new NotFoundError('Milestone not found');
        }

        return {
            milestone: LearningMilestoneDto.fromDomain(milestone),
        };
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Get('/:userId/learning-milestones')
    @ResponseSchema(GetManyLearningMilestonesByIdResponseDto)
    @OpenAPI({
        responses,
        summary: "Get learning milestones by IDs. Will silently omit milestones that doesn't belong to user",
        security: [{ cookieAuth: [] }],
    })
    async getManyMilestonesByIds(
        @Param('userId') userId: number,
        @QueryParams() params: GetManyLearningMilestonesByIdRequestDto,
        @CurrentUser() user: User,
    ): Promise<GetManyLearningMilestonesByIdResponseDto> {
        ifStudentAllowOnlySelf(user, userId);

        const milestones = await this.learningMilestonesService.getUserLearningMilestonesByIds({
            milestonesIds: params.milestoneIds.map((id) => new Id(+id)),
            userId: new Id(userId),
        });

        return {
            // @ts-expect-error TS(2322) FIXME: Type '(LearningMilestoneDto | null)[]' is not assi... Remove this comment to see the full error message
            milestones: milestones.map(LearningMilestoneDto.fromDomain),
        };
    }
}
