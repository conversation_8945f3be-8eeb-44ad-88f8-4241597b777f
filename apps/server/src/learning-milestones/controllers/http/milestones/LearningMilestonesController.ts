import { Authorized, Json<PERSON>ontroller, <PERSON> } from 'routing-controllers';
import { OpenAPI } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import responses from '../../../../core/controllers/docs/responses';
import { Role } from '../../../../users/shared/infrastructure/db/User';
import {
    LearningMilestonesServiceToken,
    StandupAttendanceMilestonesNotifierServiceToken,
} from '../../../infrastructure/di/tokens';
import { StandupAttendanceUnmetMilestonesNotifierService } from '../../../services/StandupAttendanceUnmetMilestonesNotifierService';
import { LEARNING_MILESTONES_OPEN_API_CONFIGURATION } from '../learningMilestonesOpenApiConfiguration';
import { LearningMilestonesService } from '../../../services/LearningMilestonesService';

@Service()
@OpenAPI(LEARNING_MILESTONES_OPEN_API_CONFIGURATION)
@Json<PERSON>ontroller('/learning-milestones')
export class LearningMilestonesController {
    constructor(
        @Inject(StandupAttendanceMilestonesNotifierServiceToken)
        private readonly standupAttendanceMilestonesNotifierService: StandupAttendanceUnmetMilestonesNotifierService,
        @Inject(LearningMilestonesServiceToken)
        private readonly learningMilestonesService: LearningMilestonesService,
    ) {}

    @Authorized([Role.ADMIN])
    @Post('/standup-attendance/notify-by-platform-about-unmet-milestones')
    @OpenAPI({
        responses,
        summary: 'Trigger platform notifications about user unmet standup attendance',
        security: [{ cookieAuth: [] }],
    })
    async notifyByPlatformAboutUnmetMilestones(): Promise<void> {
        await this.standupAttendanceMilestonesNotifierService.notifyUsersAboutUnmetMilestonesByPlatform();
    }

    @Authorized([Role.ADMIN])
    @Post('/standup-attendance/notify-by-email-about-unmet-milestones')
    @OpenAPI({
        responses,
        summary: 'Trigger email notifications about user unmet standup attendance',
        security: [{ cookieAuth: [] }],
    })
    async notifyByEmailAboutUnmetMilestones(): Promise<void> {
        await this.standupAttendanceMilestonesNotifierService.notifyUsersAboutUnmetMilestonesByEmail();
    }

    @Authorized([Role.ADMIN])
    @Post('/standup-attendance/notify-by-email-about-changed-requirements')
    @OpenAPI({
        responses,
        summary: 'Trigger email notifications for user about changed requirement',
        security: [{ cookieAuth: [] }],
    })
    async notifyByEmailAboutChangedRequirement(): Promise<void> {
        await this.learningMilestonesService.notifyUsersWithChangedRequirementsSinceLastWeekByEmail();
    }

    @Authorized([Role.ADMIN])
    @Post('/standup-attendance/notify-by-platform-about-changed-requirements')
    @OpenAPI({
        responses,
        summary: 'Trigger platform notifications for user about changed requirement',
        security: [{ cookieAuth: [] }],
    })
    async notifyByPlatformAboutChangedRequirement(): Promise<void> {
        await this.learningMilestonesService.notifyUsersWithChangedRequirementsSinceLastWeekByPlatform();
    }
}
