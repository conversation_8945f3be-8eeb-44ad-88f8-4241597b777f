import { LearningMilestoneDto } from './LearningMilestoneDto';
import { PaginationResponseDto } from '../../../../../core/controllers/dto/PaginationResponseDto';
import { FixNestedArrayJsonSchemaReference } from '../../../../../core/controllers/dto/decorators/FixNestedJsonSchemaReference';
import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';

export class QueryLearningMilestonesByUserResponseDto extends PaginationResponseDto<LearningMilestoneDto> {
    @Type(() => LearningMilestoneDto)
    @FixNestedArrayJsonSchemaReference(LearningMilestoneDto)
    @ValidateNested({ each: true })
    rows: LearningMilestoneDto[];
}
