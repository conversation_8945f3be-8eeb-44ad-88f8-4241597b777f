import { Type } from 'class-transformer';
import { IsDate, IsOptional } from 'class-validator';
import { PaginationRequestDto } from '../../../../../core/controllers/dto/PaginationRequestDto';

export class GetAllLearningMilestonesRequestDto extends PaginationRequestDto {
    @IsDate()
    @Type(() => Date)
    @IsOptional()
    from?: Date;

    @IsDate()
    @Type(() => Date)
    @IsOptional()
    to?: Date;
}
