import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';
import { LearningMilestoneDto } from './LearningMilestoneDto';
import { FixNestedArrayJsonSchemaReference } from '../../../../../core/controllers/dto/decorators/FixNestedJsonSchemaReference';

export class GetManyLearningMilestonesByIdResponseDto {
    @Type(() => LearningMilestoneDto)
    @FixNestedArrayJsonSchemaReference(LearningMilestoneDto)
    @ValidateNested({ each: true })
    milestones: LearningMilestoneDto[];
}
