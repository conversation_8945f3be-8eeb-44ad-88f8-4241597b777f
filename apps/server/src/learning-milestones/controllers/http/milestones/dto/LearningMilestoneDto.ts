import { IsDateString, IsEnum, IsInt } from 'class-validator';
import { Mutable, NonFunctionProperties } from '../../../../../utils/UtilityTypes';
import { LearningMilestone } from '../../../../domain/LearningMilestone';
import { LearningMilestoneType } from '../../../../domain/types/LearningMilestoneType';

export type LearningMilestoneDtoParams = Mutable<NonFunctionProperties<LearningMilestoneDto>>;

export class LearningMilestoneDto {
    @IsInt()
    id: number;

    @IsEnum(LearningMilestoneType)
    type: LearningMilestoneType;

    @IsInt()
    userId: number;

    @IsDateString()
    periodStart: string;

    @IsDateString()
    periodEnd: string;

    @IsInt()
    target: number;

    @IsInt()
    progress: number;

    @IsInt()
    explanationId?: number;

    @IsInt()
    exceptionId?: number;

    constructor(params: LearningMilestoneDtoParams) {
        Object.assign(this, params);
    }

    static fromDomain(domain?: LearningMilestone): LearningMilestoneDto | null {
        if (!domain) {
            return null;
        }

        return new LearningMilestoneDto({
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            id: domain.id.value,
            type: domain.type,
            userId: domain.userId.value,
            target: domain.target,
            progress: domain.progress,
            periodEnd: domain.period.to.toISOString(),
            periodStart: domain.period.from.toISOString(),
            explanationId: domain.explanationId?.value,
            exceptionId: domain.exceptionId?.value,
        });
    }
}
