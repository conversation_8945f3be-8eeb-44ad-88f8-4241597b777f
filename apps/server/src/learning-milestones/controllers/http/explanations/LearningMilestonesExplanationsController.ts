import { Authorized, Body, CurrentUser, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'routing-controllers';
import { OpenAPI } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import responses from '../../../../core/controllers/docs/responses';
import { ifStudentAllowOnlySelf } from '../../../../core/controllers/ifStudentAllowOnlySelf';
import Id from '../../../../core/domain/value-objects/Id';
import User, { Role } from '../../../../users/shared/infrastructure/db/User';
import { UnmetLearningMilestoneExplanation } from '../../../domain/UnmetLearningMilestoneExplanation';
import { UnmetLearningMilestonesExplanationsServiceToken } from '../../../infrastructure/di/tokens';
import { CreateExplanationRequestDto } from './dto/CreateExplanationRequestDto';
import { UnmetLearningMilestonesExplanationsService } from '../../../services/UnmetLearningMilestonesExplanationsService';
import { LEARNING_MILESTONES_OPEN_API_CONFIGURATION } from '../learningMilestonesOpenApiConfiguration';

@Service()
@OpenAPI(LEARNING_MILESTONES_OPEN_API_CONFIGURATION)
@JsonController('')
export class LearningMilestonesExplanationsController {
    constructor(
        @Inject(UnmetLearningMilestonesExplanationsServiceToken)
        private readonly unmetLearningMilestonesExplanationsService: UnmetLearningMilestonesExplanationsService,
    ) {}

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Post('/students/:userId/learning-milestone-explanations')
    @OpenAPI({
        responses,
        summary: 'Explain unmet milestones.',
        security: [{ cookieAuth: [] }],
    })
    async explainUnmetMilestones(
        @Param('userId') userIdPlain: number,
        @CurrentUser() user: User,
        @Body() dto: CreateExplanationRequestDto,
    ): Promise<void> {
        ifStudentAllowOnlySelf(user, userIdPlain);

        const userId = new Id(userIdPlain);

        await this.unmetLearningMilestonesExplanationsService.explainUnmetMilestones({
            explanation: UnmetLearningMilestoneExplanation.fromParams({
                reasoning: dto.reasoning,
                situation: dto.situation,
                userId,
            }),
            milestonesIds: dto.milestoneIds.map((id) => {
                return new Id(id);
            }),
        });
    }
}
