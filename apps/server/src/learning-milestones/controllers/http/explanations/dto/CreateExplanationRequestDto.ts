import { UnmetLearningMilestoneExplanationSituation } from '../../../../domain/types/UnmetLearningMilestoneExplanationSituation';
import { ArrayNotEmpty, IsEnum, IsString, MaxLength, MinLength } from 'class-validator';
import { IsId } from '../../../../../core/controllers/validators/IsId';

export class CreateExplanationRequestDto {
    @IsEnum(UnmetLearningMilestoneExplanationSituation)
    situation: UnmetLearningMilestoneExplanationSituation;

    @IsString()
    @MaxLength(5_000)
    @MinLength(5)
    reasoning: string;

    @IsId({ each: true })
    @ArrayNotEmpty()
    milestoneIds: number[];
}
