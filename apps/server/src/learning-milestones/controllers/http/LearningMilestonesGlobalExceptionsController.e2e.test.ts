import { faker } from '@faker-js/faker';
import 'jest-extended';
import { Request } from 'superagent';
import request from 'supertest';
import { Container } from 'typedi';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import Id from '../../../core/domain/value-objects/Id';
import { Week } from '../../../core/domain/value-objects/Week';
import { CreateOrUpdateGlobalExplanationDto } from './exceptions/dto/CreateOrUpdateGlobalExplanationDto';
import { WeekSlotDto } from './exceptions/dto/WeekSlotDto';
import { LearningMilestonesUserController } from './milestones/LearningMilestonesUserController';
import { LearningMilestoneGlobalException } from '../../domain/LearningMilestoneGlobalException';
import { LearningMilestonesGlobalExceptionsServiceToken } from '../../infrastructure/di/tokens';
import { LearningMilestonesGlobalExceptionsService } from '../../services/LearningMilestonesGlobalExceptionsService';
import Staff from '../../../users/staff/infrastructure/db/Staff';
import { IntegrationTestLearningMilestone } from '../../../test-toolkit/e2e/entities/IntegrationTestLearningMilestone';
import { IntegrationTestLearningMilestoneGlobalException } from '../../../test-toolkit/e2e/entities/IntegrationTestLearningMilestoneGlobalException';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { LearningMilestonesConstants } from '../../domain/LearningMilestonesConstants';

describe(LearningMilestonesUserController.name, () => {
    let config: Configuration;
    let admin: IntegrationTestUser<Staff>;
    let learningMilestonesGlobalExceptionsService: LearningMilestonesGlobalExceptionsService;
    const nextWeek = Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(1);
    const nextNextWeek = nextWeek.jump(1);

    function getGlobalExceptions({
        page,
        pageSize,
        actor,
    }: {
        page?: number;
        pageSize?: number;
        actor: IntegrationTestUser<Staff>;
    }): Request {
        return request(config.server)
            .get(`/learning-milestones/global-exceptions/query?page=${page ?? 1}&pageSize=${pageSize ?? 10}`)
            .set('Cookie', `token=${actor.getAuthTokenString()}`)
            .send();
    }

    function getExceptionById({ id, actor }: { id: Id; actor: IntegrationTestUser<Staff> }): Request {
        return request(config.server)
            .get(`/learning-milestones/global-exceptions/${id.value}`)
            .set('Cookie', `token=${actor.getAuthTokenString()}`)
            .send();
    }

    function getWeekSlots({ actor }: { actor: IntegrationTestUser<Staff> }): Request {
        return request(config.server)
            .get('/learning-milestones/global-exceptions/week-slots/available')
            .set('Cookie', `token=${actor.getAuthTokenString()}`)
            .send();
    }

    function createException({
        actor,
        name = faker.lorem.sentence(),
        range,
    }: {
        range: Week;
        name?: string;
        actor: IntegrationTestUser<Staff>;
    }): Request {
        return request(config.server)
            .post('/learning-milestones/global-exceptions')
            .set('Cookie', `token=${actor.getAuthTokenString()}`)
            .send({
                name,
                periodStart: range.range.from.toISOString(),
                periodEnd: range.range.to.toISOString(),
            } as CreateOrUpdateGlobalExplanationDto);
    }

    function updateException({
        id,
        actor,
        name = faker.lorem.sentence(),
        range,
    }: {
        id: Id;
        range: Week;
        name?: string;
        actor: IntegrationTestUser<Staff>;
    }): Request {
        return request(config.server)
            .put(`/learning-milestones/global-exceptions/${id.value}`)
            .set('Cookie', `token=${actor.getAuthTokenString()}`)
            .send({
                name,
                periodStart: range.range.from.toISOString(),
                periodEnd: range.range.to.toISOString(),
            } as CreateOrUpdateGlobalExplanationDto);
    }

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        admin = await IntegrationTestUser.createFakeAdmin();
        await admin.authorize();

        learningMilestonesGlobalExceptionsService = Container.get(LearningMilestonesGlobalExceptionsServiceToken);
    });

    describe('POST /learning-milestones/global-exceptions', () => {
        it('should create global exception', async () => {
            const studentA = await IntegrationTestUser.createFakeStudent();
            const studentB = await IntegrationTestUser.createFakeStudent();
            const learningMilestoneA = await IntegrationTestLearningMilestone.create({
                userId: studentA.getIdOrThrow(),
                period: nextWeek.range,
            });
            const learningMilestoneB = await IntegrationTestLearningMilestone.create({
                userId: studentB.getIdOrThrow(),
                period: nextNextWeek.range,
            });

            const result = await createException({
                actor: admin,
                range: nextWeek,
            });

            expect(result.status).toEqual(200);

            const query = await getGlobalExceptions({
                actor: admin,
            });

            expect(query.status).toEqual(200);
            expect(query.body.rows).toBeArrayOfSize(1);

            const updatedMilestoneA = await IntegrationTestLearningMilestone.getById(learningMilestoneA.getIdOrThrow());

            expect(updatedMilestoneA.params.exceptionId?.value).toEqual(result.body.id);

            const updatedMilestoneB = await IntegrationTestLearningMilestone.getById(learningMilestoneB.getIdOrThrow());

            expect(updatedMilestoneB.params.exceptionId?.value).toBeUndefined();

            await learningMilestonesGlobalExceptionsService.deleteException(new Id(result.body.id));
        });

        it('should fail if exception already exist for the week', async () => {
            const exceptionA = await createException({
                actor: admin,
                range: nextWeek,
            });

            expect(exceptionA.status).toEqual(200);

            const exceptionB = await createException({
                actor: admin,
                range: nextWeek,
            });

            expect(exceptionB.status).toEqual(409);
            expect(exceptionB.body).toMatchObject(
                expect.objectContaining({
                    message: 'Exception for this period already exists',
                }),
            );

            await learningMilestonesGlobalExceptionsService.deleteException(new Id(exceptionA.body.id));
        });
    });

    describe('PUT /learning-milestones/global-exceptions', () => {
        it('should update global exception name', async () => {
            const studentA = await IntegrationTestUser.createFakeStudent();
            const learningMilestoneA = await IntegrationTestLearningMilestone.create({
                userId: studentA.getIdOrThrow(),
                period: nextWeek.range,
            });
            const learningMilestoneB = await IntegrationTestLearningMilestone.create({
                userId: studentA.getIdOrThrow(),
                period: nextNextWeek.range,
            });

            const createResult = await createException({
                actor: admin,
                range: nextWeek,
            });

            expect(createResult.status).toEqual(200);

            const updateResult = await updateException({
                actor: admin,
                id: new Id(createResult.body.id),
                name: 'another name',
                range: nextWeek,
            });

            expect(updateResult.status).toEqual(200);

            const updatedMilestoneA = await IntegrationTestLearningMilestone.getById(learningMilestoneA.getIdOrThrow());

            expect(updatedMilestoneA.params.exceptionId?.value).toEqual(updateResult.body.id);

            const updatedMilestoneB = await IntegrationTestLearningMilestone.getById(learningMilestoneB.getIdOrThrow());

            expect(updatedMilestoneB.params.exceptionId?.value).toBeUndefined();

            const getResult = await getExceptionById({
                actor: admin,
                id: new Id(updateResult.body.id),
            });

            expect(getResult.status).toEqual(200);
            expect(getResult.body).toMatchObject({
                exception: {
                    id: updateResult.body.id,
                    name: 'another name',
                    periodStart: nextWeek.range.from.toISOString(),
                    periodEnd: nextWeek.range.to.toISOString(),
                },
            });

            await learningMilestonesGlobalExceptionsService.deleteException(new Id(updateResult.body.id));
        });

        it('should update global exception period & name', async () => {
            const studentA = await IntegrationTestUser.createFakeStudent();
            const learningMilestoneA = await IntegrationTestLearningMilestone.create({
                userId: studentA.getIdOrThrow(),
                period: nextWeek.range,
            });
            const learningMilestoneB = await IntegrationTestLearningMilestone.create({
                userId: studentA.getIdOrThrow(),
                period: nextNextWeek.range,
            });

            const createResult = await createException({
                actor: admin,
                range: nextWeek,
            });

            expect(createResult.status).toEqual(200);

            const updateResult = await updateException({
                actor: admin,
                id: new Id(createResult.body.id),
                name: 'With new period',
                range: nextNextWeek,
            });

            expect(updateResult.status).toEqual(200);

            const updatedMilestoneA = await IntegrationTestLearningMilestone.getById(learningMilestoneA.getIdOrThrow());

            expect(updatedMilestoneA.params.exceptionId?.value).toBeUndefined();

            const updatedMilestoneB = await IntegrationTestLearningMilestone.getById(learningMilestoneB.getIdOrThrow());

            expect(updatedMilestoneB.params.exceptionId?.value).toEqual(updateResult.body.id);

            const getResult = await getExceptionById({
                actor: admin,
                id: new Id(updateResult.body.id),
            });

            expect(getResult.status).toEqual(200);
            expect(getResult.body).toMatchObject({
                exception: {
                    id: updateResult.body.id,
                    name: 'With new period',
                    periodStart: nextNextWeek.range.from.toISOString(),
                    periodEnd: nextNextWeek.range.to.toISOString(),
                },
            });

            await learningMilestonesGlobalExceptionsService.deleteException(new Id(updateResult.body.id));
        });

        it('should not allow to update with invalid period', async () => {
            const createResult = await createException({
                actor: admin,
                range: nextWeek,
            });

            expect(createResult.status).toEqual(200);

            const updateResult = await updateException({
                actor: admin,
                id: new Id(createResult.body.id),
                name: 'With new period',
                range: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE),
            });

            expect(updateResult.status).toEqual(409);
            expect(updateResult.body.message).toBe('Extension can be created only starting from next week');

            await learningMilestonesGlobalExceptionsService.deleteException(new Id(createResult.body.id));
        });

        it('should not allow to update passed exception', async () => {
            const exception = await IntegrationTestLearningMilestoneGlobalException.create({
                period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).previous().range,
            });

            const updateResult = await updateException({
                actor: admin,
                id: exception.getIdOrThrow(),
                name: 'With new period',
                range: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE),
            });

            expect(updateResult.status).toEqual(409);
            expect(updateResult.body.message).toBe('Passed extension cannot be updated');

            await learningMilestonesGlobalExceptionsService.deleteException(exception.getIdOrThrow());
        });

        it('should allow to update this week exception', async () => {
            const exception = await IntegrationTestLearningMilestoneGlobalException.create({
                period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).range,
            });

            const updateResult = await updateException({
                actor: admin,
                id: exception.getIdOrThrow(),
                name: 'With new period',
                range: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).next(),
            });

            expect(updateResult.status).toEqual(200);

            await learningMilestonesGlobalExceptionsService.deleteException(exception.getIdOrThrow());
        });
    });

    describe('GET /learning-milestones/global-exceptions/week-slots/available', () => {
        it('should get free week slots', async () => {
            const createResultNextWeek = await createException({
                actor: admin,
                range: nextWeek,
            });

            expect(createResultNextWeek.status).toEqual(200);

            const fewWeeksAhead = Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(3);
            const createResultFewWeeksAhead = await createException({
                actor: admin,
                range: fewWeeksAhead,
            });

            expect(createResultFewWeeksAhead.status).toEqual(200);

            const getWeekSlotsResult = await getWeekSlots({ actor: admin });

            expect(getWeekSlotsResult.status).toEqual(200);
            expect(getWeekSlotsResult.body.slots?.length).toBe(
                LearningMilestoneGlobalException.MAX_EXTENSION_WEEKS_AHEAD - 2,
            );

            expect(
                getWeekSlotsResult.body.slots.some((slot: WeekSlotDto) => {
                    const periodStart = +new Date(slot.periodStart);
                    return periodStart === +nextWeek.range.from || periodStart === +fewWeeksAhead.range.from;
                }),
            ).toBe(false);

            await learningMilestonesGlobalExceptionsService.deleteException(new Id(createResultNextWeek.body.id));
            await learningMilestonesGlobalExceptionsService.deleteException(new Id(createResultFewWeeksAhead.body.id));
        });
    });
});
