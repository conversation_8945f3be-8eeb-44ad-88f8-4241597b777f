import { faker } from '@faker-js/faker';
import 'jest-extended';
import { Request } from 'superagent';
import request from 'supertest';
import Container from 'typedi';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import { DateRange } from '../../../core/domain/value-objects/DateRange';
import Id from '../../../core/domain/value-objects/Id';
import { Week } from '../../../core/domain/value-objects/Week';
import { LearningMilestoneDto } from './milestones/dto/LearningMilestoneDto';
import { LearningMilestonesUserController } from './milestones/LearningMilestonesUserController';
import { UnmetLearningMilestoneExplanationSituation } from '../../domain/types/UnmetLearningMilestoneExplanationSituation';
import Arrays from '../../../utils/Arrays';
import { IntegrationTestLearningMilestone } from '../../../test-toolkit/e2e/entities/IntegrationTestLearningMilestone';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { LearningMilestonesConstants } from '../../domain/LearningMilestonesConstants';

describe(LearningMilestonesUserController.name, () => {
    let config: Configuration;

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
    });

    function getUsersMilestones({
        page,
        pageSize,
        range,
        student,
    }: {
        page?: number;
        pageSize?: number;
        range?: DateRange;
        student: IntegrationTestUser;
    }): Request {
        let rangeQuery = '';
        if (range) {
            rangeQuery += `&from=${range.from.toISOString()}&to=${range.to.toISOString()}`;
        }
        return request(config.server)
            .get(
                `/students/${student.getIdOrThrow().value}/learning-milestones/query/?page=${page ?? 1}&pageSize=${
                    pageSize ?? 10
                }${rangeQuery}`,
            )
            .set('Cookie', `token=${student.getAuthTokenString()}`)
            .send();
    }

    function createExplanation({
        student,
        situation = faker.helpers.arrayElement(Object.values(UnmetLearningMilestoneExplanationSituation)),
        reasoning = faker.lorem.sentence(),
        milestones,
    }: {
        milestones: IntegrationTestLearningMilestone[];
        situation?: UnmetLearningMilestoneExplanationSituation;
        reasoning?: string;
        student: IntegrationTestUser;
    }): Request {
        return request(config.server)
            .post(`/students/${student.getIdOrThrow().value}/learning-milestone-explanations`)
            .set('Cookie', `token=${student.getAuthTokenString()}`)
            .send({
                milestoneIds: milestones.map((milestone) => milestone.getIdOrThrow().value),
                situation,
                reasoning,
            });
    }

    describe('GET /students/:userId/learning-milestones/query', () => {
        let student: IntegrationTestUser;
        let milestones: IntegrationTestLearningMilestone[];

        beforeAll(async () => {
            student = await IntegrationTestUser.createFakeStudent();
            await student.authorize();

            const thisWeek = Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE);
            const weeksCount = 10;
            // Two weeks in future + 8 in past
            const weeks = Arrays.stream(weeksCount)
                .map((i) => {
                    return thisWeek.jump(-(weeksCount - 2) + i);
                })
                .reverse();

            milestones = await Promise.all(
                weeks.map((week) => {
                    return IntegrationTestLearningMilestone.create({
                        userId: student.getIdOrThrow(),
                        period: week.range,
                        progress: 0,
                        target: 1,
                    });
                }),
            );

            const result = await createExplanation({
                student,
                milestones: [milestones[0], milestones[1]],
            });

            expect(result.status).toBe(204);
        });

        it('should get all records that are sorted', async () => {
            const res = await getUsersMilestones({
                pageSize: 1000,
                student,
            });

            expect(res.status).toBe(200);
            expect(res.body.rows).toHaveLength(10);
            expect(res.body.totalRows).toBe(10);

            const milestoneStarts = res.body.rows.map((row: LearningMilestoneDto) => {
                return +new Date(row.periodStart);
            });

            expect(milestoneStarts).toStrictEqual([...milestoneStarts].sort((a, b) => b - a));
        });

        it('should get first page of records', async () => {
            const res = await getUsersMilestones({
                pageSize: 5,
                student,
            });

            expect(res.status).toBe(200);
            expect(res.body.rows).toHaveLength(5);
            expect(res.body.totalRows).toBe(10);

            expect(res.body.rows.map((row: LearningMilestoneDto) => row.id)).toStrictEqual(
                milestones.slice(0, 5).map((row: IntegrationTestLearningMilestone) => row.getIdOrThrow().value),
            );
        });

        it('should get second page of records', async () => {
            const res = await getUsersMilestones({
                pageSize: 5,
                page: 2,
                student,
            });

            expect(res.status).toBe(200);
            expect(res.body.rows).toHaveLength(5);
            expect(res.body.totalRows).toBe(10);

            expect(res.body.rows.map((row: LearningMilestoneDto) => row.id)).toStrictEqual(
                milestones.slice(-5).map((row: IntegrationTestLearningMilestone) => row.getIdOrThrow().value),
            );
        });

        it('should get records inside range', async () => {
            const res = await getUsersMilestones({
                pageSize: 1000,
                range: DateRange.fromDatesInclusive(milestones[6].params.period.from, milestones[4].params.period.to),
                student,
            });

            expect(res.status).toBe(200);
            expect(res.body.rows).toHaveLength(3);
            expect(res.body.totalRows).toBe(3);

            expect(res.body.rows.map((row: LearningMilestoneDto) => row.id)).toStrictEqual(
                milestones.slice(4, 7).map((row: IntegrationTestLearningMilestone) => row.getIdOrThrow().value),
            );
        });
    });

    describe('POST /students/:userId/learning-milestone-explanations', () => {
        it('should create explanation if all conditions are correct', async () => {
            const student = await IntegrationTestUser.createFakeStudent();
            await student.authorize();

            await IntegrationTestLearningMilestone.create({
                userId: student.getIdOrThrow(),
                period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(2).range,
                progress: 0,
                target: 3,
            });

            const milestones = [
                await IntegrationTestLearningMilestone.create({
                    userId: student.getIdOrThrow(),
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-1).range,
                    progress: 0,
                    target: 5,
                }),
                await IntegrationTestLearningMilestone.create({
                    userId: student.getIdOrThrow(),
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).range,
                    progress: 0,
                    target: 5,
                }),
            ];

            const result = await createExplanation({
                student,
                milestones,
            });

            expect(result.status).toBe(204);

            const milestonesResponse = await getUsersMilestones({
                pageSize: 100,
                page: 1,
                student,
            });

            expect(milestonesResponse.body.rows).toHaveLength(3);

            const explanationsSetCorrectly = milestonesResponse.body.rows.every((row: LearningMilestoneDto) => {
                if ([milestones[0].getIdOrThrow().value, milestones[1].getIdOrThrow().value].includes(row.id)) {
                    return !!row.explanationId;
                }

                return !row.explanationId;
            });

            expect(explanationsSetCorrectly).toBeTrue();
        });

        it('should fail to create if trying to create for another user', async () => {
            const student = await IntegrationTestUser.createFakeStudent();
            const anotherStudent = await IntegrationTestUser.createFakeStudent();
            await student.authorize();

            const milestones = [
                await IntegrationTestLearningMilestone.create({
                    userId: anotherStudent.getIdOrThrow(),
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).range,
                    progress: 0,
                    target: 3,
                }),
            ];

            const result = await createExplanation({
                student,
                milestones,
            });

            expect(result.status).toBe(400);
            expect(result.body).toMatchObject({
                code: 400,
                message: 'Cannot explain milestone for other user',
            });
        });

        it('should fail if there is explanation already', async () => {
            const student = await IntegrationTestUser.createFakeStudent();
            await student.authorize();

            const milestones = [
                await IntegrationTestLearningMilestone.create({
                    userId: student.getIdOrThrow(),
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).range,
                    progress: 0,
                    target: 3,
                }),
            ];

            let result = await createExplanation({
                student,
                milestones,
            });

            expect(result.status).toBe(204);

            result = await createExplanation({
                student,
                milestones,
            });

            expect(result.status).toBe(400);
            expect(result.body).toMatchObject({
                code: 400,
                message: 'This milestone already has an explanation',
            });
        });

        it('should fail if trying to explain for more than 1 week before', async () => {
            const student = await IntegrationTestUser.createFakeStudent();
            await student.authorize();

            const milestones = [
                await IntegrationTestLearningMilestone.create({
                    userId: student.getIdOrThrow(),
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-2).range,
                    progress: 0,
                    target: 3,
                }),
            ];

            const result = await createExplanation({
                student,
                milestones,
            });

            expect(result.status).toBe(400);
            expect(result.body).toMatchObject({
                code: 400,
                message: 'Cannot explain milestone older than previous week',
            });
        });

        it('should fail if milestone is met', async () => {
            const student = await IntegrationTestUser.createFakeStudent();
            await student.authorize();

            const milestones = [
                await IntegrationTestLearningMilestone.create({
                    userId: student.getIdOrThrow(),
                    period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).range,
                    progress: 4,
                    target: 3,
                }),
            ];

            const result = await createExplanation({
                student,
                milestones,
            });

            expect(result.status).toBe(400);
            expect(result.body).toMatchObject({
                code: 400,
                message: 'Cannot explain met milestone',
            });
        });

        it('should fail if no milestones provided', async () => {
            const student = await IntegrationTestUser.createFakeStudent();
            await student.authorize();

            const result = await createExplanation({
                student,
                milestones: [],
            });

            expect(result.status).toBe(400);
            expect(result.body).toMatchObject({
                code: 400,
                message: "Invalid body, check 'errors' property for more info.",
            });
        });
    });

    describe('GET /students/:userId/learning-milestones/', () => {
        let student: IntegrationTestUser;
        let anotherStudent: IntegrationTestUser;
        let studentMilestones: IntegrationTestLearningMilestone[];
        let anotherStudentMilestones: IntegrationTestLearningMilestone[];

        beforeAll(async () => {
            student = await IntegrationTestUser.createFakeStudent();
            anotherStudent = await IntegrationTestUser.createFakeStudent();

            studentMilestones = await Promise.all(
                Arrays.stream(3).map((i) => {
                    return IntegrationTestLearningMilestone.create({
                        userId: student.getIdOrThrow(),
                        period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-i).range,
                        progress: 0,
                        target: 1,
                    });
                }),
            );

            anotherStudentMilestones = await Promise.all(
                Arrays.stream(3).map((i) => {
                    return IntegrationTestLearningMilestone.create({
                        userId: anotherStudent.getIdOrThrow(),
                        period: Week.current(LearningMilestonesConstants.REQUIREMENTS_TIMEZONE).jump(-i).range,
                        progress: 1,
                        target: 1,
                    });
                }),
            );

            await student.authorize();
        });

        describe('GET /students/:userId/learning-milestones/:id', () => {
            function requestMilestone({
                student,
                milestoneId,
            }: {
                student: IntegrationTestUser;
                milestoneId: Id;
            }): Request {
                return request(config.server)
                    .get(`/students/${student.getIdOrThrow().value}/learning-milestones/${milestoneId.value}`)
                    .set('Cookie', `token=${student.getAuthTokenString()}`)
                    .send();
            }

            it('should return user learning milestone', async () => {
                const result = await requestMilestone({
                    student,
                    milestoneId: studentMilestones[0].getIdOrThrow(),
                });

                expect(result.status).toBe(200);
                expect(result.body).toMatchObject({
                    milestone: {
                        id: studentMilestones[0].getIdOrThrow().value,
                    },
                });
            });

            it('should 404 in case of not existent milestone', async () => {
                const result = await requestMilestone({
                    student,
                    milestoneId: new Id(511123),
                });

                expect(result.status).toBe(404);
            });

            it('should 404 in case of other user milestone', async () => {
                const result = await requestMilestone({
                    student,
                    milestoneId: anotherStudentMilestones[0].getIdOrThrow(),
                });

                expect(result.status).toBe(404);
            });
        });

        describe('GET /students/:userId/learning-milestones/', () => {
            function requestMilestone({
                student,
                milestoneIds,
            }: {
                student: IntegrationTestUser;
                milestoneIds: Id[];
            }): Request {
                return request(config.server)
                    .get(
                        `/students/${student.getIdOrThrow().value}/learning-milestones/?${milestoneIds
                            .map((id) => `milestoneIds=${id.value}`)
                            .join('&')}`,
                    )
                    .set('Cookie', `token=${student.getAuthTokenString()}`)
                    .send();
            }

            it('should return user learning milestones', async () => {
                const result = await requestMilestone({
                    student,
                    milestoneIds: [studentMilestones[0].getIdOrThrow(), studentMilestones[2].getIdOrThrow()],
                });

                expect(result.status).toBe(200);
                expect(result.body.milestones).toHaveLength(2);

                const milestones = result.body.milestones.map((milestone: LearningMilestoneDto) => milestone.id);

                expect(milestones).toContain(studentMilestones[0].getIdOrThrow().value);
                expect(milestones).toContain(studentMilestones[2].getIdOrThrow().value);
            });

            it('should return empty milestones if not existent milestone', async () => {
                const result = await requestMilestone({
                    student,
                    milestoneIds: [new Id(511123)],
                });

                expect(result.status).toBe(200);
                expect(result.body).toMatchObject({
                    milestones: [],
                });
            });

            it('should silently not return milestones that does not belong to user', async () => {
                const result = await requestMilestone({
                    student,
                    milestoneIds: [anotherStudentMilestones[0].getIdOrThrow(), studentMilestones[1].getIdOrThrow()],
                });

                expect(result.status).toBe(200);
                expect(result.body).toMatchObject({
                    milestones: [
                        expect.objectContaining({
                            id: studentMilestones[1].getIdOrThrow().value,
                        }),
                    ],
                });
            });
        });
    });
});
