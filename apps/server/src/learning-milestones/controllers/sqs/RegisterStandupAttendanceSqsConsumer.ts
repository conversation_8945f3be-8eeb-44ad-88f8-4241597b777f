import ErrorTracker from '../../../error-tracking/infrastructure/services/ErrorTracker';
import { Configuration } from '../../../config/Configuration';
import { LoggingService } from '../../../logging/services/LoggingService';
import AbstractSqsConsumer from '../../../messaging/AbstractSqsConsumer';
import { MeetingAttendeeIntegrationEventPayload } from '../../../meetings/events/MeetingAttendeeIntegrationEvent';
import {
    MeetingStateIntegrationEvent,
    MeetingStateIntegrationEventPayload,
} from '../../../meetings/events/MeetingStateIntegrationEvent';
import { MeetingStateIntegrationEventType } from '../../../meetings/events/MeetingStateIntegrationEventType';
import { StandupAttendanceTrackerService } from '../../services/StandupAttendanceTrackerService';

export default class RegisterStandupAttendanceSqsConsumer extends AbstractSqsConsumer<MeetingAttendeeIntegrationEventPayload> {
    constructor(
        configuration: Configuration,
        loggingService: LoggingService,
        private readonly standupAttendanceTrackerService: StandupAttendanceTrackerService,
        errorTracker: ErrorTracker,
    ) {
        super(
            configuration,
            configuration.queues.learningMilestones.registerStandupAttendance,
            errorTracker,
            loggingService.createLogger(RegisterStandupAttendanceSqsConsumer.name),
        );
    }

    async onMessage(
        payload: MeetingAttendeeIntegrationEventPayload | MeetingStateIntegrationEventPayload,
    ): Promise<void> {
        if (payload.eventType !== MeetingStateIntegrationEventType.ENDED) {
            this.logger.debug('Ignoring event because it is not a meeting end event.');

            return;
        }

        const event = MeetingStateIntegrationEvent.deserialize(payload);

        this.logger.debug(`Received event: ${event.describe()}`);

        await this.standupAttendanceTrackerService.registerStandupAttendanceWhenMeetingEnded(event);
    }
}
