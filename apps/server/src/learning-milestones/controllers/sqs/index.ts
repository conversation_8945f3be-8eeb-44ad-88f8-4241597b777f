import { Container } from 'typedi';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import { LoggingModule } from '../../../logging/infrastructure/di/LoggingModule';
import Consumer from '../../../messaging/Consumer';
import RegisterStandupAttendanceSqsConsumer from './RegisterStandupAttendanceSqsConsumer';
import { StandupAttendanceTrackerServiceToken } from '../../infrastructure/di/tokens';
import { ErrorTrackingModule } from '../../../error-tracking/infrastructure/di/ErrorTrackingModule';

export function learningMilestonesConsumers(): Consumer[] {
    return [
        new RegisterStandupAttendanceSqsConsumer(
            Container.get(ConfigurationModule.CONFIGURATION_TOKEN),
            Container.get(LoggingModule.LOGGING_SERVICE_TOKEN),
            Container.get(StandupAttendanceTrackerServiceToken),
            Container.get(ErrorTrackingModule.ERROR_TRACKER_TOKEN),
        ),
    ];
}
