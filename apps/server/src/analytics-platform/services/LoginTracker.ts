import { LocalAuthenticationMethod } from '../../auth/domain/AuthenticationMethod';
import BatchFinder from '../../batches/services/BatchFinder';
import Id from '../../core/domain/value-objects/Id';
import { Role } from '../../users/shared/infrastructure/db/User';
import { StudentService } from '../../users/students/management/services/StudentService';
import Strings from '../../utils/Strings';
import Analytics from '../../analytics/Analytics';

interface LoginData {
    id: number;
    role: Role;
    identification: string;
}

interface LoginTracker {
    trackFailed(data: LoginData): Promise<void>;

    trackSuccessful(data: LoginData): Promise<void>;
}

export default LoginTracker;

export class DefaultLoginTracker implements LoginTracker {
    constructor(
        private readonly analytics: Analytics,
        private readonly studentService: StudentService,
        private readonly batchFinder: BatchFinder,
    ) {}

    async trackFailed(data: LoginData): Promise<void> {
        if (data.role !== Role.USER) {
            return;
        }

        this.analytics.loginAttempt(data.id, {
            isSuccessful: false,
            type: this.getAuthenticationMethod(data.identification),
        });
    }

    async trackSuccessful(data: LoginData): Promise<void> {
        if (data.role !== Role.USER) {
            return;
        }

        this.analytics.loginAttempt(data.id, {
            isSuccessful: true,
            type: this.getAuthenticationMethod(data.identification),
        });

        const student = await this.studentService.get(data.id);
        const batch = await this.batchFinder.findById(new Id(student.batchId));
        this.analytics.updateUser(data.id, {
            username: student.username,
            name: student.name,
            surname: student.surname,
            email: student.email,
            batchName: batch?.name.value,
            billingType: student.billingType,
            source: student.source,
        });
    }

    private getAuthenticationMethod(identification: string): LocalAuthenticationMethod {
        return Strings.isEmail(identification) ? LocalAuthenticationMethod.EMAIL : LocalAuthenticationMethod.USERNAME;
    }
}
