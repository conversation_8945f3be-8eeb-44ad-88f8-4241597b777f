import SuccessfulLoginEvent from '../../../auth/events/SuccessfulLoginEvent';
import ApplicationEventBroker from '../../../events/application/ApplicationEventBroker';
import ApplicationEventSubscriber from '../../../events/application/ApplicationEventSubscriber';
import LoginTracker from '../../services/LoginTracker';

export default class SuccessfulLoginEventTracker implements ApplicationEventSubscriber<SuccessfulLoginEvent> {
    readonly name = SuccessfulLoginEventTracker.name;

    constructor(private readonly tracker: LoginTracker) {}

    async handle(event: SuccessfulLoginEvent): Promise<void> {
        await this.tracker.trackSuccessful(event.payload);
    }

    subscribeTo(broker: ApplicationEventBroker): void {
        broker.subscribe(SuccessfulLoginEvent.NAME, this);
    }
}
