import { faker } from '@faker-js/faker';
import request from 'supertest';
import { Container } from 'typedi';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import Id from '../../../core/domain/value-objects/Id';
import { TransactionManagerToken } from '../../../core/infrastructure/di/tokens';
import InternalStudentNote from '../../domain/InternalStudentNote';
import NoteContent from '../../domain/NoteContent';
import InternalStudentNoteTypeormRepository from '../../infrastructure/db/InternalStudentNoteTypeormRepository';
import Mentor from '../../../users/mentors/infrastructure/db/Mentor';
import Staff from '../../../users/staff/infrastructure/db/Staff';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';

describe('InternalStudentNoteController IT', () => {
    let config: Configuration;
    let noteRepository: InternalStudentNoteTypeormRepository;

    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let mentor: IntegrationTestUser<Mentor>;
    let student: IntegrationTestUser;

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        noteRepository = new InternalStudentNoteTypeormRepository(Container.get(TransactionManagerToken));

        ({ admin, student, staff, mentor } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute());
    });

    describe('create', () => {
        async function createNoteTest(note: InternalStudentNote, token: string, responseCode: number): Promise<void> {
            const notesBefore = await noteRepository.getAll();

            await request(config.server)
                .post('/notes/internal/student')
                .set('Cookie', `token=${token}`)
                .send({
                    studentId: note.studentId?.value,
                    content: note.content?.value,
                })
                .expect(responseCode);

            const notesAfter = await noteRepository.getAll();
            if (responseCode === 204) {
                expect(notesAfter.length).toEqual(notesBefore.length + 1);
                expect(notesAfter).toEqual(expect.arrayContaining([{ ...note, id: expect.any(Id) }]));
            } else {
                expect(notesAfter.length).toEqual(notesBefore.length);
            }
        }

        it('should create a note with admin', async () => {
            await createNoteTest(
                new InternalStudentNote(
                    new Id(admin.getIdOrThrow()),
                    new NoteContent(faker.lorem.sentences()),
                    new Id(student.getIdOrThrow()),
                ),
                admin.getAuthTokenString(),
                204,
            );
        });

        it('should create a note with staff', async () => {
            await createNoteTest(
                new InternalStudentNote(
                    new Id(staff.getIdOrThrow()),
                    new NoteContent(faker.lorem.sentences()),
                    new Id(student.getIdOrThrow()),
                ),
                staff.getAuthTokenString(),
                204,
            );
        });

        it('should create a note with mentor', async () => {
            await createNoteTest(
                new InternalStudentNote(
                    new Id(mentor.getIdOrThrow()),
                    new NoteContent(faker.lorem.sentences()),
                    new Id(student.getIdOrThrow()),
                ),
                mentor.getAuthTokenString(),
                204,
            );
        });

        it('should not create a note with student', async () => {
            await createNoteTest(
                new InternalStudentNote(
                    new Id(student.getIdOrThrow()),
                    new NoteContent(faker.lorem.sentences()),
                    new Id(student.getIdOrThrow()),
                ),
                student.getAuthTokenString(),
                403,
            );
        });
    });
});
