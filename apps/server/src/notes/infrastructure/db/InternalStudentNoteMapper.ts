import Id from '../../../core/domain/value-objects/Id';
import TypeormPersistenceMapper from '../../../core/infrastructure/db/TypeormPersistenceMapper';
import InternalStudentNote from '../../domain/InternalStudentNote';
import NoteContent from '../../domain/NoteContent';
import InternalStudentNotePersistenceEntity from './InternalStudentNotePersistenceEntity';

export default class InternalStudentNoteMapper
    implements TypeormPersistenceMapper<InternalStudentNote, InternalStudentNotePersistenceEntity>
{
    toDomain(persistenceEntity: InternalStudentNotePersistenceEntity): InternalStudentNote {
        return new InternalStudentNote(
            new Id(persistenceEntity.authorId),
            new NoteContent(persistenceEntity.content),
            new Id(persistenceEntity.studentId),
            persistenceEntity.id ? new Id(persistenceEntity.id) : undefined,
        );
    }

    toPersistence(domainEntity: InternalStudentNote): InternalStudentNotePersistenceEntity {
        const persistenceEntity = new InternalStudentNotePersistenceEntity();
        persistenceEntity.id = domainEntity.id?.value;
        persistenceEntity.authorId = domainEntity.authorId.value;
        persistenceEntity.studentId = domainEntity.studentId.value;
        persistenceEntity.content = domainEntity.content.value;

        return persistenceEntity;
    }
}
