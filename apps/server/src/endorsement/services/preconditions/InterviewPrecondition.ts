import Endorsement from '../../domain/Endorsement';
import CompletedStageState from '../../domain/state/CompletedStageState';
import Precondition from './Precondition';

export default class InterviewPrecondition implements Precondition {
    async isSatisfied(endorsement: Endorsement): Promise<boolean> {
        return (
            endorsement.hiringProfile.state instanceof CompletedStageState &&
            endorsement.socialMedia.state instanceof CompletedStageState
        );
    }
}
