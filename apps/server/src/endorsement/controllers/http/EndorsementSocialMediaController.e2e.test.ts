import { faker } from '@faker-js/faker';
import 'reflect-metadata';
import request from 'supertest';
import { Container } from 'typedi';
import { promisify } from 'util';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import Id from '../../../core/domain/value-objects/Id';
import Order from '../../../core/domain/value-objects/Order';
import Url from '../../../core/domain/value-objects/Url';
import FullName from '../../../core/domain/value-objects/user/FullName';
import { TransactionManagerToken } from '../../../core/infrastructure/di/tokens';
import CommentEventDataDTO from './dto/history/CommentEventDataDTO';
import CommentEventDTO from './dto/history/CommentEventDTO';
import CreatorDTO from './dto/history/CreatorDTO';
import StateChangeEventDTO from './dto/history/StateChangeEventDTO';
import SocialMediaDTO from './dto/social-media/SocialMediaDTO';
import StageStateDTO from './dto/state/StageStateDTO';
import StageCommentContent from '../../domain/comments/StageCommentContent';
import EndorsementRepository from '../../domain/EndorsementRepository';
import HiringProfileRepository from '../../domain/hiring-profile/HiringProfileRepository';
import InterviewRepository from '../../domain/interview/InterviewRepository';
import HRInterviewStepTemplateRepository from '../../domain/interview/templates/HRInterviewStepTemplateRepository';
import TechInterviewStepTemplateRepository from '../../domain/interview/templates/TechInterviewStepTemplateRepository';
import GithubUrl from '../../domain/social-media/GithubUrl';
import LinkedInUrl from '../../domain/social-media/LinkedInUrl';
import SocialMedia from '../../domain/social-media/SocialMedia';
import SocialMediaRepository from '../../domain/social-media/SocialMediaRepository';
import StepSummary from '../../domain/social-media/StepSummary';
import SocialMediaEndorsementStepTemplate from '../../domain/social-media/templates/SocialMediaEndorsementStepTemplate';
import CompletedStageState from '../../domain/state/CompletedStageState';
import InProgressStageState from '../../domain/state/InProgressStageState';
import PendingStageState from '../../domain/state/PendingStageState';
import { StageStateAction } from '../../domain/state/StageState';
import SubmittedStageState from '../../domain/state/SubmittedStageState';
import HiringProfileTypeormRepository from '../../infrastructure/db/hiring-profile/HiringProfileTypeormRepository';
import HRStepTemplateTypeormRepository from '../../infrastructure/db/interview/HRStepTemplateTypeormRepository';
import InterviewTypeormRepository from '../../infrastructure/db/interview/InterviewTypeormRepository';
import TechStepTemplateTypeormRepository from '../../infrastructure/db/interview/TechStepTemplateTypeormRepository';
import GithubStepTemplateTypeormRepository from '../../infrastructure/db/social-media/GithubStepTemplateTypeormRepository';
import LinkedInStepTemplateTypeormRepository from '../../infrastructure/db/social-media/LinkedInStepTemplateTypeormRepository';
import SocialMediaTypeormRepository from '../../infrastructure/db/social-media/SocialMediaTypeormRepository';
import { EndorsementStageStateManagerToken, SocialMediaManagerToken } from '../../infrastructure/di/tokens';
import Creator from '../../services/dto/Creator';
import EndorsementStageStateManager from '../../services/EndorsementStageStateManager';
import SocialMediaManager from '../../services/SocialMediaManager';
import Mentor from '../../../users/mentors/infrastructure/db/Mentor';
import Staff from '../../../users/staff/infrastructure/db/Staff';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';
import Endorsement from '../../domain/Endorsement';

describe('EndorsementSocialMediaController IT', () => {
    let configuration: Configuration;
    let endorsementStageStateManager: EndorsementStageStateManager;
    let socialMediaManager: SocialMediaManager;
    let hiringProfileRepository: HiringProfileRepository;
    let socialMediaRepository: SocialMediaRepository;
    let githubStepTemplateRepository: GithubStepTemplateTypeormRepository;
    let linkedInStepTemplateRepository: GithubStepTemplateTypeormRepository;
    let endorsementRepository: EndorsementRepository;
    let interviewRepository: InterviewRepository;
    let hrInterviewStepTemplateRepository: HRInterviewStepTemplateRepository;
    let techInterviewStepTemplateRepository: TechInterviewStepTemplateRepository;
    let student: IntegrationTestUser;
    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let mentor: IntegrationTestUser<Mentor>;
    let githubInterviewEndorsementStepTemplate: SocialMediaEndorsementStepTemplate;
    let linkedinInterviewEndorsementStepTemplate: SocialMediaEndorsementStepTemplate;

    beforeAll(async () => {
        configuration = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        endorsementStageStateManager = Container.get(EndorsementStageStateManagerToken);
        socialMediaManager = Container.get(SocialMediaManagerToken);
        hiringProfileRepository = new HiringProfileTypeormRepository(Container.get(TransactionManagerToken));
        socialMediaRepository = new SocialMediaTypeormRepository(Container.get(TransactionManagerToken));
        githubStepTemplateRepository = new GithubStepTemplateTypeormRepository(Container.get(TransactionManagerToken));
        linkedInStepTemplateRepository = new LinkedInStepTemplateTypeormRepository(
            Container.get(TransactionManagerToken),
        );
        interviewRepository = new InterviewTypeormRepository(Container.get(TransactionManagerToken));
        hrInterviewStepTemplateRepository = new HRStepTemplateTypeormRepository(Container.get(TransactionManagerToken));
        techInterviewStepTemplateRepository = new TechStepTemplateTypeormRepository(
            Container.get(TransactionManagerToken),
        );
        endorsementRepository = new EndorsementRepository(
            Container.get(TransactionManagerToken),
            hiringProfileRepository,
            socialMediaRepository,
            githubStepTemplateRepository,
            linkedInStepTemplateRepository,
            interviewRepository,
            hrInterviewStepTemplateRepository,
            techInterviewStepTemplateRepository,
        );
        ({ admin, student, staff, mentor } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute());
    });

    beforeEach(async () => {
        const hiringProfile = await hiringProfileRepository.getByUser(student.getIdOrThrow());
        if (hiringProfile) {
            await hiringProfileRepository.delete(hiringProfile.getIdOrThrow());
        }

        const socialMedia = await socialMediaRepository.getByUser(student.getIdOrThrow());
        if (socialMedia) {
            await socialMediaRepository.delete(socialMedia.getIdOrThrow());
        }

        const interview = await interviewRepository.getByUser(student.getIdOrThrow());
        if (interview) {
            await interviewRepository.delete(interview.getIdOrThrow());
        }

        if (githubInterviewEndorsementStepTemplate) {
            await githubStepTemplateRepository.delete(githubInterviewEndorsementStepTemplate.getIdOrThrow());
        }
        if (linkedinInterviewEndorsementStepTemplate) {
            await linkedInStepTemplateRepository.delete(linkedinInterviewEndorsementStepTemplate.getIdOrThrow());
        }

        githubInterviewEndorsementStepTemplate = await githubStepTemplateRepository.save(
            new SocialMediaEndorsementStepTemplate(
                new StepSummary(faker.word.words()),
                new Url(faker.internet.url()),
                new Order(0),
            ),
        );

        linkedinInterviewEndorsementStepTemplate = await linkedInStepTemplateRepository.save(
            new SocialMediaEndorsementStepTemplate(
                new StepSummary(faker.word.words()),
                new Url(faker.internet.url()),
                new Order(0),
            ),
        );
    });

    describe('GET /endorsement/stages/social-media', () => {
        async function testGet(
            id: Id,
            token: string,
            expectedCode: number,
            expectedSocialMedia?: SocialMedia,
        ): Promise<void> {
            const res = await request(configuration.server)
                .get(`/endorsements/${id.value}/stages/social-media`)
                .set('Cookie', `token=${token}`)
                .send()
                .expect(expectedCode);

            if (expectedCode === 200) {
                expect(res.body).toEqual(SocialMediaDTO.fromSocialMedia(expectedSocialMedia as SocialMedia));
            }
        }

        it('should get a pending social media profile with a student user', async () => {
            const endorsement = await endorsementRepository.getOrCreate(student.getIdOrThrow());

            await testGet(student.getIdOrThrow(), student.getAuthTokenString(), 200, endorsement.socialMedia);
        });

        it('should get an in-progress social media profile with a student user', async () => {
            const endorsement = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsement.socialMedia.start(student.getIdOrThrow());
            await endorsementRepository.save(endorsement);

            await testGet(student.getIdOrThrow(), student.getAuthTokenString(), 200, endorsement.socialMedia);
        });

        it('should not get another students social media profile', async () => {
            await testGet(new Id(999), student.getAuthTokenString(), 403);
        });

        it('should get a social media profile with an admin user', async () => {
            const endorsement = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsement.socialMedia.start(student.getIdOrThrow());
            await endorsementRepository.save(endorsement);

            await testGet(student.getIdOrThrow(), admin.getAuthTokenString(), 200, endorsement.socialMedia);
        });

        it('should get a social media profile with a staff user', async () => {
            const endorsement = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsement.socialMedia.start(student.getIdOrThrow());
            await endorsementRepository.save(endorsement);

            await testGet(student.getIdOrThrow(), staff.getAuthTokenString(), 200, endorsement.socialMedia);
        });

        it('should not get a social media profile with a mentor user', async () => {
            await endorsementRepository.getOrCreate(student.getIdOrThrow());

            await testGet(student.getIdOrThrow(), mentor.getAuthTokenString(), 403);
        });
    });

    describe('PATCH /endorsements/:student.id/stages/social-media', () => {
        it('should set github url', async () => {
            const endorsementBefore = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsementBefore.socialMedia.start(student.getIdOrThrow());
            await endorsementRepository.save(endorsementBefore);

            await request(configuration.server)
                .patch(`/endorsements/${student.getIdOrThrow().value}/stages/social-media`)
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send({
                    github: {
                        url: 'https://github.com/test',
                    },
                })
                .expect(204);

            const endorsementAfter = await endorsementRepository.get(student.getIdOrThrow());
            expect(endorsementAfter?.socialMedia.github.url).toEqual(new GithubUrl('https://github.com/test'));
        });

        it('should unset github url', async () => {
            const endorsementBefore = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsementBefore.socialMedia.start(student.getIdOrThrow());
            endorsementBefore.socialMedia.changeGithubUrl(new GithubUrl('https://github.com/test'));
            await endorsementRepository.save(endorsementBefore);

            await request(configuration.server)
                .patch(`/endorsements/${student.getIdOrThrow().value}/stages/social-media`)
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send({
                    github: {
                        url: null,
                    },
                })
                .expect(204);

            const endorsementAfter = await endorsementRepository.get(student.getIdOrThrow());
            expect(endorsementAfter?.socialMedia.github.url).toBeUndefined();
        });

        it('should set linkedin url', async () => {
            const endorsementBefore = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsementBefore.socialMedia.start(student.getIdOrThrow());
            await endorsementRepository.save(endorsementBefore);

            await request(configuration.server)
                .patch(`/endorsements/${student.getIdOrThrow().value}/stages/social-media`)
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send({
                    linkedIn: {
                        url: 'https://linkedin.com/test',
                    },
                })
                .expect(204);

            const endorsementAfter = await endorsementRepository.get(student.getIdOrThrow());
            expect(endorsementAfter?.socialMedia.linkedIn.url).toEqual(new LinkedInUrl('https://linkedin.com/test'));
        });

        it('should unset linkedin url', async () => {
            const endorsementBefore = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsementBefore.socialMedia.start(student.getIdOrThrow());
            endorsementBefore.socialMedia.changeLinkedInUrl(new LinkedInUrl('https://linkedin.com/test'));
            await endorsementRepository.save(endorsementBefore);

            await request(configuration.server)
                .patch(`/endorsements/${student.getIdOrThrow().value}/stages/social-media`)
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send({
                    linkedIn: {
                        url: null,
                    },
                })
                .expect(204);

            const endorsementAfter = await endorsementRepository.get(student.getIdOrThrow());
            expect(endorsementAfter?.socialMedia.linkedIn.url).toBeUndefined();
        });

        it('should complete github step', async () => {
            const endorsementBefore = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsementBefore.socialMedia.start(student.getIdOrThrow());
            await endorsementRepository.save(endorsementBefore);

            await request(configuration.server)
                .patch(`/endorsements/${student.getIdOrThrow().value}/stages/social-media`)
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send({
                    github: {
                        steps: [
                            {
                                id: endorsementBefore.socialMedia.github.steps[0].getIdOrThrow().value,
                                isCompleted: true,
                            },
                        ],
                    },
                })
                .expect(204);

            const endorsementAfter = await endorsementRepository.get(student.getIdOrThrow());
            expect(endorsementAfter?.socialMedia.github.steps[0].isCompleted).toBeTruthy();
        });

        it('should reset complete github step', async () => {
            const endorsementBefore = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsementBefore.socialMedia.start(student.getIdOrThrow());
            endorsementBefore.socialMedia.changeGithubStep(
                endorsementBefore.socialMedia.github.steps[0].getIdOrThrow(),
                true,
            );
            await endorsementRepository.save(endorsementBefore);

            await request(configuration.server)
                .patch(`/endorsements/${student.getIdOrThrow().value}/stages/social-media`)
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send({
                    github: {
                        steps: [
                            {
                                id: endorsementBefore.socialMedia.github.steps[0].getIdOrThrow().value,
                                isCompleted: false,
                            },
                        ],
                    },
                })
                .expect(204);

            const endorsementAfter = await endorsementRepository.get(student.getIdOrThrow());
            expect(endorsementAfter?.socialMedia.github.steps[0].isCompleted).toBeFalsy();
        });

        it('should complete linkedin step', async () => {
            const endorsementBefore = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsementBefore.socialMedia.start(student.getIdOrThrow());
            await endorsementRepository.save(endorsementBefore);

            await request(configuration.server)
                .patch(`/endorsements/${student.getIdOrThrow().value}/stages/social-media`)
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send({
                    linkedIn: {
                        steps: [
                            {
                                id: endorsementBefore.socialMedia.linkedIn.steps[0].getIdOrThrow().value,
                                isCompleted: true,
                            },
                        ],
                    },
                })
                .expect(204);

            const endorsementAfter = await endorsementRepository.get(student.getIdOrThrow());
            expect(endorsementAfter?.socialMedia.linkedIn.steps[0].isCompleted).toBeTruthy();
        });

        it('should reset complete linkedin step', async () => {
            const endorsementBefore = await endorsementRepository.getOrCreate(student.getIdOrThrow());
            endorsementBefore.socialMedia.start(student.getIdOrThrow());
            endorsementBefore.socialMedia.changeLinkedInStep(
                endorsementBefore.socialMedia.linkedIn.steps[0].getIdOrThrow(),
                true,
            );
            await endorsementRepository.save(endorsementBefore);

            await request(configuration.server)
                .patch(`/endorsements/${student.getIdOrThrow().value}/stages/social-media`)
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send({
                    linkedIn: {
                        steps: [
                            {
                                id: endorsementBefore.socialMedia.linkedIn.steps[0].getIdOrThrow().value,
                                isCompleted: false,
                            },
                        ],
                    },
                })
                .expect(204);

            const endorsementAfter = await endorsementRepository.get(student.getIdOrThrow());
            expect(endorsementAfter?.socialMedia.linkedIn.steps[0].isCompleted).toBeFalsy();
        });
    });

    describe('POST /endorsements/:student.id/stages/social-media/states', () => {
        describe('start', () => {
            async function testStart(
                id: Id,
                comment: string | undefined,
                token: string,
                expectedCode: number,
            ): Promise<void> {
                await request(configuration.server)
                    .post(`/endorsements/${id.value}/stages/social-media/states`)
                    .set('Cookie', `token=${token}`)
                    .send({
                        comment,
                        action: StageStateAction.START,
                    })
                    .expect(expectedCode);

                const endorsement = (await endorsementRepository.get(id)) as Endorsement;
                if (expectedCode === 204) {
                    expect(endorsement.socialMedia.state).toBeInstanceOf(InProgressStageState);
                    expect(endorsement.socialMedia.state.action).toEqual(StageStateAction.START);
                    if (comment) {
                        expect(endorsement.socialMedia.comments.some((c) => c.content.value === comment)).toBeTruthy();
                    }
                } else if (endorsement) {
                    expect(endorsement.socialMedia.state).toBeInstanceOf(PendingStageState);
                    if (comment) {
                        expect(endorsement.socialMedia.comments.some((c) => c.content.value === comment)).toBeFalsy();
                    }
                }
            }

            beforeEach(async () => {
                await endorsementRepository.getOrCreate(student.getIdOrThrow());
            });

            it('should start a hiring profile with admin', async () => {
                await testStart(student.getIdOrThrow(), faker.word.words(), admin.getAuthTokenString(), 204);
            });

            it('should start a hiring profile with staff', async () => {
                await testStart(student.getIdOrThrow(), undefined, staff.getAuthTokenString(), 204);
            });

            it('should not start a hiring profile with student', async () => {
                await testStart(student.getIdOrThrow(), undefined, student.getAuthTokenString(), 403);
            });

            it('should not start a hiring profile with mentor', async () => {
                await testStart(student.getIdOrThrow(), faker.word.words(), mentor.getAuthTokenString(), 403);
            });
        });

        describe('submit', () => {
            async function testSubmit(
                id: Id,
                comment: string | undefined,
                token: string,
                expectedCode: number,
            ): Promise<void> {
                await request(configuration.server)
                    .post(`/endorsements/${id.value}/stages/social-media/states`)
                    .set('Cookie', `token=${token}`)
                    .send({
                        comment,
                        action: StageStateAction.SUBMIT,
                    })
                    .expect(expectedCode);

                const endorsement = (await endorsementRepository.get(id)) as Endorsement;
                if (expectedCode === 204) {
                    expect(endorsement.socialMedia.state).toBeInstanceOf(SubmittedStageState);
                    expect(endorsement.socialMedia.state.action).toEqual(StageStateAction.SUBMIT);
                    if (comment) {
                        expect(endorsement.socialMedia.comments.some((c) => c.content.value === comment)).toBeTruthy();
                    }
                } else if (endorsement) {
                    expect(endorsement.socialMedia.state).toBeInstanceOf(InProgressStageState);
                    if (comment) {
                        expect(endorsement.socialMedia.comments.some((c) => c.content.value === comment)).toBeFalsy();
                    }
                }
            }

            beforeEach(async () => {
                const endorsement = await endorsementRepository.getOrCreate(student.getIdOrThrow());

                endorsement.socialMedia.start(student.getIdOrThrow());
                await endorsementRepository.save(endorsement);
                await socialMediaManager.update(student.getIdOrThrow(), {
                    github: {
                        url: new GithubUrl('https://github.com/test'),
                        steps: endorsement.socialMedia.github.steps.map((step) => ({
                            id: step.getIdOrThrow(),
                            isCompleted: true,
                        })),
                    },
                    linkedIn: {
                        url: new LinkedInUrl('https://linkedin.com/test'),
                        steps: endorsement.socialMedia.linkedIn.steps.map((step) => ({
                            id: step.getIdOrThrow(),
                            isCompleted: true,
                        })),
                    },
                });
            });

            it('should submit a social media profile', async () => {
                await testSubmit(student.getIdOrThrow(), undefined, student.getAuthTokenString(), 204);
            });

            it('should submit a social media profile with admin', async () => {
                await testSubmit(student.getIdOrThrow(), faker.word.words(), admin.getAuthTokenString(), 204);
            });

            it('should submit a social media profile with staff', async () => {
                await testSubmit(student.getIdOrThrow(), undefined, staff.getAuthTokenString(), 204);
            });

            it('should not submit somebody else social media profile', async () => {
                await testSubmit(new Id(999), undefined, student.getAuthTokenString(), 403);
            });

            it('should not submit a social media profile with mentor', async () => {
                await testSubmit(student.getIdOrThrow(), faker.word.words(), mentor.getAuthTokenString(), 403);
            });
        });

        describe('restart', () => {
            async function testRestart(
                id: Id,
                comment: string | undefined,
                token: string,
                expectedCode: number,
            ): Promise<void> {
                await request(configuration.server)
                    .post(`/endorsements/${id.value}/stages/social-media/states`)
                    .set('Cookie', `token=${token}`)
                    .send({
                        comment,
                        action: StageStateAction.RESTART,
                    })
                    .expect(expectedCode);

                const endorsement = (await endorsementRepository.get(id)) as Endorsement;
                if (expectedCode === 204) {
                    expect(endorsement.socialMedia.state).toBeInstanceOf(InProgressStageState);
                    expect(endorsement.socialMedia.state.action).toEqual(StageStateAction.RESTART);
                    if (comment) {
                        expect(endorsement.socialMedia.comments.some((c) => c.content.value === comment)).toBeTruthy();
                    }
                } else if (endorsement) {
                    expect(endorsement.socialMedia.state).toBeInstanceOf(SubmittedStageState);
                    if (comment) {
                        expect(endorsement.socialMedia.comments.some((c) => c.content.value === comment)).toBeFalsy();
                    }
                }
            }

            beforeEach(async () => {
                const endorsement = await endorsementRepository.getOrCreate(student.getIdOrThrow());

                endorsement.socialMedia.start(student.getIdOrThrow());
                await endorsementRepository.save(endorsement);
                await socialMediaManager.update(student.getIdOrThrow(), {
                    github: {
                        url: new GithubUrl('https://github.com/test'),
                        steps: endorsement.socialMedia.github.steps.map((step) => ({
                            id: step.getIdOrThrow(),
                            isCompleted: true,
                        })),
                    },
                    linkedIn: {
                        url: new LinkedInUrl('https://linkedin.com/test'),
                        steps: endorsement.socialMedia.linkedIn.steps.map((step) => ({
                            id: step.getIdOrThrow(),
                            isCompleted: true,
                        })),
                    },
                });

                await endorsementStageStateManager.submit(student.getIdOrThrow(), student.getIdOrThrow(), SocialMedia);
            });

            it('should restart a social media profile', async () => {
                await testRestart(student.getIdOrThrow(), undefined, student.getAuthTokenString(), 204);
            });

            it('should restart a social media profile with admin', async () => {
                await testRestart(student.getIdOrThrow(), faker.word.words(), admin.getAuthTokenString(), 204);
            });

            it('should restart a social media profile with staff', async () => {
                await testRestart(student.getIdOrThrow(), undefined, staff.getAuthTokenString(), 204);
            });

            it('should not restart a social media profile of another student', async () => {
                await testRestart(new Id(999), undefined, student.getAuthTokenString(), 403);
            });

            it('should not restart a social media profile with mentor', async () => {
                await testRestart(student.getIdOrThrow(), faker.word.words(), mentor.getAuthTokenString(), 403);
            });
        });

        describe('reject', () => {
            async function testReject(
                id: Id,
                comment: string | undefined,
                token: string,
                expectedCode: number,
            ): Promise<void> {
                await request(configuration.server)
                    .post(`/endorsements/${id.value}/stages/social-media/states`)
                    .set('Cookie', `token=${token}`)
                    .send({
                        comment,
                        action: StageStateAction.REJECT,
                    })
                    .expect(expectedCode);

                const endorsement = (await endorsementRepository.get(id)) as Endorsement;
                if (expectedCode === 204) {
                    expect(endorsement.socialMedia.state).toBeInstanceOf(InProgressStageState);
                    expect(endorsement.socialMedia.state.action).toEqual(StageStateAction.REJECT);
                    if (comment) {
                        expect(endorsement.socialMedia.comments.some((c) => c.content.value === comment)).toBeTruthy();
                    }
                } else {
                    expect(endorsement.socialMedia.state).toBeInstanceOf(SubmittedStageState);
                    if (comment) {
                        expect(endorsement.socialMedia.comments.some((c) => c.content.value === comment)).toBeFalsy();
                    }
                }
            }

            beforeEach(async () => {
                const endorsement = await endorsementRepository.getOrCreate(student.getIdOrThrow());

                endorsement.socialMedia.start(student.getIdOrThrow());
                await endorsementRepository.save(endorsement);
                await socialMediaManager.update(student.getIdOrThrow(), {
                    github: {
                        url: new GithubUrl('https://github.com/test'),
                        steps: endorsement.socialMedia.github.steps.map((step) => ({
                            id: step.getIdOrThrow(),
                            isCompleted: true,
                        })),
                    },
                    linkedIn: {
                        url: new LinkedInUrl('https://linkedin.com/test'),
                        steps: endorsement.socialMedia.linkedIn.steps.map((step) => ({
                            id: step.getIdOrThrow(),
                            isCompleted: true,
                        })),
                    },
                });

                await endorsementStageStateManager.submit(student.getIdOrThrow(), student.getIdOrThrow(), SocialMedia);
            });

            it('should complete a social media profile with admin', async () => {
                await testReject(student.getIdOrThrow(), faker.word.words(), admin.getAuthTokenString(), 204);
            });

            it('should complete a social media profile with staff', async () => {
                await testReject(student.getIdOrThrow(), undefined, staff.getAuthTokenString(), 204);
            });

            it('should not complete a social media profile with student', async () => {
                await testReject(student.getIdOrThrow(), undefined, student.getAuthTokenString(), 403);
            });

            it('should not complete a social media profile with mentor', async () => {
                await testReject(student.getIdOrThrow(), faker.word.words(), mentor.getAuthTokenString(), 403);
            });
        });

        describe('complete', () => {
            async function testComplete(
                id: Id,
                comment: string | undefined,
                token: string,
                expectedCode: number,
            ): Promise<void> {
                await request(configuration.server)
                    .post(`/endorsements/${id.value}/stages/social-media/states`)
                    .set('Cookie', `token=${token}`)
                    .send({
                        comment,
                        action: StageStateAction.COMPLETE,
                    })
                    .expect(expectedCode);

                const endorsement = (await endorsementRepository.get(id)) as Endorsement;
                if (expectedCode === 204) {
                    expect(endorsement.socialMedia.state).toBeInstanceOf(CompletedStageState);
                    expect(endorsement.socialMedia.state.action).toEqual(StageStateAction.COMPLETE);
                    if (comment) {
                        expect(endorsement.socialMedia.comments.some((c) => c.content.value === comment)).toBeTruthy();
                    }
                } else {
                    expect(endorsement.socialMedia.state).toBeInstanceOf(SubmittedStageState);
                    if (comment) {
                        expect(endorsement.socialMedia.comments.some((c) => c.content.value === comment)).toBeFalsy();
                    }
                }
            }

            beforeEach(async () => {
                const endorsement = await endorsementRepository.getOrCreate(student.getIdOrThrow());

                endorsement.socialMedia.start(student.getIdOrThrow());
                await endorsementRepository.save(endorsement);
                await socialMediaManager.update(student.getIdOrThrow(), {
                    github: {
                        url: new GithubUrl('https://github.com/test'),
                        steps: endorsement.socialMedia.github.steps.map((step) => ({
                            id: step.getIdOrThrow(),
                            isCompleted: true,
                        })),
                    },
                    linkedIn: {
                        url: new LinkedInUrl('https://linkedin.com/test'),
                        steps: endorsement.socialMedia.linkedIn.steps.map((step) => ({
                            id: step.getIdOrThrow(),
                            isCompleted: true,
                        })),
                    },
                });

                await endorsementStageStateManager.submit(student.getIdOrThrow(), student.getIdOrThrow(), SocialMedia);
            });

            it('should complete a social media profile with admin', async () => {
                await testComplete(student.getIdOrThrow(), faker.word.words(), admin.getAuthTokenString(), 204);
            });

            it('should complete a social media profile with staff', async () => {
                await testComplete(student.getIdOrThrow(), undefined, staff.getAuthTokenString(), 204);
            });

            it('should not complete a social media profile with student', async () => {
                await testComplete(student.getIdOrThrow(), undefined, student.getAuthTokenString(), 403);
            });

            it('should not complete a social media profile with mentor', async () => {
                await testComplete(student.getIdOrThrow(), faker.word.words(), mentor.getAuthTokenString(), 403);
            });
        });
    });

    describe('GET /endorsements/:student.id/stages/social-media/comments', () => {
        async function testGetComments(id: Id, token: string, expectedCode: number): Promise<void> {
            const endorsement = await endorsementRepository.getOrCreate(id);
            endorsement.socialMedia.postComment(student.getIdOrThrow(), new StageCommentContent(faker.word.words()));
            endorsement.socialMedia.postComment(student.getIdOrThrow(), new StageCommentContent(faker.word.words()));
            endorsement.socialMedia.postComment(student.getIdOrThrow(), new StageCommentContent(faker.word.noun()));
            await endorsementRepository.save(endorsement);

            const res = await request(configuration.server)
                .get(`/endorsements/${id.value}/stages/social-media/comments`)
                .set('Cookie', `token=${token}`)
                .expect(expectedCode);

            if (expectedCode === 200) {
                expect(res.body).toEqual(
                    endorsement.socialMedia.comments.map((comment) => ({
                        id: expect.any(Number),
                        content: comment.content.value,
                        postedAt: comment.postedAt.toISOString(),
                        creator: CreatorDTO.fromCreator(
                            new Creator(
                                student.getIdOrThrow(),
                                new FullName(student.params.name as string, student.params.surname as string),
                            ),
                        ),
                    })),
                );
            }
        }

        it('should get comment with admin', async () => {
            await testGetComments(student.getIdOrThrow(), admin.getAuthTokenString(), 200);
        });

        it('should get comment with staff', async () => {
            await testGetComments(student.getIdOrThrow(), staff.getAuthTokenString(), 200);
        });

        it('should get comment with student', async () => {
            await testGetComments(student.getIdOrThrow(), student.getAuthTokenString(), 200);
        });

        it('should not get comment with mentor', async () => {
            await testGetComments(student.getIdOrThrow(), mentor.getAuthTokenString(), 403);
        });
    });

    describe('POST /endorsements/:student.id/stages/social-media/comments', () => {
        async function testPostComment(id: Id, token: string, expectedCode: number): Promise<void> {
            const endorsementBefore = (await endorsementRepository.get(student.getIdOrThrow())) as Endorsement;
            const content = faker.word.words();

            await request(configuration.server)
                .post(`/endorsements/${id.value}/stages/social-media/comments`)
                .set('Cookie', `token=${token}`)
                .send({ content })
                .expect(expectedCode);

            const endorsementAfter = await endorsementRepository.get(id);
            if (expectedCode === 204) {
                expect(endorsementAfter?.socialMedia.comments).toEqual([
                    ...endorsementBefore.socialMedia.comments,
                    expect.objectContaining({
                        content: new StageCommentContent(content),
                    }),
                ]);
            } else if (endorsementAfter) {
                expect(endorsementAfter.socialMedia.comments).toEqual(endorsementBefore?.socialMedia.comments);
            }
        }

        beforeEach(async () => {
            await endorsementRepository.getOrCreate(student.getIdOrThrow());
        });

        it('should post comment with admin', async () => {
            await testPostComment(student.getIdOrThrow(), admin.getAuthTokenString(), 204);
        });

        it('should post comment with staff', async () => {
            await testPostComment(student.getIdOrThrow(), staff.getAuthTokenString(), 204);
        });

        it('should not post comment with student', async () => {
            await testPostComment(student.getIdOrThrow(), student.getAuthTokenString(), 403);
        });

        it('should not post comment with mentor', async () => {
            await testPostComment(student.getIdOrThrow(), mentor.getAuthTokenString(), 403);
        });
    });

    describe('GET /endorsements/:student.id/stages/social-media/history', () => {
        const sleep = promisify(setTimeout);

        async function testGetHistory(token: string, code: number): Promise<void> {
            const endorsement = await endorsementRepository.getOrCreate(new Id(student.getIdOrThrow()));
            endorsement.socialMedia.start(new Id(student.getIdOrThrow()));
            endorsement.socialMedia.changeGithubUrl(new GithubUrl('https://github.com/test'));
            endorsement.socialMedia.github.steps.map((s) =>
                endorsement.socialMedia.changeGithubStep(s.getIdOrThrow(), true),
            );
            endorsement.socialMedia.changeLinkedInUrl(new LinkedInUrl('https://linkedin.com/test'));
            endorsement.socialMedia.linkedIn.steps.map((s) =>
                endorsement.socialMedia.changeLinkedInStep(s.getIdOrThrow(), true),
            );

            await sleep(1); // Avoid overriding past state
            endorsement.socialMedia.submit(new Id(student.getIdOrThrow()));
            await sleep(1);
            endorsement.socialMedia.restart(new Id(student.getIdOrThrow()));
            await sleep(1);
            endorsement.socialMedia.submit(new Id(student.getIdOrThrow()));
            await sleep(1);
            endorsement.socialMedia.complete(new Id(student.getIdOrThrow()));
            await sleep(1);
            endorsement.socialMedia.postComment(student.getIdOrThrow(), new StageCommentContent(faker.word.words()));
            endorsement.socialMedia.postComment(student.getIdOrThrow(), new StageCommentContent(faker.word.words()));
            await endorsementRepository.save(endorsement);

            const res = await request(configuration.server)
                .get(`/endorsements/${student.getIdOrThrow().value}/stages/social-media/history`)
                .set('Cookie', `token=${token}`)
                .send()
                .expect(code);

            if (code === 200) {
                const creator = CreatorDTO.fromCreator(
                    new Creator(
                        student.getIdOrThrow(),
                        new FullName(student.params.name as string, student.params.surname as string),
                    ),
                );
                expect(res.body).toEqual([
                    ...endorsement.socialMedia.stateHistory
                        .map((s) => new StateChangeEventDTO(s.validFrom, creator, new StageStateDTO(s.action, s.value)))
                        .map((e) => ({
                            ...e,
                            timestamp: e.timestamp.toISOString(),
                        })),
                    ...endorsement.socialMedia.comments
                        .map((c) => new CommentEventDTO(c.postedAt, creator, new CommentEventDataDTO(c.content.value)))
                        .map((e) => ({
                            ...e,
                            timestamp: e.timestamp.toISOString(),
                        })),
                ]);
            }
        }

        it('should get with an admin user', async () => {
            await testGetHistory(admin.getAuthTokenString(), 200);
        });

        it('should get with a staff user', async () => {
            await testGetHistory(staff.getAuthTokenString(), 200);
        });

        it('should not get with a mentor user', async () => {
            await testGetHistory(mentor.getAuthTokenString(), 403);
        });

        it('should not get with a student user', async () => {
            await testGetHistory(student.getAuthTokenString(), 403);
        });
    });
});
