import { Type } from 'class-transformer';
import { ValidateNested } from 'class-validator';
import { HistoryEventType } from '../../../../services/dto/HistoryEvent';
import StateChangeEvent from '../../../../services/dto/StateChangeEvent';
import CreatorDTO from './CreatorDTO';
import HistoryEventDTO from './HistoryEventDTO';
import StageStateDTO from '../state/StageStateDTO';

export default class StateChangeEventDTO extends HistoryEventDTO<StageStateDTO> {
    @Type(() => StageStateDTO)
    @ValidateNested()
    readonly data: StageStateDTO;

    constructor(timestamp: Date, creator: CreatorDTO, data: StageStateDTO) {
        super(HistoryEventType.STATE_CHANGE, timestamp, creator, data);
    }

    static fromStateChangeEvent(event: StateChangeEvent): StateChangeEventDTO {
        return new StateChangeEventDTO(
            event.timestamp,
            // @ts-expect-error TS(2345) FIXME: Argument of type '<PERSON><PERSON>D<PERSON> | undefined' is not a... Remove this comment to see the full error message
            event.creator ? CreatorDTO.fromCreator(event.creator) : undefined,
            StageStateDTO.fromStageState(event.data),
        );
    }
}
