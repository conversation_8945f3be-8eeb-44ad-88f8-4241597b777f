import { Type } from 'class-transformer';
import { IsInt, ValidateNested } from 'class-validator';
import 'reflect-metadata';
import SocialMedia from '../../../../domain/social-media/SocialMedia';
import StageStateDTO from '../state/StageStateDTO';
import SocialMediaProfileDTO from './SocialMediaProfileDTO';

export default class SocialMediaDTO {
    @IsInt()
    userId: number;

    @Type(() => StageStateDTO)
    @ValidateNested()
    state: StageStateDTO;

    @ValidateNested()
    @Type(() => SocialMediaProfileDTO)
    linkedIn: SocialMediaProfileDTO;

    @ValidateNested()
    @Type(() => SocialMediaProfileDTO)
    github: SocialMediaProfileDTO;

    static fromSocialMedia(socialMedia: SocialMedia): SocialMediaDTO {
        const dto = new SocialMediaDTO();
        dto.userId = socialMedia.userId.value;
        dto.state = StageStateDTO.fromStageState(socialMedia.state);
        dto.github = SocialMediaProfileDTO.fromEndorsement(socialMedia.github);
        dto.linkedIn = SocialMediaProfileDTO.fromEndorsement(socialMedia.linkedIn);

        return dto;
    }
}
