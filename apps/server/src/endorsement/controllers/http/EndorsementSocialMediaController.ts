import { Authorized, Body, CurrentUser, Get, <PERSON><PERSON><PERSON><PERSON><PERSON>, JsonController, <PERSON><PERSON>, <PERSON>, Post } from 'routing-controllers';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import responses from '../../../core/controllers/docs/responses';
import Id from '../../../core/domain/value-objects/Id';
import AuthorizationError from '../../../core/errors/AuthorizationError';
import NotFoundError from '../../../core/errors/NotFoundError';
import User, { Role } from '../../../users/shared/infrastructure/db/User';
import SocialMedia from '../../domain/social-media/SocialMedia';
import { EndorsementStageStateManagerToken, SocialMediaManagerToken } from '../../infrastructure/di/tokens';
import EndorsementStageStateManager from '../../services/EndorsementStageStateManager';
import SocialMediaManager from '../../services/SocialMediaManager';
import AbstractEndorsementStageController from './AbstractEndorsementStageController';
import CommentDTO from './dto/comments/CommentDTO';
import PostCommentDTO from './dto/comments/PostCommentDTO';
import HistoryEventDTO from './dto/history/HistoryEventDTO';
import SocialMediaDTO from './dto/social-media/SocialMediaDTO';
import SocialMediaUpdateDTO from './dto/social-media/SocialMediaUpdateDTO';
import StageStateUpdateDTO from './dto/state/StageStateUpdateDTO';
import { ifStudentAllowOnlySelf } from '../../../core/controllers/ifStudentAllowOnlySelf';

@Service()
@JsonController('/endorsements/:studentId/stages/social-media')
export default class EndorsementSocialMediaController extends AbstractEndorsementStageController {
    constructor(
        @Inject(SocialMediaManagerToken)
        private readonly socialMediaManager: SocialMediaManager,
        @Inject(EndorsementStageStateManagerToken)
        endorsementStageStateManager: EndorsementStageStateManager,
    ) {
        super(SocialMedia, endorsementStageStateManager);
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Get()
    @ResponseSchema(SocialMediaDTO)
    @OpenAPI({
        responses,
        summary: 'Returns social media endorsement info of a student',
        security: [{ cookieAuth: [] }],
    })
    async get(@Param('studentId') studentId: number, @CurrentUser() user: User): Promise<SocialMediaDTO> {
        ifStudentAllowOnlySelf(user, studentId);

        const socialMedia = await this.socialMediaManager.get(new Id(studentId));
        if (!socialMedia) {
            throw new NotFoundError('Social media profile was not found');
        }

        return SocialMediaDTO.fromSocialMedia(socialMedia);
    }

    @Authorized(Role.USER)
    @Patch()
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Updates a social media endorsement info of a student',
        security: [{ cookieAuth: [] }],
    })
    async update(
        @Param('studentId') studentId: number,
        @CurrentUser() user: User,
        @Body() data: SocialMediaUpdateDTO,
    ): Promise<void> {
        if (studentId !== user.id) {
            throw new AuthorizationError();
        }

        if (!(await this.socialMediaManager.update(new Id(studentId), data.toSocialMediaUpdateProperties()))) {
            throw new NotFoundError('Social media profile was not found');
        }
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Post('/states')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Update an endorsement stage state',
        security: [{ cookieAuth: [] }],
    })
    async updateState(
        @Param('studentId') studentId: number,
        @CurrentUser() user: User,
        @Body() data: StageStateUpdateDTO,
    ): Promise<void> {
        await super.updateState(studentId, user, data);
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Get('/comments')
    @ResponseSchema(CommentDTO, { isArray: true })
    @OpenAPI({
        responses,
        summary: 'Get endorsement stage comments',
        security: [{ cookieAuth: [] }],
    })
    async getComments(@Param('studentId') studentId: number): Promise<CommentDTO[]> {
        return await super.getComments(studentId);
    }

    @Authorized([Role.ADMIN, Role.STAFF])
    @Post('/comments')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Post endorsement stage comment',
        security: [{ cookieAuth: [] }],
    })
    async postComment(
        @Param('studentId') studentId: number,
        @CurrentUser() user: User,
        @Body() data: PostCommentDTO,
    ): Promise<void> {
        await super.postComment(studentId, user, data);
    }

    @Authorized([Role.ADMIN, Role.STAFF])
    @Get('/history')
    @ResponseSchema(HistoryEventDTO, { isArray: true })
    @OpenAPI({
        responses,
        summary: 'Endorsement stage history',
        security: [{ cookieAuth: [] }],
    })
    async getHistory(@Param('studentId') studentId: number): Promise<HistoryEventDTO<any>[]> {
        return await super.getHistory(studentId);
    }
}
