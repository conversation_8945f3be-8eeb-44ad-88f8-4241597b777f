import IllegalArgumentError from '../../../core/errors/IllegalArgumentError';
import Strings from '../../../utils/Strings';

export default class RoleName {
    readonly value: string;

    constructor(value: string) {
        if (Strings.isEmpty(value)) {
            throw IllegalArgumentError.create('Role name has to be a not empty string');
        }

        this.value = value;
    }

    static copy(name: RoleName): RoleName {
        return name ? new RoleName(name.value) : name;
    }
}
