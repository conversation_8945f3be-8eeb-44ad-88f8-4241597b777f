import { faker } from '@faker-js/faker';
import Order from '../../../core/domain/value-objects/Order';
import Url from '../../../core/domain/value-objects/Url';
import IllegalArgumentError from '../../../core/errors/IllegalArgumentError';
import IllegalStateError from '../../../core/errors/IllegalStateError';
import HRInterviewEndorsement from './HRInterviewEndorsement';
import Interview from './Interview';
import InterviewEndorsementStep from './InterviewEndorsementStep';
import StepSummary from './StepSummary';
import TechInterviewEndorsement from './TechInterviewEndorsement';
import InProgressStageState from '../state/InProgressStageState';
import SubmittedStageState from '../state/SubmittedStageState';
import TestObjects from '../../../test-toolkit/shared/TestObjects';

describe('Interview', () => {
    describe('changeHRInterviewStep', () => {
        test('should mark step as completed', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new InProgressStageState()],
                [],
                new HRInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        false,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        false,
                        new Order(1),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
                new TechInterviewEndorsement(),
            );

            interview.changeHRStep(interview.hr.steps[0].getIdOrThrow(), true);
            expect(interview.hr.steps[0].isCompleted).toBeTruthy();
            expect(interview.hr.steps[1].isCompleted).toBeFalsy();

            interview.changeHRStep(interview.hr.steps[1].getIdOrThrow(), true);
            expect(interview.hr.steps[0].isCompleted).toBeTruthy();
            expect(interview.hr.steps[1].isCompleted).toBeTruthy();
        });

        test('should mark step as not completed', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new InProgressStageState()],
                [],
                new HRInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(1),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
                new TechInterviewEndorsement(),
            );

            interview.changeHRStep(interview.hr.steps[0].getIdOrThrow(), false);
            expect(interview.hr.steps[0].isCompleted).toBeFalsy();
            expect(interview.hr.steps[1].isCompleted).toBeTruthy();

            interview.changeHRStep(interview.hr.steps[1].getIdOrThrow(), false);
            expect(interview.hr.steps[0].isCompleted).toBeFalsy();
            expect(interview.hr.steps[1].isCompleted).toBeFalsy();
        });

        test('should not change unknown step', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new InProgressStageState()],
                [],
                new HRInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
                new TechInterviewEndorsement(),
            );

            expect(() => interview.changeHRStep(TestObjects.uniqueId(), false)).toThrow(IllegalArgumentError);
        });

        test('should not change step if state is not updatable', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new SubmittedStageState()],
                [],
                new HRInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
                new TechInterviewEndorsement(),
            );

            expect(() => interview.changeHRStep(interview.hr.steps[0].getIdOrThrow(), false)).toThrow(
                IllegalStateError,
            );
        });
    });

    describe('changeTechInterviewStep', () => {
        test('should mark step as completed', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new InProgressStageState()],
                [],
                new HRInterviewEndorsement(),
                new TechInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        false,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        false,
                        new Order(1),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
            );

            interview.changeTechStep(interview.tech.steps[0].getIdOrThrow(), true);
            expect(interview.tech.steps[0].isCompleted).toBeTruthy();
            expect(interview.tech.steps[1].isCompleted).toBeFalsy();

            interview.changeTechStep(interview.tech.steps[1].getIdOrThrow(), true);
            expect(interview.tech.steps[0].isCompleted).toBeTruthy();
            expect(interview.tech.steps[1].isCompleted).toBeTruthy();
        });

        test('should mark step as not completed', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new InProgressStageState()],
                [],
                new HRInterviewEndorsement(),
                new TechInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(1),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
            );

            interview.changeTechStep(interview.tech.steps[0].getIdOrThrow(), false);
            expect(interview.tech.steps[0].isCompleted).toBeFalsy();
            expect(interview.tech.steps[1].isCompleted).toBeTruthy();

            interview.changeTechStep(interview.tech.steps[1].getIdOrThrow(), false);
            expect(interview.tech.steps[0].isCompleted).toBeFalsy();
            expect(interview.tech.steps[1].isCompleted).toBeFalsy();
        });

        test('should not change unknown step', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new InProgressStageState()],
                [],
                new HRInterviewEndorsement(),
                new TechInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
            );

            expect(() => interview.changeTechStep(TestObjects.uniqueId(), false)).toThrow(IllegalArgumentError);
        });

        test('should not change step if state is not updatable', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new SubmittedStageState()],
                [],
                new HRInterviewEndorsement(),
                new TechInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
            );

            expect(() => interview.changeTechStep(interview.tech.steps[0].getIdOrThrow(), false)).toThrow(
                IllegalStateError,
            );
        });
    });

    describe('submit', () => {
        test('should submit', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new SubmittedStageState()],
                [],
                new HRInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
                new TechInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
            );

            interview.submit(TestObjects.uniqueId());

            expect(interview.state).toBeInstanceOf(SubmittedStageState);
        });

        test('should not submit with incomplete HR interview steps', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new SubmittedStageState()],
                [],
                new HRInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        false,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
                new TechInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
            );

            expect(() => interview.submit(TestObjects.uniqueId())).toThrow(IllegalStateError);
        });

        test('should not submit with incomplete tech interview steps', () => {
            const interview = new Interview(
                TestObjects.id(),
                [new SubmittedStageState()],
                [],
                new HRInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        true,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
                new TechInterviewEndorsement([
                    new InterviewEndorsementStep(
                        new StepSummary(faker.word.words()),
                        false,
                        new Order(0),
                        new Url(faker.internet.url()),
                        TestObjects.id(),
                        TestObjects.uniqueId(),
                    ),
                ]),
            );

            expect(() => interview.submit(TestObjects.uniqueId())).toThrow(IllegalStateError);
        });
    });
});
