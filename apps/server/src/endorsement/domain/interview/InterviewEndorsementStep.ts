import DomainEntity from '../../../core/domain/DomainEntity';
import Id from '../../../core/domain/value-objects/Id';
import Order from '../../../core/domain/value-objects/Order';
import Url from '../../../core/domain/value-objects/Url';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';
import StepSummary from './StepSummary';
import InterviewEndorsementStepTemplate from './templates/InterviewEndorsementStepTemplate';

export default class InterviewEndorsementStep extends DomainEntity {
    readonly summary: StepSummary;

    readonly descriptionUrl?: Url;

    readonly isCompleted: boolean;

    readonly order: Order;

    readonly templateId?: Id;

    constructor(
        summary: StepSummary,
        isCompleted: boolean,
        order: Order,
        descriptionUrl?: Url,
        templateId?: Id,
        id?: Id,
    ) {
        super(id);

        ArgumentValidation.assert.defined(summary, 'Step summary is required');
        ArgumentValidation.assert.defined(isCompleted, 'Step completion flag is required');
        ArgumentValidation.assert.defined(order, 'Step order is required');

        this.summary = summary;
        this.isCompleted = isCompleted;
        this.order = order;
        this.descriptionUrl = descriptionUrl;
        this.templateId = templateId;
    }

    static fromTemplate(template: InterviewEndorsementStepTemplate): InterviewEndorsementStep {
        return new InterviewEndorsementStep(
            template.summary,
            false,
            template.order,
            template.descriptionUrl,
            template.id,
        );
    }

    changeCompletion(isCompleted: boolean): InterviewEndorsementStep {
        return new InterviewEndorsementStep(
            this.summary,
            isCompleted,
            this.order,
            this.descriptionUrl,
            this.templateId,
            this.id,
        );
    }
}
