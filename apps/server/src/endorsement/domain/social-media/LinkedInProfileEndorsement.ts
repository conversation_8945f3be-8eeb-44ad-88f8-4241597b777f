import Id from '../../../core/domain/value-objects/Id';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';
import LinkedInUrl from './LinkedInUrl';
import SocialMediaEndorsementStep from './SocialMediaEndorsementStep';

export default class LinkedInProfileEndorsement {
    readonly url?: LinkedInUrl;

    private readonly endorsementSteps: SocialMediaEndorsementStep[];

    // @ts-expect-error TS(2322) FIXME: Type 'undefined' is not assignable to type 'Linked... Remove this comment to see the full error message
    constructor(steps: SocialMediaEndorsementStep[] = [], url: LinkedInUrl = undefined) {
        this.endorsementSteps = steps;
        this.url = url;
    }

    get steps(): SocialMediaEndorsementStep[] {
        return [...this.endorsementSteps];
    }

    changeUrl(url: LinkedInUrl | undefined): LinkedInProfileEndorsement {
        return new LinkedInProfileEndorsement(this.endorsementSteps, url);
    }

    changeStep(stepId: Id, isCompleted: boolean): LinkedInProfileEndorsement {
        const step = this.endorsementSteps.find((s) => s.id?.equals(stepId));
        ArgumentValidation.assert.defined(step, 'Unknown LinkedIn endorsement step');

        return new LinkedInProfileEndorsement(
            this.endorsementSteps.map((s) => (s.id?.equals(stepId) ? s.changeCompletion(isCompleted) : s)),
            this.url,
        );
    }
}
