import IllegalArgumentError from '../../../core/errors/IllegalArgumentError';
import LinkedInUrl from './LinkedInUrl';

describe('LinkedInUrl', () => {
    test('should accept http URL', () => {
        expect(new LinkedInUrl('http://linkedin.com/u/test').value).toEqual('https://linkedin.com/u/test');
    });

    test('should accept https URL', () => {
        expect(new LinkedInUrl('https://LINKEDIN.com/u/test').value).toEqual('https://linkedin.com/u/test');
    });

    test('should accept www URL', () => {
        expect(new LinkedInUrl('Www.LinkedIn.com/u/test').value).toEqual('https://linkedin.com/u/test');
    });

    test('should accept URL without protocol', () => {
        expect(new LinkedInUrl('linkedin.com/u/test').value).toEqual('https://linkedin.com/u/test');
    });

    test('should accept URL with spaces', () => {
        expect(new LinkedInUrl('   linkedin.com/u/test   ').value).toEqual('https://linkedin.com/u/test');
    });

    test('should not accept non-linkedin URL', () => {
        expect(() => new LinkedInUrl('github.com/test')).toThrow(IllegalArgumentError);
    });
});
