import { Column, Entity, ManyToOne, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../../core/infrastructure/db/TypeormPersistenceEntity';
import InterviewPersistenceEntity from './InterviewPersistenceEntity';
import TechStepTemplatePersistenceEntity from './TechStepTemplatePersistenceEntity';

@Entity('endorsement_interview_tech_step')
export default class TechStepPersistenceEntity extends TypeormPersistenceEntity {
    @ManyToOne(() => InterviewPersistenceEntity, { onDelete: 'CASCADE' })
    interview: InterviewPersistenceEntity;

    @ManyToOne(() => TechStepTemplatePersistenceEntity, {
        onDelete: 'SET NULL',
    })
    private template?: TechStepTemplatePersistenceEntity;

    @Column({ nullable: true })
    @RelationId((self: TechStepPersistenceEntity) => self.template)
    templateId?: number;

    @Column()
    summary: string;

    @Column({ nullable: true })
    descriptionUrl?: string;

    @Column({ default: false })
    isCompleted: boolean;

    @Column()
    order: number;
}
