import { Column, Entity, ManyToOne, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../../core/infrastructure/db/TypeormPersistenceEntity';
import GithubStepTemplatePersistenceEntity from './GithubStepTemplatePersistenceEntity';
import SocialMediaPersistenceEntity from './SocialMediaPersistenceEntity';

@Entity('endorsement_social_media_github_step')
export default class GithubStepPersistenceEntity extends TypeormPersistenceEntity {
    @ManyToOne(() => SocialMediaPersistenceEntity, { onDelete: 'CASCADE' })
    socialMedia: SocialMediaPersistenceEntity;

    @ManyToOne(() => GithubStepTemplatePersistenceEntity, {
        onDelete: 'SET NULL',
    })
    private template?: GithubStepTemplatePersistenceEntity;

    @Column({ nullable: true })
    @RelationId((self: GithubStepPersistenceEntity) => self.template)
    templateId?: number;

    @Column()
    summary: string;

    @Column()
    descriptionUrl: string;

    @Column({ default: false })
    isCompleted: boolean;

    @Column()
    order: number;
}
