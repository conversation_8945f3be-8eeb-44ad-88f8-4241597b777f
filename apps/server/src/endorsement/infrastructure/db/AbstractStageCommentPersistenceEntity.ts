import { Column, ManyToOne } from 'typeorm';
import TypeormPersistenceEntity from '../../../core/infrastructure/db/TypeormPersistenceEntity';
import User from '../../../users/shared/infrastructure/db/User';

export default class AbstractStageCommentPersistenceEntity extends TypeormPersistenceEntity {
    @Column()
    content: string;

    @Column({ type: 'timestamptz' })
    postedAt: Date;

    creatorId?: number;

    @ManyToOne(() => User, { onDelete: 'SET NULL' })
    protected creator?: User;
}
