import DomainEntity from '../../../../core/domain/DomainEntity';
import { CostTrackingProfileDomainEventType } from '../../cost-tracking-profile/domain/CostTrackingProfileDomainEvents';
import { NonFunctionProperties, RequireOnly } from '../../../../utils/UtilityTypes';
import Id from '../../../../core/domain/value-objects/Id';

export type CostTrackingAuditRecordParams = NonFunctionProperties<CostTrackingAudit>;

export class CostTrackingAudit extends DomainEntity {
    readonly eventType: string | CostTrackingProfileDomainEventType;
    readonly actorId?: Id;
    readonly userId?: Id;
    readonly payload?: object | null;
    readonly timestamp: Date = new Date();

    constructor(params: RequireOnly<CostTrackingAuditRecordParams, 'eventType'>) {
        super(params.id);

        this.eventType = params.eventType;
        this.actorId = params.actorId;
        this.userId = params.userId;
        this.payload = params.payload;
        this.timestamp = params.timestamp || new Date();
    }
}
