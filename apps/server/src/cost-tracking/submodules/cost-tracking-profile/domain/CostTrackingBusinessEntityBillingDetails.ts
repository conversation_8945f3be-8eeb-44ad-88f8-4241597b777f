import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { CountryCodeIso2 } from '../../../../physical-address/domain/types/CountryCodeIso2';
import { CostTrackingProfileBillingDetailsType } from './CostTrackingProfileBillingDetailsType';

export type CostTrackingBusinessEntityBillingDetailsParams =
    NonFunctionProperties<CostTrackingBusinessEntityBillingDetails>;

export class CostTrackingBusinessEntityBillingDetails {
    readonly type: CostTrackingProfileBillingDetailsType.BUSINESS_ENTITY;
    readonly name: string;
    readonly companyCode: string;
    readonly country: CountryCodeIso2;
    readonly address: string;
    readonly bankName: string;
    readonly iban: string;
    readonly swift: string;
    readonly vatCode?: string;

    constructor(params: Omit<CostTrackingBusinessEntityBillingDetailsParams, 'type'>) {
        this.type = CostTrackingProfileBillingDetailsType.BUSINESS_ENTITY;
        this.name = params.name;
        this.companyCode = params.companyCode;
        this.country = params.country;
        this.address = params.address;
        this.bankName = params.bankName;
        this.iban = params.iban;
        this.swift = params.swift;
        this.vatCode = params.vatCode;
    }
}
