import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { CostTrackingProfileBillingDetailsType } from './CostTrackingProfileBillingDetailsType';
import { CountryCodeIso2 } from '../../../../physical-address/domain/types/CountryCodeIso2';

export type CostTrackingIndividualBillingDetailsParams = NonFunctionProperties<CostTrackingIndividualBillingDetails>;

export class CostTrackingIndividualBillingDetails {
    readonly type: CostTrackingProfileBillingDetailsType.INDIVIDUAL;
    readonly name: string;
    readonly surname: string;
    readonly personalCode: string;
    readonly country: CountryCodeIso2;
    readonly address: string;
    readonly individualActivityCode: string;
    readonly bankName: string;
    readonly iban: string;
    readonly swift: string;
    readonly vatCode?: string;

    constructor(params: Omit<CostTrackingIndividualBillingDetailsParams, 'type'>) {
        this.type = CostTrackingProfileBillingDetailsType.INDIVIDUAL;
        this.name = params.name;
        this.surname = params.surname;
        this.personalCode = params.personalCode;
        this.country = params.country;
        this.address = params.address;
        this.individualActivityCode = params.individualActivityCode;
        this.bankName = params.bankName;
        this.iban = params.iban;
        this.swift = params.swift;
        this.vatCode = params.vatCode;
    }
}
