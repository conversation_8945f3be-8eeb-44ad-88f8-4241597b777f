import { CostTrackingProfileService } from './CostTrackingProfileService';
import Container from 'typedi';
import { CostTrackingProfileModule } from '../infrastructure/di/CostTrackingProfileModule';
import { IntegrationTestUser } from '../../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { CostTrackingProfile } from '../domain/CostTrackingProfile';
import { CurrencyCode } from '../../../../core/domain/types/CurrencyCode';
import { Money } from '../../../../core/domain/value-objects/Money';
import RequestContextStorage from '../../../../core/infrastructure/context/RequestContextStorage';
import { RequestContextStorageToken } from '../../../../core/infrastructure/di/tokens';
import { CostTrackingAuditService } from '../../cost-tracking-audit/services/CostTrackingAuditService';
import { CostTrackingAuditModule } from '../../cost-tracking-audit/infrastructure/di/CostTrackingAuditModule';
import Staff from '../../../../users/staff/infrastructure/db/Staff';
import RequestContext from '../../../../core/infrastructure/context/RequestContext';
import { CostTrackingProfileDomainEventType } from '../domain/CostTrackingProfileDomainEvents';
import { CostTrackingBillingDetailsFactory } from '../domain/CostTrackingBillingDetails';
import { CostTrackingProfileBillingDetailsType } from '../domain/CostTrackingProfileBillingDetailsType';
import { CountryCodeIso2 } from '../../../../physical-address/domain/types/CountryCodeIso2';
import { CostTrackingHourlyRateType } from '../domain/CostTrackingHourlyRateType';

describe(CostTrackingProfileService.name, () => {
    let costTrackingProfileService: CostTrackingProfileService;
    let costTrackingAuditService: CostTrackingAuditService;
    let requestContextStorage: RequestContextStorage;
    let actor: IntegrationTestUser<Staff>;
    const billingDetailsA = CostTrackingBillingDetailsFactory.create({
        type: CostTrackingProfileBillingDetailsType.INDIVIDUAL,
        address: '',
        bankName: '',
        country: CountryCodeIso2.AD,
        iban: '',
        individualActivityCode: '',
        name: '',
        personalCode: '',
        surname: '',
        swift: '',
    });
    const billingDetailsB = CostTrackingBillingDetailsFactory.create({
        type: CostTrackingProfileBillingDetailsType.INDIVIDUAL,
        address: '',
        bankName: '',
        country: CountryCodeIso2.AE,
        iban: '',
        individualActivityCode: '',
        name: '',
        personalCode: '',
        surname: '',
        swift: '',
    });

    function withActorAction(fn: () => Promise<void>): Promise<void> {
        return new Promise<void>((resolve, reject) => {
            requestContextStorage.runWith(async () => {
                try {
                    await fn();
                } catch (e) {
                    return reject(e);
                }

                resolve();
            }, new RequestContext().setUserProperties(actor.params));
        });
    }

    beforeAll(async () => {
        costTrackingProfileService = Container.get(CostTrackingProfileModule.COST_TRACKING_PROFILE_SERVICE_TOKEN);
        costTrackingAuditService = Container.get(CostTrackingAuditModule.COST_TRACKING_AUDIT_SERVICE_TOKEN);
        requestContextStorage = Container.get(RequestContextStorageToken);
        actor = await IntegrationTestUser.createFakeAdmin();
    });

    it('should save newly create profile and add records to audit log about all fields', async () => {
        await withActorAction(async () => {
            const stl = await IntegrationTestUser.createFakeMentor();

            const profile = await costTrackingProfileService.save(
                CostTrackingProfile.create({
                    defaultHourlyRate: new Money(100, CurrencyCode.EUR),
                    userId: stl.id!,
                    isCostTrackingEnabled: true,
                    billingDetails: billingDetailsA,
                }),
            );

            expect(profile.id).toBeDefined();

            const auditRecords = await costTrackingAuditService.getForUser(stl.id!);

            expect(auditRecords).toHaveLength(4);

            const profileCreateRecord = auditRecords.find(
                (record) => record.eventType === CostTrackingProfileDomainEventType.PROFILE_CREATED,
            );

            expect(profileCreateRecord).toMatchObject({
                actorId: actor.id,
                userId: stl.id,
                payload: null,
            });

            const hourlyRateSetRecord = auditRecords.find(
                (record) => record.eventType === CostTrackingProfileDomainEventType.HOURLY_RATE_SET,
            );

            expect(hourlyRateSetRecord).toMatchObject({
                actorId: actor.id,
                userId: stl.id,
                payload: {
                    activityTypes: [],
                    hourlyRateTypes: [CostTrackingHourlyRateType.DEFAULT],
                },
            });

            const costTrackingEnabledRecord = auditRecords.find(
                (record) => record.eventType === CostTrackingProfileDomainEventType.COST_TRACKING_ENABLED,
            );

            expect(costTrackingEnabledRecord).toMatchObject({
                actorId: actor.id,
                userId: stl.id,
                payload: null,
            });

            const billingDetailsSetRecord = auditRecords.find(
                (record) => record.eventType === CostTrackingProfileDomainEventType.BILLING_DETAILS_SET,
            );

            expect(billingDetailsSetRecord).toMatchObject({
                actorId: actor.id,
                userId: stl.id,
                payload: null,
            });
        });
    });

    it('should save newly create profile and add records to audit log about all except billing info', async () => {
        await withActorAction(async () => {
            const stl = await IntegrationTestUser.createFakeMentor();

            const profile = await costTrackingProfileService.save(
                CostTrackingProfile.create({
                    defaultHourlyRate: new Money(100, CurrencyCode.EUR),
                    userId: stl.id!,
                    isCostTrackingEnabled: true,
                }),
            );

            expect(profile.id).toBeDefined();

            const auditRecords = await costTrackingAuditService.getForUser(stl.id!);

            expect(auditRecords).toHaveLength(3);

            const profileCreateRecord = auditRecords.find(
                (record) => record.eventType === CostTrackingProfileDomainEventType.PROFILE_CREATED,
            );

            expect(profileCreateRecord).toMatchObject({
                actorId: actor.id,
                userId: stl.id,
                payload: null,
            });

            const hourlyRateSetRecord = auditRecords.find(
                (record) => record.eventType === CostTrackingProfileDomainEventType.HOURLY_RATE_SET,
            );

            expect(hourlyRateSetRecord).toMatchObject({
                actorId: actor.id,
                userId: stl.id,
                payload: {
                    activityTypes: [],
                    hourlyRateTypes: [CostTrackingHourlyRateType.DEFAULT],
                },
            });

            const costTrackingEnabledRecord = auditRecords.find(
                (record) => record.eventType === CostTrackingProfileDomainEventType.COST_TRACKING_ENABLED,
            );

            expect(costTrackingEnabledRecord).toMatchObject({
                actorId: actor.id,
                userId: stl.id,
                payload: null,
            });
        });
    });

    it('should update profile and add records to audit log about billing info change', async () => {
        await withActorAction(async () => {
            const stl = await IntegrationTestUser.createFakeMentor();

            let profile = await costTrackingProfileService.save(
                CostTrackingProfile.create({
                    defaultHourlyRate: new Money(100, CurrencyCode.EUR),
                    userId: stl.id!,
                    isCostTrackingEnabled: true,
                    billingDetails: billingDetailsA,
                }),
            );

            expect(profile.id).toBeDefined();

            profile = profile.updateBillingDetails(billingDetailsB);

            await costTrackingProfileService.save(profile);

            const auditRecords = await costTrackingAuditService.getForUser(stl.id!);

            const billingDetailsUpdatedRecord = auditRecords.find(
                (record) => record.eventType === CostTrackingProfileDomainEventType.BILLING_DETAILS_UPDATED,
            );

            expect(billingDetailsUpdatedRecord).toMatchObject({
                actorId: actor.id,
                userId: stl.id,
                payload: null,
            });
        });
    });

    it('should update profile and add records to audit log about disabling cost tracking', async () => {
        await withActorAction(async () => {
            const stl = await IntegrationTestUser.createFakeMentor();

            let profile = await costTrackingProfileService.save(
                CostTrackingProfile.create({
                    defaultHourlyRate: new Money(100, CurrencyCode.EUR),
                    userId: stl.id!,
                    isCostTrackingEnabled: true,
                    billingDetails: billingDetailsA,
                }),
            );

            expect(profile.id).toBeDefined();

            profile = profile.disableCostTracking();

            await costTrackingProfileService.save(profile);

            const auditRecords = await costTrackingAuditService.getForUser(stl.id!);

            const costTrackingDisabledRecord = auditRecords.find(
                (record) => record.eventType === CostTrackingProfileDomainEventType.COST_TRACKING_DISABLED,
            );

            expect(costTrackingDisabledRecord).toMatchObject({
                actorId: actor.id,
                userId: stl.id,
                payload: null,
            });
        });
    });

    it('should not update profile and save audit if not changed', async () => {
        await withActorAction(async () => {
            const stl = await IntegrationTestUser.createFakeMentor();

            let profile = await costTrackingProfileService.save(
                CostTrackingProfile.create({
                    defaultHourlyRate: new Money(100, CurrencyCode.EUR),
                    userId: stl.id!,
                    isCostTrackingEnabled: false,
                    billingDetails: billingDetailsA,
                }),
            );

            expect(profile.id).toBeDefined();

            const after = new Date();

            profile = profile.disableCostTracking();

            await costTrackingProfileService.save(profile);

            const auditRecords = await costTrackingAuditService.getForUser(stl.id!);

            const costTrackingDisabledRecord = auditRecords.find(
                (record) =>
                    record.eventType === CostTrackingProfileDomainEventType.COST_TRACKING_DISABLED &&
                    +record.timestamp > +after,
            );

            expect(costTrackingDisabledRecord).toBeUndefined();
        });
    });

    it('should update profile and add records to audit log about enabling cost tracking', async () => {
        await withActorAction(async () => {
            const stl = await IntegrationTestUser.createFakeMentor();

            let profile = await costTrackingProfileService.save(
                CostTrackingProfile.create({
                    defaultHourlyRate: new Money(100, CurrencyCode.EUR),
                    userId: stl.id!,
                    isCostTrackingEnabled: false,
                    billingDetails: billingDetailsA,
                }),
            );

            expect(profile.id).toBeDefined();

            profile = profile.enableCostTracking();

            await costTrackingProfileService.save(profile);

            const auditRecords = await costTrackingAuditService.getForUser(stl.id!);

            const costTrackingEnabledRecord = auditRecords.find(
                (record) => record.eventType === CostTrackingProfileDomainEventType.COST_TRACKING_ENABLED,
            );

            expect(costTrackingEnabledRecord).toMatchObject({
                actorId: actor.id,
                userId: stl.id,
                payload: null,
            });
        });
    });
});
