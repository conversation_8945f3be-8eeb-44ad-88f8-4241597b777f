import { MeetingAttendanceLog } from './MeetingAttendanceLog';
import { Ref } from '../../test-toolkit/shared/Ref';
import { Container } from 'typedi';
import { TransactionManagerToken } from '../../core/infrastructure/di/tokens';
import MeetingAttendanceLogRepository from '../infrastructure/db/MeetingAttendanceLogRepository';
import { IntegrationTestMeetingAttendanceLogEntry } from '../../test-toolkit/e2e/entities/IntegrationTestMeetingAttendanceLogEntry';
import { MeetingAttendanceAction } from '../domain/MeetingAttendanceLogEntry';
import TestObjects from '../../test-toolkit/shared/TestObjects';
import { IntegrationTestMeeting } from '../../test-toolkit/e2e/entities/IntegrationTestMeeting';
import { IntegrationTestMeetingHost } from '../../test-toolkit/e2e/entities/IntegrationTestMeetingHost';
import { IntegrationTestMeetingAttendee } from '../../test-toolkit/e2e/entities/IntegrationTestMeetingAttendee';
import { IntegrationTestUser } from '../../test-toolkit/e2e/entities/IntegrationTestUser';
import { DateRange } from '../../core/domain/value-objects/DateRange';

describe(MeetingAttendanceLog, () => {
    let service: MeetingAttendanceLog;

    async function createUserAndMeeting(): Promise<{
        meeting: IntegrationTestMeeting;
        user: IntegrationTestUser;
        attendee: IntegrationTestMeetingAttendee;
    }> {
        const meetingHost = await IntegrationTestMeetingHost.create();
        const meeting = await IntegrationTestMeeting.create(meetingHost.getIdOrThrow());
        const user = await IntegrationTestUser.createFakeStudent();
        const attendee = await IntegrationTestMeetingAttendee.create(meeting.getIdOrThrow(), user.getIdOrThrow());

        return { meeting, user, attendee };
    }

    beforeAll(async () => {
        const tm = Container.get(TransactionManagerToken);
        const repository = new MeetingAttendanceLogRepository(tm);

        service = new MeetingAttendanceLog(repository);
    });

    describe(Ref.method<MeetingAttendanceLog>('getUserAttendanceInRange'), () => {
        it('should not return entries before range', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:30:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T13:00:00Z'), new Date('2025-02-05T13:30:00Z')),
            });

            expect(result.length).toBe(0);
        });

        it('should not return entries after range', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:30:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T15:00:00Z'), new Date('2025-02-05T15:30:00Z')),
            });

            expect(result.length).toBe(0);
        });

        it('should return partial range when joined before range', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:30:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T14:15:00Z'), new Date('2025-02-05T15:00:00Z')),
            });

            expect(result.length).toBe(1);
            expect(result[0].from).toEqual(new Date('2025-02-05T14:15:00Z'));
            expect(result[0].to).toEqual(new Date('2025-02-05T14:30:00Z'));
        });

        it('should return partial range when left after range', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:30:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T13:00:00Z'), new Date('2025-02-05T14:15:00Z')),
            });

            expect(result.length).toBe(1);
            expect(result[0].from).toEqual(new Date('2025-02-05T14:00:00Z'));
            expect(result[0].to).toEqual(new Date('2025-02-05T14:15:00Z'));
        });

        it('should return full range when it is contained in requested', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:30:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T13:00:00Z'), new Date('2025-02-05T15:00:00Z')),
            });

            expect(result.length).toBe(1);
            expect(result[0].from).toEqual(new Date('2025-02-05T14:00:00Z'));
            expect(result[0].to).toEqual(new Date('2025-02-05T14:30:00Z'));
        });

        it('should ignore single join when there is grouping', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:05:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:30:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T13:00:00Z'), new Date('2025-02-05T15:00:00Z')),
            });

            expect(result.length).toBe(1);
            expect(result[0].from).toEqual(new Date('2025-02-05T14:05:00Z'));
            expect(result[0].to).toEqual(new Date('2025-02-05T14:30:00Z'));
        });

        it('should ignore single left when there is grouping', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:30:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:35:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T13:00:00Z'), new Date('2025-02-05T15:00:00Z')),
            });

            expect(result.length).toBe(1);
            expect(result[0].from).toEqual(new Date('2025-02-05T14:00:00Z'));
            expect(result[0].to).toEqual(new Date('2025-02-05T14:30:00Z'));
        });

        it('should ignore single join/left entries when there is grouping', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:05:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:30:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:35:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T13:00:00Z'), new Date('2025-02-05T15:00:00Z')),
            });

            expect(result.length).toBe(1);
            expect(result[0].from).toEqual(new Date('2025-02-05T14:05:00Z'));
            expect(result[0].to).toEqual(new Date('2025-02-05T14:30:00Z'));
        });

        it('should return range from single join when no left', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T13:00:00Z'), new Date('2025-02-05T15:00:00Z')),
            });

            expect(result.length).toBe(1);
            expect(result[0].from).toEqual(new Date('2025-02-05T14:00:00Z'));
            expect(result[0].to).toEqual(new Date('2025-02-05T15:00:00Z'));
        });

        it('should return range from latest join when multiple single joins', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:05:00Z'),
                    action: MeetingAttendanceAction.JOINED,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T13:00:00Z'), new Date('2025-02-05T15:00:00Z')),
            });

            expect(result.length).toBe(1);
            expect(result[0].from).toEqual(new Date('2025-02-05T14:05:00Z'));
            expect(result[0].to).toEqual(new Date('2025-02-05T15:00:00Z'));
        });

        it('should return range up to single left when no join', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T13:00:00Z'), new Date('2025-02-05T15:00:00Z')),
            });

            expect(result.length).toBe(1);
            expect(result[0].from).toEqual(new Date('2025-02-05T13:00:00Z'));
            expect(result[0].to).toEqual(new Date('2025-02-05T14:00:00Z'));
        });

        it('should return range from earliest single left of multiple single lefts', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            await Promise.all([
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:00:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
                IntegrationTestMeetingAttendanceLogEntry.create({
                    attendee: attendee.params,
                    meeting: meeting.params,
                    occurrenceId,
                    timestamp: new Date('2025-02-05T14:05:00Z'),
                    action: MeetingAttendanceAction.LEFT,
                }),
            ]);

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(new Date('2025-02-05T13:00:00Z'), new Date('2025-02-05T15:00:00Z')),
            });

            expect(result.length).toBe(1);
            expect(result[0].from).toEqual(new Date('2025-02-05T13:00:00Z'));
            expect(result[0].to).toEqual(new Date('2025-02-05T14:00:00Z'));
        });

        it('[PLAT-6950] unusual scenario when joined and left at one moment', async () => {
            const { meeting, attendee } = await createUserAndMeeting();
            const occurrenceId = TestObjects.zoomId();

            // Important to not use Promise.all since order of records is important here
            await IntegrationTestMeetingAttendanceLogEntry.create({
                attendee: attendee.params,
                meeting: meeting.params,
                occurrenceId,
                timestamp: new Date('2025-03-25 06:13:47+00'),
                action: MeetingAttendanceAction.JOINED,
            });
            await IntegrationTestMeetingAttendanceLogEntry.create({
                attendee: attendee.params,
                meeting: meeting.params,
                occurrenceId,
                timestamp: new Date('2025-03-25 06:16:34+00'),
                action: MeetingAttendanceAction.JOINED,
            });
            await IntegrationTestMeetingAttendanceLogEntry.create({
                attendee: attendee.params,
                meeting: meeting.params,
                occurrenceId,
                timestamp: new Date('2025-03-25 06:16:34+00'),
                action: MeetingAttendanceAction.LEFT,
            });
            await IntegrationTestMeetingAttendanceLogEntry.create({
                attendee: attendee.params,
                meeting: meeting.params,
                occurrenceId,
                timestamp: new Date('2025-03-25 07:03:45+00'),
                action: MeetingAttendanceAction.LEFT,
            });

            const result = await service.getUserAttendanceInRange({
                userId: attendee.params.userId,
                range: DateRange.fromDatesInclusive(
                    new Date('2025-03-25 00:00:00+00'),
                    new Date('2025-03-26 00:00:00+00'),
                ),
            });

            expect(result.length).toBe(2);
            expect(result[0].from).toEqual(new Date('2025-03-25 06:13:47+00'));
            expect(result[0].to).toEqual(new Date('2025-03-25 06:16:34+00'));

            expect(result[1].from).toEqual(new Date('2025-03-25 06:16:34+00'));
            expect(result[1].to).toEqual(new Date('2025-03-25 07:03:45+00'));
        });
    });
});
