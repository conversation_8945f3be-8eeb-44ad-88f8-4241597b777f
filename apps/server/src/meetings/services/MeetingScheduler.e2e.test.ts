import { faker } from '@faker-js/faker';
import moment from 'moment';
import Container from 'typedi';
import RecurrenceRule from '../../calendar/domain/recurrence/RecurrenceRule';
import Id from '../../core/domain/value-objects/Id';
import Url from '../../core/domain/value-objects/Url';
import IllegalStateError from '../../core/errors/IllegalStateError';
import { TransactionManagerToken } from '../../core/infrastructure/di/tokens';
import MeetingHost from '../domain/MeetingHost';
import MeetingHostRepository from '../domain/MeetingHostRepository';
import MeetingRepository from '../domain/MeetingRepository';
import MeetingDuration from '../domain/value-objects/MeetingDuration';
import MeetingPassword from '../domain/value-objects/MeetingPassword';
import MeetingTime from '../domain/value-objects/MeetingTime';
import MeetingTopic from '../domain/value-objects/MeetingTopic';
import ZoomId from '../domain/value-objects/ZoomId';
import MeetingHostTypeormRepository from '../infrastructure/db/MeetingHostTypeormRepository';
import MeetingTypeormRepository from '../infrastructure/db/MeetingTypeormRepository';
import { MeetingSchedulerToken } from '../infrastructure/di/tokens';
import MeetingOptions from './dto/MeetingOptions';
import MeetingScheduler from './MeetingScheduler';
import Meeting from '../domain/Meeting';

describe('MeetingScheduler IT', () => {
    let meetingScheduler: MeetingScheduler;
    let meetingHostRepository: MeetingHostRepository;
    let meetingRepository: MeetingRepository;
    let meetingHost: MeetingHost;

    beforeAll(async () => {
        meetingScheduler = Container.get(MeetingSchedulerToken);

        const transactionManager = Container.get(TransactionManagerToken);
        meetingHostRepository = new MeetingHostTypeormRepository(transactionManager);
        meetingRepository = new MeetingTypeormRepository(transactionManager);
    });

    beforeEach(async () => {
        const meetings = await meetingRepository.getAll();
        await Promise.all(meetings.map((m) => meetingRepository.delete(m.getIdOrThrow())));

        const hosts = await meetingHostRepository.getAll();
        await Promise.all(hosts.map((h) => meetingHostRepository.delete(h.getIdOrThrow())));

        meetingHost = await meetingHostRepository.save(new MeetingHost(new ZoomId(faker.string.sample()), true));
    });

    describe('schedule', () => {
        async function testSchedule(
            host: MeetingHost,
            time: MeetingTime,
            topic: MeetingTopic,
            options = new MeetingOptions(),
        ): Promise<void> {
            const scheduledMeeting = await meetingScheduler.schedule({ time, topic, options });
            expect(scheduledMeeting).toEqual({
                id: expect.any(Id),
                url: expect.any(Url),
            });

            const actualMeeting = (await meetingRepository.get(scheduledMeeting.id)) as Meeting;
            expect(actualMeeting.hostId).toEqual(host.id);
            expect(actualMeeting.time).toEqual(time);
            expect(actualMeeting.topic).toEqual(topic);
            expect(actualMeeting.zoomOptions.password).toBeInstanceOf(MeetingPassword);
            expect(actualMeeting.zoomOptions.recording).toEqual(options.recording);
            expect(actualMeeting.zoomOptions.isWaitingRoom).toEqual(options.isWaitingRoom);
            expect(actualMeeting.zoomOptions.isJoinBeforeHost).toEqual(options.isJoinBeforeHost);
            expect(actualMeeting.zoomOptions.alternativeHosts).toEqual(options.alternativeHosts);
            expect(actualMeeting.zoomOptions.isRegistrationRequired).toBeTruthy();
        }

        async function testScheduleError(
            time: MeetingTime,
            topic: MeetingTopic,
            options: MeetingOptions,
            error: Error,
        ): Promise<void> {
            await expect(meetingScheduler.schedule({ time, topic, options })).rejects.toEqual(error);
        }

        it('should schedule a simple meeting', async () => {
            await testSchedule(
                meetingHost,
                new MeetingTime({
                    start: new Date(),
                    duration: new MeetingDuration(10),
                }),
                new MeetingTopic(faker.string.sample()),
            );
        });

        it('should use another host if there is a clashing meeting', async () => {
            const tomorrow = moment().add(1, 'day').hour(12).toDate();

            await testSchedule(
                meetingHost,
                new MeetingTime({
                    start: tomorrow,
                    duration: new MeetingDuration(10),
                }),
                new MeetingTopic(faker.string.sample()),
            );

            const anotherHost = await meetingHostRepository.save(
                new MeetingHost(new ZoomId(faker.string.sample()), true),
            );
            await testSchedule(
                anotherHost,
                new MeetingTime({
                    start: tomorrow,
                    duration: new MeetingDuration(10),
                }),
                new MeetingTopic(faker.string.sample()),
            );
        });

        it('should schedule a daily meeting', async () => {
            const start = new Date();
            await testSchedule(
                meetingHost,
                new MeetingTime({
                    start,
                    duration: new MeetingDuration(10),
                    recurrence: RecurrenceRule.daily({ start }),
                }),
                new MeetingTopic(faker.string.sample()),
            );
        });

        it('should fail to schedule when there is a clashing daily meeting', async () => {
            const tomorrow = moment().add(1, 'day').hour(12).toDate();
            const start = moment(tomorrow).subtract(2, 'days').toDate();

            await testSchedule(
                meetingHost,
                new MeetingTime({
                    start,
                    duration: new MeetingDuration(60),
                    recurrence: RecurrenceRule.daily({ start }),
                }),
                new MeetingTopic(faker.string.sample()),
            );

            await testScheduleError(
                new MeetingTime({
                    start: tomorrow,
                    duration: new MeetingDuration(30),
                }),
                new MeetingTopic(faker.string.sample()),
                new MeetingOptions(),
                new IllegalStateError('There are no available meeting hosts at this time'),
            );
        });

        it('should fail to schedule when there is a clashing weekly meeting', async () => {
            const tomorrow = moment().add(1, 'day').hour(12).toDate();
            const start = moment(tomorrow).subtract(4, 'weeks').toDate();

            await testSchedule(
                meetingHost,
                new MeetingTime({
                    start,
                    duration: new MeetingDuration(60),
                    recurrence: RecurrenceRule.weekly({ start }),
                }),
                new MeetingTopic(faker.string.sample()),
            );

            await testScheduleError(
                new MeetingTime({
                    start: tomorrow,
                    duration: new MeetingDuration(30),
                }),
                new MeetingTopic(faker.string.sample()),
                new MeetingOptions(),
                new IllegalStateError('There are no available meeting hosts at this time'),
            );
        });

        it('should fail to schedule when there is a clashing monthly meeting', async () => {
            const nextMonth = moment().add(1, 'month').date(1).toDate();
            const start = moment(nextMonth).subtract(2, 'months').toDate();

            await testSchedule(
                meetingHost,
                new MeetingTime({
                    start,
                    duration: new MeetingDuration(60),
                    recurrence: RecurrenceRule.monthly({ start }),
                }),
                new MeetingTopic(faker.string.sample()),
            );

            await testScheduleError(
                new MeetingTime({
                    start: nextMonth,
                    duration: new MeetingDuration(30),
                }),
                new MeetingTopic(faker.string.sample()),
                new MeetingOptions(),
                new IllegalStateError('There are no available meeting hosts at this time'),
            );
        });

        it('should fail to schedule when there is a meeting too soon after', async () => {
            const tomorrow = moment().add(1, 'day').hour(12).toDate();

            await testSchedule(
                meetingHost,
                new MeetingTime({
                    start: moment(tomorrow).add(1, 'hour').toDate(),
                    duration: new MeetingDuration(30),
                }),
                new MeetingTopic(faker.string.sample()),
            );

            await testScheduleError(
                new MeetingTime({
                    start: tomorrow,
                    duration: new MeetingDuration(30),
                }),
                new MeetingTopic(faker.string.sample()),
                new MeetingOptions(),
                new IllegalStateError('There are no available meeting hosts at this time'),
            );
        });

        it('should fail to schedule when there is a daily meeting too soon after', async () => {
            const tomorrow = moment().add(1, 'day').hour(12).toDate();
            const start = moment(tomorrow).subtract(2, 'days').add(1, 'hour').toDate();

            await testSchedule(
                meetingHost,
                new MeetingTime({
                    start,
                    duration: new MeetingDuration(30),
                    recurrence: RecurrenceRule.daily({ start }),
                }),
                new MeetingTopic(faker.string.sample()),
            );

            await testScheduleError(
                new MeetingTime({
                    start: tomorrow,
                    duration: new MeetingDuration(30),
                }),
                new MeetingTopic(faker.string.sample()),
                new MeetingOptions(),
                new IllegalStateError('There are no available meeting hosts at this time'),
            );
        });

        // TODO: check why this is failing on UTC time
        // it('should fail to schedule when there is a weekly meeting too soon after', async () => {
        //     const tomorrow = moment().add(1, 'day').hour(12).toDate();
        //     const start = moment(tomorrow).subtract(4, 'weeks').add(1, 'hour').toDate();
        //
        //     await testSchedule(
        //         meetingHost,
        //         new MeetingTime({
        //             start,
        //             duration: new MeetingDuration(30),
        //             recurrence: RecurrenceRule.weekly({ start }),
        //         }),
        //         new MeetingTopic(faker.string.sample()),
        //     );
        //
        //     await testScheduleError(
        //         new MeetingTime({
        //             start: tomorrow,
        //             duration: new MeetingDuration(30),
        //         }),
        //         new MeetingTopic(faker.string.sample()),
        //         new MeetingOptions(),
        //         new IllegalStateError('There are no available meeting hosts at this time'),
        //     );
        // });

        it('should fail to schedule when there is a monthly meeting too soon after', async () => {
            const nextMonth = moment('2022-01-01').add(1, 'month').date(1).hour(12).toDate();
            const start = moment(nextMonth).subtract(2, 'months').add(1, 'hour').toDate();

            await testSchedule(
                meetingHost,
                new MeetingTime({
                    start,
                    duration: new MeetingDuration(30),
                    recurrence: RecurrenceRule.monthly({ start }),
                }),
                new MeetingTopic(faker.string.sample()),
            );

            await testScheduleError(
                new MeetingTime({
                    start: nextMonth,
                    duration: new MeetingDuration(30),
                }),
                new MeetingTopic(faker.string.sample()),
                new MeetingOptions(),
                new IllegalStateError('There are no available meeting hosts at this time'),
            );
        });
    });
});
