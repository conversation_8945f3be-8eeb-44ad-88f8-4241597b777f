import TypeormPersistenceMapper from '../../../core/infrastructure/db/TypeormPersistenceMapper';
import MeetingAttendanceLogEntry from '../../domain/MeetingAttendanceLogEntry';
import MeetingAttendanceLogEntryPersistenceEntity from './MeetingAttendanceLogEntryPersistenceEntity';

export default class MeetingAttendanceLogEntryMapper
    implements TypeormPersistenceMapper<MeetingAttendanceLogEntry, MeetingAttendanceLogEntryPersistenceEntity>
{
    toDomain(persistence: MeetingAttendanceLogEntryPersistenceEntity): MeetingAttendanceLogEntry {
        return persistence.toMeetingAttendanceLogEntry();
    }

    toPersistence(domain: MeetingAttendanceLogEntry): MeetingAttendanceLogEntryPersistenceEntity {
        return MeetingAttendanceLogEntryPersistenceEntity.fromMeetingAttendanceLogEntry(domain);
    }
}
