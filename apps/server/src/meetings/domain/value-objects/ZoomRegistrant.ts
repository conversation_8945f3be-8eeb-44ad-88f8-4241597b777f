import Url from '../../../core/domain/value-objects/Url';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';
import ZoomId from './ZoomId';

export default class ZoomRegistrant {
    readonly id: ZoomId;

    readonly joinUrl: Url;

    constructor(id: ZoomId, joinUrl: Url) {
        ArgumentValidation.assert.defined(id, 'Zoom registrant ID is required');
        ArgumentValidation.assert.defined(joinUrl, 'Zoom registrant join URL is required');

        this.id = id;
        this.joinUrl = joinUrl;
    }
}
