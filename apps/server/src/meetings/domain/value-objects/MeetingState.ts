import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';

export enum MeetingStateType {
    PENDING = 'pending',
    IN_PROGRESS = 'in_progress',
}

export default class MeetingState {
    readonly value: MeetingStateType;

    readonly startedAt?: Date;

    readonly endedAt?: Date;

    constructor(value: MeetingStateType, startedAt?: Date, endedAt?: Date) {
        ArgumentValidation.assert.defined(value, 'State value is required');

        this.value = value;
        this.startedAt = startedAt;
        this.endedAt = endedAt;
    }

    isInProgress(): boolean {
        return this.value === MeetingStateType.IN_PROGRESS;
    }
}
