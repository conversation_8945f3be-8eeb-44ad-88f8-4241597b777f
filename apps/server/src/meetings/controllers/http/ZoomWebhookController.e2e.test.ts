import { faker } from '@faker-js/faker';
import { createHmac } from 'crypto';
import request, { Response } from 'supertest';
import Container from 'typedi';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import Url from '../../../core/domain/value-objects/Url';
import Uuid from '../../../core/domain/value-objects/Uuid';
import { TransactionManagerToken } from '../../../core/infrastructure/di/tokens';
import Meeting from '../../domain/Meeting';
import MeetingAttendee from '../../domain/MeetingAttendee';
import MeetingAttendeeRepository from '../../domain/MeetingAttendeeRepository';
import MeetingHost from '../../domain/MeetingHost';
import MeetingDuration from '../../domain/value-objects/MeetingDuration';
import MeetingState, { MeetingStateType } from '../../domain/value-objects/MeetingState';
import MeetingTime from '../../domain/value-objects/MeetingTime';
import MeetingTopic from '../../domain/value-objects/MeetingTopic';
import ZoomId from '../../domain/value-objects/ZoomId';
import ZoomMeeting from '../../domain/value-objects/ZoomMeeting';
import ZoomOptions from '../../domain/value-objects/ZoomOptions';
import MeetingAttendanceLogRepository from '../../infrastructure/db/MeetingAttendanceLogRepository';
import MeetingAttendeeTypeormRepository from '../../infrastructure/db/MeetingAttendeeTypeormRepository';
import MeetingHostTypeormRepository from '../../infrastructure/db/MeetingHostTypeormRepository';
import MeetingOccurrenceLogEntryRepository from '../../infrastructure/db/MeetingOccurrenceLogEntryRepository';
import MeetingTypeormRepository from '../../infrastructure/db/MeetingTypeormRepository';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { MockServer } from 'jest-mock-server';

interface Signature {
    timestamp: Date;
    signature: string;
}

describe('Zoom webhook controller IT', () => {
    let configuration: Configuration;
    let zoomApiUrl: Url;
    let zoomMockServer: MockServer;
    let meetingRepository: MeetingTypeormRepository;
    let meetingAttendeeRepository: MeetingAttendeeRepository;
    let meetingOccurrenceLogEntryRepository: MeetingOccurrenceLogEntryRepository;
    let meetingAttendanceLogEntryRepository: MeetingAttendanceLogRepository;
    let host: MeetingHost;

    beforeAll(async () => {
        configuration = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        zoomApiUrl = new Url(configuration.zoom.apiUrl);
        zoomMockServer = new MockServer({
            host: zoomApiUrl.hostname,
            port: zoomApiUrl.port,
        });
        const transactionManager = Container.get(TransactionManagerToken);
        meetingRepository = new MeetingTypeormRepository(transactionManager);
        meetingAttendeeRepository = new MeetingAttendeeTypeormRepository(transactionManager);
        meetingOccurrenceLogEntryRepository = new MeetingOccurrenceLogEntryRepository(transactionManager);
        meetingAttendanceLogEntryRepository = new MeetingAttendanceLogRepository(transactionManager);

        const meetingHostRepository = new MeetingHostTypeormRepository(transactionManager);
        host = await meetingHostRepository.save(new MeetingHost(new ZoomId(faker.string.sample())));

        await zoomMockServer.start();
    });

    beforeEach(async () => {
        const meetings = await meetingRepository.getAll();
        await Promise.all(meetings.map((m) => meetingRepository.delete(m.getIdOrThrow())));

        zoomMockServer.reset();
    });

    afterAll(async () => {
        await zoomMockServer.stop();
    });

    function signZoomPayload(payload: any): Signature {
        const timestamp = new Date();
        const signature = `v0=${createHmac('sha256', configuration.zoom.webhookSecretToken)
            .update(`v0:${timestamp}:${JSON.stringify(payload)}`)
            .digest('hex')}`;

        return { timestamp, signature };
    }

    function ackZoomSignature(plainToken: string): string {
        return createHmac('sha256', configuration.zoom.webhookSecretToken).update(plainToken).digest('hex');
    }

    async function sendZoomWebhook(
        endpoint: string,
        payload: any,
        event: string,
        expectedCode = 204,
    ): Promise<Response> {
        const signature = signZoomPayload(payload);

        return request(configuration.server)
            .post(endpoint)
            .set({ 'x-zm-request-timestamp': signature.timestamp.toISOString() })
            .set({ 'x-zm-signature': signature.signature })
            .send({ payload, event })
            .expect(expectedCode);
    }

    describe('webhook url validation', () => {
        async function testUrlValidation(endpoint: string): Promise<void> {
            const payload = { plainToken: faker.string.sample() };
            const res = await sendZoomWebhook(endpoint, payload, 'endpoint.url_validation', 200);

            expect(res.body).toEqual({
                ...payload,
                encryptedToken: ackZoomSignature(payload.plainToken),
            });
        }

        it('should verify meeting started url', async () => {
            await testUrlValidation('/zoom/webhooks/meetings/started');
        });

        it('should verify meeting ended url', async () => {
            await testUrlValidation('/zoom/webhooks/meetings/ended');
        });

        it('should verify meeting participant joined url', async () => {
            await testUrlValidation('/zoom/webhooks/meetings/participant-joined');
        });

        it('should verify meeting participant left url', async () => {
            await testUrlValidation('/zoom/webhooks/meetings/participant-left');
        });
    });

    describe('POST /started', () => {
        let meeting: Meeting;
        let payload: any;

        beforeEach(async () => {
            meeting = await meetingRepository.save(
                new Meeting(
                    host.getIdOrThrow(),
                    new MeetingTime({
                        start: new Date(),
                        duration: new MeetingDuration(60),
                    }),
                    new MeetingTopic(faker.string.sample()),
                    new MeetingState(MeetingStateType.PENDING),
                    new ZoomOptions(),
                    new ZoomMeeting(new ZoomId(faker.string.sample()), new Url(faker.internet.url())),
                ),
            );
            payload = {
                object: {
                    id: meeting.zoomMeeting?.id.value,
                    uuid: faker.string.uuid(),
                    start_time: new Date(),
                },
            };
        });

        it('should mark meeting as in progress on a meeting started call', async () => {
            const res = await sendZoomWebhook('/zoom/webhooks/meetings/started', payload, 'meeting.started');
            console.log(JSON.stringify(res.body, null, 2));

            const startedMeeting = (await meetingRepository.get(meeting.getIdOrThrow())) as Meeting;
            expect(startedMeeting.state.isInProgress()).toBeTruthy();
        });

        it('should create a meeting occurrence log entry on a meeting started call', async () => {
            await sendZoomWebhook('/zoom/webhooks/meetings/started', payload, 'meeting.started');

            const allOccurrences = await meetingOccurrenceLogEntryRepository.getAll();
            const testOccurrences = allOccurrences.filter((o) => o.occurrenceId.equals(new Uuid(payload.object.uuid)));
            expect(testOccurrences).toHaveLength(1);
            expect(testOccurrences[0].meetingId.equals(meeting.id)).toBeTruthy();
            expect(testOccurrences[0].occurrenceId.equals(new Uuid(payload.object.uuid))).toBeTruthy();
            expect(testOccurrences[0].timestamp.getTime()).toEqual(new Date(payload.object.start_time).getTime());
        });
    });

    describe('POST /ended', () => {
        let meeting: Meeting;
        let payload: any;

        beforeEach(async () => {
            meeting = await meetingRepository.save(
                new Meeting(
                    host.id!,
                    new MeetingTime({
                        start: new Date(),
                        duration: new MeetingDuration(60),
                    }),
                    new MeetingTopic(faker.string.sample()),
                    new MeetingState(MeetingStateType.IN_PROGRESS),
                    new ZoomOptions(),
                    new ZoomMeeting(new ZoomId(faker.string.sample()), new Url(faker.internet.url())),
                ),
            );
            payload = {
                object: {
                    id: meeting.zoomMeeting!.id.value,
                    uuid: faker.string.uuid(),
                    end_time: new Date(),
                },
            };

            zoomMockServer.post('/token').mockImplementationOnce((ctx) => {
                ctx.status = 200;
                ctx.response.body = { access_token: faker.string.uuid(), expires_in: 3600 };
            });
            const occurrenceId = encodeURIComponent(encodeURIComponent(payload.object.uuid));
            zoomMockServer
                .get(zoomApiUrl.addPath(`/past_meetings/${occurrenceId}/participants`).pathname)
                .mockImplementationOnce((ctx) => {
                    ctx.status = 200;
                    ctx.response.body = {
                        participants: [],
                    };
                });
        });

        it('should mark meeting as ended on a meeting ended call', async () => {
            await sendZoomWebhook('/zoom/webhooks/meetings/ended', payload, 'meeting.ended');

            const endedMeeting = (await meetingRepository.get(meeting.getIdOrThrow())) as Meeting;
            expect(endedMeeting.state.isInProgress()).toBeFalsy();
        });

        it('should create a meeting occurrence log entry on a meeting ended call', async () => {
            await sendZoomWebhook('/zoom/webhooks/meetings/ended', payload, 'meeting.ended');

            const allOccurrences = await meetingOccurrenceLogEntryRepository.getAll();
            const testOccurrences = allOccurrences.filter((o) => o.occurrenceId.equals(new Uuid(payload.object.uuid)));
            expect(testOccurrences).toHaveLength(1);
            expect(testOccurrences[0].meetingId.equals(meeting.id)).toBeTruthy();
            expect(testOccurrences[0].occurrenceId.equals(new Uuid(payload.object.uuid))).toBeTruthy();
            expect(testOccurrences[0].timestamp.getTime()).toEqual(new Date(payload.object.end_time).getTime());
        });
    });

    describe('POST /participant-joined', () => {
        let meeting: Meeting;
        let attendee: MeetingAttendee;

        beforeEach(async () => {
            const student = await IntegrationTestUser.createFakeStudent();
            meeting = await meetingRepository.save(
                new Meeting(
                    host.getIdOrThrow(),
                    new MeetingTime({
                        start: new Date(),
                        duration: new MeetingDuration(60),
                    }),
                    new MeetingTopic(faker.string.sample()),
                    new MeetingState(MeetingStateType.IN_PROGRESS),
                    new ZoomOptions(),
                    new ZoomMeeting(new ZoomId(faker.string.sample()), new Url(faker.internet.url())),
                ),
            );
            attendee = await meetingAttendeeRepository.save(
                new MeetingAttendee(
                    new ZoomId(faker.string.sample()),
                    meeting.getIdOrThrow(),
                    student.getIdOrThrow(),
                    new Url(faker.internet.url()),
                ),
            );
        });

        it('should create a meeting attendance log entry on a participant joined call', async () => {
            const payload = {
                object: {
                    id: meeting.zoomMeeting?.id.value,
                    uuid: faker.string.uuid(),
                    participant: {
                        registrant_id: attendee.zoomId.value,
                        join_time: new Date(),
                    },
                },
            };
            await sendZoomWebhook('/zoom/webhooks/meetings/participant-joined', payload, 'meeting.participant_joined');

            const allAttendances = await meetingAttendanceLogEntryRepository.getAll();
            const testAttendances = allAttendances.filter((a) => a.attendeeId.equals(attendee.id));
            expect(testAttendances).toHaveLength(1);
            expect(testAttendances[0].meetingId.equals(meeting.id)).toBeTruthy();
            expect(testAttendances[0].attendeeId.equals(attendee.id)).toBeTruthy();
            expect(testAttendances[0].occurrenceId.equals(new Uuid(payload.object.uuid))).toBeTruthy();
            expect(testAttendances[0].timestamp.getTime()).toEqual(
                new Date(payload.object.participant.join_time).getTime(),
            );
        });
    });

    describe('POST /participant-left', () => {
        let meeting: Meeting;
        let attendee: MeetingAttendee;

        beforeEach(async () => {
            const student = await IntegrationTestUser.createFakeStudent();
            meeting = await meetingRepository.save(
                new Meeting(
                    host.getIdOrThrow(),
                    new MeetingTime({
                        start: new Date(),
                        duration: new MeetingDuration(60),
                    }),
                    new MeetingTopic(faker.string.sample()),
                    new MeetingState(MeetingStateType.IN_PROGRESS),
                    new ZoomOptions(),
                    new ZoomMeeting(new ZoomId(faker.string.sample()), new Url(faker.internet.url())),
                ),
            );
            attendee = await meetingAttendeeRepository.save(
                new MeetingAttendee(
                    new ZoomId(faker.string.sample()),
                    meeting.getIdOrThrow(),
                    student.getIdOrThrow(),
                    new Url(faker.internet.url()),
                ),
            );
        });

        it('should create a meeting attendance log entry on a participant left call', async () => {
            const payload = {
                object: {
                    id: meeting.zoomMeeting?.id.value,
                    uuid: faker.string.uuid(),
                    participant: {
                        registrant_id: attendee.zoomId.value,
                        leave_time: new Date(),
                    },
                },
            };
            await sendZoomWebhook('/zoom/webhooks/meetings/participant-left', payload, 'meeting.participant_left');

            const allAttendances = await meetingAttendanceLogEntryRepository.getAll();
            const testAttendances = allAttendances.filter((a) => a.attendeeId.equals(attendee.id));
            expect(testAttendances).toHaveLength(1);
            expect(testAttendances[0].meetingId.equals(meeting.id)).toBeTruthy();
            expect(testAttendances[0].attendeeId.equals(attendee.id)).toBeTruthy();
            expect(testAttendances[0].occurrenceId.equals(new Uuid(payload.object.uuid))).toBeTruthy();
            expect(testAttendances[0].timestamp.getTime()).toEqual(
                new Date(payload.object.participant.leave_time).getTime(),
            );
        });
    });
});
