import ErrorTracker from '../../../error-tracking/infrastructure/services/ErrorTracker';
import { Configuration } from '../../../config/Configuration';
import Id from '../../../core/domain/value-objects/Id';
import { LoggingService } from '../../../logging/services/LoggingService';
import AbstractSqsConsumer from '../../../messaging/AbstractSqsConsumer';
import ZoomScheduler from '../../services/zoom/ZoomScheduler';
import MeetingDTO from './dto/MeetingDTO';

export default class UpdateForPlatformMeetingSqsConsumer extends AbstractSqsConsumer<MeetingDTO> {
    constructor(
        configuration: Configuration,
        loggingService: LoggingService,
        private readonly zoomScheduler: ZoomScheduler,
        errorTracker: ErrorTracker,
    ) {
        super(
            configuration,
            configuration.queues.zoom.updateForPlatformMeeting,
            errorTracker,
            loggingService.createLogger(UpdateForPlatformMeetingSqsConsumer.name),
        );
    }

    async onMessage({ meetingId }: MeetingDTO): Promise<void> {
        this.logger.debug(`Updating a Zoom meeting for a platform meeting '${meetingId}'`);

        await this.zoomScheduler.updateForMeeting(new Id(meetingId));
    }
}
