import { LocalAuthenticationMethod, SocialAuthenticationMethod } from '../auth/domain/AuthenticationMethod';
import Id from '../core/domain/value-objects/Id';
import AssignedSprintPart from '../learning/submodules/roadmap/domain/AssignedSprintPart';
import { NotificationType } from '../notifications/domain/Notification';
import { BillingType } from '../users/students/settings/domain/BillingType';
import { StudentSource } from '../users/students/settings/domain/StudentSource';
import { UserSuspensionReason } from '../users/shared/infrastructure/db/User';

export enum UserRole {
    STUDENT = 'Student',
    MENTOR = 'Mentor',
}

export interface UserTraits {
    username?: string;
    name?: string;
    surname?: string;
    email?: string;
    role?: UserRole;
    batchName?: string;
    isTrial?: boolean;
    registrationType?: LocalAuthenticationMethod | SocialAuthenticationMethod;
    currentProjectId?: number;
    currentProjectName?: string;
    currentProjectAbbreviation?: string;
    tags?: string[];
    billingType?: BillingType;
    source?: StudentSource;
}

export interface LoginAttemptData {
    type: LocalAuthenticationMethod | SocialAuthenticationMethod;
    isSuccessful: boolean;
}

export interface QuizStartedData {
    sprintPartId: number;
    sprintPartName: string;
    sprintPartAbbreviation: string;
}

export interface QuizCompletedData {
    sprintPartId: number;
    sprintPartName: string;
    sprintPartAbbreviation: string;
    quizDuration: number;
    quizEvaluation: number;
}

export interface ProjectSubmittedData {
    id: number;
    abbreviation: string;
    name: string;
}

export interface ProjectCompletedData {
    id: number;
    name: string;
    abbreviation: string;
    grade: number;
    passed: boolean;
}

export interface ProjectForfeitedData {
    id: number;
    abbreviation: string;
    correctionsReceived: number;
}

export interface CorrectionBookedData {
    sprintPartId: number;
    sprintPartName: string;
    sprintPartAbbreviation: string;
    minutesRemaining: number;
    evaluatorUsername: string;
    time: Date;
}

export interface PeerCorrectionBookedData {
    sprintPartId: number;
    sprintPartName: string;
    sprintPartAbbreviation: string;
    minutesRemaining: number;
    peerUsername: string;
    time: Date;
}

export interface CorrectionReceivedData {
    sprintPartId: number;
    sprintPartName: string;
    sprintPartAbbreviation: string;
    grade: number;
    evaluatorUsername: string;
    time: Date;
    isFlagged: boolean;
    status: string;
}

export interface CorrectionSubmittedData {
    sprintPartId: number;
    sprintPartName: string;
    sprintPartAbbreviation: string;
    grade: number;
    peerUsername: string;
    time: Date;
    isFlagged: boolean;
    status: string;
}

export interface CorrectionCanceledData {
    sprintPartAbbreviation: string;
    peerId: number;
    minutesRemaining: number;
    type: string;
}

export interface CorrectionNoShowData {
    sprintPartAbbreviation: string;
    peerId: number;
    minutesRemaining: number;
    type: string;
}

export interface CorrectionFlaggedAsFailedData {
    sprintPartId: number;
    sprintPartAbbreviation: string;
    peerUsername: string;
    minutesRemaining: number;
    isFlagged: boolean;
}

export interface StudentCorrectionFeedbackSubmittedData {
    sprintPartId: number;
    sprintPartAbbreviation: string;
    evaluatorUsername: string;
}

export interface TimeslotData {
    size: number;
    date: Date;
    daysInAdvance: number;
}

export interface ModuleAssignedData {
    id: number;
    name: string;
}

export interface EndorsementStageSubmittedData {
    stage: string;
}

export interface EndorsementStageCompletedData {
    stage: string;
}

export interface EndorsementStageRejectedData {
    stage: string;
}

export interface ModuleCompletedData {
    moduleId: number;
}

export interface SprintCompletedData {
    sprintId: number;
}

interface Analytics {
    updateUser(userId: number, traits: UserTraits): void;

    registrationStarted(userId: number): void;

    registrationCompleted(
        userId: number,
        registrationType: LocalAuthenticationMethod | SocialAuthenticationMethod,
    ): void;

    onboardingCompleted(userId: number): void;

    loginAttempt(userId: number, data: LoginAttemptData): void;

    notificationAcknowledged(userId: number, type: NotificationType): void;

    quizStarted(userId: number, data: QuizStartedData): void;

    quizCompleted(userId: number, data: QuizCompletedData): void;

    projectSubmitted(userId: number, data: ProjectSubmittedData): void;

    projectCompleted(userId: number, data: ProjectCompletedData): void;

    projectForfeited(userId: number, data: ProjectForfeitedData): void;

    correctionBooked(userId: number, data: CorrectionBookedData): void;

    peerCorrectionBooked(userId: number, data: PeerCorrectionBookedData): void;

    correctionReceived(userId: number, data: CorrectionReceivedData): void;

    correctionSubmitted(userId: number, data: CorrectionSubmittedData): void;

    correctionCanceled(userId: number, data: CorrectionCanceledData): void;

    correctionNoShow(userId: number, data: CorrectionNoShowData): void;

    correctionFlaggedAsFailed(userId: number, data: CorrectionFlaggedAsFailedData): void;

    studentCorrectionFeedbackSubmitted(userId: number, data: StudentCorrectionFeedbackSubmittedData): void;

    timeslotCreated(userId: number, data: TimeslotData): void;

    timeslotUpdated(userId: number, data: TimeslotData): void;

    timeslotDeleted(userId: number, data: TimeslotData): void;

    calendarSync(userId: number, username: string): void;

    passwordChanged(userId: number): void;

    moduleAssigned(userId: number, data: ModuleAssignedData): void;

    endorsementStageSubmitted(userId: number, data: EndorsementStageSubmittedData): void;

    endorsementStageCompleted(userId: number, data: EndorsementStageCompletedData): void;

    endorsementStageRejected(userId: number, data: EndorsementStageRejectedData): void;

    learningSuspended(userId: number, reason: UserSuspensionReason): void;

    moduleCompleted(userId: number, absolutePosition: number, data: ModuleCompletedData): void;

    sprintCompleted(userId: number, absolutePosition: number, data: SprintCompletedData): void;

    sprintPartSubmitted(userId: Id, part: AssignedSprintPart): void;

    flush(): Promise<void>;
}

export default Analytics;
