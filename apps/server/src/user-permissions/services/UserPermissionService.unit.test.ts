import { UserPermissionService } from './UserPermissionService';
import { Permission } from '../domain/Permission';
import { Role } from '../../users/shared/infrastructure/db/User';
import Id from '../../core/domain/value-objects/Id';
import { UserAccountManager } from '../../users/accounts/services/UserAccountManager';
import { deepEqual, instance, mock, reset, verify, when } from 'ts-mockito';
import IllegalArgumentError from '../../core/errors/IllegalArgumentError';
import UserAccount from '../../users/accounts/domain/UserAccount';
import { faker } from '@faker-js/faker';
import Email from '../../core/domain/value-objects/Email';
import { UserState } from '../../users/accounts/domain/UserState';
import { UserAccountFinder } from '../../users/accounts/services/UserAccountFinder';

describe(UserPermissionService.name, () => {
    const userAccountFinder = mock(UserAccountFinder);
    const userAccountManager = mock(UserAccountManager);
    const userPermissionService = new UserPermissionService(instance(userAccountManager), instance(userAccountFinder));
    const mockUserAccount = (permissions: Permission[] = []): UserAccount => {
        return new UserAccount({
            id: new Id(faker.number.int({ min: 1, max: 100 })),
            role: Role.ADMIN,
            email: new Email(faker.internet.email()),
            state: UserState.ACTIVE,
            calendarToken: faker.string.sample(),
            createdAt: faker.date.past(),
            permissions: permissions,
        });
    };

    beforeEach(() => {
        reset(userAccountFinder);
        reset(userAccountManager);
    });

    describe('hasPermission', () => {
        it('should return true if the user has the permission', async () => {
            const permissionToTest = Permission.PERMISSIONS_SET;
            const mockedUserAccount = mockUserAccount([permissionToTest]);
            const mockedUserAccountId = mockedUserAccount.id as Id;

            when(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).thenResolve(mockedUserAccount);

            const result = await userPermissionService.hasPermission(mockedUserAccountId, permissionToTest);

            expect(result).toBe(true);
            verify(userAccountFinder.findByUserId(mockedUserAccountId)).once();
        });

        it('should return false if the user does not have the permission', async () => {
            const permissionToTest = Permission.PERMISSIONS_SET;
            const mockedUserAccount = mockUserAccount();
            const mockedUserAccountId = mockedUserAccount.id as Id;

            when(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).thenResolve(mockedUserAccount);

            const result = await userPermissionService.hasPermission(mockedUserAccountId, permissionToTest);

            expect(result).toBe(false);
            verify(userAccountFinder.findByUserId(mockedUserAccountId)).once();
        });

        it('should throw an error if the user is not found', async () => {
            const mockedUserAccountId = new Id(faker.number.int({ min: 1, max: 100 }));
            when(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).thenResolve(undefined);

            await expect(
                userPermissionService.hasPermission(mockedUserAccountId, Permission.PERMISSIONS_GET),
            ).rejects.toThrow(IllegalArgumentError);
        });
    });

    describe('getPermissions', () => {
        it('should return the permissions of the user', async () => {
            const expectedPermissions = [Permission.PERMISSIONS_GET];
            const mockedUserAccount = mockUserAccount(expectedPermissions);
            const mockedUserAccountId = mockedUserAccount.id as Id;

            when(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).thenResolve(mockedUserAccount);

            const result = await userPermissionService.getPermissions(mockedUserAccountId);

            expect(result).toContainAllValues(expectedPermissions);
            verify(userAccountFinder.findByUserId(mockedUserAccountId)).once();
        });

        it('should throw an error if the user is not found', async () => {
            const mockedUserAccountId = new Id(faker.number.int({ min: 1, max: 100 }));

            when(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).thenResolve(undefined);

            await expect(userPermissionService.getPermissions(mockedUserAccountId)).rejects.toThrow(
                IllegalArgumentError,
            );
        });
    });

    describe('setPermissions', () => {
        it('should set the provided permissions for the user', async () => {
            const mockedUserAccount = mockUserAccount([Permission.PERMISSIONS_GET]);
            const mockedUserAccountId = mockedUserAccount.id as Id;
            const permissionsToSet = [Permission.COST_TRACKING_ADMINISTRATION];

            when(
                userAccountManager.setPermissions(deepEqual(mockedUserAccountId), deepEqual(permissionsToSet)),
            ).thenResolve();

            const result = await userPermissionService.setPermissions(mockedUserAccountId, permissionsToSet);

            expect(result).toContainAllValues(permissionsToSet);
            verify(userAccountManager.setPermissions(mockedUserAccountId, permissionsToSet)).once();
        });
    });

    describe('addPermissions', () => {
        it('should add new permissions to the user', async () => {
            const existingPermissions = [Permission.PERMISSIONS_SET];
            const newPermissions = [Permission.PERMISSIONS_GET];
            const expectedPermissions = [...existingPermissions, ...newPermissions];
            const mockedUserAccount = mockUserAccount(existingPermissions);
            const mockedUserAccountId = mockedUserAccount.id as Id;

            when(
                userAccountManager.setPermissions(deepEqual(mockedUserAccountId), deepEqual(expectedPermissions)),
            ).thenResolve();
            when(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).thenResolve(mockedUserAccount);

            const result = await userPermissionService.addPermissions(mockedUserAccountId, newPermissions);

            expect(result).toContainAllValues(expectedPermissions);
            verify(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).once();
            verify(
                userAccountManager.setPermissions(deepEqual(mockedUserAccountId), deepEqual(expectedPermissions)),
            ).once();
        });

        it('should not add duplicate permissions', async () => {
            const existingPermissions = [Permission.PERMISSIONS_SET];
            const newPermissions = [Permission.PERMISSIONS_GET, Permission.PERMISSIONS_SET];
            const expectedPermissions = [...existingPermissions, Permission.PERMISSIONS_GET];
            const mockedUserAccount = mockUserAccount(existingPermissions);
            const mockedUserAccountId = mockedUserAccount.id as Id;

            when(
                userAccountManager.setPermissions(deepEqual(mockedUserAccountId), deepEqual(expectedPermissions)),
            ).thenResolve();
            when(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).thenResolve(mockedUserAccount);

            const result = await userPermissionService.addPermissions(mockedUserAccountId, newPermissions);

            expect(result).toContainAllValues(expectedPermissions);
            verify(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).once();
            verify(
                userAccountManager.setPermissions(deepEqual(mockedUserAccountId), deepEqual(expectedPermissions)),
            ).once();
        });

        describe('removePermissions', () => {
            it('should remove the specified permissions from the user', async () => {
                const existingPermissions = [Permission.PERMISSIONS_GET, Permission.PERMISSIONS_SET];
                const permissionsToRemove = [Permission.PERMISSIONS_GET];
                const expectedPermissions = [Permission.PERMISSIONS_SET];
                const mockedUserAccount = mockUserAccount(existingPermissions);
                const mockedUserAccountId = mockedUserAccount.id as Id;

                when(
                    userAccountManager.setPermissions(deepEqual(mockedUserAccountId), deepEqual(expectedPermissions)),
                ).thenResolve();
                when(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).thenResolve(mockedUserAccount);

                const result = await userPermissionService.removePermissions(mockedUserAccountId, permissionsToRemove);

                expect(result).toContainAllValues(expectedPermissions);
                verify(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).once();
                verify(
                    userAccountManager.setPermissions(deepEqual(mockedUserAccountId), deepEqual(expectedPermissions)),
                ).once();
            });

            it('should not remove any permissions if the user has no matching permissions', async () => {
                const existingPermissions = [Permission.PERMISSIONS_GET];
                const permissionsToRemove = [Permission.PERMISSIONS_SET];
                const expectedPermissions = [...existingPermissions];
                const mockedUserAccount = mockUserAccount(existingPermissions);
                const mockedUserAccountId = mockedUserAccount.id as Id;

                when(
                    userAccountManager.setPermissions(deepEqual(mockedUserAccountId), deepEqual(expectedPermissions)),
                ).thenResolve();
                when(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).thenResolve(mockedUserAccount);

                const result = await userPermissionService.removePermissions(mockedUserAccountId, permissionsToRemove);

                expect(result).toContainAllValues(expectedPermissions);
                verify(userAccountFinder.findByUserId(deepEqual(mockedUserAccountId))).once();
                verify(
                    userAccountManager.setPermissions(deepEqual(mockedUserAccountId), deepEqual(expectedPermissions)),
                ).once();
            });
        });
    });
});
