import Id from '../../../core/domain/value-objects/Id';
import TypeormRepository from '../../../core/infrastructure/db/TypeormRepository';
import Transaction from '../../../core/infrastructure/Transaction';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import OAuthNonce from '../../../core/domain/oauth/OAuthNonce';
import { GithubOAuthProfile } from '../../domain/profile/GithubProfile';
import GithubProfileRepository from '../../domain/profile/GithubProfileRepository';
import GithubProfileMapper from './GithubProfileMapper';
import GithubProfilePersistenceEntity from './GithubProfilePersistenceEntity';

export default class GithubProfileTypeormRepository
    extends TypeormRepository<GithubOAuthProfile, GithubProfilePersistenceEntity>
    implements GithubProfileRepository
{
    constructor(transactionManager: TransactionManager) {
        super(transactionManager, new GithubProfileMapper(), GithubProfilePersistenceEntity);
    }

    async getByUserId(userId: Id, transaction?: Transaction): Promise<GithubOAuthProfile | undefined> {
        return this.getWhere({ userId: userId.value }, transaction);
    }

    async getByNonce(nonce: OAuthNonce, transaction?: Transaction): Promise<GithubOAuthProfile | undefined> {
        return this.getWhere({ githubNonce: nonce.value }, transaction);
    }
}
