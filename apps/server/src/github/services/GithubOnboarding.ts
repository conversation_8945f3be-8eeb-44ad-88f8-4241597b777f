import { Configuration } from '../../config/Configuration';
import Id from '../../core/domain/value-objects/Id';
import Url from '../../core/domain/value-objects/Url';
import Transaction from '../../core/infrastructure/Transaction';
import TransactionManager from '../../core/infrastructure/TransactionManager';
import { LoggingService } from '../../logging/services/LoggingService';
import Logger from '../../utils/logger/Logger';
import GithubAccessCode from '../domain/GithubAccessCode';
import GithubOrg from '../domain/GithubOrg';
import GithubUserId from '../domain/GithubUserId';
import GithubUsername from '../domain/GithubUsername';
import GithubProfileRepository from '../domain/profile/GithubProfileRepository';
import GithubHttpClient from './internal/GithubHttpClient';
import { GithubOAuthProfile, GithubOAuthProfileData } from '../domain/profile/GithubProfile';
import { OAuthProfile } from '../../core/domain/oauth/OAuthProfile';
import { QueueService } from '../../queue/QueueService';
import { UserLinkedGithubAccountChangedEventMapper } from '../controllers/events/dto/UserLinkedGithubAccountChangedEvent';
import { OAuthState } from '../../core/domain/oauth/OAuthState';
import ArgumentValidation from '../../core/utils/validation/ArgumentValidation';
import { UserOnboardingService } from '../../profile-setup-steps/services/UserOnboardingService';
import { CommonProfileSetupStepName } from '../../profile-setup-steps/domain/CommonProfileSetupStepName';

interface GithubOnboarding {
    /**
     * Starts TC user GitHub authorization and returns a URL where the authorization should be completed.
     */
    beginAuthorization(userId: Id, redirectUrl: Url): Promise<Url>;

    /**
     * Completes user's GitHub authorization based on the given nonce.
     * If the user is found, it will be authorized.
     */
    completeAuthorization(state: OAuthState, code: GithubAccessCode): Promise<GithubOAuthProfile | undefined>;

    removeAuthorization(userId: Id, transaction?: Transaction): Promise<void>;
}

export default GithubOnboarding;

export class GithubHttpOnboarding implements GithubOnboarding {
    private readonly logger: Logger;

    private readonly org: GithubOrg;

    private readonly callbackUrl: Url;

    constructor(
        configuration: Configuration,
        loggingService: LoggingService,
        private readonly transactionManager: TransactionManager,
        private readonly githubProfileRepository: GithubProfileRepository,
        private readonly githubHttpClient: GithubHttpClient,
        private readonly queueService: QueueService,
        private readonly userOnboardingService: UserOnboardingService,
    ) {
        this.logger = loggingService.createLogger(GithubHttpOnboarding.name);
        this.org = new GithubOrg(configuration.github.orgName);
        this.callbackUrl = new Url(configuration.server).addPath(configuration.github.serverRedirect);
    }

    async beginAuthorization(userId: Id, resultUrl: Url): Promise<Url> {
        this.logger.debug(`Starting auth for user ${userId.value}`);

        try {
            let profile =
                (await this.githubProfileRepository.getByUserId(userId)) ||
                (await this.githubProfileRepository.save(OAuthProfile.create(userId)));

            if (!profile.isAuthorizationStarted()) {
                profile = await this.githubProfileRepository.save(profile.beginAuthorization());
            }

            const url = this.githubHttpClient.getAuthUrl(profile, resultUrl, this.callbackUrl);
            this.logger.debug(`Generated URL for user ${userId.value}`);

            return url;
        } catch (e) {
            this.logger.error(`Failed to start authorization for user ${userId.value}`, e);
            throw e;
        }
    }

    async completeAuthorization(state: OAuthState, code: GithubAccessCode): Promise<GithubOAuthProfile | undefined> {
        this.logger.debug(`Completing authorization for nonce '${state.nonce.value}'`);

        const profile = await this.githubProfileRepository.getByNonce(state.nonce);

        if (!profile) {
            this.logger.warn(`Nonce '${state.nonce.value}' not found`);
            return undefined;
        }

        this.logger.debug(`Completing authorization for user ${profile.userId.value}`);

        try {
            const oauthClient = await this.githubHttpClient.getOauthClient(code, state.nonce);
            const githubUser = await oauthClient.getAuthorizedUser();

            const authorizedProfile = profile.completeAuthorization(
                new GithubOAuthProfileData(new GithubUserId(githubUser.id), new GithubUsername(githubUser.username)),
            );

            const githubUsername = authorizedProfile.data?.githubUsername;

            ArgumentValidation.assert.defined(githubUsername);

            const githubUserId = authorizedProfile.data?.githubUserId;

            ArgumentValidation.assert.defined(githubUserId);

            if (profile.isSameData(authorizedProfile)) {
                this.logger.warn(`User ${profile.userId.value} already authorized with same GitHub ID and username`);
                return authorizedProfile;
            }

            await this.transactionManager.execute(async (transaction) => {
                await this.githubProfileRepository.save(authorizedProfile, transaction);
                await this.userOnboardingService.completeStep(
                    profile.userId,
                    CommonProfileSetupStepName.GITHUB_SETUP,
                    transaction,
                );

                const isMember = await this.githubHttpClient.isMember(this.org, githubUsername);

                if (!isMember) {
                    await this.githubHttpClient.inviteMember(this.org, githubUserId);
                    await oauthClient.activateMembership(this.org);
                }

                await this.queueService.command({
                    command: UserLinkedGithubAccountChangedEventMapper.fromProfiles(
                        authorizedProfile.userId,
                        profile,
                        authorizedProfile,
                    ),
                });
            });

            this.logger.debug(`Authorization completed for user ${profile.userId.value}`);

            return authorizedProfile;
        } catch (e) {
            this.logger.error(`Failed to complete authorization for user ${profile.userId.value}`, e);
            throw e;
        }
    }

    async authorize(
        userId: Id,
        githubUsername: GithubUsername,
        githubUserId?: GithubUserId,
        existingTransaction?: Transaction,
    ): Promise<GithubOAuthProfile> {
        this.logger.debug(`GitHub authorization for user '${userId.value}'`);

        return await this.transactionManager.execute(async (transaction) => {
            const profile: GithubOAuthProfile =
                (await this.githubProfileRepository.getByUserId(userId, transaction)) || OAuthProfile.create(userId);

            const actualGithubUserId = githubUserId ?? (await this.githubHttpClient.getUserId(githubUsername));

            const authorizedProfile = profile.completeAuthorization(
                new GithubOAuthProfileData(actualGithubUserId, githubUsername),
            );

            if (profile.isSameData(authorizedProfile)) {
                this.logger.warn(`User ${userId.value} already authorized with same GitHub ID and username`);
                return authorizedProfile;
            }

            await this.githubProfileRepository.save(authorizedProfile, transaction);
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            const isMember = await this.githubHttpClient.isMember(this.org, authorizedProfile.data.githubUsername);
            if (!isMember) {
                await this.githubHttpClient.inviteMember(this.org, actualGithubUserId);
            }

            await this.queueService.command({
                command: UserLinkedGithubAccountChangedEventMapper.fromProfiles(
                    authorizedProfile.userId,
                    profile,
                    authorizedProfile,
                ),
            });

            return authorizedProfile;
        }, existingTransaction);
    }

    async removeAuthorization(userId: Id, existingTransaction?: Transaction): Promise<void> {
        this.logger.debug(`Removing GitHub authorization for user '${userId.value}'`);

        await this.transactionManager.execute(async (transaction) => {
            const profile = await this.githubProfileRepository.getByUserId(userId, transaction);

            if (!profile || !profile.isAuthorized()) {
                return;
            }

            await this.githubProfileRepository.save(profile.removeAuthorization(), transaction);
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            await this.githubHttpClient.convertToOutsideCollaborator(this.org, profile.data.githubUsername);
        }, existingTransaction);
    }
}
