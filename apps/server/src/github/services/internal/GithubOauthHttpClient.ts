import { Octokit } from '@octokit/rest';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';
import SystemError from '../../../core/errors/SystemError';
import GithubOrg from '../../domain/GithubOrg';
import GithubUser from '../../domain/GithubUser';
import Gith<PERSON>UserId from '../../domain/GithubUserId';
import GithubUsername from '../../domain/GithubUsername';

interface GithubOauthHttpClient {
    getAuthorizedUser(): Promise<GithubUser>;

    activateMembership(org: GithubOrg): Promise<void>;
}

export default GithubOauthHttpClient;

export class GithubOauthOctokitClient implements GithubOauthHttpClient {
    private readonly octokit: Octokit;

    constructor(octokit: Octokit) {
        ArgumentValidation.assert.defined(octokit, 'Octokit is required');
        this.octokit = octokit;
    }

    async getAuthorizedUser(): Promise<GithubUser> {
        try {
            const { data } = await this.octokit.users.getAuthenticated();

            return new GithubUser(new GithubUserId(data.id), new GithubUsername(data.login));
        } catch (e) {
            throw new SystemError('Failed to get authorized GitHub user', e);
        }
    }

    async activateMembership(org: GithubOrg): Promise<void> {
        try {
            await this.octokit.orgs.updateMembershipForAuthenticatedUser({
                org: org.value,
                state: 'active',
            });
        } catch (e) {
            throw new SystemError('Failed to activate GitHub membership', e);
        }
    }
}
