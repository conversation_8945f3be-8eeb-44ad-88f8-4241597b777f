import { Octokit } from '@octokit/rest';
import waitForExpect from 'wait-for-expect';
import { Configuration } from '../../../config/Configuration';
import Url from '../../../core/domain/value-objects/Url';
import GithubFileName from '../../domain/GithubFileName';
import GithubFileType from '../../domain/GithubFileType';
import GithubRepo from '../../domain/GithubRepo';
import GithubRepoPermission, { GithubRepoPermissionOption } from '../../domain/GithubRepoPermission';
import GithubUsername from '../../domain/GithubUsername';
import GithubHttpClient, { GithubOctokitClient } from './GithubHttpClient';
import { LoggingService } from '../../../logging/services/LoggingService';
import getAppOctokit from '../../../utils/getAppOctokit';
import { TestConfigurationService } from '../../../test-toolkit/unit/deprecated-mocks/TestConfigurationService';
import GithubFile from '../../domain/GithubFile';

/**
 * Need to override this, because CI defines different values via ENV variables for other tests.
 */
function getTestConfiguration(): Configuration {
    const originalConfig = TestConfigurationService.create().getConfigurationSync();

    return {
        ...originalConfig,
        github: {
            ...originalConfig.github,
            url: 'https://github.com',
            apiUrl: 'https://api.github.com',
        },
    };
}
// TODO https://turingcollege.atlassian.net/browse/PLAT-7307 fix test by rewriting to mocked http call
describe.skip('GithubOctokitClient', () => {
    const configuration = getTestConfiguration();
    const templateRepo = new GithubRepo(configuration.github.orgName, 'test-template');
    const username = new GithubUsername('gytis-test');
    const githubUrl = configuration.github.url;
    const githubApiUrl = configuration.github.apiUrl;
    let octokit: Octokit;
    let githubHttpClient: GithubHttpClient;

    beforeAll(async () => {
        octokit = getAppOctokit(configuration, { baseUrl: githubApiUrl });
        githubHttpClient = new GithubOctokitClient(configuration, LoggingService.create(), octokit);
    });

    describe('Is existing repo', () => {
        it('should find an existing repo', async () => {
            await expect(githubHttpClient.isExistingRepo(templateRepo)).resolves.toBeTruthy();
        });

        it('should not find non-existent repo', async () => {
            await expect(
                githubHttpClient.isExistingRepo(new GithubRepo(templateRepo.owner, 'non-existent-repo')),
            ).resolves.toBeFalsy();
        });
    });

    describe('Clone template repo', () => {
        let targetRepo: GithubRepo | undefined;

        afterEach(async () => {
            if (targetRepo) {
                await octokit.repos.delete({
                    owner: targetRepo.owner.value,
                    repo: targetRepo.name.value,
                });
                targetRepo = undefined;
            }
        }, 10000);

        // TODO https://turingcollege.atlassian.net/browse/PLAT-7307 fix test by rewriting to mocked http call
        it.skip('should clone repo only once', async () => {
            targetRepo = new GithubRepo(templateRepo.owner, `test-${Date.now()}`);

            await expect(githubHttpClient.cloneTemplate(templateRepo, targetRepo)).resolves.toBeUndefined();
            await waitForExpect(() =>
                expect(githubHttpClient.isExistingRepo(targetRepo as GithubRepo)).resolves.toBeDefined(),
            );
            await expect(githubHttpClient.cloneTemplate(templateRepo, targetRepo)).resolves.toBeUndefined();
        }, 10000);
    });

    describe('Add collaborator', () => {
        afterEach(async () => {
            await octokit.repos.removeCollaborator({
                owner: templateRepo.owner.value,
                repo: templateRepo.name.value,
                username: username.value,
            });
        });

        it('should add collaborator', async () => {
            await expect(
                githubHttpClient.addCollaborator(templateRepo, username, GithubRepoPermission.READ),
            ).resolves.toBeUndefined();

            const permissionResult = await octokit.repos.getCollaboratorPermissionLevel({
                owner: templateRepo.owner.value,
                repo: templateRepo.name.value,
                username: username.value,
            });
            expect(permissionResult.data.permission).toEqual(GithubRepoPermissionOption.READ);
        }, 10000);

        it('should update collaborator', async () => {
            await expect(
                githubHttpClient.addCollaborator(templateRepo, username, GithubRepoPermission.READ),
            ).resolves.toBeUndefined();
            await expect(
                githubHttpClient.addCollaborator(templateRepo, username, GithubRepoPermission.WRITE),
            ).resolves.toBeUndefined();

            const permissionResult = await octokit.repos.getCollaboratorPermissionLevel({
                owner: templateRepo.owner.value,
                repo: templateRepo.name.value,
                username: username.value,
            });
            expect(permissionResult.data.permission).toEqual(GithubRepoPermissionOption.WRITE);
        }, 10000);
    });

    describe('Remove collaborator', () => {
        beforeEach(async () => {
            await octokit.repos.addCollaborator({
                owner: templateRepo.owner.value,
                repo: templateRepo.name.value,
                username: username.value,
                permission: 'pull',
            });
        });

        afterEach(async () => {
            await octokit.repos.removeCollaborator({
                owner: templateRepo.owner.value,
                repo: templateRepo.name.value,
                username: username.value,
            });
        });

        it('should remove collaborator', async () => {
            await expect(githubHttpClient.removeCollaborator(templateRepo, username)).resolves.toBeUndefined();

            const permissionResult = await octokit.repos.getCollaboratorPermissionLevel({
                owner: templateRepo.owner.value,
                repo: templateRepo.name.value,
                username: username.value,
            });
            expect(permissionResult.data.permission).toEqual('none');
        }, 10000);
    });

    describe('List files', () => {
        it('should list files', async () => {
            await expect(githubHttpClient.listFiles(templateRepo)).resolves.toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        repo: templateRepo,
                        name: new GithubFileName('README.md'),
                        type: GithubFileType.file(),
                        url: new Url(`${githubUrl}/${templateRepo.toString()}/blob/main/README.md`),
                    }),
                    expect.objectContaining({
                        repo: templateRepo,
                        name: new GithubFileName('project.ipynb'),
                        type: GithubFileType.file(),
                        url: new Url(`${githubUrl}/${templateRepo.toString()}/blob/main/project.ipynb`),
                    }),
                ]),
            );
        });
    });

    describe('Download file', () => {
        it('should download a file', async () => {
            const files = await githubHttpClient.listFiles(templateRepo);
            const readme = files.find(({ name }) => name.equals(new GithubFileName('README.md'))) as GithubFile;

            await expect(githubHttpClient.downloadFile(readme.repo, readme.sha)).resolves.toBeDefined();
        });
    });
});
