import ArgumentValidation from '../../core/utils/validation/ArgumentValidation';
import GithubOrg from './GithubOrg';
import GithubRepoName from './GithubRepoName';
import { GithubRepoUrl } from './GithubRepoUrl';

export default class GithubRepo {
    readonly owner: GithubOrg;

    readonly name: G<PERSON><PERSON>RepoName;

    constructor(owner: GithubOrg | string, name: GithubRepoName | string) {
        ArgumentValidation.assert.defined(owner, 'Github repo owner is required');
        ArgumentValidation.assert.defined(name, 'Github repo name is required');

        this.owner = owner instanceof GithubOrg ? owner : new GithubOrg(owner);
        this.name = name instanceof GithubRepoName ? name : new GithubRepoName(name);
    }

    get url(): GithubRepoUrl {
        return GithubRepoUrl.fromParams({
            owner: this.owner.value,
            repository: this.name.value,
        });
    }

    toString(): string {
        return `${this.owner.value}/${this.name.value}`;
    }

    static fromUrl(repoUrl: GithubRepoUrl): GithubRepo {
        return new GithubRepo(repoUrl.owner, repoUrl.repository);
    }
}
