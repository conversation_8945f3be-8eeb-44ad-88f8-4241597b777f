import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';
import DiscordRoleId from '../../../discord/domain/DiscordRoleId';
import BatchName from '../../domain/BatchName';

export default class BatchUpdateData {
    readonly name: BatchName;

    readonly startDate?: Date;

    readonly endDate?: Date;

    readonly discordRoleId?: DiscordRoleId;

    readonly requiredPartsForDiscord: number;

    constructor(
        name: BatchName,
        startDate?: Date,
        endDate?: Date,
        discordRoleId?: DiscordRoleId,
        requiredPartsForDiscord = 3,
    ) {
        ArgumentValidation.assert.defined(name, 'Batch name is required');
        ArgumentValidation.assert.min(
            requiredPartsForDiscord,
            0,
            'Batch required parts for discord must be greater or equal to 0',
        );

        this.name = name;
        this.startDate = startDate;
        this.endDate = endDate;
        this.discordRoleId = discordRoleId;
        this.requiredPartsForDiscord = requiredPartsForDiscord;
    }
}
