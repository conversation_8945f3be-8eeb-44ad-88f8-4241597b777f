import { Column, <PERSON><PERSON>ty, ManyToOne, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../../core/infrastructure/db/TypeormPersistenceEntity';
import User from '../../../../users/shared/infrastructure/db/User';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import DocumentPersistenceEntity from '../document/DocumentPersistenceEntity';
import { DocumentSigner, DocumentSignerSigningState } from '../../../domain/signing/DocumentSigner';
import Id from '../../../../core/domain/value-objects/Id';

@Entity('document_signer')
export default class DocumentSingerPersistenceEntity extends TypeormPersistenceEntity {
    @Column({ name: 'user_id' })
    @RelationId((self: DocumentSingerPersistenceEntity) => self.user)
    userId: number;

    @Column({ name: 'document_id' })
    @RelationId((self: DocumentSingerPersistenceEntity) => self.document)
    documentId: number;

    @Column({ name: 'signing_token', nullable: true })
    signingToken: string;

    @Column({ name: 'signing_state', default: DocumentSignerSigningState.PENDING })
    signingState: DocumentSignerSigningState;

    @Column({ name: 'signing_completed_at', nullable: true, type: 'timestamptz' })
    signingCompletedAt?: Date;

    @Column({ name: 'decline_reason', nullable: true })
    declineReason: string;

    @Column({ name: 'is_company_signer', nullable: true, default: false })
    isCompanySigner: boolean;

    @ManyToOne(() => DocumentPersistenceEntity, { onDelete: 'CASCADE', orphanedRowAction: 'delete' })
    private document: never;

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    private user: never;

    constructor(params: NonFunctionProperties<DocumentSingerPersistenceEntity>);
    constructor();

    constructor(params?: NonFunctionProperties<DocumentSingerPersistenceEntity>) {
        if (!params) {
            super();
            return;
        }

        super(params.id, params.createdAt, params.updatedAt);
        Object.assign(this, params);
    }

    toDomain(): DocumentSigner {
        return new DocumentSigner({
            // @ts-expect-error TS(2345) FIXME: Argument of type 'number | undefined' is not assig... Remove this comment to see the full error message
            id: new Id(this.id),
            documentId: new Id(this.documentId),
            userId: new Id(this.userId),
            signingState: this.signingState,
            signingToken: this.signingToken,
            signingCompletedAt: this.signingCompletedAt,
            declineReason: this.declineReason,
            isCompanySigner: this.isCompanySigner,
        });
    }

    static toPersistence(domain: DocumentSigner): DocumentSingerPersistenceEntity {
        return new DocumentSingerPersistenceEntity({
            id: domain.id?.value,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            documentId: domain.documentId.value,
            // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
            userId: domain.userId.value,
            signingState: domain.signingState,
            // @ts-expect-error TS(2322) FIXME: Type 'string | undefined' is not assignable to typ... Remove this comment to see the full error message
            signingToken: domain.signingToken,
            signingCompletedAt: domain.signingCompletedAt,
            // @ts-expect-error TS(2322) FIXME: Type 'string | undefined' is not assignable to typ... Remove this comment to see the full error message
            declineReason: domain.declineReason,
            isCompanySigner: domain.isCompanySigner,
        });
    }
}
