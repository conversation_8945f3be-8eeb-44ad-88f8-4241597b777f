import TypeormPersistenceMapper from '../../../../core/infrastructure/db/TypeormPersistenceMapper';
import DocumentPersistenceEntity from './DocumentPersistenceEntity';
import { DigitalDocument } from '../../../domain/DigitalDocument';

export default class DocumentPersistenceMapper
    implements TypeormPersistenceMapper<DigitalDocument, DocumentPersistenceEntity>
{
    toDomain(persistence: DocumentPersistenceEntity): DigitalDocument {
        return persistence.toDomain();
    }

    toPersistence(domain: DigitalDocument): DocumentPersistenceEntity {
        return DocumentPersistenceEntity.fromDomain(domain);
    }
}
