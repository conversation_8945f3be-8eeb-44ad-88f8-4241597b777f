import Container from 'typedi';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import { DocumentState } from '../../domain/DocumentWorkflow';
import { DocumentTemplate } from '../../domain/DocumentTemplate';
import { DocumentGenerator } from './DocumentGenerator';
import { IntegrationTestDocument } from '../../../test-toolkit/e2e/entities/IntegrationTestDocument';
import { MakeComMockServer } from '../../../test-toolkit/e2e/mock-servers/make.com.mock-server';
import { DigitalDocument } from '../../domain/DigitalDocument';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { DocumentSigner } from '../../domain/signing/DocumentSigner';
import Staff from '../../../users/staff/infrastructure/db/Staff';
import { MakeComDocumentGenerator } from './MakeComDocumentGenerator';
import { mock } from 'ts-mockito';
import Logger from '../../../utils/logger/Logger';
import { DocumentService } from '../DocumentService';
import { DocumentSignerProfileService } from '../DocumentSignerProfileService';

describe('MakeComDocumentGenerator', () => {
    let documentGenerator: DocumentGenerator;
    let makeComMockServer: MakeComMockServer;
    let signerUser: IntegrationTestUser<Staff>;

    const generationPayload = {
        uzt_naudininkas: 'Priemonių organizavimo 1-ajam skyriui',
        ataskaita_date: '2023-12-31',
        month_start_date: '2023-12-04',
        month_end_date: '2023-12-31',
        full_name: 'Tomas Moska',
    } as const;

    beforeAll(async () => {
        const config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        documentGenerator = new MakeComDocumentGenerator(
            config,
            Container.get(DocumentService),
            Container.get(DocumentSignerProfileService),
            mock<Logger>(),
        );
        makeComMockServer = new MakeComMockServer(config);

        signerUser = await IntegrationTestUser.createFakeAdmin({
            position: 'CEO',
        });

        await makeComMockServer.start();
    });

    afterAll(() => {
        makeComMockServer.stop();
    });

    describe('initiateDocumentGeneration', () => {
        it('should not start document generation if document is not new or not failed to generate', async () => {
            const document = await IntegrationTestDocument.create(
                new DigitalDocument({
                    state: DocumentState.GENERATED,
                }),
            );

            await expect(
                documentGenerator.initiateDocumentGeneration({
                    document: document.params,
                    bucket: 'test',
                    generationPayload,
                    signers: [
                        new DocumentSigner({
                            documentId: document.getIdOrThrow(),
                            userId: signerUser.getIdOrThrow(),
                            isCompanySigner: true,
                        }),
                    ],
                    storagePrefix: '',
                    template: DocumentTemplate.GOV_LT_UZT_ATASKAITA,
                    readableName: '',
                }),
            ).rejects.toThrow(new Error('Cannot transition from "generated" to "generating"'));
        });

        it('should start generation of document', async () => {
            const document = await IntegrationTestDocument.create(new DigitalDocument());

            const mock = makeComMockServer.mockWebhook();

            await expect(
                documentGenerator.initiateDocumentGeneration({
                    document: document.params,
                    bucket: 'test',
                    generationPayload,
                    signers: [
                        new DocumentSigner({
                            documentId: document.getIdOrThrow(),
                            userId: signerUser.getIdOrThrow(),
                            isCompanySigner: true,
                        }),
                    ],
                    storagePrefix: '',
                    template: DocumentTemplate.GOV_LT_UZT_ATASKAITA,
                    readableName: '',
                }),
            ).resolves.toBeDefined();

            expect(mock).toHaveBeenCalledTimes(1);

            const updatedDocument = await IntegrationTestDocument.getDocumentById(document.getIdOrThrow());
            const requestBody = mock.mock.calls[0][0].request.body;

            expect(requestBody).toMatchObject({
                bucket: 'test',
                documentData: updatedDocument.params.generationPayload,
                documentType: DocumentTemplate.GOV_LT_UZT_ATASKAITA,
                folder: '',
                filename: updatedDocument.params.generationFile?.slice(
                    updatedDocument.params.generationFile.lastIndexOf('/') + 1,
                ),
            });

            expect(updatedDocument.params.state).toBe(DocumentState.GENERATING);
            expect(updatedDocument.params.generationRequestedAt).toBeDefined();
        });
    });

    describe('onDocumentGenerated', () => {
        it('should not react on document upload if document is not in generating state', async () => {
            let document = await IntegrationTestDocument.create(new DigitalDocument());

            document = await IntegrationTestDocument.save(
                document.params
                    .startGeneration({
                        generationRequestedAt: new Date(),
                        generationPayload,
                        signers: [
                            new DocumentSigner({
                                documentId: document.getIdOrThrow(),
                                userId: signerUser.getIdOrThrow(),
                                isCompanySigner: true,
                            }),
                        ],
                        storagePrefix: '',
                        template: DocumentTemplate.GOV_LT_UZT_ATASKAITA,
                        readableName: '',
                    })
                    .completeGeneration(new Date()),
            );

            makeComMockServer.mockWebhook();

            await expect(documentGenerator.onDocumentGenerated(document.params, new Date())).rejects.toThrow(
                new Error('Cannot transition from "generated" to "generated"'),
            );

            const updatedDocument = await IntegrationTestDocument.getDocumentById(document.getIdOrThrow());

            expect(updatedDocument.params).toMatchObject(document.params);
        });

        it('should complete document generation on s3 upload', async () => {
            let document = await IntegrationTestDocument.create(new DigitalDocument());

            document = await IntegrationTestDocument.create(
                document.params.startGeneration({
                    generationRequestedAt: new Date(),
                    generationPayload,
                    signers: [
                        new DocumentSigner({
                            documentId: document.getIdOrThrow(),
                            userId: signerUser.getIdOrThrow(),
                            isCompanySigner: true,
                        }),
                    ],
                    storagePrefix: '',
                    template: DocumentTemplate.GOV_LT_UZT_ATASKAITA,
                    readableName: '',
                }),
            );

            makeComMockServer.mockWebhook();

            const timestamp = new Date();

            await expect(documentGenerator.onDocumentGenerated(document.params, timestamp)).resolves.toBeDefined();

            const updatedDocument = await IntegrationTestDocument.getDocumentById(document.getIdOrThrow());

            expect(updatedDocument.params.state).toBe(DocumentState.GENERATED);
            expect(updatedDocument.params.generationCompletedAt?.toISOString()).toBe(timestamp.toISOString());
            expect(updatedDocument.params.generationPayload).toMatchObject(generationPayload);
        });
    });
});
