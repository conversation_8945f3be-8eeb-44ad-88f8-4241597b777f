import { Configuration } from '../../../config/Configuration';
import { DocumentGenerator, InitiateDocumentGenerationParams } from './DocumentGenerator';
import axios from 'axios';
import * as AxiosLogger from 'axios-logger';
import { DigitalDocument } from '../../domain/DigitalDocument';
import { DocumentService } from '../DocumentService';
import { DocumentTemplate } from '../../domain/DocumentTemplate';
import { DocumentSignerProfileService } from '../DocumentSignerProfileService';
import Logger from '../../../utils/logger/Logger';
import { RateLimiter } from 'limiter';

export class MakeComDocumentGenerator implements DocumentGenerator {
    private readonly axiosInstance = axios.create({});

    private readonly webhookUrl: string;

    private readonly rateLimiter: RateLimiter = new RateLimiter({
        tokensPerInterval: this.configuration.documents.generation.makeCom.rateLimit.limit,
        interval: this.configuration.documents.generation.makeCom.rateLimit.interval,
    });

    constructor(
        private readonly configuration: Configuration,
        private readonly documentService: DocumentService,
        private readonly uztDocumentSignerService: DocumentSignerProfileService,
        private readonly logger: Logger,
    ) {
        if (configuration.documents.generation.makeCom.isLoggingEnabled) {
            this.axiosInstance.interceptors.response.use(AxiosLogger.responseLogger, AxiosLogger.errorLogger);
            this.axiosInstance.interceptors.request.use((request) => {
                const headers = request.headers;
                return {
                    ...AxiosLogger.requestLogger(request),
                    headers,
                };
            }, AxiosLogger.errorLogger);
        }

        // @ts-expect-error TS(2322) FIXME: Type 'string | null' is not assignable to type 'st... Remove this comment to see the full error message
        this.webhookUrl = configuration.documents.generation.makeCom.webhookUrl;
    }

    async initiateDocumentGeneration<P>({
        document,
        signers,
        bucket,
        generationPayload,
        storagePrefix,
        template,
        readableName,
    }: InitiateDocumentGenerationParams<P>): Promise<DigitalDocument<P>> {
        document = document.startGeneration({
            generationRequestedAt: new Date(),
            generationPayload,
            signers,
            storagePrefix,
            template,
            readableName,
        });

        try {
            document = await this.documentService.save(document);

            this.logger.debug(
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                `Initiating document generation. Document ID: #${document.id.value}, template: ${document.template}`,
            );

            let companySignerPayload = {};

            const companySigner = signers.find((signer) => signer.isCompanySigner);

            if (companySigner) {
                const companySignerAccount = await this.uztDocumentSignerService.getCompanySignerProfileOrThrow(
                    // @ts-expect-error TS(2345) FIXME: Argument of type 'Id | undefined' is not assignabl... Remove this comment to see the full error message
                    companySigner.userId,
                );

                companySignerPayload = {
                    // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                    company_signer_full_name: companySignerAccount.name.toString(),
                    company_signer_position: companySignerAccount.position,
                };
            }

            await this.triggerWebhook({
                bucket,
                documentData: {
                    ...generationPayload,
                    ...companySignerPayload,
                },
                documentType: document.template,
                // @ts-expect-error TS(2322) FIXME: Type 'string | undefined' is not assignable to typ... Remove this comment to see the full error message
                folder: document.storagePrefix,
                // @ts-expect-error TS(2322) FIXME: Type 'string | undefined' is not assignable to typ... Remove this comment to see the full error message
                filename: document.generationFile,
            });

            this.logger.debug(
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                `Document generation successfully initiated. Document ID: #${document.id.value}, template: ${document.template}`,
            );

            return document;
        } catch (e) {
            this.logger.error(
                // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                `Document generation failed. Document ID: #${document.id.value}, template: ${document.template}`,
            );
            return this.documentService.save(
                document.failGeneration(new Date(), `${e?.message}${e?.response?.data ? `: ${e.response.data}` : ''}`),
            );
        }
    }

    async onDocumentGenerated(document: DigitalDocument, eventTime: Date): Promise<DigitalDocument> {
        return this.documentService.save(document.completeGeneration(eventTime));
    }

    private async triggerWebhook(params: {
        bucket: string;
        documentData: any;
        documentType: DocumentTemplate;
        folder: string;
        filename: string;
    }): Promise<void> {
        await this.rateLimiter.removeTokens(1);

        return this.axiosInstance.post(this.webhookUrl, params);
    }
}
