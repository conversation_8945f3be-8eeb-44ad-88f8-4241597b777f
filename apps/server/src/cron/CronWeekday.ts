import ArgumentValidation from '../core/utils/validation/ArgumentValidation';
import CronPatternValue from './CronPatternValue';

export default class CronWeekday extends CronPatternValue {
    constructor(value: string | number = '*') {
        super(value);
    }

    protected validateRangeNumber(value: number): number {
        ArgumentValidation.assert.between(value, 0, 6, 'Weekday has to be between 0 and 6');
        return value;
    }
}
