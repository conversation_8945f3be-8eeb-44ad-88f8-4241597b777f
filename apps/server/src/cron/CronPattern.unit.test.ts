import CronPattern from './CronPattern';

describe('CronPattern', () => {
    describe('everySecond', () => {
        it('should return a pattern for every second', () => {
            const pattern = CronPattern.everySecond();

            expect(pattern.value).toBe('*/1 * * * * *');
        });

        it('should return a pattern for every 5 seconds', () => {
            const pattern = CronPattern.everySecond(5);

            expect(pattern.value).toBe('*/5 * * * * *');
        });

        it('should return a pattern for every 10 seconds', () => {
            const pattern = CronPattern.everySecond(10);

            expect(pattern.value).toBe('*/10 * * * * *');
        });
    });

    describe('everyMinute', () => {
        it('should return a pattern for every minute', () => {
            const pattern = CronPattern.everyMinute();

            expect(pattern.value).toBe('0 */1 * * * *');
        });

        it('should return a pattern for every 5 minutes', () => {
            const pattern = CronPattern.everyMinute(5);

            expect(pattern.value).toBe('0 */5 * * * *');
        });

        it('should return a pattern for every 10 minutes', () => {
            const pattern = CronPattern.everyMinute(10);

            expect(pattern.value).toBe('0 */10 * * * *');
        });
    });

    describe('fromString', () => {
        it('should return a pattern with all wildcards', () => {
            const pattern = CronPattern.fromString('* * * * * *');

            expect(pattern.value).toBe('* * * * * *');
        });

        it('should return a pattern with all numbers', () => {
            const pattern = CronPattern.fromString('0 1 2 3 4 5');

            expect(pattern.value).toBe('0 1 2 3 4 5');
        });

        it('should return a pattern with all ranges', () => {
            const pattern = CronPattern.fromString('1-2 3-4 5-6 7-8 9-10 1-2');

            expect(pattern.value).toBe('1-2 3-4 5-6 7-8 9-10 1-2');
        });

        it('should return a pattern with all steps', () => {
            const pattern = CronPattern.fromString('*/1 */2 */3 */4 */5 */6');

            expect(pattern.value).toBe('*/1 */2 */3 */4 */5 */6');
        });

        it('should return a pattern with all mixed', () => {
            const pattern = CronPattern.fromString('1-2 */2 5-6 */4 9-10 */6');

            expect(pattern.value).toBe('1-2 */2 5-6 */4 9-10 */6');
        });

        it('should throw an error with out of range seconds', () => {
            expect(() => CronPattern.fromString('60 * * * * *')).toThrowError('Second has to be between 0 and 59');
        });

        it('should throw an error with out of range minutes', () => {
            expect(() => CronPattern.fromString('* 60 * * * *')).toThrowError('Minute has to be between 0 and 59');
        });

        it('should throw an error with out of range hours', () => {
            expect(() => CronPattern.fromString('* * 24 * * *')).toThrowError('Hour has to be between 0 and 23');
        });

        it('should throw an error with out of range month days', () => {
            expect(() => CronPattern.fromString('* * * 32 * *')).toThrowError('Month day has to be between 1 and 31');
        });

        it('should throw an error with out of range months', () => {
            expect(() => CronPattern.fromString('* * * * 13 *')).toThrowError('Month has to be between 1 and 12');
        });

        it('should throw an error with out of range weekdays', () => {
            expect(() => CronPattern.fromString('* * * * * 7')).toThrowError('Weekday has to be between 0 and 6');
        });
    });
});
