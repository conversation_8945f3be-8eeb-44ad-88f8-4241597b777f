import ArgumentValidation from '../core/utils/validation/ArgumentValidation';
import CronHour from './CronHour';
import CronMinute from './CronMinute';
import CronMonth from './CronMonth';
import CronMonthDay from './CronMonthDay';
import CronSecond from './CronSecond';
import CronWeekday from './CronWeekday';

export default class CronPattern {
    readonly value: string;

    private constructor(
        second: CronSecond = new CronSecond(),
        minute: CronMinute = new CronMinute(),
        hour: CronHour = new CronHour(),
        monthDay: CronMonthDay = new CronMonthDay(),
        month: CronMonth = new CronMonth(),
        weekday: CronWeekday = new CronWeekday(),
    ) {
        this.value = `${second.value} ${minute.value} ${hour.value} ${monthDay.value} ${month.value} ${weekday.value}`;
    }

    static everySecond(interval = 1): CronPattern {
        return new CronPattern(new CronSecond(`*/${interval}`));
    }

    static everyMinute(interval = 1): CronPattern {
        return new CronPattern(new CronSecond('0'), new CronMinute(`*/${interval}`));
    }

    /**
     * Accepts the standard cron pattern string starting with seconds.
     *
     * @param pattern second minute hour dayOfMonth month dayOfWeek
     */
    static fromString(pattern: string): CronPattern {
        const parts = pattern.split(' ');
        ArgumentValidation.assert.true(parts.length === 6, 'Cron string pattern requires 6 parts');

        return new CronPattern(
            new CronSecond(parts[0]),
            new CronMinute(parts[1]),
            new CronHour(parts[2]),
            new CronMonthDay(parts[3]),
            new CronMonth(parts[4]),
            new CronWeekday(parts[5]),
        );
    }
}
