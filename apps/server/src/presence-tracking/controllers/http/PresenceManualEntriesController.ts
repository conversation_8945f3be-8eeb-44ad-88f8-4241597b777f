import {
    Authorized,
    Body,
    CurrentUser,
    Delete,
    Get,
    HttpCode,
    JsonController,
    Param,
    Patch,
    Post,
    QueryParams,
} from 'routing-controllers';
import User, { Role } from '../../../users/shared/infrastructure/db/User';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import responses from '../../../core/controllers/docs/responses';
import { QueryPresenceManualEntriesRequestDto } from './dto/QueryPresenceManualEntriesRequestDto';
import { PresenceManualEntryDto } from './dto/PresenceManualEntryDto';
import { BatchUpdatePresenceManualEntriesDto } from './dto/BatchUpdatePresenceManualEntriesDto';
import { BatchCreatePresenceManualEntriesDto } from './dto/BatchCreatePresenceManualEntriesDto';
import { UpdatePresenceManualEntryDto } from './dto/UpdatePresenceManualEntryDto';
import { BatchDeletePresenceManualEntriesDto } from './dto/BatchDeletePresenceManualEntriesDto';
import { CreatePresenceManualEntryDto } from './dto/CreatePresenceManualEntryDto';
import { QueryPresenceManualEntriesResponseDto } from './dto/QueryPresenceManualEntriesResponseDto';
import { Inject, Service } from 'typedi';
import { PresenceManualEntryService } from '../../services/PresenceManualEntryService';
import Id from '../../../core/domain/value-objects/Id';
import { Pagination } from '../../../core/domain/value-objects/Pagination';
import { DateRange } from '../../../core/domain/value-objects/DateRange';
import NotFoundError from '../../../core/errors/NotFoundError';

@Service()
@JsonController('/presence/manual-entries')
export class PresenceManualEntriesController {
    constructor(
        @Inject()
        private readonly presenceManualEntryService: PresenceManualEntryService,
    ) {}

    @Authorized([Role.ADMIN, Role.STAFF])
    @Get()
    @ResponseSchema(QueryPresenceManualEntriesResponseDto)
    @OpenAPI({
        responses,
        summary: 'Get manual presence entries',
        security: [{ cookieAuth: [] }],
    })
    async getAll(
        @QueryParams() params: QueryPresenceManualEntriesRequestDto,
    ): Promise<QueryPresenceManualEntriesResponseDto> {
        const result = await this.presenceManualEntryService.find({
            ids: params.ids?.map((id) => new Id(id)),
            userIds: params.userIds?.map((id) => new Id(id)),
            title: params.title,
            startAfter: params.startAfter ? new Date(params.startAfter) : undefined,
            endBefore: params.endBefore ? new Date(params.endBefore) : undefined,
            pagination: Pagination.fromParams(params),
        });

        return {
            totalRows: result.totalRows,
            rows: result.rows.map((entry) => PresenceManualEntryDto.fromPresenceManualEntry(entry)),
        };
    }

    @Authorized([Role.ADMIN])
    @Post('/batch-create')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Batch create manual presence entries',
        security: [{ cookieAuth: [] }],
    })
    async batchCreate(@Body() params: BatchCreatePresenceManualEntriesDto, @CurrentUser() user: User): Promise<void> {
        await this.presenceManualEntryService.createBatch({
            userIds: params.userIds.map((id) => new Id(id)),
            creatorId: new Id(user.id),
            title: params.title,
            description: params.description,
            period: DateRange.fromDates(new Date(params.period.start), new Date(params.period.end)),
        });
    }

    @Authorized([Role.ADMIN])
    @Post('/batch-update')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Batch update manual presence entries',
        security: [{ cookieAuth: [] }],
    })
    async batchUpdate(@Body() params: BatchUpdatePresenceManualEntriesDto): Promise<void> {
        await this.presenceManualEntryService.updateBatch({
            ids: params.ids.map((id) => new Id(id)),
            title: params.title,
            description: params.description,
            period: params.period
                ? DateRange.fromDates(new Date(params.period.start), new Date(params.period.end))
                : undefined,
        });
    }

    @Authorized([Role.ADMIN])
    @Post('/batch-delete')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Batch delete manual presence entries',
        security: [{ cookieAuth: [] }],
    })
    async batchDelete(@Body() params: BatchDeletePresenceManualEntriesDto): Promise<void> {
        await this.presenceManualEntryService.deleteBatch(params.ids.map((id) => new Id(id)));
    }

    @Authorized([Role.ADMIN, Role.STAFF])
    @Get('/:id')
    @ResponseSchema(PresenceManualEntryDto)
    @OpenAPI({
        responses,
        summary: 'Get manual presence entry by id',
        security: [{ cookieAuth: [] }],
    })
    async getById(@Param('id') id: number): Promise<PresenceManualEntryDto> {
        const result = await this.presenceManualEntryService.get(new Id(id));
        if (!result) {
            throw new NotFoundError();
        }

        return PresenceManualEntryDto.fromPresenceManualEntry(result);
    }

    @Authorized([Role.ADMIN])
    @Post()
    @ResponseSchema(PresenceManualEntryDto)
    @OpenAPI({
        responses,
        summary: 'Create manual presence entry',
        security: [{ cookieAuth: [] }],
    })
    async create(
        @Body() params: CreatePresenceManualEntryDto,
        @CurrentUser() user: User,
    ): Promise<PresenceManualEntryDto> {
        const result = await this.presenceManualEntryService.create({
            userId: new Id(params.userId),
            creatorId: new Id(user.id),
            title: params.title,
            description: params.description,
            period: DateRange.fromDates(new Date(params.period.start), new Date(params.period.end)),
        });

        return PresenceManualEntryDto.fromPresenceManualEntry(result);
    }

    @Authorized([Role.ADMIN])
    @Patch('/:id')
    @ResponseSchema(PresenceManualEntryDto)
    @OpenAPI({
        responses,
        summary: 'Update manual presence entry',
        security: [{ cookieAuth: [] }],
    })
    async update(
        @Param('id') id: number,
        @Body() params: UpdatePresenceManualEntryDto,
    ): Promise<PresenceManualEntryDto> {
        const result = await this.presenceManualEntryService.update({
            id: new Id(id),
            title: params.title,
            description: params.description,
            period: params.period
                ? DateRange.fromDates(new Date(params.period.start), new Date(params.period.end))
                : undefined,
        });
        if (!result) {
            throw new NotFoundError();
        }

        return PresenceManualEntryDto.fromPresenceManualEntry(result);
    }

    @Authorized([Role.ADMIN])
    @Delete('/:id')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Delete manual presence entry',
        security: [{ cookieAuth: [] }],
    })
    async delete(@Param('id') id: number): Promise<void> {
        await this.presenceManualEntryService.delete(new Id(id));
    }
}
