import { IsId } from '../../../../core/controllers/validators/IsId';
import { IsDefined, IsString, MaxLength, <PERSON>Length, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { PeriodDto } from './PeriodDto';

export class BatchCreatePresenceManualEntriesDto {
    @IsDefined()
    @IsId({ each: true })
    userIds: number[];

    @IsDefined()
    @IsString()
    @MinLength(1)
    @MaxLength(1000)
    title: string;

    @IsDefined()
    @IsString()
    @MinLength(1)
    @MaxLength(1000)
    description: string;

    @IsDefined()
    @Type(() => PeriodDto)
    @ValidateNested()
    period: PeriodDto;
}
