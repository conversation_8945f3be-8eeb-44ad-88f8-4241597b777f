import { PaginationResponseDto } from '../../../../core/controllers/dto/PaginationResponseDto';
import { PresenceManualEntryDto } from './PresenceManualEntryDto';
import { Type } from 'class-transformer';
import { FixNestedArrayJsonSchemaReference } from '../../../../core/controllers/dto/decorators/FixNestedJsonSchemaReference';
import { ValidateNested } from 'class-validator';

export class QueryPresenceManualEntriesResponseDto extends PaginationResponseDto<PresenceManualEntryDto> {
    @Type(() => PresenceManualEntryDto)
    @FixNestedArrayJsonSchemaReference(PresenceManualEntryDto)
    @ValidateNested({ each: true })
    rows: PresenceManualEntryDto[];
}
