import { IsDateString, IsDefined } from 'class-validator';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { DateRange } from '../../../../core/domain/value-objects/DateRange';

export class PeriodDto {
    @IsDefined()
    @IsDateString()
    start: string;

    @IsDefined()
    @IsDateString()
    end: string;

    constructor(params: NonFunctionProperties<PeriodDto>) {
        Object.assign(this, params);
    }

    static fromDateRange(range: DateRange): PeriodDto {
        return new PeriodDto({
            start: range.from.toISOString(),
            end: range.to.toISOString(),
        });
    }
}
