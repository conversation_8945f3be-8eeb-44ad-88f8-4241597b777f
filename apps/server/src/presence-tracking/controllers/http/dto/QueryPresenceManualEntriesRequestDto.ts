import { IsId } from '../../../../core/controllers/validators/IsId';
import { IsDateString, IsOptional, IsString } from 'class-validator';
import { PaginationRequestDto } from '../../../../core/controllers/dto/PaginationRequestDto';

export class QueryPresenceManualEntriesRequestDto extends PaginationRequestDto {
    @IsOptional()
    @IsId({ each: true })
    ids?: number[];

    @IsOptional()
    @IsId({ each: true })
    userIds?: number[];

    @IsOptional()
    @IsString()
    title?: string;

    @IsOptional()
    @IsDateString()
    startAfter?: string;

    @IsOptional()
    @IsDateString()
    endBefore?: string;
}
