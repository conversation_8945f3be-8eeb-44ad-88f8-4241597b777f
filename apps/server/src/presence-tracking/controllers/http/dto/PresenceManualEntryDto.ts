import { IsDefined, IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ValidateNested } from 'class-validator';
import { IsId } from '../../../../core/controllers/validators/IsId';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { PresenceManualEntry } from '../../../domain/PresenceManualEntry';
import { Type } from 'class-transformer';
import { PeriodDto } from './PeriodDto';

export class PresenceManualEntryDto {
    @IsDefined()
    @IsId()
    id: number;

    @IsDefined()
    @IsId()
    userId: number;

    @IsDefined()
    @IsString()
    @MinLength(1)
    @MaxLength(1000)
    title: string;

    @IsDefined()
    @IsString()
    @MinLength(1)
    @MaxLength(1000)
    description: string;

    @IsDefined()
    @Type(() => PeriodDto)
    @ValidateNested()
    period: PeriodDto;

    constructor(params: NonFunctionProperties<PresenceManualEntryDto>) {
        Object.assign(this, params);
    }

    static fromPresenceManualEntry(entry: PresenceManualEntry): PresenceManualEntryDto {
        return new PresenceManualEntryDto({
            id: entry.getIdOrThrow().value,
            userId: entry.userId.value,
            title: entry.title,
            description: entry.description,
            period: PeriodDto.fromDateRange(entry.period),
        });
    }
}
