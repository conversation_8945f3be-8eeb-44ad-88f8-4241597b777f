import TypeormRepository, { Where } from '../../../core/infrastructure/db/TypeormRepository';
import { PresenceManualEntry } from '../../domain/PresenceManualEntry';
import { PresenceManualEntryPersistenceEntity } from './PresenceManualEntryPersistenceEntity';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import { createMapperFromEntity } from '../../../utils/createMapperFromEntity';
import { Pagination, PaginationResult } from '../../../core/domain/value-objects/Pagination';
import Id from '../../../core/domain/value-objects/Id';
import Transaction from '../../../core/infrastructure/Transaction';
import { Ordering } from '../../../core/domain/value-objects/Ordering';
import { ILike, In, Or } from 'typeorm';
import { TypeormDateRangeHelper } from '../../../core/infrastructure/db/TypeormDateRangeHelper';
import { FindOperator } from 'typeorm/find-options/FindOperator';
import { DateRange } from '../../../core/domain/value-objects/DateRange';

export interface FindProperties {
    ids?: Id[];
    userIds?: Id[];
    title?: string;
    startAfter?: Date;
    endBefore?: Date;
    pagination: Pagination;
}

export class PresenceManualEntryRepository extends TypeormRepository<
    PresenceManualEntry,
    PresenceManualEntryPersistenceEntity
> {
    constructor(tm: TransactionManager) {
        super(tm, createMapperFromEntity(PresenceManualEntryPersistenceEntity), PresenceManualEntryPersistenceEntity);
    }

    async getByUserInRange(userId: Id, range: DateRange, tx?: Transaction): Promise<PresenceManualEntry[]> {
        return this.getAllWhere(
            {
                userId: userId.value,
                period: TypeormDateRangeHelper.intersects(range),
            },
            tx,
        );
    }

    async find(params: FindProperties, tx?: Transaction): Promise<PaginationResult<PresenceManualEntry>> {
        const where: Where<PresenceManualEntryPersistenceEntity> = {};
        if (params.ids && params.ids.length > 0) {
            where.id = In(params.ids.map((id) => id.value));
        }

        if (params.userIds && params.userIds.length > 0) {
            where.userId = In(params.userIds.map((id) => id.value));
        }

        if (params.title) {
            where.title = ILike(`%${params.title}%`);
        }

        const periodWhere: FindOperator<any>[] = [];
        if (params.startAfter) {
            periodWhere.push(TypeormDateRangeHelper.isLowerSameOrAfter(params.startAfter));
        }
        if (params.endBefore) {
            periodWhere.push(TypeormDateRangeHelper.isUpperSameOrBefore(params.endBefore));
        }
        if (periodWhere.length > 0) {
            where.period = Or(...periodWhere);
        }

        return this.getAllWhereAdvanced({
            where,
            pagination: params.pagination,
            order: Ordering.fromParams({ field: 'period', direction: -1 }),
            existingTx: tx,
        });
    }
}
