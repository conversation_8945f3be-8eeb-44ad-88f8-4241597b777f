import moment from 'moment';
import { DateRange } from '../../core/domain/value-objects/DateRange';
import Id from '../../core/domain/value-objects/Id';
import { PaginationResult } from '../../core/domain/value-objects/Pagination';
import Transaction from '../../core/infrastructure/Transaction';
import TransactionManager from '../../core/infrastructure/TransactionManager';
import ArgumentValidation from '../../core/utils/validation/ArgumentValidation';
import { GovAgencyService } from '../../gov/submodules/gov-agency/services/GovAgencyService';
import { GovGroupService } from '../../gov/submodules/gov-group/services/GovGroupService';
import { SyncLearnerInfoForDayCommand } from '../../gov/submodules/gov-participation-auto-aggregation/controllers/commands/dto/SyncLearnerInfoForDayCommand';
import { GovProgramService } from '../../gov/submodules/gov-program/services/GovProgramService';
import { QueueService } from '../../queue/QueueService';
import { CreateProperties, PresenceManualEntry, UpdateProperties } from '../domain/PresenceManualEntry';
import { FindProperties, PresenceManualEntryRepository } from '../infrastructure/db/PresenceManualEntryRepository';
import { Timezone } from '../../core/domain/types/Timezone';
import { Day } from '../../core/domain/value-objects/Day';

type CreateBatchProperties = Omit<CreateProperties, 'id' | 'userId'> & { userIds: Id[] };

type UpdateBatchProperties = UpdateProperties & { ids: Id[] };

export class PresenceManualEntryService {
    constructor(
        private tm: TransactionManager,
        private readonly presenceManualEntryRepository: PresenceManualEntryRepository,
        private readonly queueService: QueueService,
        private readonly govGroupService: GovGroupService,
        private readonly govProgramService: GovProgramService,
        private readonly govAgencyService: GovAgencyService,
    ) {}

    async find(params: FindProperties): Promise<PaginationResult<PresenceManualEntry>> {
        return this.presenceManualEntryRepository.find(params);
    }

    async get(id: Id): Promise<PresenceManualEntry | undefined> {
        return this.presenceManualEntryRepository.get(id);
    }

    async getAll(): Promise<PresenceManualEntry[]> {
        return this.presenceManualEntryRepository.getAll();
    }

    async getByUserInRange(userId: Id, range: DateRange): Promise<PresenceManualEntry[]> {
        return this.presenceManualEntryRepository.getByUserInRange(userId, range);
    }

    async create(params: Omit<CreateProperties, 'id'>): Promise<PresenceManualEntry> {
        return this.save(new PresenceManualEntry(params));
    }

    async createBatch(params: CreateBatchProperties): Promise<void> {
        const { userIds, ...createParams } = params;
        await this.saveAll(userIds.map((userId) => new PresenceManualEntry({ ...createParams, userId })));
    }

    async update(params: UpdateProperties & { id: Id }): Promise<PresenceManualEntry | undefined> {
        return await this.tm.execute(async (tx) => {
            const entry = await this.presenceManualEntryRepository.get(params.id, tx);
            if (!entry) {
                return undefined;
            }

            return this.save(entry.update(params), tx);
        });
    }

    async updateBatch(params: UpdateBatchProperties): Promise<void> {
        const { ids, ...updateParams } = params;
        await this.tm.execute(async (tx) => {
            const entries = await this.presenceManualEntryRepository.getAllByIds(ids, tx);
            await this.saveAll(
                entries.map((entry) => entry.update(updateParams)),
                tx,
            );
        });
    }

    async save(entity: PresenceManualEntry, tx?: Transaction): Promise<PresenceManualEntry> {
        return this.tm.execute(async (transaction) => {
            transaction.addBeforeCommitAction(async () => {
                await this.triggerAttendanceSync([{ userId: entity.userId, period: entity.period! }]);
            });

            return this.presenceManualEntryRepository.save(entity, transaction);
        }, tx);
    }

    async saveAll(entities: PresenceManualEntry[], tx?: Transaction): Promise<PresenceManualEntry[]> {
        return this.tm.execute(async (transaction) => {
            transaction.addBeforeCommitAction(async () => {
                await this.triggerAttendanceSync(
                    entities.map((entity) => {
                        return { userId: entity.userId, period: entity.period! };
                    }),
                );
            });

            return this.presenceManualEntryRepository.saveAll(entities, transaction);
        }, tx);
    }

    async delete(id: Id): Promise<void> {
        await this.tm.execute(async (transaction) => {
            const entry = await this.presenceManualEntryRepository.get(id);

            if (!entry) {
                return;
            }

            transaction.addBeforeCommitAction(async () => {
                await this.triggerAttendanceSync([entry]);
            });

            await this.presenceManualEntryRepository.delete(id, transaction);
        });
    }

    async deleteBatch(ids: Id[]): Promise<void> {
        await this.tm.execute(async (transaction) => {
            const entries = await this.presenceManualEntryRepository.getAllByIds(ids);

            if (entries.length === 0) {
                return;
            }

            transaction.addBeforeCommitAction(async () => {
                await this.triggerAttendanceSync(
                    entries.map((entry) => {
                        return { userId: entry.userId, period: entry.period! };
                    }),
                );
            });

            await this.presenceManualEntryRepository.deleteByIds(ids, transaction);
        });
    }

    async deleteAll(): Promise<void> {
        await this.presenceManualEntryRepository.deleteAll();
    }

    // TODO GOVv2: Abstract this from GOV specific code
    private async triggerAttendanceSync(data: Array<Pick<PresenceManualEntry, 'userId' | 'period'>>): Promise<void> {
        if (data.length === 0) {
            return;
        }

        const govGroup = await this.govGroupService.getByUserId(data[0].userId);

        if (!govGroup) {
            return;
        }

        const govProgram = await this.govProgramService.get(govGroup.programId);
        ArgumentValidation.assert.defined(govProgram);

        const govAgency = await this.govAgencyService.getById(govProgram.agencyId);
        ArgumentValidation.assert.defined(govAgency);

        await this.queueService.commandMany(
            data.map(({ userId, period }) => {
                return {
                    message: new SyncLearnerInfoForDayCommand({
                        userId,
                        day: Day.fromDate(
                            moment(period.from).tz(govAgency.timezone).startOf('day').utc(true).toDate(),
                            govAgency.timezone,
                        ).switchTimezone(Timezone.UTC),
                        triggerMonthSync: true,
                    }),
                    options: {
                        startAfter: moment().add(10, 'seconds').toDate(),
                    },
                };
            }),
        );
    }
}
