import Id from '../../core/domain/value-objects/Id';
import { DateRange } from '../../core/domain/value-objects/DateRange';
import DiscordUserId from '../../discord/domain/DiscordUserId';
import { DiscordTrackerClient } from '../../discord-tracker/services/DiscordTrackerClient';
import { DiscordProfileHistoryService } from '../../discord-profile/service/DiscordProfileHistoryService';
import { VirtualClassroomService } from '../../virtual-classroom/services/VirtualClassroomService';
import { PresenceManualEntryService } from './PresenceManualEntryService';
import { MeetingAttendanceLog } from '../../meetings/services/MeetingAttendanceLog';
import { Configuration } from '../../config/Configuration';
import DiscordGuildId from '../../discord/domain/DiscordGuildId';

export class PresenceTrackingService {
    private readonly discordGuildId: DiscordGuildId;

    constructor(
        private readonly discordTrackerClient: DiscordTrackerClient,
        private readonly discordProfileHistoryService: DiscordProfileHistoryService,
        private readonly virtualClassroomService: VirtualClassroomService,
        private readonly presenceManualEntryService: PresenceManualEntryService,
        private readonly meetingAttendanceLog: MeetingAttendanceLog,
        configuration: Configuration,
    ) {
        this.discordGuildId = new DiscordGuildId(configuration.discord.guildId);
    }

    async getOnlinePresenceIntervals(userId: Id, range: DateRange): Promise<DateRange[]> {
        const profileRanges = await this.getDiscordProfileRanges(userId, range);
        const results = await Promise.all(
            profileRanges.map((profileRange) =>
                this.discordTrackerClient.getPresenceIntervals({
                    discordGuildId: this.discordGuildId,
                    discordUserId: profileRange.discordUserId,
                    range: profileRange.range,
                }),
            ),
        );

        return results.flat();
    }

    async getVoiceChannelPresenceIntervals(userId: Id, range: DateRange): Promise<DateRange[]> {
        const virtualClassrooms = await this.virtualClassroomService.getVirtualClassrooms();
        if (virtualClassrooms.length === 0) {
            return [];
        }

        const profileRanges = await this.getDiscordProfileRanges(userId, range);
        const results = await Promise.all(
            profileRanges.map((profileRange) =>
                this.discordTrackerClient.getVoiceChannelPresenceIntervals({
                    discordUserId: profileRange.discordUserId,
                    discordChannelIds: virtualClassrooms.map((vc) => vc.discordChannelId),
                    range: profileRange.range,
                }),
            ),
        );

        return results.flat();
    }

    async getMeetingPresenceIntervals(userId: Id, range: DateRange): Promise<DateRange[]> {
        return this.meetingAttendanceLog.getUserAttendanceInRange({
            userId,
            range,
        });
    }

    async getManualPresenceIntervals(userId: Id, range: DateRange): Promise<DateRange[]> {
        const entries = await this.presenceManualEntryService.getByUserInRange(userId, range);

        return entries.map((entry) => entry.period);
    }

    private async getDiscordProfileRanges(
        userId: Id,
        range: DateRange,
    ): Promise<
        {
            range: DateRange;
            discordUserId: DiscordUserId;
        }[]
    > {
        const discordProfileHistory = await this.discordProfileHistoryService.getProfileHistory(userId);

        // TODO reimplement this to handle open and closed boundaries
        return discordProfileHistory
            .filter((profile) => profile.period.contains(range.from) || profile.period.contains(range.to))
            .map((profile) => ({
                range: DateRange.fromDatesInclusive(
                    !profile.period.from || profile.period.from < range.from ? range.from : profile.period.from,
                    !profile.period.to || profile.period.to > range.to ? range.to : profile.period.to,
                ),
                discordUserId: profile.discordUserId,
            }));
    }
}
