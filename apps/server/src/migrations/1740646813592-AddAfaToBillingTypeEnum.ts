import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAfaToBillingTypeEnum1740646813592 implements MigrationInterface {
    name = 'AddAfaToBillingTypeEnum1740646813592';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            'ALTER TYPE "public"."student_settings_billing_type_enum" RENAME TO "student_settings_billing_type_enum_old"',
        );
        await queryRunner.query(
            "CREATE TYPE \"public\".\"student_settings_billing_type_enum\" AS ENUM('tuition', 'tuition_osmos', 'discount', 'scholarship', 'uzt', 'afa')",
        );
        await queryRunner.query('ALTER TABLE "student_settings" ALTER COLUMN "billing_type" DROP DEFAULT');
        await queryRunner.query(
            'ALTER TABLE "student_settings" ALTER COLUMN "billing_type" TYPE "public"."student_settings_billing_type_enum" USING "billing_type"::"text"::"public"."student_settings_billing_type_enum"',
        );
        await queryRunner.query('ALTER TABLE "student_settings" ALTER COLUMN "billing_type" SET DEFAULT \'tuition\'');
        await queryRunner.query('DROP TYPE "public"."student_settings_billing_type_enum_old"');

        await queryRunner.query(
            'ALTER TYPE "public"."student_settings_audit_billing_type_enum" RENAME TO "student_settings_audit_billing_type_enum_old"',
        );
        await queryRunner.query(
            "CREATE TYPE \"public\".\"student_settings_audit_billing_type_enum\" AS ENUM('tuition', 'tuition_osmos', 'discount', 'scholarship', 'uzt', 'afa')",
        );
        await queryRunner.query(
            'ALTER TABLE "student_settings_audit" ALTER COLUMN "billing_type" TYPE "public"."student_settings_audit_billing_type_enum" USING "billing_type"::"text"::"public"."student_settings_audit_billing_type_enum"',
        );
        await queryRunner.query('DROP TYPE "public"."student_settings_audit_billing_type_enum_old"');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            "CREATE TYPE \"public\".\"student_settings_audit_billing_type_enum_old\" AS ENUM('tuition', 'tuition_osmos', 'discount', 'scholarship', 'uzt')",
        );
        await queryRunner.query(
            'ALTER TABLE "student_settings_audit" ALTER COLUMN "billing_type" TYPE "public"."student_settings_audit_billing_type_enum_old" USING "billing_type"::"text"::"public"."student_settings_audit_billing_type_enum_old"',
        );
        await queryRunner.query('DROP TYPE "public"."student_settings_audit_billing_type_enum"');
        await queryRunner.query(
            'ALTER TYPE "public"."student_settings_audit_billing_type_enum_old" RENAME TO "student_settings_audit_billing_type_enum"',
        );
        await queryRunner.query(
            "CREATE TYPE \"public\".\"student_settings_billing_type_enum_old\" AS ENUM('tuition', 'tuition_osmos', 'discount', 'scholarship', 'uzt')",
        );
        await queryRunner.query('ALTER TABLE "student_settings" ALTER COLUMN "billing_type" DROP DEFAULT');
        await queryRunner.query(
            'ALTER TABLE "student_settings" ALTER COLUMN "billing_type" TYPE "public"."student_settings_billing_type_enum_old" USING "billing_type"::"text"::"public"."student_settings_billing_type_enum_old"',
        );
        await queryRunner.query('ALTER TABLE "student_settings" ALTER COLUMN "billing_type" SET DEFAULT \'tuition\'');
        await queryRunner.query('DROP TYPE "public"."student_settings_billing_type_enum"');
        await queryRunner.query(
            'ALTER TYPE "public"."student_settings_billing_type_enum_old" RENAME TO "student_settings_billing_type_enum"',
        );
    }
}
