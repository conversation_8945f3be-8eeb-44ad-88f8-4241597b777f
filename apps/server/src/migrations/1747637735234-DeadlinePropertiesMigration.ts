import { MigrationInterface, QueryRunner } from 'typeorm';

export class DeadlinePropertiesMigration1747637735234 implements MigrationInterface {
    name = 'DeadlinePropertiesMigration1747637735234';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "sprint" ADD "deadline_properties" jsonb');
        await queryRunner.query(
            "UPDATE sprint SET deadline_properties = jsonb_build_object('a', easy_deadline_days,'b', baseline_deadline_days,'c', hard_deadline_days, 'd', baseline_deadline_days)",
        );
        await queryRunner.query('ALTER TABLE "sprint" ALTER COLUMN "deadline_properties" SET NOT NULL');
        await queryRunner.query('ALTER TABLE "sprint" DROP COLUMN "easy_deadline_days"');
        await queryRunner.query('ALTER TABLE "sprint" DROP COLUMN "baseline_deadline_days"');
        await queryRunner.query('ALTER TABLE "sprint" DROP COLUMN "hard_deadline_days"');
        await queryRunner.query(
            'ALTER TYPE "public"."student_settings_deadline_type_enum" RENAME TO "student_settings_deadline_type_enum_old"',
        );
        await queryRunner.query(
            "CREATE TYPE \"public\".\"student_settings_deadline_type_enum\" AS ENUM('a', 'b', 'c', 'd')",
        );
        await queryRunner.query(`
            ALTER TABLE student_settings 
            ALTER COLUMN deadline_type TYPE varchar 
            USING deadline_type::varchar;
        `);
        await queryRunner.query(`
            UPDATE student_settings 
            SET deadline_type = CASE deadline_type
                WHEN 'easy' THEN 'a'
                WHEN 'baseline' THEN 'b'
                WHEN 'hard' THEN 'c'
                ELSE deadline_type
            END
        `);
        await queryRunner.query(
            'ALTER TABLE "student_settings" ALTER COLUMN "deadline_type" TYPE "public"."student_settings_deadline_type_enum" USING "deadline_type"::"text"::"public"."student_settings_deadline_type_enum"',
        );
        await queryRunner.query('DROP TYPE "public"."student_settings_deadline_type_enum_old"');
        await queryRunner.query(
            'ALTER TYPE "public"."student_settings_audit_deadline_type_enum" RENAME TO "student_settings_audit_deadline_type_enum_old"',
        );
        await queryRunner.query(
            "CREATE TYPE \"public\".\"student_settings_audit_deadline_type_enum\" AS ENUM('a', 'b', 'c', 'd')",
        );
        await queryRunner.query(`
            ALTER TABLE student_settings_audit 
            ALTER COLUMN deadline_type TYPE varchar 
            USING deadline_type::varchar;
        `);
        await queryRunner.query(`
            UPDATE student_settings_audit 
            SET deadline_type = CASE deadline_type
                WHEN 'easy' THEN 'a'
                WHEN 'baseline' THEN 'b'
                WHEN 'hard' THEN 'c'
                ELSE deadline_type
            END
        `);
        await queryRunner.query(
            'ALTER TABLE "student_settings_audit" ALTER COLUMN "deadline_type" TYPE "public"."student_settings_audit_deadline_type_enum" USING "deadline_type"::"text"::"public"."student_settings_audit_deadline_type_enum"',
        );
        await queryRunner.query('DROP TYPE "public"."student_settings_audit_deadline_type_enum_old"');
        await queryRunner.query('ALTER TABLE student_settings RENAME COLUMN deadline_type TO deadline_category');
        await queryRunner.query('ALTER TABLE student_settings_audit RENAME COLUMN deadline_type TO deadline_category');
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            "CREATE TYPE \"public\".\"student_settings_audit_deadline_type_enum_old\" AS ENUM('easy', 'baseline', 'hard')",
        );
        await queryRunner.query(`
            ALTER TABLE student_settings_audit 
            ALTER COLUMN deadline_category TYPE varchar 
            USING deadline_category::varchar;
        `);
        await queryRunner.query(`
            UPDATE student_settings_audit 
            SET deadline_category = CASE deadline_category
                WHEN 'a' THEN 'easy'
                WHEN 'b' THEN 'baseline'
                WHEN 'c' THEN 'hard'
                WHEN 'd' THEN 'baseline'
                ELSE deadline_category
            END
        `);
        await queryRunner.query(
            'ALTER TABLE "student_settings_audit" ALTER COLUMN "deadline_category" TYPE "public"."student_settings_audit_deadline_type_enum_old" USING "deadline_category"::"text"::"public"."student_settings_audit_deadline_type_enum_old"',
        );
        await queryRunner.query('DROP TYPE "public"."student_settings_audit_deadline_type_enum"');

        await queryRunner.query(
            'ALTER TYPE "public"."student_settings_audit_deadline_type_enum_old" RENAME TO "student_settings_audit_deadline_type_enum"',
        );
        await queryRunner.query(`
            ALTER TABLE student_settings
            ALTER COLUMN deadline_category TYPE varchar 
            USING deadline_category::varchar;
        `);
        await queryRunner.query(`
            UPDATE student_settings
            SET deadline_category = CASE deadline_category
                WHEN 'a' THEN 'easy'
                WHEN 'b' THEN 'baseline'
                WHEN 'c' THEN 'hard'
                WHEN 'd' THEN 'baseline'
                ELSE deadline_category
            END
        `);
        await queryRunner.query(
            "CREATE TYPE \"public\".\"student_settings_deadline_type_enum_old\" AS ENUM('easy', 'baseline', 'hard')",
        );
        await queryRunner.query(
            'ALTER TABLE "student_settings" ALTER COLUMN "deadline_category" TYPE "public"."student_settings_deadline_type_enum_old" USING "deadline_category"::"text"::"public"."student_settings_deadline_type_enum_old"',
        );
        await queryRunner.query('DROP TYPE "public"."student_settings_deadline_type_enum"');
        await queryRunner.query(
            'ALTER TYPE "public"."student_settings_deadline_type_enum_old" RENAME TO "student_settings_deadline_type_enum"',
        );

        await queryRunner.query('ALTER TABLE "sprint" ADD "hard_deadline_days" integer');
        await queryRunner.query('ALTER TABLE "sprint" ADD "baseline_deadline_days" integer');
        await queryRunner.query('ALTER TABLE "sprint" ADD "easy_deadline_days" integer');
        await queryRunner.query(
            "UPDATE sprint SET easy_deadline_days = (deadline_properties->>'a')::integer, baseline_deadline_days = (deadline_properties->>'b')::integer, hard_deadline_days = (deadline_properties->>'c')::integer",
        );
        await queryRunner.query('ALTER TABLE "sprint" DROP COLUMN "deadline_properties"');
        await queryRunner.query('ALTER TABLE "sprint" ALTER COLUMN "hard_deadline_days" SET NOT NULL');
        await queryRunner.query('ALTER TABLE "sprint" ALTER COLUMN "baseline_deadline_days" SET NOT NULL');
        await queryRunner.query('ALTER TABLE "sprint" ALTER COLUMN "easy_deadline_days" SET NOT NULL');

        await queryRunner.query('ALTER TABLE student_settings RENAME COLUMN deadline_category TO deadline_type');
        await queryRunner.query('ALTER TABLE student_settings_audit RENAME COLUMN deadline_category TO deadline_type');
    }
}
