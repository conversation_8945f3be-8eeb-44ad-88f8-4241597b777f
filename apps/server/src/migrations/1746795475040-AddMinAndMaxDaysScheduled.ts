import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddMinAndMaxDaysScheduled1746795475040 implements MigrationInterface {
    name = 'AddMinAndMaxDaysScheduled1746795475040';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.addColumn(
            'gov_week_schedule_requirements',
            new TableColumn({
                name: 'min_days_scheduled',
                type: 'integer',
                isNullable: true,
            }),
        );

        await queryRunner.addColumn(
            'gov_week_schedule_requirements',
            new TableColumn({
                name: 'max_days_scheduled',
                type: 'integer',
                isNullable: true,
            }),
        );

        await queryRunner.query(
            'UPDATE "gov_week_schedule_requirements" SET min_days_scheduled = days_required, max_days_scheduled = days_required',
        );

        await queryRunner.changeColumn(
            'gov_week_schedule_requirements',
            'min_days_scheduled',
            new TableColumn({
                name: 'min_days_scheduled',
                type: 'integer',
                isNullable: false,
            }),
        );

        await queryRunner.changeColumn(
            'gov_week_schedule_requirements',
            'max_days_scheduled',
            new TableColumn({
                name: 'max_days_scheduled',
                type: 'integer',
                isNullable: false,
            }),
        );

        await queryRunner.dropColumn('gov_week_schedule_requirements', 'days_required');

        await queryRunner.query(
            'ALTER TABLE "gov_week_schedule_requirements" ADD CONSTRAINT "CHK_af1160232a92a14c4a6581653b" CHECK (min_days_scheduled >= 1)',
        );
        await queryRunner.query(
            'ALTER TABLE "gov_week_schedule_requirements" ADD CONSTRAINT "CHK_674c155f1e020d4b835fe90af1" CHECK (max_days_scheduled <= 7)',
        );
        await queryRunner.query(
            'ALTER TABLE "gov_week_schedule_requirements" ADD CONSTRAINT "CHK_9e30b2b9fb03816990a9ff80fe" CHECK (max_days_scheduled >= min_days_scheduled)',
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            'ALTER TABLE "gov_week_schedule_requirements" DROP CONSTRAINT "CHK_9e30b2b9fb03816990a9ff80fe"',
        );
        await queryRunner.query(
            'ALTER TABLE "gov_week_schedule_requirements" DROP CONSTRAINT "CHK_674c155f1e020d4b835fe90af1"',
        );
        await queryRunner.query(
            'ALTER TABLE "gov_week_schedule_requirements" DROP CONSTRAINT "CHK_af1160232a92a14c4a6581653b"',
        );

        await queryRunner.addColumn(
            'gov_week_schedule_requirements',
            new TableColumn({
                name: 'days_required',
                type: 'integer',
                isNullable: true,
            }),
        );

        await queryRunner.query('UPDATE "gov_week_schedule_requirements" SET days_required = min_days_scheduled');

        await queryRunner.changeColumn(
            'gov_week_schedule_requirements',
            'days_required',
            new TableColumn({
                name: 'days_required',
                type: 'integer',
                isNullable: false,
            }),
        );

        await queryRunner.query('ALTER TABLE "gov_week_schedule_requirements" DROP COLUMN "max_days_scheduled"');
        await queryRunner.query('ALTER TABLE "gov_week_schedule_requirements" DROP COLUMN "min_days_scheduled"');
    }
}
