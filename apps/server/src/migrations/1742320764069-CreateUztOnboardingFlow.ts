import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateUztOnboardingFlow1742320764069 implements MigrationInterface {
    name = 'CreateUztOnboardingFlow1742320764069';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "onboarding_step" DROP CONSTRAINT "FK_c7cce998e0b6f3a873b318dc456"');
        await queryRunner.query(
            'ALTER TYPE "public"."onboarding_flow_type_enum" RENAME TO "onboarding_flow_type_enum_old"',
        );
        await queryRunner.query('CREATE TYPE "public"."onboarding_flow_type_enum" AS ENUM(\'default\', \'uzt\')');
        await queryRunner.query(
            'ALTER TABLE "onboarding_flow" ALTER COLUMN "type" TYPE "public"."onboarding_flow_type_enum" USING "type"::"text"::"public"."onboarding_flow_type_enum"',
        );
        await queryRunner.query('DROP TYPE "public"."onboarding_flow_type_enum_old"');
        await queryRunner.query('ALTER TABLE "onboarding_flow_step" DROP CONSTRAINT "FK_6fce1271c8e7b0a60a303a087a4"');
        await queryRunner.query('ALTER TABLE "onboarding_flow_step" DROP CONSTRAINT "REL_6fce1271c8e7b0a60a303a087a"');
        await queryRunner.query(
            'ALTER TABLE "onboarding_flow_step" ADD CONSTRAINT "FK_6fce1271c8e7b0a60a303a087a4" FOREIGN KEY ("step_id") REFERENCES "onboarding_step"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
        );
        await queryRunner.query(
            'ALTER TABLE "onboarding_step" ADD CONSTRAINT "FK_eaf835d8d90df8f2017b87633c6" FOREIGN KEY ("parent_step_id") REFERENCES "onboarding_step"("id") ON DELETE CASCADE ON UPDATE NO ACTION',
        );

        await queryRunner.query('INSERT INTO "onboarding_flow" (type) VALUES (\'uzt\')');
        await queryRunner.query(`
            INSERT INTO "onboarding_flow_step" (flow_id, step_id)
            SELECT flow.id, step.id
            FROM onboarding_flow flow
            JOIN onboarding_step step ON true
            WHERE flow.type = 'uzt'
                AND step.type IN ('get_accepted', 'finalize_documents', 'setup_account', 'uzt_information')
            ORDER BY step.position
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            'DELETE FROM "onboarding_flow_step" WHERE flow_id = (SELECT id FROM "onboarding_flow" WHERE type = \'uzt\')',
        );
        await queryRunner.query('DELETE FROM "onboarding_flow" WHERE type = \'uzt\'');

        await queryRunner.query('ALTER TABLE "onboarding_step" DROP CONSTRAINT "FK_eaf835d8d90df8f2017b87633c6"');
        await queryRunner.query('ALTER TABLE "onboarding_flow_step" DROP CONSTRAINT "FK_6fce1271c8e7b0a60a303a087a4"');
        await queryRunner.query(
            'ALTER TABLE "onboarding_flow_step" ADD CONSTRAINT "REL_6fce1271c8e7b0a60a303a087a" UNIQUE ("step_id")',
        );
        await queryRunner.query(
            'ALTER TABLE "onboarding_flow_step" ADD CONSTRAINT "FK_6fce1271c8e7b0a60a303a087a4" FOREIGN KEY ("step_id") REFERENCES "onboarding_step"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
        );
        await queryRunner.query('CREATE TYPE "public"."onboarding_flow_type_enum_old" AS ENUM(\'default\')');
        await queryRunner.query(
            'ALTER TABLE "onboarding_flow" ALTER COLUMN "type" TYPE "public"."onboarding_flow_type_enum_old" USING "type"::"text"::"public"."onboarding_flow_type_enum_old"',
        );
        await queryRunner.query('DROP TYPE "public"."onboarding_flow_type_enum"');
        await queryRunner.query(
            'ALTER TYPE "public"."onboarding_flow_type_enum_old" RENAME TO "onboarding_flow_type_enum"',
        );
        await queryRunner.query(
            'ALTER TABLE "onboarding_step" ADD CONSTRAINT "FK_c7cce998e0b6f3a873b318dc456" FOREIGN KEY ("parent_step_id") REFERENCES "onboarding_step"("id") ON DELETE CASCADE ON UPDATE NO ACTION',
        );
    }
}
