import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddParticipationMonth1737991087094 implements MigrationInterface {
    name = 'AddParticipationMonth1737991087094';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            CREATE TABLE "uzt_participation_month" (
                "id" SERIAL NOT NULL, "month" TIMESTAMP WITH TIME ZONE NOT NULL,
                "user_id" integer NOT NULL,
                "attended_hours" numeric(6,2) NOT NULL DEFAULT '0',
                "scheduled_hours" numeric(6,2) NOT NULL DEFAULT '0',
                "missed_with_valid_reason_hours" numeric(6,2) NOT NULL DEFAULT '0',
                "attended_days" integer NOT NULL DEFAULT '0',
                "scheduled_days" integer NOT NULL DEFAULT '0',
                "missed_with_valid_reason_days" integer NOT NULL DEFAULT '0',
                "last_synced_at" TIMESTAMP WITH TIME ZONE,
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "check_uzt_participation_month_month_is_first_day"
                    CHECK (date_trunc('month', "month") = "month"),
                CONSTRAINT "check_uzt_participation_month_attended_lte_scheduled_hours"
                    CHECK (attended_hours <= scheduled_hours),
                CONSTRAINT "check_uzt_participation_month_attended_hours_gte_0"
                    CHECK (attended_hours >= 0),
                CONSTRAINT "check_uzt_participation_month_scheduled_hours_gte_0"
                    CHECK (scheduled_hours >= 0),
                CONSTRAINT "check_uzt_participation_month_missed_lte_scheduled_hours"
                    CHECK (missed_with_valid_reason_hours <= scheduled_hours),
                CONSTRAINT "check_uzt_participation_month_missed_hours_gte_0"
                    CHECK (missed_with_valid_reason_hours >= 0),
                CONSTRAINT "check_uzt_participation_month_attended_lte_scheduled_days"
                    CHECK (attended_days <= scheduled_days),
                CONSTRAINT "check_uzt_participation_month_attended_days_gte_0"
                    CHECK (attended_days >= 0),
                CONSTRAINT "check_uzt_participation_month_scheduled_days_gte_0"
                    CHECK (scheduled_days >= 0),
                CONSTRAINT "check_uzt_participation_month_missed_lte_scheduled_days"
                    CHECK (missed_with_valid_reason_days <= scheduled_days),
                CONSTRAINT "check_uzt_participation_month_missed_days_is_positive"
                    CHECK (missed_with_valid_reason_days >= 0),
                CONSTRAINT "PK_11a641aeee7b930aec553d1f74f" PRIMARY KEY ("id")
            )`);
        await queryRunner.query(`
            CREATE UNIQUE INDEX "idx_uzt_participation_month_month_user_id"
                ON "uzt_participation_month" ("month", "user_id")
        `);
        await queryRunner.query(`
            ALTER TABLE "uzt_participation_month"
                ADD CONSTRAINT "FK_53514493ab4372b971a552bf7b0"
                    FOREIGN KEY ("user_id")
                        REFERENCES "platform_user"("id")
                        ON DELETE CASCADE
                        ON UPDATE NO ACTION
        `);
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(`
            ALTER TABLE "uzt_participation_month"
                DROP CONSTRAINT "FK_53514493ab4372b971a552bf7b0"
        `);
        await queryRunner.query('DROP INDEX "public"."idx_uzt_participation_month_month_user_id"');
        await queryRunner.query('DROP TABLE "uzt_participation_month"');
    }
}
