import { MigrationInterface, QueryRunner } from 'typeorm';

export class RenameTooLongConstraint1734713576559 implements MigrationInterface {
    name = 'RenameTooLongConstraint1734713576559';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            'ALTER TABLE "learner_week_schedule_requirements_override" DROP CONSTRAINT "learner_week_schedule_requirements_override_week_user_id_exclus"',
        );
        await queryRunner.query(
            `
                ALTER TABLE "learner_week_schedule_requirements_override"
                    ADD CONSTRAINT "learner_week_schedule_requirements_override_week_user_id_exc"
                        EXCLUDE USING gist ("user_id" WITH =, "week" WITH &&)
            `,
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            'ALTER TABLE "learner_week_schedule_requirements_override" DROP CONSTRAINT "learner_week_schedule_requirements_override_week_user_id_exc"',
        );
        await queryRunner.query(
            `
                ALTER TABLE "learner_week_schedule_requirements_override"
                    ADD CONSTRAINT "learner_week_schedule_requirements_override_week_user_id_exclus"
                        EXCLUDE USING gist (user_id WITH =, week WITH &&)
            `,
        );
    }
}
