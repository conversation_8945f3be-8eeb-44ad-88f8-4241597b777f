import { MigrationInterface, QueryRunner } from 'typeorm';

export class GovAgencyTableAndSeed1745324117706 implements MigrationInterface {
    name = 'GovAgencyTableAndSeed1745324117706';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('CREATE TYPE "public"."gov_agency_type_enum" AS ENUM(\'uzt\', \'afa\')');
        await queryRunner.query(
            `CREATE TABLE "gov_agency" (
                "id" SERIAL NOT NULL,
                "name" character varying NOT NULL,
                "type" "public"."gov_agency_type_enum" NOT NULL,
                "timezone" character varying NOT NULL DEFAULT \'Europe/Vilnius\',
                "holiday_group_id" integer NOT NULL,
                "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
                CONSTRAINT "UQ_ed39c0aae286aa476a59fd2b77a" UNIQUE ("name"),
                CONSTRAINT "UQ_ed1db40d1d3b7c283fd9e12dfee" UNIQUE ("type"),
                CONSTRAINT "PK_659e972e24f0d09b4b9a2cf4de6" PRIMARY KEY ("id")
            )`,
        );
        await queryRunner.query(
            'ALTER TABLE "gov_agency" ADD CONSTRAINT "FK_9adbf5f6546c0a6ab6864c17887" FOREIGN KEY ("holiday_group_id") REFERENCES "holiday_group"("id") ON DELETE NO ACTION ON UPDATE NO ACTION',
        );
        await queryRunner.query(
            'INSERT INTO "gov_agency" ("name", "type", "timezone", "holiday_group_id") SELECT \'UŽT\', \'uzt\', \'Europe/Vilnius\', id FROM holiday_group WHERE name = \'Lithuania\'',
        );
        await queryRunner.query(
            'INSERT INTO "gov_agency" ("name", "type", "timezone", "holiday_group_id") SELECT \'AfA\', \'afa\', \'Europe/Berlin\', id FROM holiday_group WHERE name = \'Germany\'',
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query('ALTER TABLE "gov_agency" DROP CONSTRAINT "FK_9adbf5f6546c0a6ab6864c17887"');
        await queryRunner.query('DROP TABLE "gov_agency"');
        await queryRunner.query('DROP TYPE "public"."gov_agency_type_enum"');
    }
}
