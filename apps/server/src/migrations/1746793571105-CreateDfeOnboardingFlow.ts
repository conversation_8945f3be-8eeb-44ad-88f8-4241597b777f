import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateDfeOnboardingFlow1746793571105 implements MigrationInterface {
    name = 'CreateDfeOnboardingFlow1746793571105';

    public async up(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            'ALTER TYPE "public"."onboarding_flow_type_enum" RENAME TO "onboarding_flow_type_enum_old"',
        );
        await queryRunner.query(
            "CREATE TYPE \"public\".\"onboarding_flow_type_enum\" AS ENUM('default', 'uzt', 'afa', 'dfe')",
        );
        await queryRunner.query(
            'ALTER TABLE "onboarding_flow" ALTER COLUMN "type" TYPE "public"."onboarding_flow_type_enum" USING "type"::"text"::"public"."onboarding_flow_type_enum"',
        );
        await queryRunner.query('DROP TYPE "public"."onboarding_flow_type_enum_old"');

        await queryRunner.query('INSERT INTO "onboarding_flow" (type) VALUES (\'dfe\')');
        const steps = ['get_accepted', 'finalize_documents', 'setup_account'];
        await Promise.all(
            steps.map(async (step, index) => {
                await queryRunner.query(
                    `
                    INSERT INTO "onboarding_flow_step" (flow_id, position, step_id)
                    SELECT flow.id, $1, step.id FROM onboarding_flow flow JOIN onboarding_step step ON true
                    WHERE flow.type = 'dfe' AND step.type = $2
                `,
                    [index + 1, step],
                );
            }),
        );
    }

    public async down(queryRunner: QueryRunner): Promise<void> {
        await queryRunner.query(
            'DELETE FROM "onboarding_flow_step" WHERE flow_id = (SELECT id FROM "onboarding_flow" WHERE type = \'dfe\')',
        );
        await queryRunner.query('DELETE FROM "onboarding_flow" WHERE type = \'dfe\'');
        await queryRunner.query(
            "CREATE TYPE \"public\".\"onboarding_flow_type_enum_old\" AS ENUM('default', 'uzt', 'afa')",
        );
        await queryRunner.query(
            'ALTER TABLE "onboarding_flow" ALTER COLUMN "type" TYPE "public"."onboarding_flow_type_enum_old" USING "type"::"text"::"public"."onboarding_flow_type_enum_old"',
        );
        await queryRunner.query('DROP TYPE "public"."onboarding_flow_type_enum"');
        await queryRunner.query(
            'ALTER TYPE "public"."onboarding_flow_type_enum_old" RENAME TO "onboarding_flow_type_enum"',
        );
    }
}
