import Logger from '../../utils/logger/Logger';
import { HolidayGroupRepository } from '../infrastructure/db/HolidayGroupRepository';
import { HolidayGroup } from '../domain/HolidayGroup';
import Transaction from '../../core/infrastructure/Transaction';

export class HolidayGroupFinder {
    constructor(
        private readonly holidayGroupRepository: HolidayGroupRepository,
        private readonly logger: Logger,
    ) {}

    async getAll(tx?: Transaction): Promise<HolidayGroup[]> {
        return this.holidayGroupRepository.getAll(tx);
    }
}
