import validator from 'validator';

export default class Strings {
    static toStartCase(str: string): string {
        return str
            .split(' ')
            .map((s) => Strings.capitalize(s.trim().toLocaleLowerCase()))
            .join(' ');
    }

    static capitalize(str: string): string {
        return str.charAt(0).toLocaleUpperCase() + str.substr(1);
    }

    static isEmpty(str: string): boolean {
        return !str || validator.isEmpty(str, { ignore_whitespace: false });
    }

    static isEmail(str: string): boolean {
        // @ts-expect-error TS(2322) FIXME: Type 'string | boolean' is not assignable to type ... Remove this comment to see the full error message
        return str && validator.isEmail(str);
    }
}
