import { LoggerFormat } from './LoggerFormat';
import winston from 'winston';

export class LoggerJsonFormat implements LoggerFormat {
    getFormat(name?: string): winston.Logform.Format {
        return winston.format.combine(
            winston.format.label({ label: name }),
            winston.format.timestamp(),
            winston.format.json({
                deterministic: true,
                maximumDepth: 5,
            }),
        );
    }
}
