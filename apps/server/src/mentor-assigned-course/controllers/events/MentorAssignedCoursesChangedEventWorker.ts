import { Job } from 'pg-boss';
import {
    MentorAssignedCoursesChangedEvent,
    MentorAssignedCoursesChangedEventDto,
} from './dto/MentorAssignedCoursesChangedEvent';
import { AppQueues } from '../../../queue/domain/AppQueues';
import { QueueWorker } from '../../../queue/domain/QueueWorker';
import { QueueService } from '../../../queue/QueueService';
import { ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand } from '../commands/dto/ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand';

export class MentorAssignedCoursesChangedEventWorker implements QueueWorker {
    readonly queuePattern = AppQueues.schema.user.mentor.assignedCourse.changed.queue;
    readonly options = {
        newJobCheckIntervalSeconds: 1,
    };

    constructor(private readonly queueService: QueueService) {}

    async handler(job: Job<MentorAssignedCoursesChangedEventDto>): Promise<void> {
        const event = AppQueues.getMapperByClass(MentorAssignedCoursesChangedEvent).deserialize(job.data);

        await this.queueService.command({
            command: new ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand(event),
            options: {
                subQueue: event.userId.value,
            },
        });
    }
}
