import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { Serialized } from '../../../../core/utils/Serialized';
import Id from '../../../../core/domain/value-objects/Id';

export type MentorAssignedCoursesChangedEventParams = NonFunctionProperties<MentorAssignedCoursesChangedEvent>;
export type MentorAssignedCoursesChangedEventDto = Serialized<MentorAssignedCoursesChangedEventParams>;

export class MentorAssignedCoursesChangedEvent {
    readonly userId: Id;
    readonly removedCourseIds: Id[];
    readonly newAssignedCourseIds: Id[];

    constructor(params: MentorAssignedCoursesChangedEventParams) {
        Object.assign(this, params);
    }
}

export class MentorAssignedCoursesChangedEventMapper {
    serialize(command: MentorAssignedCoursesChangedEvent): MentorAssignedCoursesChangedEventDto {
        return {
            userId: command.userId.value,
            removedCourseIds: command.removedCourseIds.map((id) => id.value),
            newAssignedCourseIds: command.newAssignedCourseIds.map((id) => id.value),
        };
    }

    deserialize(data: MentorAssignedCoursesChangedEventDto): MentorAssignedCoursesChangedEvent {
        return new MentorAssignedCoursesChangedEvent({
            userId: new Id(data.userId),
            removedCourseIds: data.removedCourseIds.map((id) => new Id(id)),
            newAssignedCourseIds: data.newAssignedCourseIds.map((id) => new Id(id)),
        });
    }
}
