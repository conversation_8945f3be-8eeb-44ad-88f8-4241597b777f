import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { Serialized } from '../../../../core/utils/Serialized';
import Id from '../../../../core/domain/value-objects/Id';

export type ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandParams =
    NonFunctionProperties<ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand>;
export type ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandDto =
    Serialized<ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandParams>;

export class ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand {
    readonly userId: Id;
    readonly removedCourseIds: Id[];
    readonly newAssignedCourseIds: Id[];

    constructor(params: ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandParams) {
        Object.assign(this, params);
    }
}

export class ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandMapper {
    serialize(
        command: ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand,
    ): ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandDto {
        return {
            userId: command.userId.value,
            removedCourseIds: command.removedCourseIds.map((id) => id.value),
            newAssignedCourseIds: command.newAssignedCourseIds.map((id) => id.value),
        };
    }

    deserialize(
        data: ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandDto,
    ): ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand {
        return new ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand({
            userId: new Id(data.userId),
            removedCourseIds: data.removedCourseIds.map((id) => new Id(id)),
            newAssignedCourseIds: data.newAssignedCourseIds.map((id) => new Id(id)),
        });
    }
}
