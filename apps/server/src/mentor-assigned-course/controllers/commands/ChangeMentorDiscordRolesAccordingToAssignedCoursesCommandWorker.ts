import { Job } from 'pg-boss';
import {
    ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand,
    ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandDto,
} from './dto/ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand';
import { AppQueues } from '../../../queue/domain/AppQueues';
import { QueueWorker } from '../../../queue/domain/QueueWorker';
import { QueueService } from '../../../queue/QueueService';
import { DiscordChangeRolesCommand } from '../../../discord/controllers/commands/dto/DiscordChangeRolesCommand';
import { DiscordProfileService } from '../../../discord-profile/service/DiscordProfileService';
import Logger from '../../../utils/logger/Logger';
import CourseFinder from '../../../education/courses/services/CourseFinder';
import { Configuration } from '../../../config/Configuration';
import DiscordGuildId from '../../../discord/domain/DiscordGuildId';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';

export class ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandWorker implements QueueWorker {
    readonly queuePattern =
        AppQueues.schema.user.mentor.assignedCourse.changeDiscordRolesAccordingToAssignedCourses.wildcard;
    readonly options = {
        newJobCheckIntervalSeconds: 1,
    };

    private readonly guildId = new DiscordGuildId(this.configuration.discord.guildId);

    constructor(
        private readonly configuration: Configuration,
        private readonly queueService: QueueService,
        private readonly discordProfileService: DiscordProfileService,
        private readonly logger: Logger,
        private readonly courseFinder: CourseFinder,
    ) {}

    async handler(job: Job<ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandDto>): Promise<void> {
        const command = AppQueues.getMapperByClass(
            ChangeMentorDiscordRolesAccordingToAssignedCoursesCommand,
        ).deserialize(job.data);

        const discordProfile = await this.discordProfileService.getByUserId(command.userId);

        if (!ArgumentValidation.is.defined(discordProfile) || !discordProfile?.isAuthorized()) {
            this.logger.error(
                `Discord profile not found or not authorized for user with id: ${command.userId.value}. Skipping role change.`,
            );

            return;
        }

        const courses = await this.courseFinder.findByIds([
            ...command.removedCourseIds,
            ...command.newAssignedCourseIds,
        ]);

        const discordRoles = courses.map((course) => course.mentorDiscordRoleId);

        const removeRoleIds = discordRoles
            .slice(0, command.removedCourseIds.length)
            .filter(ArgumentValidation.is.defined);
        const addRoleIds = discordRoles.slice(command.removedCourseIds.length).filter(ArgumentValidation.is.defined);

        if (removeRoleIds.length === 0 && addRoleIds.length === 0) {
            this.logger.debug(`No discord roles to change for user with id: ${command.userId.value}.`);

            return;
        }

        await this.queueService.command({
            command: new DiscordChangeRolesCommand({
                discordUserId: discordProfile.data!.discordUserId!,
                guildId: this.guildId,
                removeRoleIds,
                addRoleIds,
            }),
        });
    }
}
