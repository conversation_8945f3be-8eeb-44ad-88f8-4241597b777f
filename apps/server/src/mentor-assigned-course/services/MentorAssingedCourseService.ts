import Id from '../../core/domain/value-objects/Id';
import { MentorAssignedCourseRepository } from '../infrastructure/db/mentor-assigned-course/MentorAssignedCourseRepository';
import CourseFinder from '../../education/courses/services/CourseFinder';
import ArgumentValidation from '../../core/utils/validation/ArgumentValidation';
import { MentorAssignedCourse } from '../../users/mentors/domain/MentorAssignedCourse';
import Transaction from '../../core/infrastructure/Transaction';
import TransactionManager from '../../core/infrastructure/TransactionManager';
import DiscordRoleId from '../../discord/domain/DiscordRoleId';
import Arrays from '../../core/collections/Arrays';
import { QueueService } from '../../queue/QueueService';
import { MentorAssignedCoursesChangedEvent } from '../controllers/events/dto/MentorAssignedCoursesChangedEvent';

export class MentorAssignedCourseService {
    constructor(
        private readonly mentorAssignedCourseRepository: MentorAssignedCourseRepository,
        private readonly courseFinder: CourseFinder,
        private readonly transactionManager: TransactionManager,
        private readonly queueService: QueueService,
    ) {}

    async setCoursesAssignedToMentor(mentorId: Id, courseIds: Id[], tx?: Transaction): Promise<void> {
        ArgumentValidation.assert.notEmpty(courseIds, 'Course IDs must not be empty');

        const courses = await this.courseFinder.findByIds(courseIds);

        const missingCourses = courseIds.filter((id) => !courses.some((course) => id.equals(course.id)));

        ArgumentValidation.assert.true(
            missingCourses.length === 0,
            `Courses with IDs ${missingCourses.join(', ')} are not found`,
        );

        const existingConnections = await this.mentorAssignedCourseRepository.getByUserIds([mentorId]);

        const newAssignedCourseIds = courseIds.filter(
            (courseId) => !existingConnections.some((connection) => connection.courseId.equals(courseId)),
        );
        const removedCourses = existingConnections.filter(
            (connection) => !courseIds.some((courseId) => connection.courseId.equals(courseId)),
        );
        const removedCourseIds = removedCourses.map((course) => course.courseId);

        await this.transactionManager.execute(async (transaction) => {
            if (removedCourseIds.length + newAssignedCourseIds.length !== 0) {
                transaction.addBeforeCommitAction(async () => {
                    await this.queueService.publish({
                        event: new MentorAssignedCoursesChangedEvent({
                            userId: mentorId,
                            removedCourseIds,
                            newAssignedCourseIds,
                        }),
                    });
                });
            }

            if (removedCourses.length > 0) {
                await this.mentorAssignedCourseRepository.deleteForUserByCourseIds(
                    mentorId,
                    removedCourseIds,
                    transaction,
                );
            }

            if (newAssignedCourseIds.length > 0) {
                await this.mentorAssignedCourseRepository.saveAll(
                    newAssignedCourseIds.map(
                        (courseId) =>
                            new MentorAssignedCourse({
                                userId: mentorId,
                                courseId,
                            }),
                    ),
                    transaction,
                );
            }
        }, tx);
    }

    async findByUserIds(userIds: Id[], tx?: Transaction): Promise<MentorAssignedCourse[]> {
        return this.mentorAssignedCourseRepository.getByUserIds(userIds, tx);
    }

    async findAll(tx?: Transaction): Promise<MentorAssignedCourse[]> {
        return this.mentorAssignedCourseRepository.getAll(tx);
    }

    async getAssignedCoursesMentorDiscordRoles(userId: Id): Promise<DiscordRoleId[]> {
        const assignedCourses = await this.mentorAssignedCourseRepository.getByUserIds([userId]);
        const courses = await this.courseFinder.findByIds(assignedCourses.map((course) => course.courseId));

        return Arrays.unique(courses.map((course) => course.mentorDiscordRoleId).filter(ArgumentValidation.is.defined));
    }
}
