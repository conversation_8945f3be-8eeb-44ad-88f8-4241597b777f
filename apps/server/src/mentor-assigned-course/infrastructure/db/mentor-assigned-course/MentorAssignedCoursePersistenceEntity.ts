import { Column, Entity, Index, ManyToOne, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../../core/infrastructure/db/TypeormPersistenceEntity';
import User from '../../../../users/shared/infrastructure/db/User';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import Id from '../../../../core/domain/value-objects/Id';
import CoursePersistenceEntity from '../../../../education/courses/infrastructure/db/CoursePersistenceEntity';
import { MentorAssignedCourse } from '../../../../users/mentors/domain/MentorAssignedCourse';

@Entity('mentor_assigned_course')
@Index(['userId', 'courseId'], { unique: true })
export default class MentorAssignedCoursePersistenceEntity extends TypeormPersistenceEntity {
    @Column({ name: 'user_id' })
    @RelationId((self: MentorAssignedCoursePersistenceEntity) => self.user)
    userId: number;

    @Column({ name: 'course_id' })
    @RelationId((self: MentorAssignedCoursePersistenceEntity) => self.course)
    courseId: number;

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    private user: never;

    @ManyToOne(() => CoursePersistenceEntity, { onDelete: 'CASCADE' })
    private course: never;

    constructor(params?: NonFunctionProperties<MentorAssignedCoursePersistenceEntity>) {
        if (!params) {
            super();
            return;
        }
        super(params.id, params.createdAt, params.updatedAt);
        Object.assign(this, params);
    }

    toDomain(): MentorAssignedCourse {
        return new MentorAssignedCourse({
            id: this.id ? new Id(this.id) : undefined,
            userId: new Id(this.userId),
            courseId: new Id(this.courseId),
        });
    }

    static fromDomain(domain: MentorAssignedCourse): MentorAssignedCoursePersistenceEntity {
        return new MentorAssignedCoursePersistenceEntity({
            id: domain.id?.value,
            userId: domain.userId.value,
            courseId: domain.courseId.value,
        });
    }
}
