import MentorAssignedCoursePersistenceEntity from './MentorAssignedCoursePersistenceEntity';
import { MentorAssignedCourse } from '../../../../users/mentors/domain/MentorAssignedCourse';
import TransactionManager from '../../../../core/infrastructure/TransactionManager';
import { createMapperFromEntity } from '../../../../utils/createMapperFromEntity';
import TypeormRepository from '../../../../core/infrastructure/db/TypeormRepository';
import Id from '../../../../core/domain/value-objects/Id';
import { In } from 'typeorm';
import Transaction from '../../../../core/infrastructure/Transaction';
import ArgumentValidation from '../../../../core/utils/validation/ArgumentValidation';

export class MentorAssignedCourseRepository extends TypeormRepository<
    MentorAssignedCourse,
    MentorAssignedCoursePersistenceEntity
> {
    constructor(tm: TransactionManager) {
        super(tm, createMapperFromEntity(MentorAssignedCoursePersistenceEntity), MentorAssignedCoursePersistenceEntity);
    }

    getByUserIds(userIds: Id[], tx?: Transaction): Promise<MentorAssignedCourse[]> {
        ArgumentValidation.assert.notEmpty(userIds, 'User IDs must not be empty');

        return this.getAllWhere(
            { userId: userIds.length === 1 ? userIds[0].value : In(userIds.map((id) => id.value)) },
            tx,
        );
    }

    deleteForUserByCourseIds(userId: Id, courseIds: Id[], tx?: Transaction): Promise<void> {
        return this.deleteAllWhere({ userId: userId.value, courseId: In(courseIds.map((id) => id.value)) }, tx);
    }
}
