import { MentorAssignedCourseService } from '../../services/MentorAssingedCourseService';
import { tokenFor } from '../../../di/helpers/tokenFor';
import { MentorAssignedCourseRepository } from '../db/mentor-assigned-course/MentorAssignedCourseRepository';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import Container from 'typedi';
import { QueueModule } from '../../../queue/infrastructure/di/QueueModule';
import { MentorAssignedCoursesChangedEventWorker } from '../../controllers/events/MentorAssignedCoursesChangedEventWorker';
import { ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandWorker } from '../../controllers/commands/ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandWorker';
import { LoggingModule } from '../../../logging/infrastructure/di/LoggingModule';
import { Configuration } from '../../../config/Configuration';
import { DiscordProfileModule } from '../../../discord-profile/infrastructure/di/DiscordProfileModule';
import { CourseModule } from '../../../education/courses/infrastructure/di/CourseModule';

export class MentorAssignedCourseModule {
    static readonly MENTOR_ASSIGNED_COURSE_SERVICE_TOKEN = tokenFor(MentorAssignedCourseService);
    /** @deprecated: use only in tests */
    static readonly MENTOR_ASSIGNED_COURSE_REPOSITORY_TOKEN = tokenFor(MentorAssignedCourseRepository);

    constructor(readonly mentorAssignedCourseService: MentorAssignedCourseService) {}

    static init(
        configuration: Configuration,
        courseModule: CourseModule,
        transactionManager: TransactionManager,
        queueModule: QueueModule,
        discordProfileModule: DiscordProfileModule,
        loggingModule: LoggingModule,
    ): MentorAssignedCourseModule {
        const mentorAssignedCourseRepository = new MentorAssignedCourseRepository(transactionManager);
        const mentorAssignedCourseService = new MentorAssignedCourseService(
            mentorAssignedCourseRepository,
            courseModule.courseFinder,
            transactionManager,
            queueModule.queueService,
        );

        queueModule.queueWorkerRegistry.registerWorkers([
            new MentorAssignedCoursesChangedEventWorker(queueModule.queueService),
            new ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandWorker(
                configuration,
                queueModule.queueService,
                discordProfileModule.discordProfileService,
                loggingModule.loggingService.createLogger(
                    ChangeMentorDiscordRolesAccordingToAssignedCoursesCommandWorker.name,
                ),
                courseModule.courseFinder,
            ),
        ]);

        Container.set(MentorAssignedCourseModule.MENTOR_ASSIGNED_COURSE_SERVICE_TOKEN, mentorAssignedCourseService);
        Container.set(
            MentorAssignedCourseModule.MENTOR_ASSIGNED_COURSE_REPOSITORY_TOKEN,
            mentorAssignedCourseRepository,
        );

        return new MentorAssignedCourseModule(mentorAssignedCourseService);
    }
}
