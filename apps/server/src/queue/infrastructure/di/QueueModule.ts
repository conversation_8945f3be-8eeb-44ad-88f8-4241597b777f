import PgBoss from 'pg-boss';
import { Configuration } from '../../../config/Configuration';
import { tokenFor } from '../../../di/helpers/tokenFor';
import { QueueHealthService } from '../../QueueHealthService';
import Container from 'typedi';
import { QueueService } from '../../QueueService';
import { QueueWorkerRegistry } from '../../QueueWorkerRegistry';
import { LoggingModule } from '../../../logging/infrastructure/di/LoggingModule';
import ErrorTracker from '../../../error-tracking/infrastructure/services/ErrorTracker';
import Logger from '../../../utils/logger/Logger';
import { QueueScheduleRegistry } from '../../QueueScheduleRegistry';
import { ModuleOnStart, ModuleOnStop } from '../../../di/types/ApplicationModule';

export class QueueModule implements ModuleOnStart, ModuleOnStop {
    static TOKEN = tokenFor(QueueModule);
    static QUEUE_HEALTH_SERVICE_TOKEN = tokenFor(QueueHealthService);
    static QUEUE_SERVICE_TOKEN = tokenFor(QueueService);
    static QUEUE_WORKER_REGISTRY_TOKEN = tokenFor(QueueWorkerRegistry);

    constructor(
        readonly queueService: QueueService,
        readonly queueWorkerRegistry: QueueWorkerRegistry,
        readonly queueHealthService: QueueHealthService,
        readonly queueScheduleRegistry: QueueScheduleRegistry,
        private readonly boss: PgBoss,
        private readonly logger: Logger,
        private readonly errorTracker: ErrorTracker,
    ) {}

    moduleOnStart(): Promise<void> {
        return this.start();
    }

    moduleOnStop(): Promise<void> {
        return this.stop();
    }

    getName(): string {
        return QueueModule.name;
    }

    static init(configuration: Configuration, loggingModule: LoggingModule, errorTracker: ErrorTracker): QueueModule {
        const logger = loggingModule.loggingService.createLogger(QueueModule.name);
        const boss = new PgBoss({
            application_name: 'Turing College Backend',
            database: configuration.db.database,
            user: configuration.db.username,
            password: configuration.db.password,
            host: configuration.db.host,
            port: configuration.db.port,
            monitorStateIntervalMinutes: 10,
        });

        const queueService = new QueueService(boss, loggingModule.loggingService.createLogger(QueueService.name));
        const queueWorkerRegistry = new QueueWorkerRegistry(
            boss,
            loggingModule.loggingService.createLogger(QueueWorkerRegistry.name),
            errorTracker,
        );
        const queueScheduleRegistry = new QueueScheduleRegistry(
            loggingModule.loggingService.createLogger(QueueScheduleRegistry.name),
            errorTracker,
            queueService,
        );
        const queueHealthService = new QueueHealthService(
            boss,
            loggingModule.loggingService.createLogger(QueueHealthService.name),
        );

        Container.set(QueueModule.QUEUE_HEALTH_SERVICE_TOKEN, queueHealthService);
        Container.set(QueueModule.QUEUE_SERVICE_TOKEN, queueService);
        Container.set(QueueModule.QUEUE_WORKER_REGISTRY_TOKEN, queueWorkerRegistry);

        const module = new QueueModule(
            queueService,
            queueWorkerRegistry,
            queueHealthService,
            queueScheduleRegistry,
            boss,
            logger,
            errorTracker,
        );

        Container.set(QueueModule.TOKEN, module);

        return module;
    }

    async start(): Promise<void> {
        this.logger.info('Starting QueueModule...');
        try {
            this.boss.on('error', (error) => {
                this.logger.error('PGBoss error occurred', error);
                this.errorTracker.track(error);
            });

            this.queueWorkerRegistry.subscribeHandlersOnBoss();
            await this.boss.start();
            await this.queueScheduleRegistry.scheduleOnBoss();

            this.logger.info('PGBoss started successfully');
        } catch (e) {
            this.logger.error('Failed to start PGBoss', e);
            this.errorTracker.track(e);
        }
    }

    async stop(): Promise<void> {
        this.logger.debug('STOPPING_PG_BOSS');

        try {
            await this.boss.stop();

            this.logger.debug('PG_BOSS_STOPPED');
        } catch (e) {
            this.logger.error('FAILED_TO_STOP_PG_BOSS', e);
            this.errorTracker.track(e);
        }
    }
}
