import { <PERSON>I<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import Id from '../../../../../core/domain/value-objects/Id';
import LearningComponentName from '../../../../common/LearningComponentName';
import Module from '../../../domain/Module';

export default class BaseModuleDTO {
    @IsInt()
    @Min(Id.MIN_VALUE)
    id: number;

    @IsString()
    @MinLength(LearningComponentName.MIN_LENGTH)
    @MaxLength(LearningComponentName.MAX_LENGTH)
    name: string;

    static fromModule(module: Module): BaseModuleDTO {
        return {
            // @ts-expect-error TS(2322) FIXME: Type 'number | undefined' is not assignable to typ... Remove this comment to see the full error message
            id: module.id?.value,
            name: module.name.name,
        };
    }
}
