import { ArrayContains, Not } from 'typeorm';
import Id from '../../../../core/domain/value-objects/Id';
import TypeormBaseRepository from '../../../../core/infrastructure/db/TypeormBaseRepository';
import Transaction from '../../../../core/infrastructure/Transaction';
import TransactionManager from '../../../../core/infrastructure/TransactionManager';
import { createMapperFromEntity } from '../../../../utils/createMapperFromEntity';
import { SprintPartReviewerPermission } from '../../domain/SprintPartReviewerPermission';
import { SprintPartReviewerPermissionPersistenceEntity } from './SprintPartReviewerPermissionPersistenceEntity';

import { CorrectionType } from '../../../../corrections/domain/CorrectionType';

export class SprintPartReviewerPermissionRepository extends TypeormBaseRepository<
    SprintPartReviewerPermission,
    SprintPartReviewerPermissionPersistenceEntity
> {
    constructor(tm: TransactionManager) {
        super(
            tm,
            createMapperFromEntity(SprintPartReviewerPermissionPersistenceEntity),
            SprintPartReviewerPermissionPersistenceEntity,
        );
    }

    async getAllBySprintPart(sprintPartId: Id, tx?: Transaction): Promise<SprintPartReviewerPermission[]> {
        return this.getAllWhere({ sprintPartId: sprintPartId.value }, tx);
    }

    async getAllByUser(userId: Id, tx?: Transaction): Promise<SprintPartReviewerPermission[]> {
        return this.getAllWhere({ userId: userId.value }, tx);
    }

    async getBySprintPartAndUser(
        {
            sprintPartId,
            userId,
        }: {
            sprintPartId: Id;
            userId: Id;
        },
        tx?: Transaction,
    ): Promise<SprintPartReviewerPermission | undefined> {
        return this.getWhere({ sprintPartId: sprintPartId.value, userId: userId.value }, tx);
    }

    async getAllowedBySprintPart(
        {
            sprintPartId,
            permission,
        }: {
            sprintPartId: Id;
            permission: CorrectionType;
        },
        tx?: Transaction,
    ): Promise<SprintPartReviewerPermission[]> {
        return this.getAllWhere({ sprintPartId: sprintPartId.value, permissions: ArrayContains([permission]) }, tx);
    }

    async getDeniedBySprintPart(
        {
            sprintPartId,
            permission,
        }: {
            sprintPartId: Id;
            permission: CorrectionType;
        },
        tx?: Transaction,
    ): Promise<SprintPartReviewerPermission[]> {
        return this.getAllWhere(
            { sprintPartId: sprintPartId.value, permissions: Not(ArrayContains([permission])) },
            tx,
        );
    }

    async save(permission: SprintPartReviewerPermission, tx?: Transaction): Promise<void> {
        await this.rawRepository.save(this.mapper.toPersistence(permission), tx);
    }

    async saveAll(permissions: SprintPartReviewerPermission[], tx?: Transaction): Promise<void> {
        if (permissions.length === 0) {
            return;
        }

        await this.rawRepository.saveAll(
            permissions.map((permission) => this.mapper.toPersistence(permission)),
            tx,
        );
    }

    async delete({ sprintPartId, userId }: { sprintPartId: Id; userId: Id }, tx?: Transaction): Promise<void> {
        await this.deleteAllWhere({ sprintPartId: sprintPartId.value, userId: userId.value }, tx);
    }
}
