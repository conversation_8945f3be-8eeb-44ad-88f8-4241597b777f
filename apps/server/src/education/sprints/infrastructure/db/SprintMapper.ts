import Id from '../../../../core/domain/value-objects/Id';
import TypeormPersistenceMapper from '../../../../core/infrastructure/db/TypeormPersistenceMapper';
import SkillMapper from '../../../../learning/submodules/skills/infrastructure/db/SkillMapper';
import LearningComponentName from '../../../common/LearningComponentName';
import DeadlineProperties from '../../domain/DeadlineProperties';
import Sprint from '../../domain/Sprint';
import SprintPartMapper from './SprintPartMapper';
import SprintPersistenceEntity from './SprintPersistenceEntity';

export default class SprintMapper implements TypeormPersistenceMapper<Sprint, SprintPersistenceEntity> {
    private readonly skillMapper = new SkillMapper();

    private readonly sprintPartMapper = new SprintPartMapper();

    toDomain(entity: SprintPersistenceEntity): Sprint {
        return new Sprint(
            {
                name: new LearningComponentName(entity.name, entity.internalName),
                deadlineProperties: new DeadlineProperties({
                    a: entity.deadlineProperties.a,
                    b: entity.deadlineProperties.b,
                    c: entity.deadlineProperties.c,
                    d: entity.deadlineProperties.d,
                }),
                parts: entity.parts
                    ?.sort((a, b) => a.position - b.position)
                    .map((part) => this.sprintPartMapper.toDomain(part)),
                skills: entity.skills?.map((skill) => this.skillMapper.toDomain(skill)),
            },
            entity.id ? new Id(entity.id) : undefined,
        );
    }

    toPersistence(sprint: Sprint): SprintPersistenceEntity {
        return SprintPersistenceEntity.fromSprint(sprint);
    }
}
