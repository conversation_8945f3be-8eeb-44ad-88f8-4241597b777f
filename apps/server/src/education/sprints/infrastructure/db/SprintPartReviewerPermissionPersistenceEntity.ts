import { <PERSON>umn, <PERSON><PERSON>ty, <PERSON>ToOne, RelationId } from 'typeorm';
import SprintPartPersistenceEntity from './SprintPartPersistenceEntity';
import User from '../../../../users/shared/infrastructure/db/User';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { SprintPartReviewerPermission } from '../../domain/SprintPartReviewerPermission';
import Id from '../../../../core/domain/value-objects/Id';
import TypeormBasePersistenceEntity from '../../../../core/infrastructure/db/TypeormBasePersistenceEntity';
import { CorrectionType } from '../../../../corrections/domain/CorrectionType';

@Entity(SprintPartReviewerPermissionPersistenceEntity.TABLE_NAME)
export class SprintPartReviewerPermissionPersistenceEntity extends TypeormBasePersistenceEntity {
    private static readonly TABLE_NAME = 'sprint_part_reviewer_permission';

    private static readonly COLUMN_NAME_SPRINT_PART_ID = 'sprint_part_id';

    private static readonly COLUMN_NAME_USER_ID = 'user_id';

    @Column({ name: SprintPartReviewerPermissionPersistenceEntity.COLUMN_NAME_SPRINT_PART_ID, primary: true })
    @RelationId((self: SprintPartReviewerPermissionPersistenceEntity) => self.sprintPart)
    sprintPartId: number;

    @Column({ name: SprintPartReviewerPermissionPersistenceEntity.COLUMN_NAME_USER_ID, primary: true })
    @RelationId((self: SprintPartReviewerPermissionPersistenceEntity) => self.user)
    userId: number;

    @Column({ type: 'enum', enum: CorrectionType, array: true, default: '{}' })
    permissions: CorrectionType[];

    @ManyToOne(() => SprintPartPersistenceEntity, { onDelete: 'CASCADE' })
    private sprintPart: never;

    @ManyToOne(() => User, { onDelete: 'CASCADE' })
    private user: never;

    constructor(params?: NonFunctionProperties<SprintPartReviewerPermissionPersistenceEntity>) {
        super(params?.createdAt, params?.updatedAt);

        Object.assign(this, params);
    }

    static fromDomain(domain: SprintPartReviewerPermission): SprintPartReviewerPermissionPersistenceEntity {
        return new SprintPartReviewerPermissionPersistenceEntity({
            sprintPartId: domain.sprintPartId.value,
            userId: domain.userId.value,
            permissions: domain.permissions,
        });
    }

    toDomain(): SprintPartReviewerPermission {
        return new SprintPartReviewerPermission({
            sprintPartId: new Id(this.sprintPartId),
            userId: new Id(this.userId),
            permissions: this.permissions,
        });
    }
}
