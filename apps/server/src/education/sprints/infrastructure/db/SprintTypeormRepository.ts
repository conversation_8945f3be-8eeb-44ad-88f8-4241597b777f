import { In } from 'typeorm';
import Id from '../../../../core/domain/value-objects/Id';
import Transaction from '../../../../core/infrastructure/Transaction';
import TransactionManager from '../../../../core/infrastructure/TransactionManager';
import TypeormRepository from '../../../../core/infrastructure/db/TypeormRepository';
import LearningComponentName from '../../../common/LearningComponentName';
import LearningComponentSlug from '../../../common/LearningComponentSlug';
import Sprint from '../../domain/Sprint';
import SprintRepository from '../../domain/SprintRepository';
import SprintMapper from './SprintMapper';
import SprintPersistenceEntity from './SprintPersistenceEntity';

export default class SprintTypeormRepository
    extends TypeormRepository<Sprint, SprintPersistenceEntity>
    implements SprintRepository
{
    constructor(tm: TransactionManager) {
        super(tm, new SprintMapper(), SprintPersistenceEntity, ['parts', 'skills', 'skills.category']);
    }

    async getByIds(ids: Id[], tx?: Transaction): Promise<Sprint[]> {
        return await this.getAllWhere({ id: In(ids.map((id) => id.value)) }, tx);
    }

    async getByName(name: LearningComponentName, tx?: Transaction): Promise<Sprint | undefined> {
        return await this.getWhere({ name: name.name }, tx);
    }

    async getByPart(partId: Id, tx?: Transaction): Promise<Sprint | undefined> {
        const sprintWithPart = await this.getWhere({ parts: { id: partId.value } }, tx);
        if (!sprintWithPart) {
            return undefined;
        }

        return await this.get(sprintWithPart.id!, tx);
    }

    async getByPartSlug(partSlug: LearningComponentSlug, tx?: Transaction): Promise<Sprint | undefined> {
        const sprintWithPart = await this.getWhere({ parts: { slug: partSlug.value } }, tx);
        if (!sprintWithPart) {
            return undefined;
        }

        return await this.get(sprintWithPart.id!, tx);
    }
}
