import { faker } from '@faker-js/faker';
import 'reflect-metadata';
import request from 'supertest';
import Container from 'typedi';
import AccessToken from '../../../../auth/domain/AccessToken';
import { Configuration } from '../../../../config/Configuration';
import Id from '../../../../core/domain/value-objects/Id';
import { TransactionManagerToken } from '../../../../core/infrastructure/di/tokens';
import LearningComponentDescription from '../../../common/LearningComponentDescription';
import LearningComponentName from '../../../common/LearningComponentName';
import LearningComponentSlug from '../../../common/LearningComponentSlug';
import BaseSprintPartDTO from './dto/BaseSprintPartDTO';
import EvaluationPropertiesDTO from './dto/EvaluationPropertiesDTO';
import SprintDTO from './dto/SprintDTO';
import SprintPartCreateDTO from './dto/SprintPartCreateDTO';
import SprintPartDTO from './dto/SprintPartDTO';
import SprintUpdateDTO from './dto/SprintUpdateDTO';
import SprintsController from './SprintsController';
import DeadlineProperties from '../../domain/DeadlineProperties';
import SprintRepository from '../../domain/SprintRepository';
import SprintTypeormRepository from '../../infrastructure/db/SprintTypeormRepository';
import GithubRepo from '../../../../github/domain/GithubRepo';
import Mentor from '../../../../users/mentors/infrastructure/db/Mentor';
import Staff from '../../../../users/staff/infrastructure/db/Staff';
import { TestConfigurationService } from '../../../../test-toolkit/unit/deprecated-mocks/TestConfigurationService';
import TestObjects from '../../../../test-toolkit/shared/TestObjects';
import { IntegrationTestUser } from '../../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';
import { IntegrationTestsCreateSprintWithPartsUseCase } from '../../../../test-toolkit/e2e/use-cases/integration-tests-create-sprint-with-parts.use-case';
import { getRandomDeadlineProperties, getRandomSprint, getRandomSprintPart } from './e2e-helpers';

describe(SprintsController.name, () => {
    let configuration: Configuration;
    let sprintRepository: SprintRepository;
    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let mentor: IntegrationTestUser<Mentor>;
    let student: IntegrationTestUser;

    beforeAll(async () => {
        configuration = TestConfigurationService.create().getConfigurationSync();
        sprintRepository = new SprintTypeormRepository(Container.get(TransactionManagerToken));

        ({ admin, student, staff, mentor } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute());

        await IntegrationTestsCreateSprintWithPartsUseCase.execute();
        await IntegrationTestsCreateSprintWithPartsUseCase.execute();
        await IntegrationTestsCreateSprintWithPartsUseCase.execute();
        await IntegrationTestsCreateSprintWithPartsUseCase.execute();
    });

    describe('GET /sprints/:id', () => {
        async function testGet(token?: AccessToken, code = 200): Promise<void> {
            const sprint = faker.helpers.arrayElement(await sprintRepository.getAll());

            const res = await request(configuration.server)
                .get(`/sprints/${sprint.id?.value}`)
                .set('Cookie', `token=${token?.token}`)
                .send()
                .expect(code);

            if (code === 200) {
                expect(res.body).toEqual(SprintDTO.fromSprint(sprint));
            }
        }

        it('should get with authorized users', async () => {
            await testGet(admin.authToken);
            await testGet(staff.authToken);
        });

        it('should not get with unauthorized user', async () => {
            await testGet(mentor.authToken, 403);
            await testGet(student.authToken, 403);
            await testGet(undefined, 403);
        });
    });

    describe('PATCH /sprints/:id', () => {
        let sprintId: Id;

        beforeEach(async () => {
            const sprint = await sprintRepository.save(getRandomSprint());
            sprintId = sprint.id as Id;
        });

        async function testPatch(data: SprintUpdateDTO, token?: AccessToken, code = 200): Promise<void> {
            const sprintBefore = await sprintRepository.getOrFail(sprintId);

            const res = await request(configuration.server)
                .patch(`/sprints/${sprintId.value}`)
                .set('Cookie', `token=${token?.token}`)
                .send(data)
                .expect(code);

            const sprintAfter = await sprintRepository.getOrFail(sprintId);

            if (code === 200) {
                const expectedName = new LearningComponentName(
                    data.name ?? sprintBefore.name.name,
                    data.internalName ?? sprintBefore.name.internalName,
                );
                const expectedDeadlineProperties = new DeadlineProperties(
                    data.deadlineProperties ?? sprintBefore.deadlineProperties,
                );

                expect(res.body).toMatchObject({
                    name: expectedName.name,
                    internalName: expectedName.internalName,
                    deadlineProperties: expectedDeadlineProperties,
                });
                expect(sprintAfter.name).toEqual(expectedName);
                expect(sprintAfter.deadlineProperties).toEqual(expectedDeadlineProperties);
            } else {
                expect(sprintAfter).toEqual(sprintBefore);
            }
        }

        it('should patch with authorized users', async () => {
            await testPatch({ name: TestObjects.uniqueWords() }, admin.authToken);
            await testPatch({ internalName: TestObjects.uniqueWords() }, admin.authToken);
            await testPatch({ deadlineProperties: getRandomDeadlineProperties() }, admin.authToken);
        });

        it('should not patch with unauthorized users', async () => {
            const properties = {
                name: TestObjects.uniqueWords(),
                deadlineProperties: getRandomDeadlineProperties(),
            };

            await testPatch(properties, staff.authToken, 403);
            await testPatch(properties, mentor.authToken, 403);
            await testPatch(properties, student.authToken, 403);
            await testPatch(properties, undefined, 403);
        });
    });

    describe('GET /sprints/:id/parts', () => {
        async function testGet(token?: AccessToken, code = 200): Promise<void> {
            const sprint = faker.helpers.arrayElement(await sprintRepository.getAll());

            const res = await request(configuration.server)
                .get(`/sprints/${sprint.id?.value}/parts`)
                .set('Cookie', `token=${token?.token}`)
                .send()
                .expect(code);

            if (code === 200) {
                expect(res.body).toHaveLength(sprint.parts.length);
                expect(res.body).toEqual(expect.arrayContaining(sprint.parts.map(BaseSprintPartDTO.fromSprintPart)));
            }
        }

        it('should get with authorized users', async () => {
            await testGet(admin.authToken);
            await testGet(staff.authToken);
        });

        it('should not get with unauthorized users', async () => {
            await testGet(mentor.authToken, 403);
            await testGet(student.authToken, 403);
            await testGet(undefined, 403);
        });
    });

    describe('POST /sprints/:id/parts', () => {
        async function testPost(data: SprintPartCreateDTO, token?: AccessToken, code = 200): Promise<void> {
            const sprintBefore = faker.helpers.arrayElement(await sprintRepository.getAll());

            const res = await request(configuration.server)
                .post(`/sprints/${sprintBefore.id?.value}/parts`)
                .set('Cookie', `token=${token?.token}`)
                .send(data)
                .expect(code);

            const sprintAfter = await sprintRepository.getOrFail(sprintBefore.id as Id);
            if (code === 200) {
                const part = sprintAfter.parts[sprintAfter.parts.length - 1];

                expect(res.body).toEqual(SprintPartDTO.fromSprintPart(part));
                expect(sprintAfter.parts).toHaveLength(sprintBefore.parts.length + 1);
                expect(part.slug).toEqual(new LearningComponentSlug(data.slug));
                expect(part.name).toEqual(new LearningComponentName(data.name, data.internalName));
                expect(part.description).toEqual(new LearningComponentDescription(data.description));
                expect(part.contentType).toEqual(data.contentType);
                expect(part.contentRepository).toEqual(
                    new GithubRepo(configuration.github.orgName, data.contentRepositoryName),
                );
                expect(part.solutionRepository).toEqual(
                    data.solutionRepositoryName
                        ? new GithubRepo(configuration.github.orgName, data.solutionRepositoryName)
                        : undefined,
                );
                expect(part.evaluationProperties).toEqual(data.evaluationProperties.toEvaluationProperties());
            } else {
                expect(sprintAfter).toEqual(sprintBefore);
            }
        }

        it('should add part with authorized users', async () => {
            const part = getRandomSprintPart();
            const data = new SprintPartCreateDTO();
            data.slug = part.slug.value;
            data.name = part.name.name;
            data.internalName = part.name.internalName;
            data.description = part.description.value;
            data.contentType = part.contentType;
            data.contentRepositoryName = part.contentRepository.name.value;
            data.solutionRepositoryName = part.solutionRepository?.name.value;
            data.evaluationProperties = EvaluationPropertiesDTO.fromEvaluationProperties(part.evaluationProperties);

            await testPost(data, admin.authToken);
        });

        it('should not add part with unauthorized users', async () => {
            const part = getRandomSprintPart();
            const data = new SprintPartCreateDTO();
            data.slug = part.slug.value;
            data.name = part.name.name;
            data.internalName = part.name.internalName;
            data.description = part.description.value;
            data.contentType = part.contentType;
            data.contentRepositoryName = part.contentRepository.name.value;
            data.solutionRepositoryName = part.solutionRepository?.name.value;
            data.evaluationProperties = EvaluationPropertiesDTO.fromEvaluationProperties(part.evaluationProperties);

            await testPost(data, staff.authToken, 403);
            await testPost(data, mentor.authToken, 403);
            await testPost(data, student.authToken, 403);
            await testPost(data, undefined, 403);
        });

        it('should not add part with taken slug', async () => {
            const part = getRandomSprintPart();
            const data = new SprintPartCreateDTO();
            data.slug = part.slug.value;
            data.name = part.name.name;
            data.internalName = part.name.internalName;
            data.description = part.description.value;
            data.contentType = part.contentType;
            data.contentRepositoryName = part.contentRepository.name.value;
            data.solutionRepositoryName = part.solutionRepository?.name.value;
            data.evaluationProperties = EvaluationPropertiesDTO.fromEvaluationProperties(part.evaluationProperties);

            await testPost(data, admin.authToken);
            await testPost(data, admin.authToken, 400);
        });
    });
});
