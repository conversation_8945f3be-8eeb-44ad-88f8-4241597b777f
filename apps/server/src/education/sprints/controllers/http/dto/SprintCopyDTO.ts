import { IsDefined, Is<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import LearningComponentName from '../../../../common/LearningComponentName';

import { IsId } from '../../../../../core/controllers/validators/IsId';
import LearningComponentSlug from '../../../../common/LearningComponentSlug';
export default class SprintCopyDTO {
    @IsDefined()
    @IsId()
    sprintId: number;

    @IsDefined()
    @IsString()
    @MinLength(1)
    @MaxLength(LearningComponentName.MAX_LENGTH)
    namePrefix: string;

    @IsDefined()
    @IsString()
    @MinLength(1)
    @MaxLength(LearningComponentName.MAX_LENGTH)
    internalNamePrefix: string;

    @IsDefined()
    @IsString()
    @MinLength(1)
    @MaxLength(LearningComponentSlug.MAX_LENGTH)
    slugPrefix: string;
}
