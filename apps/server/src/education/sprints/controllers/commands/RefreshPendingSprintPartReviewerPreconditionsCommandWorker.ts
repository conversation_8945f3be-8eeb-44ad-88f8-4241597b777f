import { QueueWorker } from '../../../../queue/domain/QueueWorker';
import { AppQueues } from '../../../../queue/domain/AppQueues';
import { Job } from 'pg-boss';
import { QueueService } from '../../../../queue/QueueService';
import { RefreshPendingPreconditionsCommand } from '../../../../learning/submodules/roadmap/controllers/commands/dto/RefreshPendingPreconditionsCommand';
import {
    RefreshPendingSprintPartReviewerPreconditionsCommand,
    RefreshPendingSprintPartReviewerPreconditionsCommandDto,
} from './dto/RefreshPendingSprintPartReviewerPreconditionsCommand';
import { SprintPartReviewerPermissionsService } from '../../services/SprintPartReviewerPermissionsService';

export class RefreshPendingSprintPartReviewerPreconditionsCommandWorker implements QueueWorker {
    readonly queuePattern = AppQueues.schema.education.sprintPart.refreshPendingReviewerPreconditions.wildcard;

    readonly options = {
        newJobCheckIntervalSeconds: 1,
        teamSize: 5,
        teamConcurrency: 5,
    };

    constructor(
        private readonly queueService: QueueService,
        private readonly sprintPartReviewerPermissionsService: SprintPartReviewerPermissionsService,
    ) {}

    async handler(job: Job<RefreshPendingSprintPartReviewerPreconditionsCommandDto>): Promise<void> {
        const command = AppQueues.getMapperByClass(RefreshPendingSprintPartReviewerPreconditionsCommand).deserialize(
            job.data,
        );

        const permissions = await this.sprintPartReviewerPermissionsService.getAllBySprintPart(command.sprintPartId);

        return this.queueService.commandMany(
            permissions
                .filter((permission) => permission.permissions.length > 0)
                .map((permission) => ({
                    message: new RefreshPendingPreconditionsCommand({ userId: permission.userId }),
                    options: { subQueue: permission.userId.value },
                })),
        );
    }
}
