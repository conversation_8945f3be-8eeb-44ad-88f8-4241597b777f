import { IsInt, IsOptional, IsString, Min } from 'class-validator';
import ArgumentValidation from '../../../../../core/utils/validation/ArgumentValidation';
import ReviewQuestion from '../../../domain/ReviewQuestion';

export class ReviewQuestionDto {
    @IsInt()
    id: number;

    @IsInt()
    sprintPartId: number;

    @IsString()
    text: string;

    @IsString()
    @IsOptional()
    image?: string | null;

    @IsInt()
    @Min(1)
    weight: number;

    @IsInt()
    position: number;

    static fromDomain(question: ReviewQuestion): ReviewQuestionDto {
        ArgumentValidation.assert.defined(question.id?.value, 'Question ID is required');

        return {
            id: question.id.value,
            sprintPartId: question.sprintPartId.value,
            text: question.text,
            image: question.image,
            weight: question.weight,
            position: question.position,
        };
    }
}
