import { Type } from 'class-transformer';
import { ArrayNotEmpty, IsArray, ValidateNested } from 'class-validator';
import { IsId } from '../../../../../core/controllers/validators/IsId';
import { ReviewQuestionUpdateDto } from './ReviewQuestionUpdateDto';

class QuestionUpdate extends ReviewQuestionUpdateDto {
    @IsId()
    id: number;
}

export class ReviewQuestionBatchUpdateDto {
    @IsArray()
    @ArrayNotEmpty()
    @ValidateNested({ each: true })
    @Type(() => QuestionUpdate)
    questions: QuestionUpdate[];
}
