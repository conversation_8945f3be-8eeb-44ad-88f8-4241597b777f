import { anything, deepEqual, instance, mock, reset, verify, when } from 'ts-mockito';
import Id from '../../../core/domain/value-objects/Id';
import TestObjects from '../../../test-toolkit/shared/TestObjects';
import ReviewQuestion from '../domain/ReviewQuestion';
import ReviewQuestionRepository from '../infrastructure/db/ReviewQuestionRepository';
import { ReviewQuestionService } from './ReviewQuestionService';

const sprintPartId = TestObjects.id();

const testQuestions = [
    {
        sprintPartId,
        id: new Id(1),
        text: 'test question 1',
        image: 'test image 1',
        weight: 1,
        position: 0,
    },
    {
        sprintPartId,
        id: new Id(2),
        text: 'test question 2',
        image: undefined,
        weight: 1,
        position: 1,
    },
].map((entity) => ReviewQuestion.fromParams(entity));

describe(ReviewQuestionService.name, () => {
    const correctionQuestionRepositoryMock = mock<ReviewQuestionRepository>();
    const correctionQuestionService = new ReviewQuestionService(instance(correctionQuestionRepositoryMock));

    beforeEach(() => {
        reset(correctionQuestionRepositoryMock);
    });

    describe('getAll', () => {
        test('should return questions', async () => {
            when(correctionQuestionRepositoryMock.findBySprintPart(anything(), anything())).thenResolve(testQuestions);
            await expect(correctionQuestionService.getBySprintPart(sprintPartId)).resolves.toEqual<ReviewQuestion[]>(
                testQuestions,
            );
        });
    });

    describe('create', () => {
        test('should create question', async () => {
            when(correctionQuestionRepositoryMock.save(anything(), anything())).thenResolve(testQuestions[0]);

            const data = {
                text: testQuestions[0].text,
                image: testQuestions[0].image,
                weight: testQuestions[0].weight,
                position: testQuestions[0].position,
            };

            await expect(correctionQuestionService.create(sprintPartId, data)).resolves.toEqual<ReviewQuestion>(
                testQuestions[0],
            );

            const expectedQuestion = ReviewQuestion.fromParams({
                ...data,
                sprintPartId,
            });
            verify(correctionQuestionRepositoryMock.save(deepEqual(expectedQuestion), anything())).once();
        });

        test('should handle create error', async () => {
            when(correctionQuestionRepositoryMock.save(anything(), anything())).thenThrow(new Error('test'));

            const data = {
                text: testQuestions[0].text,
                image: testQuestions[0].image,
                weight: testQuestions[0].weight,
                position: testQuestions[0].position,
            };

            await expect(correctionQuestionService.create(sprintPartId, data)).rejects.toThrow('test');
            verify(correctionQuestionRepositoryMock.save(anything(), anything())).once();
        });
    });

    describe('update', () => {
        test('should update question', async () => {
            const questionToUpdate = testQuestions[0];

            when(
                correctionQuestionRepositoryMock.findBySprintPart(deepEqual(new Id(sprintPartId.value)), anything()),
            ).thenResolve([questionToUpdate]);
            when(correctionQuestionRepositoryMock.save(anything(), anything())).thenResolve(questionToUpdate);

            const data = {
                text: questionToUpdate.text,
                image: questionToUpdate.image,
                weight: questionToUpdate.weight,
                position: questionToUpdate.position,
            };

            await expect(
                correctionQuestionService.update(sprintPartId, questionToUpdate.id!, data),
            ).resolves.toEqual<ReviewQuestion>(questionToUpdate);

            const expectedQuestion = ReviewQuestion.fromParams({
                id: questionToUpdate.id,
                sprintPartId: sprintPartId,
                ...data,
            });
            verify(correctionQuestionRepositoryMock.save(deepEqual(expectedQuestion), anything())).once();
        });

        test('should not update non-existing question', async () => {
            when(correctionQuestionRepositoryMock.findBySprintPart(anything(), anything())).thenResolve([]);

            const data = {
                text: testQuestions[0].text,
                image: testQuestions[0].image,
                weight: testQuestions[0].weight,
                position: testQuestions[0].position,
            };

            await expect(correctionQuestionService.update(sprintPartId, new Id(1000), data)).rejects.toThrow(
                `Question '1000' of sprint part '${sprintPartId.value}' was not found`,
            );
        });
    });

    describe('updateBatch', () => {
        test('should update questions', async () => {
            const updates = testQuestions.map((q) => ({
                id: q.getIdOrThrow(),
                text: q.text,
                image: q.image,
                weight: q.weight,
                position: q.position,
            }));

            when(correctionQuestionRepositoryMock.findBySprintPart(anything(), anything())).thenResolve(testQuestions);
            when(correctionQuestionRepositoryMock.saveAll(anything(), anything())).thenResolve(testQuestions);

            await expect(correctionQuestionService.updateBatch(sprintPartId, updates)).resolves.toEqual(testQuestions);

            const expectedQuestions = updates.map((update) =>
                ReviewQuestion.fromParams({
                    id: new Id(update.id),
                    sprintPartId: new Id(sprintPartId.value),
                    text: update.text,
                    image: update.image,
                    weight: update.weight,
                    position: update.position,
                }),
            );
            verify(correctionQuestionRepositoryMock.saveAll(deepEqual(expectedQuestions), anything())).once();
        });

        test('should not update questions from different sprint part', async () => {
            const differentSprintPartId = sprintPartId.value + 1;
            when(correctionQuestionRepositoryMock.findBySprintPart(anything(), anything())).thenResolve([]);

            const updates = testQuestions.map((q) => ({
                id: q.getIdOrThrow(),
                text: q.text,
                image: q.image,
                weight: q.weight,
                position: q.position,
            }));

            await expect(correctionQuestionService.updateBatch(new Id(differentSprintPartId), updates)).rejects.toThrow(
                `Question '${updates[0].id}' of sprint part '${differentSprintPartId}' was not found`,
            );

            verify(correctionQuestionRepositoryMock.saveAll(anything(), anything())).never();
        });

        test('should handle database error', async () => {
            const updates = testQuestions.map((q) => ({
                id: q.getIdOrThrow(),
                text: q.text,
                image: q.image,
                weight: q.weight,
                position: q.position,
            }));

            when(correctionQuestionRepositoryMock.findBySprintPart(anything(), anything())).thenResolve(testQuestions);
            when(correctionQuestionRepositoryMock.saveAll(anything(), anything())).thenThrow(new Error('test'));

            await expect(correctionQuestionService.updateBatch(sprintPartId, updates)).rejects.toThrow('test');
        });

        test('should handle empty batch update', async () => {
            await expect(correctionQuestionService.updateBatch(sprintPartId, [])).rejects.toThrow(
                'No questions were selected to update',
            );
            verify(correctionQuestionRepositoryMock.saveAll(anything(), anything())).never();
        });
    });

    describe('delete', () => {
        test('should delete', async () => {
            when(correctionQuestionRepositoryMock.findBySprintPart(anything(), anything())).thenResolve([
                testQuestions[0],
            ]);
            when(correctionQuestionRepositoryMock.delete(anything(), anything())).thenResolve();

            const questionId = new Id(testQuestions[0].id!.value);
            await expect(correctionQuestionService.delete(sprintPartId, questionId)).toResolve();
            verify(correctionQuestionRepositoryMock.delete(deepEqual(questionId), anything())).once();
        });

        test('should not delete non-existent question', async () => {
            when(correctionQuestionRepositoryMock.findBySprintPart(anything(), anything())).thenResolve([]);

            await expect(correctionQuestionService.delete(sprintPartId, new Id(999))).rejects.toThrow(
                `Question '999' of sprint part '${sprintPartId.value}' was not found`,
            );

            verify(correctionQuestionRepositoryMock.delete(anything(), anything())).never();
        });

        test('should handle database error', async () => {
            when(correctionQuestionRepositoryMock.findBySprintPart(anything(), anything())).thenResolve([
                testQuestions[0],
            ]);
            when(correctionQuestionRepositoryMock.delete(anything(), anything())).thenThrow(new Error('test'));

            await expect(correctionQuestionService.delete(sprintPartId, new Id(1))).rejects.toThrow('test');
        });
    });
});
