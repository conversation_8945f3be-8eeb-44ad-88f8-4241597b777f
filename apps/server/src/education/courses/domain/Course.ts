import DomainEntity from '../../../core/domain/DomainEntity';
import Id from '../../../core/domain/value-objects/Id';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';
import DiscordRoleId from '../../../discord/domain/DiscordRoleId';
import LearningComponentName from '../../common/LearningComponentName';
import ModuleSlot from './ModuleSlot';
import { CourseOnboardingConnection } from './CourseOnboardingConnection';
import { ReviewsPassingScore } from './value-objects/ReviewsPassingScore';

interface CourseProperties {
    name: LearningComponentName | string;
    isEndorsementEnabled?: boolean;
    studentDiscordRoleId?: DiscordRoleId | string | null;
    mentorDiscordRoleId?: DiscordRoleId | string | null;
    onboardingConnections: CourseOnboardingConnection[];
    slots?: ReadonlyArray<ModuleSlot>;
    reviewsPassingScore?: ReviewsPassingScore;
}

export default class Course extends DomainEntity {
    private static readonly DEFAULT_IS_ENDORSEMENT_ENABLED = true;

    readonly name: LearningComponentName;

    readonly isEndorsementEnabled: boolean;

    readonly studentDiscordRoleId?: DiscordRoleId;

    readonly mentorDiscordRoleId?: DiscordRoleId;

    readonly onboardingConnections: CourseOnboardingConnection[];

    readonly slots: ReadonlyArray<ModuleSlot>;

    readonly reviewsPassingScore: ReviewsPassingScore;

    // @ts-expect-error TS(2322) FIXME: Type 'undefined' is not assignable to type 'Id'.
    constructor(properties: CourseProperties, id: Id = undefined) {
        super(id);

        ArgumentValidation.assert.defined(properties, 'Course properties are required');

        this.name = new LearningComponentName(properties.name);
        this.isEndorsementEnabled = properties.isEndorsementEnabled ?? Course.DEFAULT_IS_ENDORSEMENT_ENABLED;
        this.studentDiscordRoleId = properties.studentDiscordRoleId
            ? new DiscordRoleId(properties.studentDiscordRoleId)
            : undefined;
        this.mentorDiscordRoleId = properties.mentorDiscordRoleId
            ? new DiscordRoleId(properties.mentorDiscordRoleId)
            : undefined;
        this.slots = properties.slots ? [...properties.slots] : [];
        this.validateSlots(this.slots);
        this.onboardingConnections = properties.onboardingConnections;
        this.reviewsPassingScore = properties.reviewsPassingScore ?? ReviewsPassingScore.buildDefault();
    }

    rename(name: LearningComponentName): Course {
        return new Course({ ...this, name }, this.id);
    }

    enableEndorsement(): Course {
        return new Course({ ...this, isEndorsementEnabled: true }, this.id);
    }

    disableEndorsement(): Course {
        return new Course({ ...this, isEndorsementEnabled: false }, this.id);
    }

    changeStudentDiscordRole(studentDiscordRoleId?: DiscordRoleId | string | null): Course {
        return new Course({ ...this, studentDiscordRoleId }, this.id);
    }

    changeMentorDiscordRole(mentorDiscordRoleId: DiscordRoleId | string): Course {
        return new Course({ ...this, mentorDiscordRoleId }, this.id);
    }

    changeOnboardingConnections(onboardingConnections: CourseOnboardingConnection[]): Course {
        ArgumentValidation.assert.notEmpty(onboardingConnections, 'At least one onboarding connection is required');

        return new Course({ ...this, onboardingConnections }, this.id);
    }

    changeReviewsPassingScore(reviewsPassingScore: ReviewsPassingScore): Course {
        return new Course({ ...this, reviewsPassingScore }, this.id);
    }

    addModuleSlot(moduleSlot: ModuleSlot): Course {
        ArgumentValidation.assert.true(
            moduleSlot.moduleIds?.length > 0,
            'Module slot needs to have at least one module',
        );

        return new Course(
            {
                ...this,
                slots: [...this.slots, moduleSlot],
            },
            this.id,
        );
    }

    changeModuleSlot(slotId: Id, moduleSlot: ModuleSlot): Course {
        ArgumentValidation.assert.defined(slotId, 'Module slot id is required');
        ArgumentValidation.assert.true(
            moduleSlot.moduleIds?.length > 0,
            'Module slot needs to have at least one module',
        );

        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        const slotIndex = this.slots.findIndex((slot) => slot.id.equals(slotId));
        ArgumentValidation.assert.true(slotIndex >= 0, 'Module slot not found');

        return new Course(
            {
                ...this,
                slots: this.slots.map((slot, i) => (i === slotIndex ? moduleSlot : slot)),
            },
            this.id,
        );
    }

    deleteModuleSlot(slotId: Id): Course {
        ArgumentValidation.assert.defined(slotId, 'Module slot id is required');

        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        const slotIndex = this.slots.findIndex((slot) => slot.id.equals(slotId));
        ArgumentValidation.assert.true(slotIndex >= 0, 'Module slot not found');

        return new Course({ ...this, slots: this.slots.filter((s, i) => i !== slotIndex) }, this.id);
    }

    getSlotById(slotId: Id): ModuleSlot | undefined {
        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
        return this.slots.find((slot) => slot.id.equals(slotId));
    }

    private validateSlots(slots: ReadonlyArray<ModuleSlot>, selectedModules: ReadonlyArray<Id> = []): void {
        if (slots.length === 0) {
            return;
        }

        const currentPosition = selectedModules.length;
        const [current, ...remaining] = slots;
        const availableModules = current.moduleIds.filter((id) => !selectedModules.some((sid) => sid.equals(id)));
        ArgumentValidation.assert.min(
            availableModules.length,
            1,
            `Module slot '${currentPosition + 1}' might not have a valid module option`,
        );

        availableModules.forEach((id) => this.validateSlots(remaining, [...selectedModules, id]));
    }
}
