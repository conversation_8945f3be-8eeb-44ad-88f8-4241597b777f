import {
    Authorized,
    Body,
    Delete,
    Get,
    Http<PERSON><PERSON>,
    JsonController,
    NotFoundError,
    Param,
    Patch,
    Post,
    Put,
} from 'routing-controllers';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import responses from '../../../../core/controllers/docs/responses';
import Arrays from '../../../../core/collections/Arrays';
import Id from '../../../../core/domain/value-objects/Id';
import { Role } from '../../../../users/shared/infrastructure/db/User';
import { ModuleFinderToken } from '../../../modules/infrastructure/di/tokens';
import ModuleFinder from '../../../modules/services/ModuleFinder';
import Course from '../../domain/Course';
import { CourseManagerToken } from '../../infrastructure/di/tokens';
import CourseManager from '../../services/CourseManager';
import CourseCreateDTO from './dto/CourseCreateDTO';
import CourseDTO from './dto/CourseDTO';
import CourseUpdateDTO from './dto/CourseUpdateDTO';
import ModuleSlotCreateDTO from './dto/ModuleSlotCreateDTO';
import ModuleSlotDTO from './dto/ModuleSlotDTO';
import ModuleSlotUpdateDTO from './dto/ModuleSlotUpdateDTO';

@Service()
@JsonController('/courses')
export default class CoursesController {
    constructor(
        @Inject(CourseManagerToken)
        private readonly courseManager: CourseManager,
        @Inject(ModuleFinderToken)
        private readonly moduleFinder: ModuleFinder,
    ) {}

    @Authorized([Role.ADMIN, Role.STAFF, Role.MENTOR])
    @Get()
    @ResponseSchema(CourseDTO, { isArray: true })
    @OpenAPI({
        responses,
        summary: 'Returns all courses',
        security: [{ cookieAuth: [] }],
    })
    async getAll(): Promise<CourseDTO[]> {
        const courses = await this.courseManager.findAll();

        return courses.map(CourseDTO.fromCourse);
    }

    @Authorized(Role.ADMIN)
    @Post()
    @ResponseSchema(CourseDTO)
    @OpenAPI({
        responses,
        summary: 'Creates a course',
        security: [{ cookieAuth: [] }],
    })
    async create(@Body() data: CourseCreateDTO): Promise<CourseDTO> {
        const course = await this.courseManager.create({
            name: data.name,
            internalName: data.internalName,
            isEndorsementEnabled: data.isEndorsementEnabled,
            onboardingConnections: data.onboardingConnections,
            studentDiscordRoleId: data.studentDiscordRoleId,
            mentorDiscordRoleId: data.mentorDiscordRoleId,
            reviewsPassingScore: data.reviewsPassingScore?.toDomain(),
        });

        return CourseDTO.fromCourse(course);
    }

    @Authorized(Role.ADMIN)
    @Patch('/:id')
    @ResponseSchema(CourseDTO)
    @OpenAPI({
        responses,
        summary: 'Updates a course',
        security: [{ cookieAuth: [] }],
    })
    async update(@Param('id') id: number, @Body() data: CourseUpdateDTO): Promise<CourseDTO> {
        const course = await this.courseManager.update(new Id(id), {
            name: data.name,
            internalName: data.internalName,
            isEndorsementEnabled: data.isEndorsementEnabled,
            studentDiscordRoleId: data.studentDiscordRoleId,
            mentorDiscordRoleId: data.mentorDiscordRoleId,
            onboardingConnections: data.onboardingConnections,
            reviewsPassingScore: data.reviewsPassingScore?.toDomain(),
        });

        return CourseDTO.fromCourse(course);
    }

    @Authorized(Role.ADMIN)
    @Delete('/:id')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Deletes a course',
        security: [{ cookieAuth: [] }],
    })
    async delete(@Param('id') id: number): Promise<void> {
        await this.courseManager.delete(new Id(id));
    }

    @Authorized([Role.ADMIN, Role.STAFF])
    @Get('/:id/slots')
    @ResponseSchema(ModuleSlotDTO, { isArray: true })
    @OpenAPI({
        responses,
        summary: 'Get course module slots',
        security: [{ cookieAuth: [] }],
    })
    async getModuleSlots(@Param('id') id: number): Promise<ModuleSlotDTO[]> {
        const course = await this.courseManager.findById(new Id(id));
        if (!course) {
            throw new NotFoundError('Course not found');
        }

        return this.getCourseModuleSlots(course);
    }

    @Authorized([Role.ADMIN])
    @Post('/:id/slots')
    @ResponseSchema(ModuleSlotDTO, { isArray: true })
    @OpenAPI({
        responses,
        summary: 'Create course module slot',
        security: [{ cookieAuth: [] }],
    })
    async createModuleSlot(@Param('id') id: number, @Body() data: ModuleSlotCreateDTO): Promise<ModuleSlotDTO[]> {
        const course = await this.courseManager.addModuleSlot(new Id(id), {
            moduleIds: data.moduleIds.map((mid) => new Id(mid)),
            standupAttendanceRequirement: data.standupAttendanceRequirement,
        });
        if (!course) {
            throw new NotFoundError('Course not found');
        }

        return this.getCourseModuleSlots(course);
    }

    @Authorized([Role.ADMIN])
    @Put('/:courseId/slots/:slotId')
    @ResponseSchema(ModuleSlotDTO, { isArray: true })
    @OpenAPI({
        responses,
        summary: 'Update course module slot',
        security: [{ cookieAuth: [] }],
    })
    async updateModuleSlot(
        @Param('courseId') courseId: number,
        @Param('slotId') slotId: number,
        @Body() data: ModuleSlotUpdateDTO,
    ): Promise<ModuleSlotDTO[]> {
        const course = await this.courseManager.updateModuleSlot(new Id(courseId), new Id(slotId), {
            moduleIds: data.moduleIds.map((mid) => new Id(mid)),
            standupAttendanceRequirement: data.standupAttendanceRequirement,
        });
        if (!course) {
            throw new NotFoundError('Course not found');
        }

        return this.getCourseModuleSlots(course);
    }

    @Authorized([Role.ADMIN])
    @Delete('/:courseId/slots/:slotId')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Delete course module slot',
        security: [{ cookieAuth: [] }],
    })
    async deleteModuleSlot(@Param('courseId') courseId: number, @Param('slotId') slotId: number): Promise<void> {
        await this.courseManager.deleteModuleSlot(new Id(courseId), new Id(slotId));
    }

    private async getCourseModuleSlots(course: Course): Promise<ModuleSlotDTO[]> {
        const moduleIds = Arrays.unique(course.slots.flatMap((s) => s.moduleIds));
        const modules = await this.moduleFinder.findByIds(moduleIds);

        return ModuleSlotDTO.fromCourseAndModules(course, modules);
    }
}
