import Id from '../../../core/domain/value-objects/Id';
import ImageString from '../../../core/domain/value-objects/ImageString';
import Transaction from '../../../core/infrastructure/Transaction';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import QuizAnswer from '../domain/QuizAnswer';
import QuizQuestion from '../domain/QuizQuestion';
import QuizQuestionRepository from '../domain/QuizQuestionRepository';

export interface QuizAnswerProperties {
    text: string;
    isCorrect: boolean;
    position: number;
}

export interface QuizQuestionProperties {
    text: string;
    image?: string;
    answers: QuizAnswerProperties[];
    isRepeatable: boolean;
}

export class QuizQuestionService {
    constructor(
        private readonly tm: TransactionManager,
        private readonly quizQuestionRepository: QuizQuestionRepository,
    ) {}

    async get(id: Id, tx?: Transaction): Promise<QuizQuestion | undefined> {
        return await this.quizQuestionRepository.get(id, tx);
    }

    async getBySprintParts(partIds: Id[], tx?: Transaction): Promise<QuizQuestion[]> {
        return await this.quizQuestionRepository.getBySprintParts(partIds, tx);
    }

    async getRepeatableBySprintParts(partIds: Id[], tx?: Transaction): Promise<QuizQuestion[]> {
        return await this.quizQuestionRepository.getRepeatableBySprintParts(partIds, tx);
    }

    async create(sprintPartId: Id, data: QuizQuestionProperties, tx?: Transaction): Promise<QuizQuestion | undefined> {
        const answers = data.answers.map(
            (answer) =>
                new QuizAnswer({
                    text: answer.text,
                    isCorrect: answer.isCorrect,
                    position: answer.position,
                }),
        );
        const quizQuestion = new QuizQuestion({
            sprintPartId,
            text: data.text,
            image: data.image ? new ImageString(data.image) : undefined,
            isRepeatable: data.isRepeatable,
            answers,
        });

        return await this.quizQuestionRepository.save(quizQuestion, tx);
    }

    async update(id: Id, data: QuizQuestionProperties, tx?: Transaction): Promise<QuizQuestion | undefined> {
        const question = await this.quizQuestionRepository.get(id, tx);
        if (!question) {
            return undefined;
        }

        return await this.quizQuestionRepository.save(
            question
                .changeText(data.text)
                .changeImage(data.image ? new ImageString(data.image) : undefined)
                .changeAnswers(
                    data.answers.map(
                        (a) =>
                            new QuizAnswer({
                                text: a.text,
                                isCorrect: a.isCorrect,
                                position: a.position,
                            }),
                    ),
                )
                .changeIsRepeatable(data.isRepeatable),
            tx,
        );
    }

    async copySprintPartQuestions(
        params: { sourceSprintPartId: Id; targetSprintPartId: Id },
        tx?: Transaction,
    ): Promise<void> {
        const sourceQuestions = await this.quizQuestionRepository.getBySprintParts([params.sourceSprintPartId], tx);
        if (sourceQuestions.length === 0) {
            return;
        }

        const newQuestions = sourceQuestions.map((question) => {
            return new QuizQuestion({
                sprintPartId: params.targetSprintPartId,
                text: question.text,
                image: question.image,
                isRepeatable: question.isRepeatable,
                answers: question.answers.map((answer) => {
                    return new QuizAnswer({
                        text: answer.text,
                        isCorrect: answer.isCorrect,
                        position: answer.position,
                    });
                }),
            });
        });

        await this.quizQuestionRepository.saveAll(newQuestions, tx);
    }

    async delete(id: Id, tx?: Transaction): Promise<void> {
        await this.quizQuestionRepository.delete(id, tx);
    }
}
