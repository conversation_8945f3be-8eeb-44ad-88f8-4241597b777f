import DomainEntity from '../../../core/domain/DomainEntity';
import Id from '../../../core/domain/value-objects/Id';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';

interface QuizAnswerProperties {
    text: string;
    isCorrect: boolean;
    position: number;
}

export default class QuizAnswer extends DomainEntity {
    readonly text: string;

    readonly isCorrect: boolean;

    readonly position: number;

    constructor(properties: QuizAnswerProperties, id?: Id) {
        super(id);

        const { text } = properties;
        ArgumentValidation.assert.notEmpty(text, 'Quiz answer text cannot be empty');
        ArgumentValidation.assert.defined(properties.isCorrect, 'Quiz answer is correct flag is required');
        ArgumentValidation.assert.defined(properties.position, 'Quiz answer position is required');

        this.text = text;
        this.isCorrect = properties.isCorrect;
        this.position = properties.position;
    }
}
