import TypeormAuditPersistenceEntityProvider from '../../../core/infrastructure/db/TypeormAuditPersistenceEntityProvider';
import { CalendarSettingsPersistenceEntity } from './CalendarSettingsPersistenceEntity';
import { CalendarSettingsAuditPersistenceEntity } from './CalendarSettingsAuditPersistenceEntity';
import { Objects } from '../../../utils/Objects';
import { AuditAction } from '../../../core/infrastructure/db/TypeormAuditPersistenceEntity';
import RequestContext from '../../../core/infrastructure/context/RequestContext';

export class CalendarSettingsAuditPersistenceEntityProvider
    implements
        TypeormAuditPersistenceEntityProvider<
            CalendarSettingsPersistenceEntity,
            CalendarSettingsAuditPersistenceEntity
        >
{
    insert(
        entity: CalendarSettingsPersistenceEntity,
        context?: RequestContext,
    ): CalendarSettingsAuditPersistenceEntity {
        return new CalendarSettingsAuditPersistenceEntity({
            ...Objects.omit(entity, ['id', 'createdAt', 'updatedAt']),
            // @ts-expect-error TS(2322) FIXME: Type 'number | undefined' is not assignable to typ... Remove this comment to see the full error message
            calendarSettingsId: entity.id,
            auditAction: AuditAction.INSERT,
            auditUserId: context?.userId?.value,
            auditUserIp: context?.ip?.value,
            auditTimestamp: new Date(),
        });
    }

    update(
        entity: CalendarSettingsPersistenceEntity,
        context?: RequestContext,
    ): CalendarSettingsAuditPersistenceEntity {
        return new CalendarSettingsAuditPersistenceEntity({
            ...Objects.omit(entity, ['id', 'createdAt', 'updatedAt']),
            // @ts-expect-error TS(2322) FIXME: Type 'number | undefined' is not assignable to typ... Remove this comment to see the full error message
            calendarSettingsId: entity.id,
            auditAction: AuditAction.UPDATE,
            auditUserId: context?.userId?.value,
            auditUserIp: context?.ip?.value,
            auditTimestamp: new Date(),
        });
    }

    delete(
        entity: CalendarSettingsPersistenceEntity,
        context?: RequestContext,
    ): CalendarSettingsAuditPersistenceEntity {
        return new CalendarSettingsAuditPersistenceEntity({
            ...Objects.omit(entity, ['id', 'createdAt', 'updatedAt']),
            // @ts-expect-error TS(2322) FIXME: Type 'number | undefined' is not assignable to typ... Remove this comment to see the full error message
            calendarSettingsId: entity.id,
            auditAction: AuditAction.DELETE,
            auditUserId: context?.userId?.value,
            auditUserIp: context?.ip?.value,
            auditTimestamp: new Date(),
        });
    }
}
