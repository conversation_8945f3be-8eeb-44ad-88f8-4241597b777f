import TypeormAuditingRepository from '../../../core/infrastructure/db/TypeormAuditingRepository';
import { CalendarSettings } from '../../domain/CalendarSettings';
import { CalendarSettingsPersistenceEntity } from './CalendarSettingsPersistenceEntity';
import { CalendarSettingsAuditPersistenceEntity } from './CalendarSettingsAuditPersistenceEntity';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import RequestContextStorage from '../../../core/infrastructure/context/RequestContextStorage';
import { CalendarSettingsAuditPersistenceEntityProvider } from './CalendarSettingsAuditPersistenceEntityProvider';
import Id from '../../../core/domain/value-objects/Id';
import Transaction from '../../../core/infrastructure/Transaction';
import { createMapperFromEntity } from '../../../utils/createMapperFromEntity';
import { In } from 'typeorm';

export class CalendarSettingsRepository extends TypeormAuditingRepository<
    CalendarSettings,
    CalendarSettingsPersistenceEntity,
    CalendarSettingsAuditPersistenceEntity
> {
    constructor(transactionManager: TransactionManager, requestContextStorage: RequestContextStorage) {
        super(
            transactionManager,
            requestContextStorage,
            createMapperFromEntity<CalendarSettings, CalendarSettingsPersistenceEntity>(
                CalendarSettingsPersistenceEntity,
            ),
            new CalendarSettingsAuditPersistenceEntityProvider(),
            CalendarSettingsPersistenceEntity,
            CalendarSettingsAuditPersistenceEntity,
        );
    }

    async getByUser(userId: Id, tx?: Transaction): Promise<CalendarSettings | undefined> {
        return await this.getWhere({ userId: userId.value }, tx);
    }

    async getByUsers(userIds: Id[], tx?: Transaction): Promise<CalendarSettings[]> {
        if (userIds.length === 0) {
            return [];
        }

        return await this.getAllWhere({ userId: In(userIds.map((id) => id.value)) }, tx);
    }
}
