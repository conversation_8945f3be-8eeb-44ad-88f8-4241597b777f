import DomainEntity from '../../core/domain/DomainEntity';
import Id from '../../core/domain/value-objects/Id';
import { NonFunctionProperties } from '../../utils/UtilityTypes';
import ArgumentValidation from '../../core/utils/validation/ArgumentValidation';
import { Objects } from '../../utils/Objects';
import Time from '../../utils/Time';

type CalendarSettingsConstructorProperties = NonFunctionProperties<CalendarSettings>;

export type CalendarSettingsUpdateProperties = Partial<Omit<CalendarSettingsConstructorProperties, 'userId' | 'id'>>;

export class CalendarSettings extends DomainEntity {
    static readonly MIN_DAILY_REVIEW_LIMIT = 1;

    static readonly MAX_DAILY_REVIEW_LIMIT = 9;

    static readonly DEFAULT_DAILY_REVIEW_LIMIT = 3;

    static readonly MIN_TIME_BEFORE_REVIEW_MS = Time.hoursToMillis(1);

    static readonly MAX_TIME_BEFORE_REVIEW_MS = Time.hoursToMillis(48);

    static readonly DEFAULT_TIME_BEFORE_REVIEW_MS = Time.hoursToMillis(6);

    static readonly MIN_TIME_BETWEEN_REVIEWS_MS = Time.minutesToMillis(15);

    static readonly MAX_TIME_BETWEEN_REVIEWS_MS = Time.minutesToMillis(60);

    static readonly DEFAULT_TIME_BETWEEN_REVIEWS_MS = Time.minutesToMillis(15);

    readonly userId: Id;

    /**
     * The number of reviews that can be done in a day.
     */
    readonly dailyReviewLimit: number;

    /**
     * Minimum number of milliseconds before a review when scheduling.
     */
    readonly timeBeforeReview: number;

    /**
     * Minimum number of milliseconds between reviews.
     */
    readonly timeBetweenReviews: number;

    constructor(params: CalendarSettingsConstructorProperties) {
        super(params.id);

        Object.assign(this, params);

        ArgumentValidation.assert.between(
            this.dailyReviewLimit,
            CalendarSettings.MIN_DAILY_REVIEW_LIMIT,
            CalendarSettings.MAX_DAILY_REVIEW_LIMIT,
            `Daily review limit must be between ${CalendarSettings.MIN_DAILY_REVIEW_LIMIT} and ${CalendarSettings.MAX_DAILY_REVIEW_LIMIT}. Was ${this.dailyReviewLimit}.`,
        );
        ArgumentValidation.assert.between(
            this.timeBeforeReview,
            CalendarSettings.MIN_TIME_BEFORE_REVIEW_MS,
            CalendarSettings.MAX_TIME_BEFORE_REVIEW_MS,
            `Time before review must be between ${CalendarSettings.MIN_TIME_BEFORE_REVIEW_MS} and ${CalendarSettings.MAX_TIME_BEFORE_REVIEW_MS}. Was ${this.timeBeforeReview}.`,
        );
        ArgumentValidation.assert.between(
            this.timeBetweenReviews,
            CalendarSettings.MIN_TIME_BETWEEN_REVIEWS_MS,
            CalendarSettings.MAX_TIME_BETWEEN_REVIEWS_MS,
            `Time between reviews must be between ${CalendarSettings.MIN_TIME_BETWEEN_REVIEWS_MS} and ${CalendarSettings.MAX_TIME_BETWEEN_REVIEWS_MS}. Was ${this.timeBetweenReviews}.`,
        );
    }

    static createWithDefaults(userId: Id, overrides?: Partial<CalendarSettingsUpdateProperties>): CalendarSettings {
        return new CalendarSettings({
            userId,
            dailyReviewLimit: CalendarSettings.DEFAULT_DAILY_REVIEW_LIMIT,
            timeBeforeReview: CalendarSettings.DEFAULT_TIME_BEFORE_REVIEW_MS,
            timeBetweenReviews: CalendarSettings.DEFAULT_TIME_BETWEEN_REVIEWS_MS,
            ...overrides,
        });
    }

    update(params: CalendarSettingsUpdateProperties): CalendarSettings {
        return new CalendarSettings({
            ...this,
            ...Objects.omitNullOrUndefined(params),
        });
    }
}
