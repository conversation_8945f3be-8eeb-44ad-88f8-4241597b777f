import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { Configuration } from '../../../config/Configuration';
import Container from 'typedi';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import { Role } from '../../../users/shared/infrastructure/db/User';
import request from 'supertest';
import { CalendarSettings } from '../../domain/CalendarSettings';
import { CalendarSettingsService } from '../../services/CalendarSettingsService';

async function initCalendarSettings(users: IntegrationTestUser<any>[]): Promise<void> {
    const calendarSettingsService = Container.get(CalendarSettingsService);

    await Promise.all(users.map((user) => calendarSettingsService.initCalendarSettings(user.getIdOrThrow())));
}

describe('CalendarSettingsController', () => {
    let configuration: Configuration;
    const users: IntegrationTestUser<any>[] = [];

    beforeAll(async () => {
        configuration = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        const { admin, student, staff, mentor } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute();
        users.push(admin, student, staff, mentor);
        await initCalendarSettings([student, mentor]);
    });

    describe('GET /users/:userId/settings/calendar', () => {
        const authTestCases = [
            {
                description: 'should get mentor calendar settings with admin',
                actorRole: Role.ADMIN,
                userRole: Role.MENTOR,
                expectedCode: 200,
            },
            {
                description: 'should get student calendar settings with admin',
                actorRole: Role.ADMIN,
                userRole: Role.USER,
                expectedCode: 200,
            },
            {
                description: 'should get mentor calendar settings with staff',
                actorRole: Role.STAFF,
                userRole: Role.MENTOR,
                expectedCode: 200,
            },
            {
                description: 'should get student calendar settings with staff',
                actorRole: Role.STAFF,
                userRole: Role.USER,
                expectedCode: 200,
            },
            {
                description: 'should get mentor calendar settings with mentor',
                actorRole: Role.MENTOR,
                userRole: Role.MENTOR,
                expectedCode: 200,
            },
            {
                description: 'should not get student calendar settings with mentor',
                actorRole: Role.MENTOR,
                userRole: Role.USER,
                expectedCode: 403,
            },
            {
                description: 'should not get mentor calendar settings with student',
                actorRole: Role.USER,
                userRole: Role.MENTOR,
                expectedCode: 403,
            },
            {
                description: 'should get student calendar settings with student',
                actorRole: Role.USER,
                userRole: Role.USER,
                expectedCode: 200,
            },
        ];

        it.each(authTestCases)('$description', async ({ actorRole, userRole, expectedCode }) => {
            const actor = users.find((user) => user.params.role === actorRole);
            const user = users.find((user) => user.params.role === userRole);

            await request(configuration.server)
                .get(`/users/${user?.getIdOrThrow().value}/settings/calendar`)
                .set('Cookie', `token=${actor?.getAuthTokenString()}`)
                .send()
                .expect(expectedCode);
        });
    });

    describe('PATCH /users/:userId/settings/calendar', () => {
        const authTestCases = [
            {
                description: 'should update mentor calendar settings with admin',
                actorRole: Role.ADMIN,
                userRole: Role.MENTOR,
                expectedCode: 204,
            },
            {
                description: 'should update student calendar settings with admin',
                actorRole: Role.ADMIN,
                userRole: Role.USER,
                expectedCode: 204,
            },
            {
                description: 'should not update mentor calendar settings with staff',
                actorRole: Role.STAFF,
                userRole: Role.MENTOR,
                expectedCode: 403,
            },
            {
                description: 'should not update student calendar settings with staff',
                actorRole: Role.STAFF,
                userRole: Role.USER,
                expectedCode: 403,
            },
            {
                description: 'should update mentor calendar settings with mentor',
                actorRole: Role.MENTOR,
                userRole: Role.MENTOR,
                expectedCode: 204,
            },
            {
                description: 'should not update student calendar settings with mentor',
                actorRole: Role.MENTOR,
                userRole: Role.USER,
                expectedCode: 403,
            },
            {
                description: 'should not update mentor calendar settings with student',
                actorRole: Role.USER,
                userRole: Role.MENTOR,
                expectedCode: 403,
            },
            {
                description: 'should update student calendar settings with student',
                actorRole: Role.USER,
                userRole: Role.USER,
                expectedCode: 204,
            },
        ];

        it.each(authTestCases)('$description', async ({ actorRole, userRole, expectedCode }) => {
            const actor = users.find((user) => user.params.role === actorRole);
            const user = users.find((user) => user.params.role === userRole);

            await request(configuration.server)
                .patch(`/users/${user?.getIdOrThrow().value}/settings/calendar`)
                .set('Cookie', `token=${actor?.getAuthTokenString()}`)
                .send({
                    dailyReviewLimit: CalendarSettings.DEFAULT_DAILY_REVIEW_LIMIT,
                })
                .expect(expectedCode);
        });
    });
});
