import { validate as validateUuid } from 'uuid';
import validator from 'validator';
import { Nullish } from '../../../utils/UtilityTypes';
import IllegalArgumentError from '../../errors/IllegalArgumentError';
import { ArgumentValidationIs } from './ArgumentValidationIs';

interface WithLength {
    length?: Nullish<number>;
}

interface WithTimeGetter {
    getTime(): number;
}

/**
 * Helper functions that validates argument and throws error if validation fails
 * */
export class ArgumentValidationAssert {
    private readonly isValidation = new ArgumentValidationIs();

    defined<T>(arg: T, message = 'Argument is not defined'): asserts arg is Exclude<T, null | undefined> {
        if (!this.isValidation.defined(arg)) {
            throw new IllegalArgumentError(message);
        }
    }

    true(arg: Nullish<boolean>, message = 'Argument must be true'): asserts arg is true {
        if (arg !== true) {
            throw new IllegalArgumentError(message);
        }
    }

    false(arg: Nullish<boolean>, message = 'Argument must be false'): asserts arg is false {
        if (arg !== false) {
            throw new IllegalArgumentError(message);
        }
    }

    truthy(arg: any, message = 'Argument must be truly'): void {
        if (!arg) {
            throw new IllegalArgumentError(message);
        }
    }

    falsy(arg: any, message = 'Argument must be falsy'): void {
        if (!!arg) {
            throw new IllegalArgumentError(message);
        }
    }

    min(arg: Nullish<number>, min: number, message = `Argument has to be >= ${min}`): asserts arg is number {
        this.number(arg);

        if (arg! < min) {
            throw new IllegalArgumentError(message);
        }
    }

    max(arg: Nullish<number>, max: number, message = `Argument has to be <= ${max}`): asserts arg is number {
        this.number(arg);

        if (arg > max) {
            throw new IllegalArgumentError(message);
        }
    }

    between(
        arg: Nullish<number>,
        min: number,
        max: number,
        message = `Argument has to be between ${min} and ${max}`,
    ): asserts arg is number {
        this.number(arg);

        this.min(arg, min, message);
        this.max(arg, max, message);
    }

    number(arg: Nullish<number>, message = 'Argument must be a number'): asserts arg is number {
        this.defined(arg);

        if (isNaN(arg)) {
            throw new IllegalArgumentError(message);
        }
    }

    minLength(
        arg: Nullish<WithLength>,
        min: number,
        message = `Argument has to have length >= ${min}`,
    ): asserts arg is WithLength {
        this.defined(arg?.length);

        if (arg.length < min) {
            throw new IllegalArgumentError(message);
        }
    }

    maxLength(
        arg: Nullish<WithLength>,
        max: number,
        message = `Argument has to have length <= ${max}`,
    ): asserts arg is WithLength {
        this.defined(arg?.length);

        if (arg.length > max) {
            throw new IllegalArgumentError(message);
        }
    }

    lengthBetween(
        arg: Nullish<WithLength>,
        min: number,
        max: number,
        message = `Argument has to have length between ${min} and ${max}`,
    ): asserts arg is WithLength {
        this.defined(arg);

        this.minLength(arg, min, message);
        this.maxLength(arg, max, message);
    }

    notEmpty(arg: Nullish<WithLength>, message = 'Argument cannot be empty'): asserts arg is WithLength {
        this.defined(arg);
        this.minLength(arg, 1, message);
    }

    email(arg: Nullish<string>, message = 'Argument must be a valid email'): asserts arg is string {
        this.defined(arg);

        if (!validator.isEmail(arg)) {
            throw new IllegalArgumentError(message);
        }
    }

    enum<EV, ET extends { [key: string]: EV }>(arg: Nullish<EV>, enm: ET): asserts arg is ET[keyof ET] {
        if (!this.isValidation.enum(arg, enm)) {
            throw new IllegalArgumentError(`Argument value ${arg} is not a valid enum value`);
        }
    }

    before(
        arg: Nullish<WithTimeGetter>,
        limit: WithTimeGetter,
        message = `Argument has to be before '${limit}`,
    ): asserts arg is WithTimeGetter {
        this.number(arg?.getTime());

        if (arg?.getTime() >= limit.getTime()) {
            throw new IllegalArgumentError(message);
        }
    }

    sameOrBefore(
        arg: Nullish<WithTimeGetter>,
        limit: WithTimeGetter,
        message = `Argument has to be same or before '${limit}`,
    ): asserts arg is WithTimeGetter {
        this.number(arg?.getTime());

        if (arg?.getTime() > limit.getTime()) {
            throw new IllegalArgumentError(message);
        }
    }

    after(
        arg: Nullish<WithTimeGetter>,
        limit: WithTimeGetter,
        message = `Argument has to be after '${limit}`,
    ): asserts arg is WithTimeGetter {
        this.number(arg?.getTime());

        if (arg?.getTime() <= limit.getTime()) {
            throw new IllegalArgumentError(message);
        }
    }

    sameOrAfter(
        arg: Nullish<WithTimeGetter>,
        limit: WithTimeGetter,
        message = `Argument has to be same or after '${limit}`,
    ): asserts arg is WithTimeGetter {
        this.number(arg?.getTime());

        if (arg?.getTime() < limit.getTime()) {
            throw new IllegalArgumentError(message);
        }
    }

    lessThan(argA: Nullish<number>, argB: Nullish<number>, message = 'Argument A should be less than B'): void {
        if (!this.isValidation.defined(argA) || !this.isValidation.defined(argB) || isNaN(argA) || isNaN(argB)) {
            throw new IllegalArgumentError(message);
        }

        if (argA < argB) {
            return;
        }

        throw new IllegalArgumentError(message);
    }

    lessOrEqualTo(
        argA: Nullish<number>,
        argB: Nullish<number>,
        message = 'Argument A should be less or equal to B',
    ): void {
        if (!this.isValidation.defined(argA) || !this.isValidation.defined(argB) || isNaN(argA) || isNaN(argB)) {
            throw new IllegalArgumentError(message);
        }

        if (argA <= argB) {
            return;
        }

        throw new IllegalArgumentError(message);
    }

    greaterThan(argA: Nullish<number>, argB: Nullish<number>, message = 'Argument A should be greater than B'): void {
        if (!this.isValidation.defined(argA) || !this.isValidation.defined(argB) || isNaN(argA) || isNaN(argB)) {
            throw new IllegalArgumentError(message);
        }

        if (argA > argB) {
            return;
        }

        throw new IllegalArgumentError(message);
    }

    greaterOrEqualTo(
        argA: Nullish<number>,
        argB: Nullish<number>,
        message = 'Argument A should be greater or equal to B',
    ): void {
        if (!this.isValidation.defined(argA) || !this.isValidation.defined(argB) || isNaN(argA) || isNaN(argB)) {
            throw new IllegalArgumentError(message);
        }

        if (argA >= argB) {
            return;
        }

        throw new IllegalArgumentError(message);
    }

    matchesPattern(str: Nullish<string>, pattern: RegExp, message = 'Argument has a wrong format'): void {
        if (!this.isValidation.matchingPattern(str, pattern)) {
            throw new IllegalArgumentError(message);
        }
    }

    notMatchesPattern(str: Nullish<string>, pattern: RegExp, message = 'Argument has a wrong format'): void {
        if (this.isValidation.matchingPattern(str, pattern)) {
            throw new IllegalArgumentError(message);
        }
    }

    uuid(value: string, message = 'Argument has to be a valid UUID string'): void {
        this.true(validateUuid(value), message);
    }

    phoneNumber(str: string, message = 'Argument is not a phone number'): void {
        this.matchesPattern(str, /^\+[1-9]\d{1,14}$/, message);
    }

    ip(str: string, message = 'Argument is not an IP'): void {
        this.true(!!str && validator.isIP(str), message);
    }

    url(str: string, message = 'Argument is not a URL'): void {
        this.true(!!str && validator.isURL(str, { require_tld: false }), message);
    }

    fail(message = 'Validation failed'): never {
        throw new IllegalArgumentError(message);
    }
}
