import Url from './Url';
import IllegalArgumentError from '../../errors/IllegalArgumentError';

describe('Url', () => {
    describe('constructor', () => {
        test('should not allow undefined URL value', () => {
            expect(() => new Url(undefined as unknown as string)).toThrow(IllegalArgumentError);
        });

        test('should not allow empty URL value', () => {
            expect(() => new Url('')).toThrow(IllegalArgumentError);
        });

        test('should not allow illegal URL value', () => {
            expect(() => new Url('https://1234567890')).toThrow(IllegalArgumentError);
        });
    });

    describe('addPath', () => {
        test('should add path to root', () => {
            expect(new Url('https://example.com').addPath('test').value).toEqual('https://example.com/test');
        });

        test('should add path to another path', () => {
            expect(new Url('https://example.com/test').addPath('path').value).toEqual('https://example.com/test/path');
        });

        test('should add multi-path', () => {
            expect(new Url('https://example.com').addPath('test/path').value).toEqual('https://example.com/test/path');
        });

        test('should add path with slashes', () => {
            expect(new Url('https://example.com').addPath('/test/path/').value).toEqual(
                'https://example.com/test/path',
            );
        });

        test('should add number path ', () => {
            expect(new Url('https://example.com').addPath(1).value).toEqual('https://example.com/1');
        });

        test('should add an empty path ', () => {
            expect(new Url('https://example.com').addPath('').value).toEqual('https://example.com/');
        });

        test('should not add undefined path', () => {
            expect(() => new Url('https://example.com').addPath(undefined as unknown as string)).toThrow(
                IllegalArgumentError,
            );
        });
    });

    describe('addQuery', () => {
        test('add simple query', () => {
            expect(new Url('https://github.com').addQuery('q', 'test').value).toEqual('https://github.com/?q=test');
        });

        test('add multiple simple queries', () => {
            expect(new Url('https://github.com').addQuery('q', 'test').addQuery('page', '1').value).toEqual(
                'https://github.com/?q=test&page=1',
            );
        });

        test('add query with a URL', () => {
            expect(new Url('https://github.com').addQuery('q', 'https://github.com').value).toEqual(
                'https://github.com/?q=https%3A%2F%2Fgithub.com',
            );
        });

        test('add query with space', () => {
            expect(new Url('https://github.com').addQuery('q', 'test test').value).toEqual(
                'https://github.com/?q=test+test',
            );
        });

        test('add query with special characters', () => {
            expect(new Url('https://github.com').addQuery('q', 'test!@$%^&*()_+{}|:"<>').value).toEqual(
                'https://github.com/?q=test%21%40%24%25%5E%26*%28%29_%2B%7B%7D%7C%3A%22%3C%3E',
            );
        });

        test('should not double escape query', () => {
            expect(
                new Url('https://github.com').addQuery('q1', 't1:test one').addQuery('q2', 't2:test two').value,
            ).toEqual('https://github.com/?q1=t1%3Atest+one&q2=t2%3Atest+two');
        });
    });
});
