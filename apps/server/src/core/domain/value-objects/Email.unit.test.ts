import Email from './Email';

describe('Email', () => {
    describe('isGmail', () => {
        test('should recognise gmail addresses', () => {
            expect(new Email('<EMAIL>').isGmail()).toBeTruthy();
            expect(new Email('<EMAIL>').isGmail()).toBeTruthy();
            expect(new Email('<EMAIL>').isGmail()).toBeTruthy();
            expect(new Email('<EMAIL>').isGmail()).toBeTruthy();
            expect(new Email('<EMAIL>').isGmail()).toBeTruthy();
        });

        test('should not recognise non-gmail addresses', () => {
            expect(new Email('<EMAIL>').isGmail()).toBeFalsy();
            expect(new Email('<EMAIL>').isGmail()).toBeFalsy();
            expect(new Email('<EMAIL>').isGmail()).toBeFalsy();
            expect(new Email('<EMAIL>').isGmail()).toBeFalsy();
        });
    });

    describe('isGoogleMail', () => {
        test('should recognise googlemail addresses', () => {
            expect(new Email('<EMAIL>').isGoogleMail()).toBeTruthy();
            expect(new Email('<EMAIL>').isGoogleMail()).toBeTruthy();
            expect(new Email('<EMAIL>').isGoogleMail()).toBeTruthy();
            expect(new Email('<EMAIL>').isGoogleMail()).toBeTruthy();
            expect(new Email('<EMAIL>').isGoogleMail()).toBeTruthy();
        });

        test('should not recognise non-googlemail addresses', () => {
            expect(new Email('<EMAIL>').isGoogleMail()).toBeFalsy();
            expect(new Email('<EMAIL>').isGoogleMail()).toBeFalsy();
            expect(new Email('<EMAIL>').isGoogleMail()).toBeFalsy();
            expect(new Email('<EMAIL>').isGoogleMail()).toBeFalsy();
        });
    });

    describe('removeDots', () => {
        test('should remove dots', () => {
            expect(new Email('<EMAIL>').removeDots().value).toEqual('<EMAIL>');
            expect(new Email('<EMAIL>').removeDots().value).toEqual('<EMAIL>');
            expect(new Email('<EMAIL>').removeDots().value).toEqual('<EMAIL>');
            expect(new Email('<EMAIL>').removeDots().value).toEqual('<EMAIL>');
        });
    });

    describe('changeDomain', () => {
        test('should change domain', () => {
            expect(new Email('<EMAIL>').changeDomain('example.com').value).toEqual('<EMAIL>');
            expect(new Email('<EMAIL>').changeDomain('example.com').value).toEqual('<EMAIL>');
            expect(new Email('<EMAIL>').changeDomain('example.com').value).toEqual('<EMAIL>');
            expect(new Email('<EMAIL>').changeDomain('example.com').value).toEqual('<EMAIL>');
        });
    });
});
