import { DateRange } from './DateRange';
import ArgumentValidation from '../../utils/validation/ArgumentValidation';
import { Timezone } from '../types/Timezone';
import moment from 'moment';
import 'moment-timezone';
import SystemError from '../../errors/SystemError';
import { Day } from './Day';
import { DateStringWithoutTime } from '../../../../../shared/src/common/types/date-string';

export class Week {
    private readonly start: moment.Moment;
    private readonly end: moment.Moment;

    protected constructor(
        readonly range: DateRange,
        readonly timezone: Timezone,
    ) {
        ArgumentValidation.assert.true(range.isBoundariesInclusive(), 'Week boundaries should be inclusive');

        ArgumentValidation.assert.true(
            Week.isValidStartOfWeek(range.from, timezone),
            `Week should start at Monday 00:00 (${timezone}), received ${moment.tz(range.from, timezone).format()}`,
        );
        this.start = moment.tz(range.from, timezone);

        ArgumentValidation.assert.true(
            Week.isValidEndOfWeek(range.to, timezone),
            `Week should end at Sunday 23:59:59 (${timezone}), received ${moment.tz(range.to, timezone).format()}`,
        );
        this.end = moment.tz(range.to, timezone);

        const diff = this.end.diff(this.start, 'days');

        // Not requiring any fixed amount since it can be different due to daylight saving time
        // Since we have requirements about ends of the week, we can just check that it is less than 2 weeks
        ArgumentValidation.assert.true(diff <= 8, `Week should be 7 days long, received ${diff}`);
    }

    next(): Week {
        return this.jump(1);
    }

    previous(): Week {
        return this.jump(-1);
    }

    jump(amount: number): Week {
        return Week.fromDate(
            moment.tz(this.range.from, this.timezone).add(3, 'day').add(amount, 'week').toDate(),
            this.timezone,
        );
    }

    diff(other: Week): number {
        if (this.timezone !== other.timezone) {
            throw new SystemError('Cannot compare weeks in different timezones');
        }

        return this.start.diff(other.start, 'weeks');
    }

    isBefore(other: Week): boolean {
        if (this.timezone !== other.timezone) {
            throw new SystemError('Cannot compare weeks in different timezones');
        }

        return this.start.isBefore(other.start);
    }

    isAfter(other: Week): boolean {
        if (this.timezone !== other.timezone) {
            throw new SystemError('Cannot compare weeks in different timezones');
        }

        return this.start.isAfter(other.start);
    }

    equals(other: Week): boolean {
        if (this.timezone !== other.timezone) {
            throw new SystemError('Cannot compare weeks in different timezones');
        }

        return this.start.isSame(other.start);
    }

    switchTimezone(timezone: Timezone): Week {
        if (this.timezone === timezone) {
            return this;
        }

        return Week.fromDate(this.start.add(3, 'day').toDate(), timezone);
    }

    getRelativeWeekNumber(relativeTo: Week): number {
        return this.start.diff(relativeTo.start, 'weeks') + 1;
    }

    getRangeFromThisWeekStartToNextWeekEnd(): DateRange {
        return DateRange.fromDatesInclusive(this.start.toDate(), this.end.clone().add(1, 'week').toDate());
    }

    order(): number {
        return this.range.from.getTime();
    }

    getDays(): Day[] {
        const days: Day[] = [];
        const current = this.start.clone();

        while (current.isSameOrBefore(this.end)) {
            days.push(Day.fromDate(current.toDate(), this.timezone));
            current.add(1, 'day');
        }

        ArgumentValidation.assert.true(days.length === 7, 'Week should have 7 days');

        return days;
    }

    toStartDateString(): DateStringWithoutTime {
        return this.start.format('YYYY-MM-DD');
    }

    toJson(): object {
        return {
            start: this.start.format(),
            end: this.end.format(),
            timezone: this.timezone,
        };
    }

    static fromDates(start: Date, end: Date, timezone: Timezone): Week {
        return new Week(DateRange.fromDatesInclusive(start, end), timezone);
    }

    static fromDate(dateWithinWeek: Date, timezone: Timezone): Week {
        return new Week(
            DateRange.fromDatesInclusive(
                Week.getStartOfWeek(dateWithinWeek, timezone),
                Week.getEndOfWeek(dateWithinWeek, timezone),
            ),
            timezone,
        );
    }

    static fromDay(day: Day): Week {
        return new Week(
            DateRange.fromDatesInclusive(
                Week.getStartOfWeek(day.toDate(), day.getTimezone()),
                Week.getEndOfWeek(day.toDate(), day.getTimezone()),
            ),
            day.getTimezone(),
        );
    }

    /*
     * Returns all weeks in the given range
     * Will include edge weeks even if they are not fully in the range
     * */
    static getAllForRange(range: DateRange, timezone: Timezone): Week[] {
        if (range.hasInfiniteBound()) {
            throw new SystemError('Cannot get weeks for infinite range');
        }

        const start = Week.getStartOfWeek(range.from, timezone);
        const end = Week.getEndOfWeek(range.to, timezone);

        const weeks: Week[] = [];
        let current = moment.tz(start, timezone);
        while (range.isUpperBoundInclusive() ? current.isSameOrBefore(end) : current.isBefore(end)) {
            weeks.push(Week.fromDate(current.toDate(), timezone));
            current = current.clone().add(7, 'days');
        }

        return weeks;
    }

    static fromRange(range: DateRange, timezone: Timezone): Week {
        return new Week(range, timezone);
    }

    static current(timezone: Timezone): Week {
        return this.fromDate(new Date(), timezone);
    }

    private static isValidStartOfWeek(start: Date, timezone: Timezone): boolean {
        return +this.getStartOfWeek(start, timezone) === +start;
    }

    private static isValidEndOfWeek(end: Date, timezone: Timezone): boolean {
        const endWeek = this.getEndOfWeek(end, timezone);
        return +endWeek === +end;
    }

    private static getStartOfWeek(date: Date, timezone: Timezone): Date {
        return moment.tz(date, timezone).isoWeekday(1).startOf('isoWeek').toDate();
    }

    private static getEndOfWeek(date: Date, timezone: Timezone): Date {
        return moment.tz(date, timezone).isoWeekday(1).endOf('isoWeek').toDate();
    }
}
