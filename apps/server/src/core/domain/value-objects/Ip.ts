import ArgumentValidation from '../../utils/validation/ArgumentValidation';
import SingleValue from './SingleValue';

export default class Ip extends SingleValue<string> {
    protected validate(value: string): string {
        const result = value?.trim();
        ArgumentValidation.assert.defined(result, 'IP value is required');
        ArgumentValidation.assert.ip(result, 'IP value is invalid');
        return result;
    }
}
