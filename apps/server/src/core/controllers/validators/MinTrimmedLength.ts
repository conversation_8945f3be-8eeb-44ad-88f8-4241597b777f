import {
    registerDecorator,
    ValidationArguments,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';
import validator from 'validator';

@ValidatorConstraint({ name: 'minTrimmedLength' })
export class MinTrimmedLengthConstraint implements ValidatorConstraintInterface {
    validate(value: any, args: ValidationArguments): boolean {
        const [minLength] = args.constraints;
        return (
            typeof value === 'string' &&
            typeof minLength === 'number' &&
            validator.isLength(validator.trim(value), { min: minLength })
        );
    }

    defaultMessage(): string {
        return '$property length is less than $constraint1';
    }
}

export function MinTrimmedLength(minLength: number, validationOptions?: ValidationOptions) {
    return (object: any, propertyName: string): void => {
        registerDecorator({
            target: object.constructor,
            propertyName,
            options: validationOptions,
            constraints: [minLength],
            validator: MinTrimmedLengthConstraint,
        });
    };
}
