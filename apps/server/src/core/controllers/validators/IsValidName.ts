import {
    registerDecorator,
    ValidationOptions,
    ValidatorConstraint,
    ValidatorConstraintInterface,
} from 'class-validator';
import FullName from '../../domain/value-objects/user/FullName';

@ValidatorConstraint({ name: 'isValidName' })
export class IsValidNameConstraint implements ValidatorConstraintInterface {
    validate(value: any): boolean {
        return !!value && (value.match(FullName.FORBIDDEN_SYMBOLS_REGEX) || []).length === 0;
    }

    defaultMessage(): string {
        return '$property contains invalid characters';
    }
}

export function IsValidName(validationOptions?: ValidationOptions) {
    return (object: any, propertyName: string): void => {
        registerDecorator({
            target: object.constructor,
            propertyName,
            options: validationOptions,
            validator: IsValidNameConstraint,
        });
    };
}
