import { Request, Response } from 'express';
import { ExpressMiddlewareInterface, Middleware } from 'routing-controllers';
import Container, { Service } from 'typedi';
import { RequestContextStorageToken } from '../di/tokens';
import RequestContext from './RequestContext';

@Service()
@Middleware({ type: 'before' })
export default class RequestContextMiddleware implements ExpressMiddlewareInterface {
    private readonly storage = Container.get(RequestContextStorageToken);

    use(req: Request, res: Response, next: (err?: any) => any): void {
        if (req.ip) {
            this.storage.runWith(next, RequestContext.builder().withIp(req.ip).build());
        }
    }
}
