import { DataSource, EntityManager } from 'typeorm';
import Transaction from '../Transaction';
import TransactionManager, { ExecuteParams, TransactionalAction } from '../TransactionManager';
import { DatabaseErrorMapper } from './errors/DatabaseErrorMapper';

export default class TypeormTransactionManager implements TransactionManager {
    private static readonly ERROR_CODE_FOREIGN_KEY_VIOLATION = '23503';

    private static readonly ERROR_CODE_UNIQUE_VIOLATION = '23505';

    /**
     * TypeORM save method does not work well with a default 'READ COMMITTED' isolation level.
     *
     * Example scenario.
     *
     * There is an object: { id: 1, name: null, surname: null }
     * Two operations: A (change name) and B (change surname), happen in this order:
     * A reads, B reads, A updates, A commits, B updates, B commits.
     *
     * TypeORM save operations takes a full object, does one more read and compares what has changed.
     * With a 'READ COMMITTED' isolation level, the second read of B will see the changes made by the A.
     * However, it locally has a name = null, it thinks that it is being changes and overrides A's change.
     */
    private static readonly ISOLATION_LEVEL = 'REPEATABLE READ';

    private readonly entityManager: EntityManager;

    constructor(readonly datasource: DataSource) {
        this.entityManager = this.datasource.manager;
    }

    async execute<Result>(
        actionOrParams: TransactionalAction<Result> | ExecuteParams<any>,
        currentTransaction?: Transaction,
    ): Promise<Result> {
        const { action, transaction, errorMapper } =
            actionOrParams instanceof Function
                ? { action: actionOrParams, transaction: currentTransaction, errorMapper: undefined }
                : actionOrParams;

        return await this.executeAction(action, transaction, errorMapper);
    }

    private async executeAction<Result>(
        action: TransactionalAction<Result>,
        currentTransaction?: Transaction,
        errorMapper?: DatabaseErrorMapper,
    ): Promise<Result> {
        try {
            if (currentTransaction) {
                return await action(currentTransaction);
            }

            return await this.entityManager.transaction(
                TypeormTransactionManager.ISOLATION_LEVEL,
                async (transactionEntityManager) => {
                    const transaction = new Transaction(transactionEntityManager);

                    const result = await action(transaction);
                    await Promise.all(
                        transaction.beforeCommitActions.map((beforeCommitAction) => beforeCommitAction()),
                    );

                    return result;
                },
            );
        } catch (e) {
            throw errorMapper?.map(e) ?? e;
        }
    }
}
