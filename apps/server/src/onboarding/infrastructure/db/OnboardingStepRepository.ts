import TypeormRepository from '../../../core/infrastructure/db/TypeormRepository';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import { createMapperFromEntity } from '../../../utils/createMapperFromEntity';
import { OnboardingStepPersistenceEntity } from './OnboardingStepPersistenceEntity';
import { OnboardingStep } from '../../domain/OnboardingStep';
import { OnboardingStepType } from '../../domain/OnboardingStepType';
import { In } from 'typeorm';
import Transaction from '../../../core/infrastructure/Transaction';

export class OnboardingStepRepository extends TypeormRepository<OnboardingStep, OnboardingStepPersistenceEntity> {
    constructor(tm: TransactionManager) {
        super(tm, createMapperFromEntity(OnboardingStepPersistenceEntity), OnboardingStepPersistenceEntity, [
            'childrenSteps',
        ]);
    }

    async getAllByTypes(types: OnboardingStepType[], tx?: Transaction): Promise<OnboardingStep[]> {
        return types.length > 0 ? this.getAllWhere({ type: In(types) }, tx) : [];
    }

    async getByType(type: OnboardingStepType, tx?: Transaction): Promise<OnboardingStep | undefined> {
        return this.getWhere({ type }, tx);
    }
}
