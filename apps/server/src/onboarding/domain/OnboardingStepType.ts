export type OnboardingStepType = OnboardingMainStepType | OnboardingSetupAccountSubStepType;

export enum OnboardingMainStepType {
    GET_ACCEPTED = 'get_accepted',
    FINALIZE_DOCUMENTS = 'finalize_documents',
    SETUP_ACCOUNT = 'setup_account',
    UZT_INFORMATION = 'uzt_information',
}

export enum OnboardingSetupAccountSubStepType {
    PERSONAL_INFORMATION = 'personal_information',
    ACADEMIC_BACKGROUND = 'academic_background',
    EMPLOYMENT_HISTORY = 'employment_history',
    COMPLIANCE_INFORMATION = 'compliance_information',
    DISCORD_SETUP = 'discord_setup',
    GITHUB_SETUP = 'github_setup',
    GOOGLE_SETUP = 'google_setup',
}

export const ONBOARDING_STEP_TYPES = {
    ...OnboardingMainStepType,
    ...OnboardingSetupAccountSubStepType,
};
