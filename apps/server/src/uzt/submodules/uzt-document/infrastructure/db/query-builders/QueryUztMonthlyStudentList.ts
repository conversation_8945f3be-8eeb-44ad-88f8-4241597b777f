import moment from 'moment';
import { <PERSON>rackets, EntityManager } from 'typeorm';
import { SelectQueryBuilder } from 'typeorm/query-builder/SelectQueryBuilder';
import Arrays from '../../../../../../core/collections/Arrays';
import { DateRange } from '../../../../../../core/domain/value-objects/DateRange';
import Id from '../../../../../../core/domain/value-objects/Id';
import { Ordering } from '../../../../../../core/domain/value-objects/Ordering';
import { Pagination } from '../../../../../../core/domain/value-objects/Pagination';
import { DocumentState } from '../../../../../../document/domain/DocumentWorkflow';
import DocumentPersistenceEntity from '../../../../../../document/infrastructure/db/document/DocumentPersistenceEntity';
import { GovAgencyType } from '../../../../../../gov/submodules/gov-agency/domain/GovAgencyType';
import { GovAgencyPersistenceEntity } from '../../../../../../gov/submodules/gov-agency/infrastructure/db/GovAgencyPersistenceEntity';
import { GovGroupPersistenceEntity } from '../../../../../../gov/submodules/gov-group/infrastructure/db/GovGroupPersistenceEntity';
import { GovProfilePersistenceEntity } from '../../../../../../gov/submodules/gov-profile/infrastructure/db/gov-profile/GovProfilePersistenceEntity';
import { GovProgramPersistenceEntity } from '../../../../../../gov/submodules/gov-program/infrastructure/db/GovProgramPersistenceEntity';
import { UserState } from '../../../../../../users/accounts/domain/UserState';
import User from '../../../../../../users/shared/infrastructure/db/User';
import StudentSettingsAuditPersistenceEntity from '../../../../../../users/students/settings/db/StudentSettingsAuditPersistenceEntity';
import StudentSettingsPersistenceEntity from '../../../../../../users/students/settings/db/StudentSettingsPersistenceEntity';
import { BillingType } from '../../../../../../users/students/settings/domain/BillingType';
import { UztDocumentType } from '../../../domain/documents/UztDocumentType';
import { UztDocumentState, UztDocumentWorkflow } from '../../../domain/documents/UztDocumentWorkflow';
import { UztMonthlyStudent } from '../../../domain/UztMonthlyStudent';
import { UztDocumentPersistenceEntity } from '../uzt-document/UztDocumentPersistenceEntity';

export enum GetUztMonthlyStudentListSortableField {
    LEARNER_FULL_NAME = 'learnerFullName',
    GOV_GROUP_NAME = 'govGroupName',
    ATASKAITA_STATE = `${UztDocumentType.ATASKAITA}State`,
    SASKAITA_STATE = `${UztDocumentType.SASKAITA}State`,
    ZINIARASTIS_STATE = `${UztDocumentType.ZINIARASTIS}State`,
    AKTAS_STATE = `${UztDocumentType.AKTAS}State`,
    TVARKARASTIS_STATE = `${UztDocumentType.TVARKARASTIS}State`,
    CERTIFICATE_STATE = `${UztDocumentType.CERTIFICATE}State`,
    PRIEDAS_STATE = `${UztDocumentType.PRIEDAS}State`,
}

export interface QueryUztMonthlyStudentListParams {
    month: Date;
    pagination: Pagination;
    filtering: {
        student?: string;
        govGroupId?: Id[];
        states?: Partial<Record<UztDocumentType, Array<DocumentState | UztDocumentState | null>>>;
    };
    ordering?: Ordering<Record<GetUztMonthlyStudentListSortableField, any>>;
}

export class QueryUztMonthlyStudentList {
    private static readonly documentStateColumns = Object.values(UztDocumentType).reduce(
        (acc, type) => {
            return { ...acc, [type]: `${type}_state` };
        },
        {} as Record<UztDocumentType, string>,
    );
    private static readonly documentStateExpressions = Object.values(UztDocumentType).reduce(
        (acc, type) => {
            return {
                ...acc,
                [type]: `
                        MAX(CASE WHEN uzt_document.type = '${type}'
                            THEN (CASE
                                WHEN uzt_document.state = '${UztDocumentState.UNSET}'
                                THEN document.state
                                ELSE uzt_document.state
                            END)
                        END)
                    `,
            };
        },
        {} as Record<UztDocumentType, string>,
    );

    private readonly subQuery: SelectQueryBuilder<any>;

    constructor(
        private readonly params: QueryUztMonthlyStudentListParams,
        private readonly entityManager: EntityManager,
    ) {
        this.subQuery = this.buildSubquery();
    }

    async executeGetCount(): Promise<number> {
        const countQuery = this.entityManager
            .createQueryBuilder()
            .addCommonTableExpression(this.subQuery, 'cte')
            .select('COUNT(*)', 'count')
            .from('cte', 'cte');

        const result = await countQuery.execute();

        return result?.[0]?.count || 0;
    }

    async executeGetData(): Promise<UztMonthlyStudent[]> {
        let dataQuery = this.entityManager
            .createQueryBuilder()
            .addCommonTableExpression(this.subQuery, 'cte')
            .select('*')
            .from('cte', 'cte')
            .limit(this.params.pagination.pageSize)
            .offset(this.params.pagination.offset);

        dataQuery = this.addDocumentStateFiltering(dataQuery);
        dataQuery = this.addOrdering(dataQuery);

        const result = await dataQuery.execute();

        return result.map((row: any) => {
            const certificateState = row[QueryUztMonthlyStudentList.documentStateColumns[UztDocumentType.CERTIFICATE]];
            const priedasState = row[QueryUztMonthlyStudentList.documentStateColumns[UztDocumentType.PRIEDAS]];

            return new UztMonthlyStudent({
                id: new Id(row.user_id),
                fullName: row.username ? `${row.name} ${row.surname} | ${row.username}` : '',
                govGroupName: row.group_name,
                govGroupId: new Id(row.group_id),
                ataskaitaState:
                    row[QueryUztMonthlyStudentList.documentStateColumns[UztDocumentType.ATASKAITA]] ||
                    DocumentState.NEW,
                saskaitaState:
                    row[QueryUztMonthlyStudentList.documentStateColumns[UztDocumentType.SASKAITA]] || DocumentState.NEW,
                ziniarastisState:
                    row[QueryUztMonthlyStudentList.documentStateColumns[UztDocumentType.ZINIARASTIS]] ||
                    DocumentState.NEW,
                aktasState:
                    row[QueryUztMonthlyStudentList.documentStateColumns[UztDocumentType.AKTAS]] || DocumentState.NEW,
                tvarkarastisState:
                    row[QueryUztMonthlyStudentList.documentStateColumns[UztDocumentType.TVARKARASTIS]] ||
                    DocumentState.NEW,
                priedasState: row['is_first_month'] ? priedasState || DocumentState.NEW : priedasState,
                certificateState: row['is_last_month'] ? certificateState || DocumentState.NEW : certificateState,
            });
        });
    }

    private buildSubquery(): SelectQueryBuilder<any> {
        const endOfMonth = moment(this.params.month).utc().endOf('month').toDate();
        const monthRange = DateRange.fromDatesInclusive(this.params.month, endOfMonth);

        let subQuery = this.entityManager
            .createQueryBuilder(GovProfilePersistenceEntity, 'gov_profile')
            .select('gov_profile.user_id', 'user_id')
            .addSelect('MAX(platform_user.name)', 'name')
            .addSelect('MAX(platform_user.surname)', 'surname')
            .addSelect('MAX(platform_user.username)', 'username')
            .addSelect('MAX(gov_group.name)', 'group_name')
            .addSelect('MAX(gov_group.id)', 'group_id')
            .addSelect('MAX(LOWER(gov_group.period)) <@ :range::tstzrange', 'is_first_month')
            .addSelect('MAX(UPPER(gov_group.period)) <@ :range::tstzrange', 'is_last_month')
            .innerJoin(GovGroupPersistenceEntity, 'gov_group', 'gov_profile.group_id = gov_group.id')
            .innerJoin(GovProgramPersistenceEntity, 'gov_program', 'gov_group.program_id = gov_program.id')
            .innerJoin(GovAgencyPersistenceEntity, 'gov_agency', 'gov_program.agency_id = gov_agency.id')
            .innerJoin(
                User,
                'platform_user',
                'platform_user.id = gov_profile.user_id and platform_user.state NOT IN (:...uztInactiveUserStates)',
                {
                    uztInactiveUserStates: [UserState.SUSPENDED, UserState.BLOCKED],
                },
            )
            .leftJoin(
                UztDocumentPersistenceEntity,
                'uzt_document',
                'gov_profile.user_id = uzt_document.user_id AND uzt_document.month = :month AND uzt_document.deleted_at IS NULL',
            )
            // Join queried month audit history that contains billingType uzt
            .leftJoin(
                (qb) =>
                    qb
                        .subQuery() // Start subquery
                        .select(['student_settings_audit.student_id'])
                        .from(StudentSettingsAuditPersistenceEntity, 'student_settings_audit')
                        .where('student_settings_audit.billing_type = :auditBillingType')
                        .andWhere('student_settings_audit.created_at >= :month')
                        .andWhere('student_settings_audit.created_at <= :endOfMonth'),
                'audit_month',
                'audit_month.student_id = gov_profile.user_id',
            )
            /*
             * Join last entry of audit history that comes right before current month start
             * (if last entry is uzt we will include in result)
             */
            .leftJoin(
                (qb) =>
                    qb
                        .subQuery()
                        .select(
                            'DISTINCT ON (student_settings_audit.student_id) student_settings_audit.student_id, student_settings_audit.created_at, student_settings_audit.billing_type',
                        )
                        .from(StudentSettingsAuditPersistenceEntity, 'student_settings_audit')
                        .where('student_settings_audit.created_at < :month')
                        .orderBy('student_settings_audit.student_id')
                        .addOrderBy('student_settings_audit.created_at', 'DESC'),
                'audit_latest_entry',
                'audit_latest_entry.student_id = gov_profile.user_id',
            )
            .leftJoin(
                StudentSettingsPersistenceEntity,
                'student_settings',
                'student_settings.student_id = gov_profile.user_id',
            )
            .leftJoin(DocumentPersistenceEntity, 'document', 'uzt_document.document_id = document.id')
            .where('gov_group.period && :range::tstzrange')
            .andWhere('gov_agency.type = :govAgencyType')
            .andWhere(
                new Brackets((qb) => {
                    qb.where('audit_month.student_id IS NOT NULL')
                        .orWhere('student_settings.billing_type = :billingType')
                        .orWhere('audit_latest_entry.billing_type = :auditBillingType');
                }),
            )
            .groupBy('gov_profile.user_id')
            .setParameter('range', monthRange.serialize())
            .setParameter('month', this.params.month)
            .setParameter('billingType', BillingType.UZT)
            .setParameter('auditBillingType', BillingType.UZT)
            .setParameter('endOfMonth', endOfMonth)
            .setParameter('govAgencyType', GovAgencyType.UZT);

        Object.values(UztDocumentType).forEach((type: UztDocumentType) => {
            subQuery = subQuery.addSelect(
                QueryUztMonthlyStudentList.documentStateExpressions[type],
                QueryUztMonthlyStudentList.documentStateColumns[type],
            );
        });

        if (this.params.filtering.govGroupId?.length) {
            subQuery = subQuery.andWhere('gov_group.id IN (:...govGroupId)', {
                govGroupId: this.params.filtering.govGroupId.map((id) => id.value),
            });
        }

        if (this.params.filtering.student) {
            subQuery = subQuery.andWhere(
                "CONCAT(platform_user.name, ' ', platform_user.surname, ' | ', platform_user.username) ILIKE :student",
                {
                    student: `%${this.params.filtering.student}%`,
                },
            );
        }

        return subQuery;
    }

    private addDocumentStateFiltering(builder: SelectQueryBuilder<any>): SelectQueryBuilder<any> {
        Object.keys(this.params.filtering.states || {})
            .filter((document: UztDocumentType) => this.params.filtering.states?.[document]?.length || 0 > 0)
            .forEach((documentType: UztDocumentType) => {
                const statuses = this.addEquivalentScheduledStates(this.params.filtering.states?.[documentType] || []);
                const documentStateColumn = QueryUztMonthlyStudentList.documentStateColumns[documentType];

                const hasNewOrUnset = statuses.includes(DocumentState.NEW) || statuses.includes(UztDocumentState.UNSET);

                let filter = `${documentStateColumn} IN (:...filteredStatuses)`;

                if (documentType === UztDocumentType.CERTIFICATE) {
                    if (statuses.includes(null)) {
                        filter += ` OR (${documentStateColumn} IS NULL AND NOT is_last_month)`;
                    }
                    if (hasNewOrUnset) {
                        filter += ` OR (${documentStateColumn} IS NULL AND is_last_month)`;
                    }
                } else if (documentType === UztDocumentType.PRIEDAS) {
                    if (statuses.includes(null)) {
                        filter += ` OR (${documentStateColumn} IS NULL AND NOT is_first_month)`;
                    }
                    if (hasNewOrUnset) {
                        filter += ` OR (${documentStateColumn} IS NULL AND is_first_month)`;
                    }
                } else {
                    if (hasNewOrUnset) {
                        filter += ` OR ${documentStateColumn} IS NULL`;
                    }
                }

                builder = builder.andWhere(filter).setParameter('filteredStatuses', statuses);
            });

        return builder;
    }

    private addEquivalentScheduledStates(
        states: Array<DocumentState | UztDocumentState | null>,
    ): Array<DocumentState | UztDocumentState | null> {
        const additionalStates = [];

        if (states.includes(DocumentState.GENERATING)) {
            additionalStates.push(DocumentState.GENERATION_SCHEDULED);
        }

        if (states.includes(DocumentState.SENDING_TO_SIGN)) {
            additionalStates.push(DocumentState.SIGNING_SCHEDULED);
        }

        if (states.includes(UztDocumentState.SENDING)) {
            additionalStates.push(UztDocumentState.SENDING_SCHEDULED);
        }

        return Arrays.primitivesUnique([...states, ...additionalStates]);
    }

    private addOrdering(builder: SelectQueryBuilder<any>): SelectQueryBuilder<any> {
        if (!this.params.ordering) {
            return builder;
        }

        if (this.params.ordering.field === GetUztMonthlyStudentListSortableField.LEARNER_FULL_NAME) {
            return builder.orderBy('(name, surname, username)', this.params.ordering.directionString);
        }

        if (this.params.ordering.field === GetUztMonthlyStudentListSortableField.GOV_GROUP_NAME) {
            return builder.orderBy('group_name', this.params.ordering.directionString);
        }

        const documentType = this.getDocumentTypeFromSortableField(this.params.ordering.field);

        const depthMap = UztDocumentWorkflow.getMergedDepthMap();

        const cases = Object.entries(depthMap).reduce((acc, [state, depth]) => {
            return [
                ...acc,
                `WHEN ${QueryUztMonthlyStudentList.documentStateColumns[documentType]} = '${state}' THEN ${depth + 1}`,
            ];
        }, []);

        if (documentType === UztDocumentType.CERTIFICATE) {
            cases.push('WHEN is_last_month THEN 0');
        }

        if (documentType === UztDocumentType.PRIEDAS) {
            cases.push('WHEN is_first_month THEN 0');
        }

        cases.push('ELSE 10000');

        return builder.orderBy(`CASE ${cases.join('\n')} END`, this.params.ordering.directionString);
    }

    private getDocumentTypeFromSortableField(sortableField: GetUztMonthlyStudentListSortableField): UztDocumentType {
        return sortableField.replace('State', '') as UztDocumentType;
    }
}
