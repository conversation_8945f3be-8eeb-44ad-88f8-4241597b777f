import { DigitalDocument } from '../../../../../../document/domain/DigitalDocument';
import { UztTvarkarastisDocumentPayload } from './UztTvarkarastisDocumentPayload.external';
import { ConfigureProperties } from '../../../../../../utils/UtilityTypes';
import { UztDocument, UztDocumentParams } from '../UztDocument';
import { UztDocumentType } from '../UztDocumentType';

export type UztTvarkarastisDocumentConstructorParams = ConfigureProperties<
    UztTvarkarastisDocument,
    {
        omit: 'type' | 'workflow';
        optional: 'state';
    }
>;

export class UztTvarkarastisDocument extends UztDocument<UztTvarkarastisDocumentPayload> {
    readonly document: DigitalDocument<UztTvarkarastisDocumentPayload>;

    constructor(params: UztTvarkarastisDocumentConstructorParams) {
        super({
            type: UztDocumentType.TVARKARASTIS,
            ...params,
        });
    }

    fromParams(params: UztDocumentParams<UztTvarkarastisDocumentPayload>): UztDocument<UztTvarkarastisDocumentPayload> {
        return new UztTvarkarastisDocument(params);
    }
}
