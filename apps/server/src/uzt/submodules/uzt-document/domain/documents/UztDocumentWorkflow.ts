import { Workflow, WorkflowDepthMap } from '../../../../../core/domain/Workflow';
import { DocumentState, DocumentWorkflow } from '../../../../../document/domain/DocumentWorkflow';
import { Objects } from '../../../../../utils/Objects';

export enum UztDocumentState {
    UNSET = 'unset',
    SENDING_SCHEDULED = 'sending_scheduled',
    SENDING = 'sending',
    SENT = 'sent',
    SENDING_FAILED = 'sending_failed',
}

export class UztDocumentWorkflow {
    static readonly WORKFLOW = new Workflow<UztDocumentState>({
        start: UztDocumentState.UNSET,
        end: UztDocumentState.SENT,
        transitions: {
            [UztDocumentState.UNSET]: [UztDocumentState.SENDING, UztDocumentState.SENDING_SCHEDULED],
            [UztDocumentState.SENDING_SCHEDULED]: [UztDocumentState.SENDING, UztDocumentState.SENDING_FAILED],
            [UztDocumentState.SENDING]: [UztDocumentState.SENT, UztDocumentState.SENDING_FAILED],
            [UztDocumentState.SENDING_FAILED]: [UztDocumentState.SENDING, UztDocumentState.SENDING_SCHEDULED],
            [UztDocumentState.SENT]: [UztDocumentState.SENDING, UztDocumentState.SENDING_SCHEDULED],
        },
    });

    static getMergedDepthMap(): WorkflowDepthMap<UztDocumentState | DocumentState> {
        return {
            ...DocumentWorkflow.WORKFLOW.getDepthMap(),
            /*
             * Since UZT doc workflow is continuation of plain doc workflow,
             * we add 100 as default depth for UZT states to be after plain doc states
             * */
            ...(Objects.entries(UztDocumentWorkflow.WORKFLOW.getDepthMap()).reduce((acc, [state, depth]) => {
                return {
                    ...acc,
                    [state]: depth + 100,
                };
            }, {}) as WorkflowDepthMap<UztDocumentState>),
        };
    }
}
