import { IsEnum, IsInt, IsString } from 'class-validator';
import { DocumentState } from '../../../../../../../document/domain/DocumentWorkflow';
import { UztDocumentState } from '../../../../domain/documents/UztDocumentWorkflow';
import { UztMonthlyStudent } from '../../../../domain/UztMonthlyStudent';
import { UztDocumentNotApplicableStateDto } from './UztDocumentNotApplicableStateDto';

export class UztMonthlyStudentDto {
    @IsString()
    learnerFullName: string;

    @IsInt()
    learnerId: number;

    @IsString()
    govGroupName: string;

    @IsInt()
    govGroupId: number;

    @IsEnum({ ...DocumentState, ...UztDocumentState })
    ataskaitaState: DocumentState | UztDocumentState;

    @IsEnum({ ...DocumentState, ...UztDocumentState })
    saskaitaState: DocumentState | UztDocumentState;

    @IsEnum({ ...DocumentState, ...UztDocumentState })
    ziniarastisState: DocumentState | UztDocumentState;

    @IsEnum({ ...DocumentState, ...UztDocumentState })
    aktasState: DocumentState | UztDocumentState;

    @IsEnum({ ...DocumentState, ...UztDocumentState })
    tvarkarastisState?: DocumentState | UztDocumentState | UztDocumentNotApplicableStateDto;

    @IsEnum({ ...DocumentState, ...UztDocumentState })
    certificateState?: DocumentState | UztDocumentState | UztDocumentNotApplicableStateDto;

    @IsEnum({ ...DocumentState, ...UztDocumentState })
    priedasState?: DocumentState | UztDocumentState | UztDocumentNotApplicableStateDto;

    static fromDomain(domain: UztMonthlyStudent): UztMonthlyStudentDto {
        return {
            learnerFullName: domain.fullName,
            learnerId: domain.id.value,
            govGroupName: domain.govGroupName,
            govGroupId: domain.govGroupId.value,
            ataskaitaState: domain.ataskaitaState,
            saskaitaState: domain.saskaitaState,
            ziniarastisState: domain.ziniarastisState,
            aktasState: domain.aktasState,
            tvarkarastisState: domain.tvarkarastisState || UztDocumentNotApplicableStateDto.NOT_APPLICABLE,
            certificateState: domain.certificateState || UztDocumentNotApplicableStateDto.NOT_APPLICABLE,
            priedasState: domain.priedasState || UztDocumentNotApplicableStateDto.NOT_APPLICABLE,
        };
    }
}
