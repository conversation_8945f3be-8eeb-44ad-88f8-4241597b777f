import moment from 'moment';
import { v4 as uuid } from 'uuid';
import Id from '../../../../core/domain/value-objects/Id';
import Transaction from '../../../../core/infrastructure/Transaction';
import TransactionManager from '../../../../core/infrastructure/TransactionManager';
import ArgumentValidation from '../../../../core/utils/validation/ArgumentValidation';
import { DigitalDocument } from '../../../../document/domain/DigitalDocument';
import { DocumentState } from '../../../../document/domain/DocumentWorkflow';
import { DocumentModuleFacade } from '../../../../document/services/DocumentModuleFacade';
import { GovAgencyType } from '../../../../gov/submodules/gov-agency/domain/GovAgencyType';
import { GovGroupService } from '../../../../gov/submodules/gov-group/services/GovGroupService';
import SqsProducer from '../../../../messaging/SqsProducer';
import { isUztBillingType } from '../../../../users/students/settings/domain/BillingType';
import { StudentSettingsFinder } from '../../../../users/students/settings/services/StudentSettingsFinder';
import Logger from '../../../../utils/logger/Logger';
import { UztDocumentCommandDto, UztDocumentCommandDtoMapper } from '../controllers/sqs/dto/UztDocumentCommandDto';
import { UztDocumentFactory } from '../domain/documents/UztDocumentFactory';
import { UztDocumentType } from '../domain/documents/UztDocumentType';
import { UztDocumentGenerateCommand } from '../domain/UztDocumentCommand';
import { UztDocumentGenerationStrategyFactory } from './generation-strategy/UztDocumentGenerationStrategyFactory';
import { UztDocumentService } from './UztDocumentService';

export interface ScheduledUztDocumentGenerationParams {
    month: Date;
    userId: Id;
    companySigner: Id;
    type: UztDocumentType;
    tx?: Transaction;
    skipBillingTypeCheck?: boolean;
}

export class UztDocumentGenerationService {
    constructor(
        private readonly documentModuleFacade: DocumentModuleFacade,
        private readonly uztDocumentService: UztDocumentService,
        private readonly uztDocumentGenerationStrategyFactory: UztDocumentGenerationStrategyFactory,
        private readonly commandQueue: SqsProducer<UztDocumentCommandDto>,
        private readonly logger: Logger,
        private readonly govGroupService: GovGroupService,
        private readonly studentSettingsFinder: StudentSettingsFinder,
        private readonly transactionManager: TransactionManager,
    ) {}

    async initiateGenerationOfUztDocument({
        userId,
        month,
        companySigner,
        documentType,
    }: {
        userId: Id;
        month: Date;
        companySigner: Id;
        documentType: UztDocumentType;
    }): Promise<Id> {
        await this.documentModuleFacade.getCompanySignerProfileOrThrow(companySigner);

        const existingUztDocument = await this.uztDocumentService.getUserDocumentsForMonthByType({
            month,
            userId,
            type: documentType,
        });

        if (existingUztDocument) {
            existingUztDocument.document.workflow.validateTransition(
                existingUztDocument.document.state,
                DocumentState.GENERATING,
            );
        }

        try {
            const generationStrategy = this.uztDocumentGenerationStrategyFactory.fromType(documentType);
            const generationPayload = await generationStrategy.getGenerationPayload({ month, userId });
            const template = generationStrategy.getTemplate();
            const readableName = generationStrategy.getReadableName();
            const storagePrefix = `uzt/${userId.value}/${moment(month).format('YYYY-MM')}`;

            if (existingUztDocument) {
                await this.documentModuleFacade.initiateDocumentGeneration({
                    generationPayload,
                    template,
                    readableName,
                    storagePrefix,
                    document: existingUztDocument.document,
                    signers: generationStrategy.getSigners({
                        companySigner,
                        userId,
                        documentId: existingUztDocument.document.id,
                    }),
                });

                // @ts-expect-error TS(2322) FIXME: Type 'Id | undefined' is not assignable to type 'I... Remove this comment to see the full error message
                return existingUztDocument.id;
            }

            const document = await this.documentModuleFacade.saveDocument(new DigitalDocument());

            let uztDocument = UztDocumentFactory.fromParams({
                type: documentType,
                userId,
                month,
                document,
            });

            await this.documentModuleFacade.initiateDocumentGeneration({
                document,
                generationPayload,
                template,
                readableName,
                storagePrefix,
                signers: generationStrategy.getSigners({
                    companySigner,
                    userId,
                    documentId: document.id,
                }),
            });

            uztDocument = await this.uztDocumentService.save(uztDocument);

            // @ts-expect-error TS(2322) FIXME: Type 'Id | undefined' is not assignable to type 'I... Remove this comment to see the full error message
            return uztDocument.id;
        } catch (e) {
            if (existingUztDocument) {
                await this.documentModuleFacade.saveDocument(
                    existingUztDocument.document.failGeneration(new Date(), e.message),
                );
            }
            throw e;
        }
    }

    async scheduleGenerationOfBatchOfUztDocument({
        month,
        companySigner,
        type,
        groupIds = [],
    }: {
        month: Date;
        companySigner: Id;
        type: UztDocumentType;
        groupIds?: Id[];
    }): Promise<void> {
        const monthString = moment(month).format('YYYY-MM');

        if (type === UztDocumentType.CERTIFICATE) {
            const offboardingGovGroups = await this.govGroupService.find({
                endMonth: month,
                govAgencyType: GovAgencyType.UZT,
            });

            groupIds.forEach((groupId) => {
                ArgumentValidation.assert.true(
                    offboardingGovGroups.some((offboardingGroup) => groupId.equals(offboardingGroup.id)),
                    `Group #${groupId.value} does not end at ${monthString} or does not exist`,
                );
            });

            groupIds = groupIds.length ? groupIds : offboardingGovGroups.map((group) => group.getIdOrThrow());
            ArgumentValidation.assert.false(groupIds.length === 0, `No groups ending at ${monthString} found`);
        }

        await this.documentModuleFacade.getCompanySignerProfileOrThrow(companySigner);

        const usersWithSuchDocument = await this.uztDocumentService.getUsersToBatchGenerateDocuments({
            month,
            groupIds,
            type,
        });

        await this.transactionManager.execute(async (transaction) => {
            for (const userId of usersWithSuchDocument) {
                await this.scheduleGenerationOfUztDocument({
                    month,
                    userId,
                    companySigner,
                    type,
                    skipBillingTypeCheck: true,
                    tx: transaction,
                });
            }
        });

        this.logger.debug(`Scheduled ${usersWithSuchDocument.length} UZT document for generation`);
    }

    async scheduleGenerationOfUztDocument({
        month,
        userId,
        companySigner,
        type,
        tx,
        skipBillingTypeCheck,
    }: ScheduledUztDocumentGenerationParams): Promise<void> {
        await this.transactionManager.execute(async (transaction) => {
            if (!skipBillingTypeCheck) {
                const studentSettings = await this.studentSettingsFinder.findByUserId(userId, transaction);
                ArgumentValidation.assert.true(
                    isUztBillingType(studentSettings?.billingType),
                    `User #${userId.value} does not have UZT billing type`,
                );
            }

            const uztDocument = await this.uztDocumentService.getUserDocumentsForMonthByType({
                month,
                userId,
                type,
                tx: transaction,
            });

            if (!uztDocument) {
                const document = await this.documentModuleFacade.saveDocument(
                    new DigitalDocument().scheduleGeneration(),
                    transaction,
                );

                await this.uztDocumentService.save(
                    UztDocumentFactory.fromParams({
                        type,
                        userId,
                        month,
                        document,
                    }),
                    transaction,
                );
            } else {
                await this.documentModuleFacade.saveDocument(uztDocument.document.scheduleGeneration(), transaction);
            }

            transaction.addBeforeCommitAction(async () => {
                await this.commandQueue.send({
                    body: UztDocumentCommandDtoMapper.fromDomain(
                        new UztDocumentGenerateCommand(month, userId, companySigner, type),
                    ),
                    groupId: userId.value.toString(),
                    deduplicationId: uuid(),
                });
            });
        }, tx);
    }
}
