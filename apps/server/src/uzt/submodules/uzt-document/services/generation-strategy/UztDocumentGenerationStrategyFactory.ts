import { UztDocumentType } from '../../domain/documents/UztDocumentType';
import { UztDocumentGenerationStrategy } from './UztDocumentGenerationStrategy';
import { assertNever } from '../../../../../utils/assert-never';
import { UztTvarkarastisDocumentGenerationStrategy } from './types/UztTvarkarastisDocumentGenerationStrategy';
import { UztCertificateDocumentGenerationStrategy } from './types/UztCertificateDocumentGenerationStrategy';
import { UztPriedasDocumentGenerationStrategy } from './types/UztPriedasDocumentGenerationStrategy';

export class UztDocumentGenerationStrategyFactory {
    private readonly uztAtaskaitaDocumentGenerationStrategy: UztDocumentGenerationStrategy;

    private readonly uztSaskaitaDocumentGenerationStrategy: UztDocumentGenerationStrategy;

    private readonly uztZiniarastisDocumentGenerationStrategy: UztDocumentGenerationStrategy;

    private readonly uztAktasDocumentGenerationStrategy: UztDocumentGenerationStrategy;

    private readonly uztTvarkarastisDocumentGenerationStrategy: UztTvarkarastisDocumentGenerationStrategy;

    private readonly uztCertificateDocumentGenerationStrategy: UztCertificateDocumentGenerationStrategy;

    private readonly uztPriedasDocumentGenerationStrategy: UztPriedasDocumentGenerationStrategy;

    constructor(params: {
        uztAtaskaitaDocumentGenerationStrategy: UztDocumentGenerationStrategy;
        uztSaskaitaDocumentGenerationStrategy: UztDocumentGenerationStrategy;
        uztZiniarastisDocumentGenerationStrategy: UztDocumentGenerationStrategy;
        uztAktasDocumentGenerationStrategy: UztDocumentGenerationStrategy;
        uztTvarkarastisDocumentGenerationStrategy: UztTvarkarastisDocumentGenerationStrategy;
        uztCertificateDocumentGenerationStrategy: UztCertificateDocumentGenerationStrategy;
        uztPriedasDocumentGenerationStrategy: UztPriedasDocumentGenerationStrategy;
    }) {
        this.uztAtaskaitaDocumentGenerationStrategy = params.uztAtaskaitaDocumentGenerationStrategy;
        this.uztSaskaitaDocumentGenerationStrategy = params.uztSaskaitaDocumentGenerationStrategy;
        this.uztZiniarastisDocumentGenerationStrategy = params.uztZiniarastisDocumentGenerationStrategy;
        this.uztAktasDocumentGenerationStrategy = params.uztAktasDocumentGenerationStrategy;
        this.uztTvarkarastisDocumentGenerationStrategy = params.uztTvarkarastisDocumentGenerationStrategy;
        this.uztCertificateDocumentGenerationStrategy = params.uztCertificateDocumentGenerationStrategy;
        this.uztPriedasDocumentGenerationStrategy = params.uztPriedasDocumentGenerationStrategy;
    }

    fromType(type: UztDocumentType): UztDocumentGenerationStrategy {
        switch (type) {
            case UztDocumentType.ATASKAITA:
                return this.uztAtaskaitaDocumentGenerationStrategy;
            case UztDocumentType.SASKAITA:
                return this.uztSaskaitaDocumentGenerationStrategy;
            case UztDocumentType.ZINIARASTIS:
                return this.uztZiniarastisDocumentGenerationStrategy;
            case UztDocumentType.AKTAS:
                return this.uztAktasDocumentGenerationStrategy;
            case UztDocumentType.TVARKARASTIS:
                return this.uztTvarkarastisDocumentGenerationStrategy;
            case UztDocumentType.CERTIFICATE:
                return this.uztCertificateDocumentGenerationStrategy;
            case UztDocumentType.PRIEDAS:
                return this.uztPriedasDocumentGenerationStrategy;
            default:
                assertNever(type);
        }
    }
}
