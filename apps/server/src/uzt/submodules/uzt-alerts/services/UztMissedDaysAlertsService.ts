import Id from '../../../../core/domain/value-objects/Id';
import TransactionManager from '../../../../core/infrastructure/TransactionManager';
import { Markdown } from '../../../../core/utils/Markdown';
import ArgumentValidation from '../../../../core/utils/validation/ArgumentValidation';
import DiscordMessenger from '../../../../discord/services/messaging/DiscordMessenger';
import { GovGroupService } from '../../../../gov/submodules/gov-group/services/GovGroupService';
import { GovParticipationService } from '../../../../gov/submodules/gov-participation/services/GovParticipationService';
import { SendTemplatedEmailCommand } from '../../../../mailer/controllers/commands/dto/SendTemplatedEmailCommand';
import { MailType } from '../../../../mailer/types/TemplateParams';
import { QueueService } from '../../../../queue/QueueService';
import { UserAccountFinder } from '../../../../users/accounts/services/UserAccountFinder';
import Logger from '../../../../utils/logger/Logger';
import { UztConstants } from '../../../helpers/UztConstants';
import { UztMark } from '../../uzt-participation/domain/UztMark';
import { UztResultingUztMarkCalculator } from '../../uzt-participation/domain/UztResultingUztMarkCalculator';
import { UztParticipationService } from '../../uzt-participation/services/UztParticipationService';
import { UztAlert } from '../domain/UztAlert';
import { UztAlertType } from '../domain/UztAlertType';
import { UztAlertService } from './UztAlertService';

export class UztMissedDaysAlertsService {
    constructor(
        private readonly govGroupService: GovGroupService,
        private readonly logger: Logger,
        private readonly userAccountFinder: UserAccountFinder,
        private readonly queueService: QueueService,
        private readonly discordMessenger: DiscordMessenger,
        private readonly uztAlertsService: UztAlertService,
        private readonly transactionManager: TransactionManager,
        private readonly govParticipationService: GovParticipationService,
        private readonly uztParticipationService: UztParticipationService,
    ) {}

    async checkMissedDaysAndNotifyLearner({
        userId,
        forceSendCurrentThreshold,
    }: {
        userId: Id;
        forceSendCurrentThreshold?: boolean;
    }): Promise<void> {
        this.logger.debug('CHECKING_MISSED_DAYS', {
            userId: userId.value,
            forceSendCurrentThreshold,
        });

        const group = await this.govGroupService.getByUserId(userId);
        ArgumentValidation.assert.defined(group, 'User must be in a GOV group');
        ArgumentValidation.assert.true(group.isActive(), 'User must be in an active GOV group');

        const user = await this.userAccountFinder.findByUserId(userId);
        ArgumentValidation.assert.defined(user, 'User not exists');
        ArgumentValidation.assert.true(user.isActive(), 'User must be active');

        if (!forceSendCurrentThreshold) {
            const yesterdayParticipation = await this.govParticipationService.getYesterdayParticipation(
                userId,
                UztConstants.UZT_TIMEZONE,
            );

            if (!UztResultingUztMarkCalculator.fromParticipationDay(yesterdayParticipation).is(UztMark.N)) {
                this.logger.debug('DID_NOT_MISS_YESTERDAY', {
                    userId: userId.value,
                    forceSendCurrentThreshold,
                });

                return;
            }
        }

        const lastAlert = await this.uztAlertsService.getLatestAlertOfType(userId, UztAlertType.MISSED_DAYS);
        const consecutiveMissedDays = await this.uztParticipationService.getConsecutiveMissedDays(
            userId,
            UztConstants.UZT_TIMEZONE,
        );

        const bypassedThresholds = UztConstants.MISSED_CONSECUTIVE_DAYS_NOTIFICATION_THRESHOLDS.filter((threshold) => {
            const bypassed = threshold <= consecutiveMissedDays;

            if (forceSendCurrentThreshold || !bypassed) {
                return bypassed;
            }

            if (!lastAlert) {
                return true;
            }

            return threshold > lastAlert.value;
        });

        // Notifying only about the highest threshold that was bypassed
        const notifyThreshold = bypassedThresholds[bypassedThresholds.length - 1];

        if (!ArgumentValidation.is.defined(notifyThreshold)) {
            this.logger.debug('NOT_BYPASSED_NEW_THRESHOLD', { userId: userId.value, consecutiveMissedDays });

            return;
        }

        this.logger.debug('SENDING_ALERTS', { userId: userId.value, consecutiveMissedDays, notifyThreshold });

        await this.transactionManager.execute(async (tx) => {
            tx.addBeforeCommitAction(async () => {
                await this.queueService.command({
                    command: new SendTemplatedEmailCommand({
                        to: user.email,
                        templateParams: {
                            type: MailType.UZT_ALERTS_MISSED_DAYS,
                            firstName: user.name?.firstName || '',
                            threshold: notifyThreshold,
                        },
                    }),
                });

                await this.discordMessenger.sendDirectMessage(
                    userId,
                    this.getDiscordAlertMessage({
                        firstName: user.name?.firstName || '',
                        threshold: notifyThreshold,
                    }),
                );
            });

            await this.uztAlertsService.save(
                UztAlert.create({
                    userId,
                    type: UztAlertType.MISSED_DAYS,
                    threshold: notifyThreshold,
                    value: consecutiveMissedDays,
                }),
            );
        });
    }

    private getDiscordAlertMessage({ firstName, threshold }: { firstName: string; threshold: number }): string {
        return Markdown.create()
            .h3(`Hi ${firstName},`)
            .newline()
            .append(
                Markdown.create()
                    .append('You have been absent from learning activities for')
                    .space()
                    .b(`${threshold} consecutive days.`),
            )
            .br()
            .append(
                Markdown.create()
                    .append('To remind you, UZT closely tracks attendance, and upon joining Turing College,')
                    .space()
                    .append('you signed a vocational training contract. Article 4.2 states that failure to')
                    .space()
                    .append('attend learning activities for more than')
                    .space()
                    .b('five consecutive days')
                    .append(', compels us to terminate the contract.'),
            )
            .br()
            .append(
                Markdown.create()
                    .append('If there is a justifiable explanation for your absence that we are not yet aware of,')
                    .space()
                    .append('please inform us within')
                    .space()
                    .b('the next 3 days.'),
            )
            .br()
            .if(
                threshold < 5,
                Markdown.create()
                    .append(
                        'Otherwise, please connect to any of the tracked learning activities on your scheduled time',
                    )
                    .space()
                    .b('as soon as possible.'),
                Markdown.create()
                    .append('Otherwise, if you don’t connect to any tracked learning activities on your scheduled time')
                    .space()
                    .b('today')
                    .append(
                        ', we are obligated by UZT to terminate your study agreement in the next five working days.',
                    ),
            )
            .toString();
    }
}
