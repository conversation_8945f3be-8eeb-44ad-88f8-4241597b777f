import DomainEntity from '../../../../core/domain/DomainEntity';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import Id from '../../../../core/domain/value-objects/Id';
import { UztAlertType } from './UztAlertType';

export type UztAlertParams = NonFunctionProperties<UztAlert>;
export type UztAlertCreateParams = NonFunctionProperties<UztAlert>;

export class Uzt<PERSON>lert extends DomainEntity {
    readonly userId: Id;
    readonly type: UztAlertType;
    readonly threshold: number;
    readonly value: number;

    protected constructor({ id, userId, type, threshold, value }: UztAlertParams) {
        super(id);

        this.userId = userId;
        this.type = type;
        this.threshold = threshold;
        this.value = value;
    }

    static fromParams(params: UztAlertParams): UztAlert {
        return new UztAlert(params);
    }

    static create(params: UztAlertCreateParams): UztAlert {
        return new UztAlert(params);
    }
}
