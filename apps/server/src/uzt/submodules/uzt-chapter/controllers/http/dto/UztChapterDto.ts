import { IsInt, IsString, IsUrl } from 'class-validator';
import { UztChapter } from '../../../domain/UztChapter';
import { IUztChapterDto } from '../../../../../../../../shared/src/gov/uzt/chapter/api/uzt-chapter.dto';

export class UztChapterDto implements IUztChapterDto {
    @IsInt()
    id: number;

    @IsString()
    name: string;

    @IsString()
    email: string;

    @IsString()
    deliveryEmail: string;

    @IsString()
    address: string;

    @IsString()
    phoneNumber: string;

    @IsString()
    departmentName: string;

    @IsUrl()
    schedulesDashboardUrl: string;

    constructor(params?: Partial<UztChapterDto>) {
        if (params) {
            Object.assign(this, params);
        }
    }

    static fromUztChapter(uztChapter: UztChapter): UztChapterDto {
        return new UztChapterDto({
            id: uztChapter.id?.value,
            name: uztChapter.name,
            email: uztChapter.email.value,
            deliveryEmail: uztChapter.deliveryEmail.value,
            address: uztChapter.address,
            phoneNumber: uztChapter.phoneNumber,
            departmentName: uztChapter.departmentName,
            schedulesDashboardUrl: uztChapter.schedulesDashboardUrl.value,
        });
    }
}
