import { Job } from 'pg-boss';

import { QueueWorker } from '../../../../../queue/domain/QueueWorker';
import { AppQueues } from '../../../../../queue/domain/AppQueues';
import {
    SyncMissingContractTermNotificationCommand,
    SyncMissingContractTermNotificationCommandDto,
} from './dto/SyncMissingContractTermNotificationCommand';
import { UztMissingContractTermNotificationService } from '../../services/UztMissingContractTermNotificationService';
import { QueueService } from '../../../../../queue/QueueService';
import { NotificationManager } from '../../../../../notifications/NotificationManager';
import { NotificationType } from '../../../../../notifications/domain/Notification';
import Arrays from '../../../../../core/collections/Arrays';
import Id from '../../../../../core/domain/value-objects/Id';
import { UztProfileService } from '../../services/UztProfileService';

export class SyncMissingContractTermNotificationCommandWorker implements QueueWorker {
    readonly queuePattern = AppQueues.schema.uzt.profile.syncMissingContractTermNotification.queue;

    constructor(
        private readonly uztMissingContractTermNotificationService: UztMissingContractTermNotificationService,
        private readonly uztProfileService: UztProfileService,
        private readonly queueService: QueueService,
        private readonly notificationManager: NotificationManager,
    ) {}

    async handler(job: Job<SyncMissingContractTermNotificationCommandDto>): Promise<void> {
        const command = AppQueues.getMapperByClass(SyncMissingContractTermNotificationCommand).deserialize(job.data);

        if (command.userId !== 'all') {
            return this.uztMissingContractTermNotificationService.syncNotification({ userId: command.userId });
        }

        const usersToSync = await this.getUsersToSync(command);

        await this.queueService.commandMany(
            usersToSync.map((userId) => {
                return {
                    message: new SyncMissingContractTermNotificationCommand({
                        userId,
                    }),
                };
            }),
        );
    }

    async getUsersToSync(command: SyncMissingContractTermNotificationCommand): Promise<Id[]> {
        if (command.userId !== 'all') {
            return [];
        }

        const profilesWithMissingContractTerm = await this.uztProfileService.getAllWithMissingContractTerm();
        const userIdsWithMissingContractTerm = profilesWithMissingContractTerm.map((profile) => profile.userId);

        const usersWithExistingNotification =
            await this.notificationManager.getUsersWithUnacknowledgedNotificationOfType([
                NotificationType.UZT_CONTRACT_TERM_MISSING,
            ]);

        return Arrays.unique([...userIdsWithMissingContractTerm, ...usersWithExistingNotification]);
    }
}
