import { UserConnectionsService } from '../../services/UserConnectionsService';
import Container from 'typedi';
import { GoogleProfileModule } from '../../../google-account/infrastructure/di/GoogleProfileModule';
import { LoggingModule } from '../../../logging/infrastructure/di/LoggingModule';
import { SprintsModule } from '../../../education/sprints/infrastructure/di/SprintsModule';
import { GithubModule } from '../../../github/infrastructure/di/GithubModule';
import { DiscordModule } from '../../../discord/infrastructure/di/DiscordModule';
import { ModuleWithControllers } from '../../../di/types/ApplicationModule';
import UserConnectionsController from '../../controllers/http/UserConnectionsController';

export class UserConnectionsModule implements ModuleWithControllers {
    private constructor(public readonly userConnectionsService: UserConnectionsService) {}

    getName(): string {
        return UserConnectionsModule.name;
    }

    getControllers(): any[] {
        return [UserConnectionsController];
    }

    static init(
        githubModule: GithubModule,
        googleProfileModule: GoogleProfileModule,
        discordModule: DiscordModule,
        loggingModule: LoggingModule,
        sprintsModule: SprintsModule,
    ): UserConnectionsModule {
        const userConnectionsService = new UserConnectionsService(
            githubModule.githubProfileFinder,
            googleProfileModule.googleProfileService,
            discordModule.discordOnboardingChecker,
            discordModule.discordHttpClient,
            loggingModule.loggingService.createLogger(UserConnectionsService.name),
            sprintsModule.sprintPartReviewerPreconditionsService,
        );

        Container.set(UserConnectionsService, userConnectionsService);

        return new UserConnectionsModule(userConnectionsService);
    }
}
