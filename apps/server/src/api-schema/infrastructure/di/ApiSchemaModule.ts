import { ModuleWithControllers } from '../../../di/types/ApplicationModule';
import { ApiSchemaController } from '../../controllers/http/ApiSchemaController';

export class ApiSchemaModule implements ModuleWithControllers {
    getName(): string {
        return ApiSchemaModule.name;
    }

    getControllers(): any[] {
        return [ApiSchemaController];
    }

    static init(): ApiSchemaModule {
        return new ApiSchemaModule();
    }
}
