import Id from '../../../core/domain/value-objects/Id';
import Username from '../../../core/domain/value-objects/user/Username';
import Transaction from '../../../core/infrastructure/Transaction';
import { Role } from '../../shared/infrastructure/db/User';
import StringFilter from '../domain/StringFilter';
import UserAccount from '../domain/UserAccount';
import Email from '../../../core/domain/value-objects/Email';
import { UserAccountRepository } from '../infrastructure/db/UserAccountRepository';

export class UserAccountFinder {
    constructor(private readonly userAccountRepository: UserAccountRepository) {}

    async findByUsername(username: Username, transaction?: Transaction): Promise<UserAccount | undefined> {
        return this.userAccountRepository.getByUsername(username, transaction);
    }

    async findByUserId(userId: Id, transaction?: Transaction): Promise<UserAccount | undefined> {
        return this.userAccountRepository.getByUserId(userId, transaction);
    }

    async findByUserIds(userIds: Id[], transaction?: Transaction): Promise<UserAccount[]> {
        return this.userAccountRepository.getByUserIds(userIds, transaction);
    }

    async findByRole(role: Role, transaction?: Transaction): Promise<UserAccount[]> {
        return this.userAccountRepository.getByRole(role, transaction);
    }

    async findByRoles(roles: Role[], transaction?: Transaction): Promise<UserAccount[]> {
        return this.userAccountRepository.getByRoles(roles, transaction);
    }

    async findByNameLike(filter: StringFilter, transaction?: Transaction): Promise<UserAccount[]> {
        return await this.userAccountRepository.getByNameLike(filter, transaction);
    }

    async findWithPosition(transaction?: Transaction): Promise<UserAccount[]> {
        return await this.userAccountRepository.getWithPosition(transaction);
    }

    async findByEmails(emails: Email[], transaction?: Transaction): Promise<UserAccount[]> {
        return this.userAccountRepository.getByEmails(emails, transaction);
    }
}
