import latinize from 'latinize';
import Username from '../../../core/domain/value-objects/user/Username';
import IllegalArgumentError from '../../../core/errors/IllegalArgumentError';
import { UserAccountFinder } from './UserAccountFinder';

export class UsernameGenerator {
    private static readonly DEFAULT_USERNAME_LENGTH = 6;

    private static readonly DEFAULT_SURNAME_PART_LENGTH = 5;

    constructor(private readonly userAccountFinder: UserAccountFinder) {}

    async generate(name: string, surname: string): Promise<string> {
        if (!name) {
            throw new IllegalArgumentError('Name is missing');
        }

        if (!surname) {
            throw new IllegalArgumentError('Surname is missing');
        }

        const cleanName = latinize(name)
            .toLowerCase()
            .replace(/[^a-z]/g, '');
        const cleanSurname = latinize(surname)
            .toLowerCase()
            .replace(/[^a-z]/g, '');

        return this.findNextAvailableUsername(
            cleanName,
            cleanSurname,
            this.getInitialNamePartLength(cleanName, cleanSurname),
            this.getInitialSurnamePartLength(cleanSurname),
            UsernameGenerator.DEFAULT_USERNAME_LENGTH,
        );
    }

    private async findNextAvailableUsername(
        name: string,
        surname: string,
        namePartLength: number,
        surnamePartLength: number,
        usernameLength: number,
    ): Promise<string> {
        const username = this.generateUsername(name, surname, namePartLength, surnamePartLength, usernameLength);
        const existingAccount = await this.userAccountFinder.findByUsername(new Username(username));
        if (!existingAccount) {
            return username;
        }

        const usedAllName = namePartLength === name.length;
        const usedAllSurname = surnamePartLength === surname.length;

        return this.findNextAvailableUsername(
            name,
            surname,
            !usedAllName ? namePartLength + 1 : namePartLength,
            usedAllName && !usedAllSurname ? surnamePartLength + 1 : surnamePartLength,
            usernameLength + 1,
        );
    }

    private generateUsername(
        name: string,
        surname: string,
        namePartLength: number,
        surnamePartLength: number,
        usernameLength: number,
    ): string {
        const username = `${name.substr(0, namePartLength)}${surname.substr(0, surnamePartLength)}`;

        return username.padEnd(usernameLength, username.slice(-1));
    }

    private getInitialNamePartLength(name: string, surname: string): number {
        if (surname.length < UsernameGenerator.DEFAULT_USERNAME_LENGTH - 1) {
            return Math.min(UsernameGenerator.DEFAULT_USERNAME_LENGTH - surname.length, name.length);
        }

        return 1;
    }

    private getInitialSurnamePartLength(surname: string): number {
        return Math.min(UsernameGenerator.DEFAULT_SURNAME_PART_LENGTH, surname.length);
    }
}
