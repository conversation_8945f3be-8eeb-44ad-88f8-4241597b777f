import Id from '../../../../../core/domain/value-objects/Id';
import { Serialized } from '../../../../../core/utils/Serialized';
import { NonFunctionProperties } from '../../../../../utils/UtilityTypes';
import { UserState } from '../../../domain/UserState';
import { Role, UserSuspensionReason } from '../../../../shared/infrastructure/db/User';
import { Change } from '../../../../../core/utils/Change';

export type UserStateChangedEventParams = NonFunctionProperties<UserStateChangedEvent>;
export type UserStateChangedEventDto = Serialized<UserStateChangedEventParams>;

export class UserStateChangedEvent {
    readonly userId: Id;
    readonly state: Change<UserState>;
    readonly reason?: UserSuspensionReason;
    readonly role: Role;

    constructor(params: UserStateChangedEventParams) {
        Object.assign(this, params);
    }
}

export class UserStateChangedEventMapper {
    serialize(event: UserStateChangedEvent): UserStateChangedEventDto {
        return {
            userId: event.userId.value,
            state: event.state,
            reason: event.reason,
            role: event.role,
        };
    }

    deserialize(data: UserStateChangedEventDto): UserStateChangedEvent {
        return new UserStateChangedEvent({
            userId: new Id(data.userId),
            state: new Change(data.state.before, data.state.after),
            reason: data.reason,
            role: data.role,
        });
    }
}
