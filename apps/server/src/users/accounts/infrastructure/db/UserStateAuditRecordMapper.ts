import TypeormPersistenceMapper from '../../../../core/infrastructure/db/TypeormPersistenceMapper';
import { UserStateAuditRecord } from '../../domain/UserStateAuditRecord';
import { UserStateAuditRecordPersistenceEntity } from './UserStateAuditRecordPersistenceEntity';

export class UserStateAuditRecordMapper
    implements TypeormPersistenceMapper<UserStateAuditRecord, UserStateAuditRecordPersistenceEntity>
{
    toDomain(persistence: UserStateAuditRecordPersistenceEntity): UserStateAuditRecord {
        return persistence.toDomain();
    }

    toPersistence(domain: UserStateAuditRecord): UserStateAuditRecordPersistenceEntity {
        return UserStateAuditRecordPersistenceEntity.fromDomain(domain);
    }
}
