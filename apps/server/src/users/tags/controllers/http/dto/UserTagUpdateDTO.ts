import { IsOptional, IsString, <PERSON>, <PERSON><PERSON>eng<PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import UserTagColor from '../../../domain/UserTagColor';
import UserTagDescription from '../../../domain/UserTagDescription';
import UserTagName from '../../../domain/UserTagName';

export default class UserTagUpdateDTO {
    @IsOptional()
    @IsString()
    @MinLength(UserTagName.MIN_LENGTH)
    @MaxLength(UserTagName.MAX_LENGTH)
    @Matches(UserTagName.REGEX)
    name?: string;

    @IsOptional()
    @IsString()
    @MinLength(UserTagDescription.MIN_LENGTH)
    @MaxLength(UserTagDescription.MAX_LENGTH)
    description?: string;

    @IsOptional()
    @IsString()
    @Matches(UserTagColor.REGEX)
    color?: string;
}
