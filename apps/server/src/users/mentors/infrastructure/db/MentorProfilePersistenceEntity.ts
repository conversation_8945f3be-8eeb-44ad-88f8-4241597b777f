import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, RelationId } from 'typeorm';
import { DateString } from '../../../../core/domain/value-objects/DateString';
import Id from '../../../../core/domain/value-objects/Id';
import TypeormPersistenceEntity from '../../../../core/infrastructure/db/TypeormPersistenceEntity';
import { CountryCodeIso2 } from '../../../../physical-address/domain/types/CountryCodeIso2';
import { PhysicalAddressPersistenceEntity } from '../../../../physical-address/infrastructure/db/entities/PhysicalAddressPersistenceEntity';
import { PhysicalAddressTypeormMapper } from '../../../../physical-address/infrastructure/db/entities/PhysicalAddressTypeormMapper';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';
import { EmploymentStatus } from '../../../shared/domain/EmploymentStatus';
import { Gender } from '../../../shared/domain/Gender';
import { WorkIndustry } from '../../../shared/domain/WorkIndustry';
import { YearsOfExperience } from '../../../shared/domain/YearsOfExperience';
import EmploymentPosition from '../../domain/EmploymentPosition';
import MentorProfile from '../../domain/MentorProfile';
import { StlContractType } from '../../domain/StlContractType';
import Mentor from './Mentor';
import { Timezone } from '../../../../core/domain/types/Timezone';

@Entity('mentor_profile')
export default class MentorProfilePersistenceEntity extends TypeormPersistenceEntity {
    @Column()
    @RelationId((self: MentorProfilePersistenceEntity) => self.user)
    userId: number;

    @Column({ nullable: true })
    employmentPosition?: string;

    @Column({
        name: 'gender',
        nullable: true,
        type: 'enum',
        enum: Gender,
    })
    gender?: Gender;

    @Column({ name: 'phone_number', nullable: true })
    phoneNumber?: string;

    @Column({ name: 'citizenship_country', nullable: true })
    citizenshipCountry?: CountryCodeIso2;

    @Column(() => PhysicalAddressPersistenceEntity, {
        prefix: 'address',
    })
    physicalAddress?: PhysicalAddressPersistenceEntity;

    @Column({ name: 'date_of_birth', type: 'date', nullable: true })
    dateOfBirth?: string;

    @Column({ name: 'timezone', nullable: true })
    timezone?: Timezone;

    @Column({ name: 'contract_type', nullable: true, type: 'enum', enum: StlContractType })
    contractType?: StlContractType;

    @Column({
        name: 'employment_status',
        nullable: true,
    })
    employmentStatus?: EmploymentStatus;

    @Column({
        name: 'work_industry',
        nullable: true,
    })
    workIndustry?: WorkIndustry;

    @Column({
        name: 'other_work_industry',
        nullable: true,
    })
    otherWorkIndustry?: string;

    @Column({ name: 'company_name', nullable: true })
    companyName?: string;

    @Column({ name: 'job_title', nullable: true })
    jobTitle?: string;

    @Column({
        name: 'seniority_level',
        nullable: true,
    })
    seniorityLevel?: YearsOfExperience;

    @OneToOne(() => Mentor, { onDelete: 'CASCADE' })
    @JoinColumn()
    private user: Mentor;

    constructor(params?: NonFunctionProperties<MentorProfilePersistenceEntity>) {
        if (!params) {
            super();
            return;
        }
        super(params.id, params.createdAt, params.updatedAt);
        Object.assign(this, params);
    }

    static fromDomain(domain: MentorProfile): MentorProfilePersistenceEntity {
        return new MentorProfilePersistenceEntity({
            id: domain.id?.value,
            userId: domain.userId.value,
            employmentPosition: domain.employmentPosition?.value,
            gender: domain.gender,
            phoneNumber: domain.phoneNumber,
            citizenshipCountry: domain.citizenshipCountry,
            physicalAddress: domain?.physicalAddress
                ? PhysicalAddressTypeormMapper.toPersistence(domain.physicalAddress)
                : undefined,
            dateOfBirth: domain.dateOfBirth?.value,
            timezone: domain.timezone,
            employmentStatus: domain.employmentStatus,
            workIndustry: domain.workIndustry,
            otherWorkIndustry: domain.otherWorkIndustry,
            companyName: domain.companyName,
            jobTitle: domain.jobTitle,
            seniorityLevel: domain.seniorityLevel,
            contractType: domain.contractType,
        });
    }

    toDomain(): MentorProfile {
        return new MentorProfile({
            id: this.id ? new Id(this.id) : undefined,
            userId: new Id(this.userId),
            employmentPosition: this.employmentPosition ? new EmploymentPosition(this.employmentPosition) : undefined,
            gender: this.gender,
            phoneNumber: this.phoneNumber,
            citizenshipCountry: this.citizenshipCountry,
            physicalAddress: this?.physicalAddress
                ? PhysicalAddressTypeormMapper.toDomain(this.physicalAddress)
                : undefined,
            dateOfBirth: this.dateOfBirth ? DateString.fromDateString(this.dateOfBirth) : undefined,
            timezone: this.timezone,
            employmentStatus: this.employmentStatus,
            workIndustry: this.workIndustry,
            otherWorkIndustry: this.otherWorkIndustry,
            companyName: this.companyName,
            jobTitle: this.jobTitle,
            seniorityLevel: this.seniorityLevel,
            contractType: this.contractType,
        });
    }
}
