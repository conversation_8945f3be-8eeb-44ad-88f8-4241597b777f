import { Response } from 'express';
import { Get, <PERSON>ttp<PERSON><PERSON>, JsonController, <PERSON>q, <PERSON>s, UseBefore } from 'routing-controllers';
import { Inject, Service } from 'typedi';
import SignupRateLimiterMiddleware from '../../../../app/middleware/SignupRateLimiterMiddleware';
import {
    googleAuthCallbackMiddleware,
    googleAuthMiddleware,
    RequestWithGoogleUser,
} from '../../../../core/middleware/AuthMiddleware';
import CookieAuthorizer from '../../../../auth/controllers/http/middleware/CookieAuthorizer';
import { SocialAuthenticationMethod } from '../../../../auth/domain/AuthenticationMethod';
import GoogleAuthenticator from '../../../../auth/services/GoogleAuthenticator';
import LocalAuthenticator from '../../../../auth/services/LocalAuthenticator';
import { Configuration } from '../../../../config/Configuration';
import { ConfigurationModule } from '../../../../config/infrastructure/di/ConfigurationModule';
import Email from '../../../../core/domain/value-objects/Email';
import Url from '../../../../core/domain/value-objects/Url';
import FullName from '../../../../core/domain/value-objects/user/FullName';
import HttpError from '../../../../core/errors/HttpError';
import { SignupFinisherToken } from '../../../../di/tokens';
import { LoggingModule } from '../../../../logging/infrastructure/di/LoggingModule';
import { LoggingService } from '../../../../logging/services/LoggingService';
import Logger from '../../../../utils/logger/Logger';
import GoogleSignupStrategyFactory from '../../express/GoogleSignupStrategyFactory';
import { SignupFinisher } from '../../services/SignupFinisher';

@Service()
@JsonController('/signup/google')
export default class GoogleSignupController {
    private readonly logger: Logger;

    private readonly redirectUrl: Url;

    constructor(
        @Inject(ConfigurationModule.CONFIGURATION_TOKEN)
        configuration: Configuration,
        @Inject(LoggingModule.LOGGING_SERVICE_TOKEN)
        loggingService: LoggingService,
        @Inject(SignupFinisherToken)
        private readonly signupFinisher: SignupFinisher,
        @Inject()
        private readonly localAuthenticator: LocalAuthenticator,
        @Inject()
        private readonly googleAuthenticator: GoogleAuthenticator,
        @Inject()
        private readonly cookieAuthorizer: CookieAuthorizer,
    ) {
        this.logger = loggingService.createLogger(GoogleSignupController.name);
        this.redirectUrl = new Url(configuration.client.url).addPath(configuration.signup.client.redirectPath);
    }

    @Get()
    @HttpCode(302)
    @UseBefore(googleAuthMiddleware(GoogleSignupStrategyFactory.STRATEGY_NAME))
    beginGoogleRegistration(): void {
        // Nothing to do
    }

    @Get('/callback')
    @UseBefore(SignupRateLimiterMiddleware, googleAuthCallbackMiddleware(GoogleSignupStrategyFactory.STRATEGY_NAME))
    async completeGoogleRegistration(@Req() req: RequestWithGoogleUser, @Res() res: Response): Promise<Response> {
        let redirectResult = this.redirectUrl;
        try {
            const googleProfile = req.googleProfile;
            if (googleProfile) {
                const googleAccessToken = await this.googleAuthenticator.authenticate(googleProfile);
                if (googleAccessToken) {
                    this.cookieAuthorizer.setCookie(res, googleAccessToken);
                } else {
                    const newUser = await this.signupFinisher.finish(
                        // @ts-expect-error TS(2532) FIXME: Object is possibly 'undefined'.
                        new Email([...googleProfile.emails].shift().value),
                        new FullName(googleProfile.name.givenName, googleProfile.name.familyName),
                        undefined,
                        SocialAuthenticationMethod.GOOGLE,
                    );
                    if (newUser) {
                        const localAccessToken = await this.localAuthenticator.authenticateAutomatically(newUser);
                        this.cookieAuthorizer.setCookie(res, localAccessToken);
                    } else {
                        redirectResult = HttpError.notFound('Google user does not match email of the account').addToUrl(
                            this.redirectUrl,
                        );
                    }
                }
            } else {
                redirectResult = HttpError.badRequest('Missing Google profile info').addToUrl(this.redirectUrl);
            }
        } catch (e) {
            this.logger.warn('Google signup failed', e);
            redirectResult = HttpError.fromError(e, 'Google signup failed').addToUrl(this.redirectUrl);
        }

        res.redirect(redirectResult.value);

        return res;
    }
}
