import { plainToClass } from 'class-transformer';
import { validate } from 'class-validator';
import { DateString } from '../../../../../core/domain/value-objects/DateString';
import { CountryCodeIso2 } from '../../../../../physical-address/domain/types/CountryCodeIso2';
import { SetStudentProfileSetupComplianceParamsDto } from './StudentProfileSetupComplianceParamsDto';
import { Timezone } from '../../../../../core/domain/types/Timezone';

describe(SetStudentProfileSetupComplianceParamsDto.name, () => {
    it('should pass if all fields are set and valid for lithuanian', async () => {
        await expect(
            validate(
                plainToClass(SetStudentProfileSetupComplianceParamsDto, {
                    citizenshipCountry: CountryCodeIso2.LT,
                    residenceCountry: CountryCodeIso2.LT,
                    countryPersonalCode: '33309240064',
                    dateOfBirth: DateString.today().value,
                    timezone: Timezone.EUROPE_VILNIUS,
                    residenceFullAddressString: '12333, Vilnius, Gedimino pr., 12',
                } as SetStudentProfileSetupComplianceParamsDto),
            ),
        ).resolves.toMatchObject([]);
    });

    it('should pass if all fields are set and valid for non-lithuanian', async () => {
        await expect(
            validate(
                plainToClass(SetStudentProfileSetupComplianceParamsDto, {
                    citizenshipCountry: 'FR',
                    residenceCountry: 'FR',
                    dateOfBirth: DateString.today().value,
                    timezone: Timezone.EUROPE_PARIS,
                    residenceFullAddressString: '12333, Paris, South pr., 12',
                }),
            ),
        ).resolves.toMatchObject([]);
    });

    it('should allow to not specify timezone', async () => {
        await expect(
            validate(
                plainToClass(SetStudentProfileSetupComplianceParamsDto, {
                    citizenshipCountry: 'FR',
                    residenceCountry: 'FR',
                    dateOfBirth: DateString.today().value,
                    residenceFullAddressString: '12333, Paris, South pr., 12',
                }),
            ),
        ).resolves.toMatchObject([]);
    });

    it('should allow only Intl compatible timezone value', async () => {
        await expect(
            validate(
                plainToClass(SetStudentProfileSetupComplianceParamsDto, {
                    citizenshipCountry: 'FR',
                    residenceCountry: 'FR',
                    dateOfBirth: DateString.today().value,
                    timezone: Timezone.EUROPE_PARIS,
                    residenceFullAddressString: '12333, Paris, South pr., 12',
                }),
            ),
        ).resolves.toMatchObject([]);

        await expect(
            validate(
                plainToClass(SetStudentProfileSetupComplianceParamsDto, {
                    citizenshipCountry: 'FR',
                    residenceCountry: 'FR',
                    dateOfBirth: DateString.today().value,
                    timezone: '+3 MOSCOW',
                    residenceFullAddressString: '12333, Paris, South pr., 12',
                }),
            ),
        ).resolves.toMatchObject([
            { constraints: { isEnum: expect.stringContaining('timezone must be one of the following values') } },
        ]);
    });

    it('should fail for invalid values', async () => {
        await expect(
            validate(
                plainToClass(SetStudentProfileSetupComplianceParamsDto, {
                    citizenshipCountry: 12,
                    residenceCountry: [],
                    dateOfBirth: '22',
                    residenceFullAddressString: 1,
                }),
            ),
        ).resolves.toMatchObject([
            {
                constraints: {
                    isEnum: expect.stringContaining('citizenshipCountry must be one of the following values'),
                },
            },
            {
                constraints: {
                    isEnum: expect.stringContaining('residenceCountry must be one of the following values'),
                },
            },
            { constraints: { isString: 'residenceFullAddressString must be a string' } },
            { constraints: { isDateStringWithoutTime: 'dateOfBirth must be an ISO 8601 date string without time' } },
        ]);

        await expect(validate(plainToClass(SetStudentProfileSetupComplianceParamsDto, {}))).resolves.toMatchObject([
            {
                constraints: {
                    isEnum: expect.stringContaining('citizenshipCountry must be one of the following values'),
                },
            },
            {
                constraints: {
                    isEnum: expect.stringContaining('residenceCountry must be one of the following values'),
                },
            },
            { constraints: { isString: 'residenceFullAddressString must be a string' } },
            { constraints: { isDateStringWithoutTime: 'dateOfBirth must be an ISO 8601 date string without time' } },
        ]);
    });

    it('should fail if lithuanian personal code is invalid', async () => {
        await expect(
            validate(
                plainToClass(SetStudentProfileSetupComplianceParamsDto, {
                    citizenshipCountry: 'LT',
                    residenceCountry: 'LT',
                    countryPersonalCode: '23131882',
                    dateOfBirth: DateString.today().value,
                    timezone: Timezone.EUROPE_VILNIUS,
                    residenceFullAddressString: '12333, Vilnius, Gedimino pr., 12',
                }),
            ),
        ).resolves.toMatchObject([
            { constraints: { isCountryPersonalCode: 'Country personal code is invalid for LT' } },
        ]);
    });
});
