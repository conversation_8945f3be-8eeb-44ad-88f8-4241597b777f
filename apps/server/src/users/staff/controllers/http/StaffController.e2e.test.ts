import 'jest-extended';
import { faker } from '@faker-js/faker';
import moment from 'moment';
import { NotFoundError } from 'routing-controllers';
import request from 'supertest';
import Container from 'typedi';
import { Configuration } from '../../../../config/Configuration';
import { ConfigurationModule } from '../../../../config/infrastructure/di/ConfigurationModule';
import Mentor from '../../../mentors/infrastructure/db/Mentor';
import Staff, { isStaff } from '../../infrastructure/db/Staff';
import { Role } from '../../../shared/infrastructure/db/User';
import IllegalArgumentError from '../../../../core/errors/IllegalArgumentError';
import { StaffService } from '../../services/StaffService';
import isSorted from '../../../../test-toolkit/shared/isSorted';
import { IntegrationTestUser } from '../../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';
import { UserState } from '../../../accounts/domain/UserState';
import { IntegrationTestUserStateAudit } from '../../../../test-toolkit/e2e/entities/IntegrationTestUserStateAudit';
import { StaffRepository } from '../../infrastructure/db/StaffRepository';

describe('Staff controller IT', () => {
    let config: Configuration;
    let staffService: StaffService;
    let staffRepository: StaffRepository;
    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let mentor: IntegrationTestUser<Mentor>;
    let student: IntegrationTestUser;

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        staffService = Container.get(StaffService);
        staffRepository = Container.get(StaffRepository);

        ({ admin, student, staff, mentor } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute());
    });

    describe('GET /staff', () => {
        it('should return 200 OK and a list of staff', async () => {
            const res = await request(config.server)
                .get('/staff')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(200);

            expect(res.body.staff).toBeDefined();
            expect(res.body.staff).toSatisfyAll(isStaff);
            expect(res.body.staff).toSatisfy((s: Staff[]) => isSorted(s, 'createdAt', 'desc'));
        });

        it('should return 403 Forbidden if no token is provided', async () => {
            const invalidToken = 'test123';

            const res = await request(config.server)
                .get('/staff')
                .set('Cookie', `token=${invalidToken}`)
                .send()
                .expect(403);

            expect(res.body.staff).toBeUndefined();
        });
    });

    describe('POST /staff', () => {
        it('should return 200 OK and create', async () => {
            const email = '<EMAIL>';
            const role = Role.STAFF;

            const res = await request(config.server)
                .post('/staff')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send({ email, role })
                .expect(200);

            expect(res.body.id).toBeDefined();
            expect(res.body.email).toBe(email);
            expect(res.body.role).toBe(role);

            const foundRes = await request(config.server)
                .get('/staff')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(200);

            expect(foundRes.body.staff).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        email,
                        role,
                        id: res.body.id,
                    }),
                ]),
            );
        });

        it('should return 400 Bad Request if user email is already taken', async () => {
            const name = 'John';
            const surname = 'Doe';
            const email = staff.params.email;
            const role = Role.STAFF;

            const data = {
                name,
                surname,
                email,
                role,
            };

            const res = await request(config.server)
                .post('/staff')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(data)
                .expect(400);

            expect(res.body.name).toBe(IllegalArgumentError.name);
            expect(res.body.message).toBe('Email is already in use');
        });
    });

    describe('PUT /staff/:id', () => {
        it('should return 200 OK and update user by id', async () => {
            const newStaff = await IntegrationTestUser.createFakeStaff();
            const update = {
                name: 'John',
                surname: 'Doe',
                email: '<EMAIL>',
                role: Role.ADMIN,
            };

            const res = await request(config.server)
                .put(`/staff/${newStaff.getIdOrThrow().value}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(update)
                .expect(200);

            expect(res.body.name).toBe(update.name);
            expect(res.body.surname).toBe(update.surname);
            expect(res.body.email).toBe(update.email);
            expect(res.body.role).toBe(update.role);

            const foundRes = await request(config.server)
                .get('/staff')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(200);

            expect(foundRes.body.staff).toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        ...update,
                        id: newStaff.getIdOrThrow().value,
                    }),
                ]),
            );
        });

        it('should return 404 Not Found if user by id is not found', async () => {
            const name = 'John';
            const surname = 'Doe';
            const email = '<EMAIL>';
            const role = Role.STAFF;

            const update = {
                name,
                surname,
                email,
                role,
            };

            const res = await request(config.server)
                .put('/staff/999')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(update)
                .expect(404);

            expect(res.body.name).toBe(NotFoundError.name);
        });

        it('should return 400 Bad Request if email is already taken', async () => {
            const anotherStaff = await IntegrationTestUser.createFakeStaff();
            const name = 'John';
            const surname = 'Doe';
            const email = anotherStaff.params.email;
            const role = Role.STAFF;

            const update = {
                name,
                surname,
                email,
                role,
            };

            const res = await request(config.server)
                .put(`/staff/${staff.getIdOrThrow().value}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(update)
                .expect(400);

            expect(res.body.name).toBe(IllegalArgumentError.name);
            expect(res.body.message).toBe('Email is already in use');
        });

        it('should return 404 Not Found if user to be updated is not staff', async () => {
            const name = 'John';
            const surname = 'Doe';
            const email = '<EMAIL>';
            const role = Role.STAFF;

            const update = {
                name,
                surname,
                email,
                role,
            };

            const res = await request(config.server)
                .put(`/staff/${student.getIdOrThrow().value}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send(update)
                .expect(404);

            expect(res.body.name).toBe(NotFoundError.name);
        });
    });

    describe('DELETE /staff/:id', () => {
        it('should return 204 No Content and delete user by id', async () => {
            const newStaff = await staffRepository.create({
                username: faker.person.firstName().toLowerCase(),
                password: '$2b$10$c0iQXLdte8NAsIurIipWc.p./upIcvOvBzlGDI1D6IP/c2mQnH6e2',
                email: faker.internet.email(),
                name: faker.person.firstName(),
                surname: faker.person.lastName(),
                role: 'staff',
                calendarToken: faker.lorem.word(),
                logins: [],
                tokens: [],
                createdAt: moment().subtract(2, 'day').toDate(),
            } as any);

            await request(config.server)
                .delete(`/staff/${newStaff.id}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(204)
                .expect({});

            const foundRes = await request(config.server)
                .get('/staff')
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(200);

            expect(foundRes.body.staff).not.toEqual(
                expect.arrayContaining([
                    expect.objectContaining({
                        id: newStaff.id,
                    }),
                ]),
            );
        });

        it('should return 404 Not Found if user by id is not found', async () => {
            const id = '999';

            const res = await request(config.server)
                .delete(`/staff/${id}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(404);

            expect(res.body.name).toBe(NotFoundError.name);
        });

        it('should return 404 Not Found if user is not admin or staff member', async () => {
            const res = await request(config.server)
                .delete(`/staff/${student.getIdOrThrow().value}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(404);

            expect(res.body.name).toBe(NotFoundError.name);
        });

        it('should return 400 Bad Request if user is deleting himself', async () => {
            const res = await request(config.server)
                .delete(`/staff/${admin.getIdOrThrow().value}`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(400);

            expect(res.body.name).toBe(IllegalArgumentError.name);
            expect(res.body.message).toBe('User cannot delete himself');
        });
    });

    describe('PUT /mentor/:id/activate', () => {
        it('should activate a staff with an admin', async () => {
            const newStaff = await IntegrationTestUser.createFakeStaff({ state: UserState.BLOCKED });

            await IntegrationTestUserStateAudit.create({ userId: newStaff.getIdOrThrow(), state: UserState.ACTIVE });

            await request(config.server)
                .put(`/staff/${newStaff.getIdOrThrow().value}/activate`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(204);

            await expect(staffService.get(newStaff.getIdOrThrow().value)).resolves.toEqual(
                expect.objectContaining({ state: UserState.ACTIVE }),
            );
        });

        it('should activate an admin with an admin', async () => {
            const newAdmin = await IntegrationTestUser.createFakeAdmin({ state: UserState.BLOCKED });

            await IntegrationTestUserStateAudit.create({ userId: newAdmin.getIdOrThrow(), state: UserState.ACTIVE });

            await request(config.server)
                .put(`/staff/${newAdmin.getIdOrThrow().value}/activate`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(204);

            await expect(staffService.get(newAdmin.getIdOrThrow().value)).resolves.toEqual(
                expect.objectContaining({ state: UserState.ACTIVE }),
            );
        });

        it('should not activate staff with a staff', async () => {
            await request(config.server)
                .put('/staff/999/activate')
                .set('Cookie', `token=${staff.getAuthTokenString()}`)
                .send()
                .expect(403);
        });

        it('should not activate staff with a mentor', async () => {
            await request(config.server)
                .put('/staff/999/activate')
                .set('Cookie', `token=${mentor.getAuthTokenString()}`)
                .send()
                .expect(403);
        });

        it('should not activate staff with a student', async () => {
            await request(config.server)
                .put('/staff/999/activate')
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send()
                .expect(403);
        });
    });

    describe('PUT /mentor/:id/block', () => {
        it('should block a staff with an admin', async () => {
            const newStaff = await IntegrationTestUser.createFakeStaff();

            await request(config.server)
                .put(`/staff/${newStaff.getIdOrThrow().value}/block`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(204);

            await expect(staffService.get(newStaff.getIdOrThrow().value)).resolves.toEqual(
                expect.objectContaining({ state: UserState.BLOCKED }),
            );
        });

        it('should block an admin with an admin', async () => {
            const newStaff = await IntegrationTestUser.createFakeStaff();

            await request(config.server)
                .put(`/staff/${newStaff.getIdOrThrow().value}/block`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(204);

            await expect(staffService.get(newStaff.getIdOrThrow().value)).resolves.toEqual(
                expect.objectContaining({ state: UserState.BLOCKED }),
            );
        });

        it('should not block itself', async () => {
            await request(config.server)
                .put(`/staff/${admin.id}/block`)
                .set('Cookie', `token=${admin.getAuthTokenString()}`)
                .send()
                .expect(400);
        });

        it('should not block staff with a staff', async () => {
            await request(config.server)
                .put('/staff/999/block')
                .set('Cookie', `token=${staff.getAuthTokenString()}`)
                .send()
                .expect(403);
        });

        it('should not block staff with a mentor', async () => {
            await request(config.server)
                .put('/staff/999/block')
                .set('Cookie', `token=${mentor.getAuthTokenString()}`)
                .send()
                .expect(403);
        });

        it('should not block staff with a student', async () => {
            await request(config.server)
                .put('/staff/999/block')
                .set('Cookie', `token=${student.getAuthTokenString()}`)
                .send()
                .expect(403);
        });
    });
});
