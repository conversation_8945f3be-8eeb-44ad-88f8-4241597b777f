import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from 'class-validator';
import { IsValidName } from '../../../../../../core/controllers/validators/IsValidName';
import { StaffRole, staffRoles } from '../../../../infrastructure/db/Staff';
import FullName from '../../../../../../core/domain/value-objects/user/FullName';

export default class StaffWriteDTO {
    @IsValidName()
    @MinLength(FullName.MIN_LENGTH)
    @MaxLength(FullName.MAX_LENGTH)
    readonly name: string;

    @IsValidName()
    @MinLength(FullName.MIN_LENGTH)
    @MaxLength(FullName.MAX_LENGTH)
    readonly surname: string;

    @IsEmail()
    readonly email: string;

    @IsIn(staffRoles)
    readonly role: StaffRole;
}
