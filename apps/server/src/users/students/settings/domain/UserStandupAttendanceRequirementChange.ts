import Id from '../../../../core/domain/value-objects/Id';
import { NonFunctionProperties } from '../../../../utils/UtilityTypes';

type UserStandupAttendanceRequirementChangeParams = NonFunctionProperties<UserStandupAttendanceRequirementChange>;

export class UserStandupAttendanceRequirementChange {
    readonly userId: Id;
    readonly target: {
        before: number;
        after: number;
    };

    private constructor(params: UserStandupAttendanceRequirementChangeParams) {
        Object.assign(this, params);
    }

    static fromParams(params: UserStandupAttendanceRequirementChangeParams): UserStandupAttendanceRequirementChange {
        return new UserStandupAttendanceRequirementChange(params);
    }
}
