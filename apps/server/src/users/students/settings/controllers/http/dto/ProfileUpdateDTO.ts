import { IsBoolean, IsDate, IsEnum, IsOptional } from 'class-validator';
import { DeadlineCategory } from '../../../domain/DeadlineCategory';
import { StudentSettingsUpdateParams } from '../../../domain/StudentSettings';
import { StudentSource } from '../../../domain/StudentSource';

export default class ProfileUpdateDTO {
    @IsOptional()
    @IsBoolean()
    isDeadlineMandatory?: boolean;

    @IsOptional()
    @IsEnum(DeadlineCategory)
    deadlineCategory?: DeadlineCategory;

    @IsOptional()
    @IsBoolean()
    isEndorsementEnabled?: boolean;

    @IsOptional()
    @IsBoolean()
    isReviewRecorded?: boolean;

    @IsOptional()
    @IsBoolean()
    isPendingSupport?: boolean;

    @IsOptional()
    @IsDate()
    endDate?: Date;

    @IsOptional()
    @IsEnum(StudentSource)
    source?: StudentSource;

    toStudentSettingsUpdateParams(): StudentSettingsUpdateParams {
        return {
            isDeadlineMandatory: this.isDeadlineMandatory,
            deadlineCategory: this.deadlineCategory,
            isEndorsementEnabled: this.isEndorsementEnabled,
            isPendingSupport: this.isPendingSupport,
            endDate: this.endDate,
            source: this.source,
        };
    }
}
