import { faker } from '@faker-js/faker';
import moment from 'moment';
import 'reflect-metadata';
import request from 'supertest';
import Container from 'typedi';
import { BatchFinderToken } from '../../../../../batches/infrastructure/di/tokens';
import BatchFinder from '../../../../../batches/services/BatchFinder';
import { Configuration } from '../../../../../config/Configuration';
import { ConfigurationModule } from '../../../../../config/infrastructure/di/ConfigurationModule';
import Id from '../../../../../core/domain/value-objects/Id';
import { RequestContextStorageToken, TransactionManagerToken } from '../../../../../core/infrastructure/di/tokens';
import ArgumentValidation from '../../../../../core/utils/validation/ArgumentValidation';
import { IntegrationTestBatch } from '../../../../../test-toolkit/e2e/entities/IntegrationTestBatch';
import { IntegrationTestCourse } from '../../../../../test-toolkit/e2e/entities/IntegrationTestCourse';
import { IntegrationTestGovAgency } from '../../../../../test-toolkit/e2e/entities/IntegrationTestGovAgency';
import { IntegrationTestGovGroup } from '../../../../../test-toolkit/e2e/entities/IntegrationTestGovGroup';
import { IntegrationTestGovProgram } from '../../../../../test-toolkit/e2e/entities/IntegrationTestGovProgram';
import { IntegrationTestUser } from '../../../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestUztChapter } from '../../../../../test-toolkit/e2e/entities/IntegrationTestUztChapter';
import { IntegrationTestUztProfile } from '../../../../../test-toolkit/e2e/entities/IntegrationTestUztProfile';
import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';
import { createMapperFromEntity } from '../../../../../utils/createMapperFromEntity';
import { UztContractDto } from '../../../../../uzt/submodules/uzt-profile/controllers/http/dto/UztContractDto';
import { UztCouponDto } from '../../../../../uzt/submodules/uzt-profile/controllers/http/dto/UztCouponDto';
import { UztProfileCreateDto } from '../../../../../uzt/submodules/uzt-profile/controllers/http/dto/UztProfileCreateDto';
import { UztCoupon } from '../../../../../uzt/submodules/uzt-profile/domain/UztCoupon';
import Mentor from '../../../../mentors/infrastructure/db/Mentor';
import { UztContractTerm } from '../../../../shared/domain/UztContractTerm';
import Staff from '../../../../staff/infrastructure/db/Staff';
import StudentSettingsPersistenceEntity from '../../db/StudentSettingsPersistenceEntity';
import { StudentSettingsRepository } from '../../db/StudentSettingsRepository';
import { BillingType } from '../../domain/BillingType';
import { DeadlineCategory } from '../../domain/DeadlineCategory';
import { StudentSource } from '../../domain/StudentSource';
import StudentType from '../../domain/StudentType';
import { StudentSettingsCreator } from '../../services/StudentSettingsCreator';
import { StudentSettingsFinder } from '../../services/StudentSettingsFinder';
import AggregateStudentSettingsDTO from './dto/AggregateStudentSettingsDTO';
import { StudentBillingTypeChangeDTO } from './dto/StudentBillingTypeChangeDTO';
import { IntegrationTestGovProfile } from '../../../../../test-toolkit/e2e/entities/IntegrationTestGovProfile';
import { GovProfileCreateDto } from '../../../../../gov/submodules/gov-profile/controllers/http/dto/GovProfileCreateDto';

describe('StudentSettingsController IT', () => {
    let config: Configuration;
    let studentSettingsRepository: StudentSettingsRepository;
    let studentSettingsCreator: StudentSettingsCreator;
    let studentSettingsFinder: StudentSettingsFinder;
    let batchFinder: BatchFinder;
    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let mentor: IntegrationTestUser<Mentor>;
    let student: IntegrationTestUser;
    let anotherStudent: IntegrationTestUser;

    let batch: IntegrationTestBatch;
    let course: IntegrationTestCourse;

    let anotherBatch: IntegrationTestBatch;

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        studentSettingsRepository = new StudentSettingsRepository(
            Container.get(TransactionManagerToken),
            Container.get(RequestContextStorageToken),
            createMapperFromEntity(StudentSettingsPersistenceEntity),
        );
        studentSettingsCreator = Container.get(StudentSettingsCreator);
        studentSettingsFinder = Container.get(StudentSettingsFinder);
        batchFinder = Container.get(BatchFinderToken);

        course = await IntegrationTestCourse.create();
        batch = await IntegrationTestBatch.create({ courseId: course.getIdOrThrow() });
        anotherBatch = await IntegrationTestBatch.create({ courseId: course.getIdOrThrow() });

        ({ admin, student, staff, mentor } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute());

        anotherStudent = await IntegrationTestUser.createFakeStudent();
        await anotherStudent.authorize();
    });

    beforeEach(async () => {
        await recreateSettings();
    });

    async function recreateSettings(): Promise<void> {
        const settings = await Promise.all(
            [student.params, anotherStudent.params].map((studentEntity) => {
                return studentSettingsRepository.getByStudent(new Id(studentEntity.id));
            }),
        );
        await Promise.all(settings.filter((s) => !!s).map((s) => studentSettingsRepository.delete(s.getIdOrThrow())));
        await student.createStudentSettings(batch.getIdOrThrow());
        await anotherStudent.createStudentSettings(batch.getIdOrThrow());
    }

    describe('GET /students/:id/settings', () => {
        async function testGetSettings(id: Id, token: string | undefined, code: number): Promise<void> {
            const res = await request(config.server)
                .get(`/students/${id.value}/settings`)
                .set('Cookie', `token=${token}`)
                .expect(code);

            if (code === 204) {
                const studentSettings = await studentSettingsFinder.findByUserId(id);
                ArgumentValidation.assert.defined(studentSettings, 'Student settings not found');

                const batch = await batchFinder.findById(studentSettings.batchId);
                ArgumentValidation.assert.defined(batch, 'Batch not found');

                expect(res.body).toEqual(AggregateStudentSettingsDTO.fromCourse(batch.course));
            }
        }

        it('should get its own settings', async () => {
            await testGetSettings(student.getIdOrThrow(), student.getAuthTokenString(), 200);
        });

        it('should not get somebody else settings', async () => {
            await testGetSettings(anotherStudent.getIdOrThrow(), student.getAuthTokenString(), 403);
        });

        it('should not get student settings with admin', async () => {
            await testGetSettings(student.getIdOrThrow(), admin.getAuthTokenString(), 403);
        });

        it('should not get student settings with staff', async () => {
            await testGetSettings(student.getIdOrThrow(), staff.getAuthTokenString(), 403);
        });

        it('should not get student settings with mentor', async () => {
            await testGetSettings(student.getIdOrThrow(), mentor.getAuthTokenString(), 403);
        });
    });

    describe('GET /students/:id/settings/billing-type', () => {
        async function executeRequest(token: string | undefined, expectedCode: number): Promise<any> {
            const res = await request(config.server)
                .get(`/students/${student.getIdOrThrow().value}/settings/billing-type`)
                .set('Cookie', `token=${token}`)
                .send()
                .expect(expectedCode);

            return res.body;
        }

        beforeEach(async () => {
            await recreateSettings();
            await IntegrationTestUztProfile.deleteAll();
        });

        it('should get student billing type info with authorized users', async () => {
            await executeRequest(admin.getAuthTokenString(), 200);
            await executeRequest(staff.getAuthTokenString(), 200);
        });

        it('should not get student billing type info with unauthorized users', async () => {
            await executeRequest(mentor.getAuthTokenString(), 403);
            await executeRequest(student.getAuthTokenString(), 403);
            await executeRequest(undefined, 403);
        });
    });

    describe('PATCH /students/:id/settings/billing-type', () => {
        async function executeRequest(
            dto: StudentBillingTypeChangeDTO,
            token: string | undefined,
            expectedCode: number,
        ): Promise<void> {
            await request(config.server)
                .put(`/students/${student.getIdOrThrow().value}/settings/billing-type`)
                .set('Cookie', `token=${token}`)
                .send(dto)
                .expect(expectedCode);
        }

        beforeEach(async () => {
            await recreateSettings();
            await IntegrationTestUztProfile.deleteAll();
            await IntegrationTestGovProfile.deleteAll();
        });

        it('should change billing type to discount', async () => {
            await executeRequest({ billingType: BillingType.DISCOUNT }, admin.getAuthTokenString(), 204);
            await expect(studentSettingsRepository.getByStudent(student.getIdOrThrow())).resolves.toEqual(
                expect.objectContaining({ billingType: BillingType.DISCOUNT }),
            );
            await expect(IntegrationTestUztProfile.getByUser(student.getIdOrThrow())).resolves.toBeUndefined();
        });

        it('should change billing type to scholarship', async () => {
            await executeRequest({ billingType: BillingType.SCHOLARSHIP }, admin.getAuthTokenString(), 204);
            await expect(studentSettingsRepository.getByStudent(student.getIdOrThrow())).resolves.toEqual(
                expect.objectContaining({ billingType: BillingType.SCHOLARSHIP }),
            );
            await expect(IntegrationTestUztProfile.getByUser(student.getIdOrThrow())).resolves.toBeUndefined();
        });

        it('should change billing type to tuition', async () => {
            await executeRequest({ billingType: BillingType.TUITION }, admin.getAuthTokenString(), 204);
            await expect(studentSettingsRepository.getByStudent(student.getIdOrThrow())).resolves.toEqual(
                expect.objectContaining({ billingType: BillingType.TUITION }),
            );
            await expect(IntegrationTestUztProfile.getByUser(student.getIdOrThrow())).resolves.toBeUndefined();
        });

        it('should change billing type to uzt with new uzt profile', async () => {
            const uztChapter = await IntegrationTestUztChapter.create();
            const uztAgency = await IntegrationTestGovAgency.getOrCreateUzt();
            const govProgram = await IntegrationTestGovProgram.create({
                agencyId: uztAgency.getIdOrThrow(),
            });
            const govGroup = await IntegrationTestGovGroup.create({ programId: govProgram.getIdOrThrow() });
            const uztCoupon = new UztCoupon({ code: faker.string.uuid(), date: new Date() });

            await executeRequest(
                {
                    billingType: BillingType.UZT,
                    uztProfile: new UztProfileCreateDto({
                        chapterId: uztChapter.getIdOrThrow().value,
                        coupon: UztCouponDto.fromUztCoupon(uztCoupon),
                        uztContract: new UztContractDto({
                            number: faker.string.uuid(),
                            date: moment.utc().startOf('day').toDate().toISOString(),
                            term: faker.helpers.enumValue(UztContractTerm),
                        }),
                    }),
                    govProfile: new GovProfileCreateDto({
                        groupId: govGroup.getIdOrThrow().value,
                    }),
                },
                admin.getAuthTokenString(),
                204,
            );
            await expect(studentSettingsRepository.getByStudent(student.getIdOrThrow())).resolves.toEqual(
                expect.objectContaining({ billingType: BillingType.UZT }),
            );
            const uztProfile = await IntegrationTestUztProfile.getByUser(student.getIdOrThrow());
            const govProfile = await IntegrationTestGovProfile.getByUserId(student.getIdOrThrow());
            ArgumentValidation.assert.defined(uztProfile, 'Uzt profile not found');
            ArgumentValidation.assert.defined(govProfile, 'Gov profile not found');
            expect(uztProfile.chapterId).toEqual(uztChapter.getIdOrThrow());
            expect(govProfile.params.groupId).toEqual(govGroup.getIdOrThrow());
            expect(uztProfile.coupon.code).toEqual(uztCoupon.code);
            expect(uztProfile.coupon.date.toISOString()).toEqual(uztCoupon.date.toISOString());
        });

        it('should change billing type to uzt with existing uzt profile', async () => {
            const uztChapter = await IntegrationTestUztChapter.create();
            const uztAgency = await IntegrationTestGovAgency.getOrCreateUzt();
            const govProgram = await IntegrationTestGovProgram.create({
                agencyId: uztAgency.getIdOrThrow(),
            });
            const govGroup = await IntegrationTestGovGroup.create({ programId: govProgram.getIdOrThrow() });
            await IntegrationTestGovProfile.create({
                groupId: govGroup.getIdOrThrow(),
                userId: student.getIdOrThrow(),
            });
            const uztCoupon = new UztCoupon({ code: faker.string.uuid(), date: new Date() });
            await IntegrationTestUztProfile.create({
                userId: student.getIdOrThrow(),
                chapterId: uztChapter.getIdOrThrow(),
            });

            await executeRequest(
                {
                    billingType: BillingType.UZT,
                    uztProfile: new UztProfileCreateDto({
                        chapterId: uztChapter.getIdOrThrow().value,
                        coupon: UztCouponDto.fromUztCoupon(uztCoupon),
                        uztContract: new UztContractDto({
                            number: faker.string.uuid(),
                            date: moment.utc().startOf('day').toDate().toISOString(),
                            term: faker.helpers.enumValue(UztContractTerm),
                        }),
                    }),
                    govProfile: new GovProfileCreateDto({
                        groupId: govGroup.getIdOrThrow().value,
                    }),
                },
                admin.getAuthTokenString(),
                204,
            );
            await expect(studentSettingsRepository.getByStudent(student.getIdOrThrow())).resolves.toEqual(
                expect.objectContaining({ billingType: BillingType.UZT }),
            );
            const uztProfileAfter = await IntegrationTestUztProfile.getByUser(student.getIdOrThrow());
            ArgumentValidation.assert.defined(uztProfileAfter, 'Uzt profile not found');
            const govProfileAfter = await IntegrationTestGovProfile.getByUserId(student.getIdOrThrow());
            ArgumentValidation.assert.defined(govProfileAfter, 'Gov profile not found');
            expect(uztProfileAfter.chapterId).toEqual(uztChapter.getIdOrThrow());
            expect(govProfileAfter.params.groupId).toEqual(govGroup.getIdOrThrow());
            expect(uztProfileAfter.coupon.code).toEqual(uztCoupon.code);
            expect(uztProfileAfter.coupon.date.toISOString()).toEqual(uztCoupon.date.toISOString());
        });

        it('should not change billing type to uzt without uzt properties', async () => {
            await executeRequest({ billingType: BillingType.UZT }, admin.getAuthTokenString(), 400);
        });

        it('should not change with unauthorized users', async () => {
            await executeRequest({ billingType: BillingType.SCHOLARSHIP }, staff.getAuthTokenString(), 403);
            await executeRequest({ billingType: BillingType.SCHOLARSHIP }, mentor.getAuthTokenString(), 403);
            await executeRequest({ billingType: BillingType.SCHOLARSHIP }, student.getAuthTokenString(), 403);
            await executeRequest({ billingType: BillingType.SCHOLARSHIP }, undefined, 403);
        });
    });

    describe('PATCH /students/:id/profile pendingSupport', () => {
        async function testIsPendingSupport(
            isPendingSupport: boolean,
            token: string | undefined,
            expectedCode: number,
        ): Promise<void> {
            await request(config.server)
                .patch(`/students/${student.getIdOrThrow().value}/settings`)
                .set('Cookie', `token=${token}`)
                .send({ isPendingSupport })
                .expect(expectedCode);

            if (expectedCode === 204) {
                const settings = await studentSettingsRepository.getByStudent(student.getIdOrThrow());
                expect(settings).toEqual(expect.objectContaining({ isPendingSupport }));
            }
        }

        beforeEach(async () => {
            await recreateSettings();
        });

        it('should change pending support flag with admin', async () => {
            await testIsPendingSupport(true, admin.getAuthTokenString(), 204);
            await testIsPendingSupport(false, admin.getAuthTokenString(), 204);
        });

        it('should not change pending support flag with with unauthorized users', async () => {
            await testIsPendingSupport(true, staff.getAuthTokenString(), 403);
            await testIsPendingSupport(true, mentor.getAuthTokenString(), 403);
            await testIsPendingSupport(true, student.getAuthTokenString(), 403);
            await testIsPendingSupport(true, undefined, 403);
        });
    });

    describe('PATCH /students/:id/settings isDeadlineMandatory', () => {
        async function testChangeIsDeadlineMandatory(
            isDeadlineMandatory: boolean,
            token: string | undefined,
            expectedCode: number,
        ): Promise<void> {
            await request(config.server)
                .patch(`/students/${student.getIdOrThrow().value}/settings`)
                .set('Cookie', `token=${token}`)
                .send({ isDeadlineMandatory })
                .expect(expectedCode);

            if (expectedCode === 204) {
                const settings = await studentSettingsRepository.getByStudent(student.getIdOrThrow());
                expect(settings).toEqual(expect.objectContaining({ isDeadlineMandatory }));
            }
        }

        beforeEach(async () => {
            await recreateSettings();
        });

        it('should change with admin', async () => {
            await testChangeIsDeadlineMandatory(true, admin.getAuthTokenString(), 204);
            await testChangeIsDeadlineMandatory(false, admin.getAuthTokenString(), 204);
            await testChangeIsDeadlineMandatory(true, admin.getAuthTokenString(), 204);
        });

        it('should not change with unauthorized users', async () => {
            await testChangeIsDeadlineMandatory(true, staff.getAuthTokenString(), 403);
            await testChangeIsDeadlineMandatory(true, mentor.getAuthTokenString(), 403);
            await testChangeIsDeadlineMandatory(true, student.getAuthTokenString(), 403);
            await testChangeIsDeadlineMandatory(true, undefined, 403);
        });
    });

    describe('PATCH /students/:id/settings deadlineCategory', () => {
        async function testChangeDeadlineCategory(
            deadlineCategory: DeadlineCategory,
            token: string | undefined,
            expectedCode: number,
        ): Promise<void> {
            await request(config.server)
                .patch(`/students/${student.getIdOrThrow().value}/settings`)
                .set('Cookie', `token=${token}`)
                .send({ deadlineCategory })
                .expect(expectedCode);

            if (expectedCode === 204) {
                const settings = await studentSettingsRepository.getByStudent(student.getIdOrThrow());
                expect(settings).toEqual(expect.objectContaining({ deadlineCategory }));
            }
        }

        beforeEach(async () => {
            await recreateSettings();
        });

        it('should change with admin', async () => {
            await testChangeDeadlineCategory(DeadlineCategory.B, admin.getAuthTokenString(), 204);
            await testChangeDeadlineCategory(DeadlineCategory.C, admin.getAuthTokenString(), 204);
            await testChangeDeadlineCategory(DeadlineCategory.A, admin.getAuthTokenString(), 204);
        });

        it('should not change with unauthorized users', async () => {
            await testChangeDeadlineCategory(DeadlineCategory.B, staff.getAuthTokenString(), 403);
            await testChangeDeadlineCategory(DeadlineCategory.B, mentor.getAuthTokenString(), 403);
            await testChangeDeadlineCategory(DeadlineCategory.B, student.getAuthTokenString(), 403);
            await testChangeDeadlineCategory(DeadlineCategory.B, undefined, 403);
        });
    });

    describe('PATCH /students/:id/settings isEndorsementEnabled', () => {
        async function testIsEndorsementEnabled(
            isEndorsementEnabled: boolean,
            token: string | undefined,
            expectedCode: number,
        ): Promise<void> {
            await request(config.server)
                .patch(`/students/${student.getIdOrThrow().value}/settings`)
                .set('Cookie', `token=${token}`)
                .send({ isEndorsementEnabled })
                .expect(expectedCode);

            if (expectedCode === 204) {
                const settings = await studentSettingsRepository.getByStudent(student.getIdOrThrow());
                expect(settings).toEqual(expect.objectContaining({ isEndorsementEnabled }));
            }
        }

        beforeEach(async () => {
            await recreateSettings();
        });

        it('should change with admin', async () => {
            await testIsEndorsementEnabled(true, admin.getAuthTokenString(), 204);
            await testIsEndorsementEnabled(false, admin.getAuthTokenString(), 204);
        });

        it('should not change with unauthorized users', async () => {
            await testIsEndorsementEnabled(true, staff.getAuthTokenString(), 403);
            await testIsEndorsementEnabled(true, mentor.getAuthTokenString(), 403);
            await testIsEndorsementEnabled(true, student.getAuthTokenString(), 403);
            await testIsEndorsementEnabled(true, undefined, 403);
        });
    });

    describe('PATCH /students/:id/settings endDate', () => {
        async function testChangeEndDate(
            endDate: Date,
            token: string | undefined,
            expectedCode: number,
        ): Promise<void> {
            await request(config.server)
                .patch(`/students/${student.getIdOrThrow().value}/settings`)
                .set('Cookie', `token=${token}`)
                .send({ endDate })
                .expect(expectedCode);

            if (expectedCode === 204) {
                const settings = await studentSettingsRepository.getByStudent(student.getIdOrThrow());
                ArgumentValidation.assert.defined(settings, 'Settings not found');

                expect(settings.endDate).toEqual(endDate);
            }
        }

        beforeEach(async () => {
            await recreateSettings();
        });

        it('should change with admin', async () => {
            await testChangeEndDate(faker.date.future(), admin.getAuthTokenString(), 204);
        });

        it('should not change with unauthorized users', async () => {
            await testChangeEndDate(faker.date.future(), staff.getAuthTokenString(), 403);
            await testChangeEndDate(faker.date.future(), mentor.getAuthTokenString(), 403);
            await testChangeEndDate(faker.date.future(), student.getAuthTokenString(), 403);
            await testChangeEndDate(faker.date.future(), undefined, 403);
        });
    });

    describe('PUT /students/:id/settings/type', () => {
        let updatingStudent: IntegrationTestUser;

        async function testChangeType(
            startDate: Date,
            endDate: Date,
            token: string | undefined,
            expectedCode: number,
            expectedStartDate: Date,
            expectedEndDate: Date,
            type: StudentType,
        ): Promise<void> {
            await request(config.server)
                .put(`/students/${updatingStudent.getIdOrThrow().value}/settings/type`)
                .set('Cookie', `token=${token}`)
                .send({ type: type.value, startDate, endDate })
                .expect(expectedCode);

            if (expectedCode === 204) {
                const settings = await studentSettingsRepository.getByStudent(updatingStudent.getIdOrThrow());
                ArgumentValidation.assert.defined(settings, 'Settings not found');

                expect(settings.startDate).toEqual(expectedStartDate);
                expect(settings.endDate).toEqual(expectedEndDate);
                expect(settings.studentType).toEqual(type);
            }
        }

        beforeAll(async () => {
            updatingStudent = await IntegrationTestUser.createFakeStudent();
        });

        beforeEach(async () => {
            const settings = await studentSettingsRepository.getByStudent(updatingStudent.getIdOrThrow());
            if (settings) {
                await studentSettingsRepository.delete(settings.getIdOrThrow());
            }
        });

        describe('when student is REGULAR', () => {
            it('should change to JTL', async () => {
                const originalStartDate = moment().subtract(1, 'week').toDate();
                const originalEndDate = moment(originalStartDate).add(1, 'year').toDate();
                await studentSettingsCreator.create({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    startDate: originalStartDate,
                    endDate: originalEndDate,
                    studentType: StudentType.regular(),
                    source: faker.helpers.enumValue(StudentSource),
                });

                await testChangeType(
                    originalStartDate,
                    originalEndDate,
                    admin.getAuthTokenString(),
                    204,
                    originalStartDate,
                    originalEndDate,
                    StudentType.jtl(),
                );
            });

            it('should not affect startDate when changing to JTL with admin', async () => {
                const originalStartDate = moment().subtract(1, 'week').toDate();
                const originalEndDate = moment(originalStartDate).add(1, 'year').toDate();
                await studentSettingsCreator.create({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    startDate: originalStartDate,
                    endDate: originalEndDate,
                    studentType: StudentType.regular(),
                    source: faker.helpers.enumValue(StudentSource),
                });
                const firstStartDate = new Date();
                const firstEndDate = new Date();

                await testChangeType(
                    firstStartDate,
                    firstEndDate,
                    admin.getAuthTokenString(),
                    204,
                    originalStartDate,
                    originalEndDate,
                    StudentType.jtl(),
                );
            });

            it('should not allow to change to TRIAL', async () => {
                await studentSettingsCreator.create({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    startDate: new Date(),
                    endDate: moment().add(1, 'year').toDate(),
                    studentType: StudentType.regular(),
                    source: faker.helpers.enumValue(StudentSource),
                });
                const firstStartDate = new Date();
                const firstEndDate = moment(firstStartDate).add(1, 'year').toDate();

                await testChangeType(
                    firstStartDate,
                    firstEndDate,
                    admin.getAuthTokenString(),
                    400,
                    firstStartDate,
                    firstEndDate,
                    StudentType.trial(),
                );
            });

            it('should allow to change to REGULAR', async () => {
                const originalStartDate = moment().subtract(1, 'week').toDate();
                const originalEndDate = moment(originalStartDate).add(1, 'year').toDate();
                await studentSettingsCreator.create({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    startDate: originalStartDate,
                    endDate: originalEndDate,
                    studentType: StudentType.regular(),
                    source: faker.helpers.enumValue(StudentSource),
                });

                await testChangeType(
                    originalStartDate,
                    originalEndDate,
                    admin.getAuthTokenString(),
                    204,
                    originalStartDate,
                    originalEndDate,
                    StudentType.regular(),
                );
            });
        });

        describe('when student is JTL', () => {
            it('should change to REGULAR', async () => {
                const originalStartDate = moment().subtract(1, 'week').toDate();
                const originalEndDate = moment(originalStartDate).add(1, 'year').toDate();
                await studentSettingsCreator.create({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    startDate: originalStartDate,
                    endDate: originalEndDate,
                    studentType: StudentType.jtl(),
                    source: faker.helpers.enumValue(StudentSource),
                });

                await testChangeType(
                    originalStartDate,
                    originalEndDate,
                    admin.getAuthTokenString(),
                    204,
                    originalStartDate,
                    originalEndDate,
                    StudentType.regular(),
                );
            });

            it('should not affect startDate when changing to REGULAR with admin', async () => {
                const originalStartDate = moment().subtract(1, 'week').toDate();
                const originalEndDate = moment(originalStartDate).add(1, 'year').toDate();
                await studentSettingsCreator.create({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    startDate: originalStartDate,
                    endDate: originalEndDate,
                    studentType: StudentType.jtl(),
                    source: faker.helpers.enumValue(StudentSource),
                });
                const firstStartDate = new Date();
                const firstEndDate = moment(firstStartDate).add(1, 'year').toDate();

                await testChangeType(
                    firstStartDate,
                    firstEndDate,
                    admin.getAuthTokenString(),
                    204,
                    originalStartDate,
                    originalEndDate,
                    StudentType.regular(),
                );
            });

            it('should not allow to change to TRIAL', async () => {
                await studentSettingsCreator.create({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    startDate: new Date(),
                    endDate: moment().add(1, 'year').toDate(),
                    studentType: StudentType.jtl(),
                    source: faker.helpers.enumValue(StudentSource),
                });
                const firstStartDate = new Date();
                const firstEndDate = moment(firstStartDate).add(1, 'year').toDate();

                await testChangeType(
                    firstStartDate,
                    firstEndDate,
                    admin.getAuthTokenString(),
                    400,
                    firstStartDate,
                    firstEndDate,
                    StudentType.trial(),
                );
            });

            it('should allow to change to JTL', async () => {
                const originalStartDate = moment().subtract(1, 'week').toDate();
                const originalEndDate = moment(originalStartDate).add(1, 'year').toDate();
                await studentSettingsCreator.create({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    startDate: originalStartDate,
                    endDate: originalEndDate,
                    studentType: StudentType.jtl(),
                    source: faker.helpers.enumValue(StudentSource),
                });

                await testChangeType(
                    originalStartDate,
                    originalEndDate,
                    admin.getAuthTokenString(),
                    204,
                    originalStartDate,
                    originalEndDate,
                    StudentType.jtl(),
                );
            });
        });

        describe('when student is TRIAL', () => {
            it('should change to REGULAR with admin', async () => {
                await studentSettingsCreator.createTrial({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    studentType: StudentType.trial(),
                    source: faker.helpers.enumValue(StudentSource),
                });
                const firstStartDate = new Date();
                const firstEndDate = moment(firstStartDate).add(1, 'year').toDate();

                await testChangeType(
                    firstStartDate,
                    firstEndDate,
                    admin.getAuthTokenString(),
                    204,
                    firstStartDate,
                    firstEndDate,
                    StudentType.regular(),
                );
            });

            it('should change to JTL with admin', async () => {
                await studentSettingsCreator.createTrial({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    studentType: StudentType.trial(),
                    source: faker.helpers.enumValue(StudentSource),
                });
                const firstStartDate = new Date();
                const firstEndDate = moment(firstStartDate).add(1, 'year').toDate();

                await testChangeType(
                    firstStartDate,
                    firstEndDate,
                    admin.getAuthTokenString(),
                    204,
                    firstStartDate,
                    firstEndDate,
                    StudentType.jtl(),
                );
            });

            it('should not allow to change to TRIAL with admin', async () => {
                await studentSettingsCreator.createTrial({
                    studentId: updatingStudent.getIdOrThrow(),
                    batchId: batch.getIdOrThrow(),
                    studentType: StudentType.trial(),
                    source: faker.helpers.enumValue(StudentSource),
                });
                const originalStartDate = moment().subtract(1, 'week').toDate();
                const originalEndDate = moment(originalStartDate).add(1, 'year').toDate();

                await testChangeType(
                    originalStartDate,
                    originalEndDate,
                    admin.getAuthTokenString(),
                    400,
                    originalStartDate,
                    originalEndDate,
                    StudentType.trial(),
                );
            });
        });

        it('should not change type with unauthorized users', async () => {
            await studentSettingsCreator.createTrial({
                studentId: updatingStudent.getIdOrThrow(),
                batchId: batch.getIdOrThrow(),
                studentType: StudentType.trial(),
                source: faker.helpers.enumValue(StudentSource),
            });
            const startDate = new Date();
            const endDate = moment(startDate).add(1, 'year').toDate();

            await testChangeType(
                startDate,
                endDate,
                staff.getAuthTokenString(),
                403,
                startDate,
                endDate,
                StudentType.regular(),
            );
            await testChangeType(
                startDate,
                endDate,
                mentor.getAuthTokenString(),
                403,
                startDate,
                endDate,
                StudentType.regular(),
            );
            await testChangeType(
                startDate,
                endDate,
                student.getAuthTokenString(),
                403,
                startDate,
                endDate,
                StudentType.regular(),
            );
            await testChangeType(startDate, endDate, undefined, 403, startDate, endDate, StudentType.regular());
        });
    });

    describe('PUT /students/:id/settings/batch', () => {
        async function testChangeBatch(batchId: Id, token: string | undefined, expectedCode: number): Promise<void> {
            await request(config.server)
                .put(`/students/${student.getIdOrThrow().value}/settings/batch`)
                .set('Cookie', `token=${token}`)
                .send({ batchId: batchId.value })
                .expect(expectedCode);

            if (expectedCode === 204) {
                const settings = await studentSettingsRepository.getByStudent(student.getIdOrThrow());
                expect(settings).toEqual(expect.objectContaining({ batchId }));
            }
        }

        beforeEach(async () => {
            await recreateSettings();
        });

        it('should change batch with admin', async () => {
            await testChangeBatch(anotherBatch.getIdOrThrow(), admin.getAuthTokenString(), 204);
        });

        it('should not change batch with unauthorized users', async () => {
            await testChangeBatch(anotherBatch.getIdOrThrow(), staff.getAuthTokenString(), 403);
            await testChangeBatch(anotherBatch.getIdOrThrow(), mentor.getAuthTokenString(), 403);
            await testChangeBatch(anotherBatch.getIdOrThrow(), student.getAuthTokenString(), 403);
            await testChangeBatch(anotherBatch.getIdOrThrow(), undefined, 403);
        });
    });
});
