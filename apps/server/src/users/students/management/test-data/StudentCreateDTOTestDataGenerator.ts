import StudentCreateDTO, { StudentCreateProfileDto } from '../controllers/http/dto/StudentCreateDTO';
import { BillingType } from '../../settings/domain/BillingType';
import { DeadlineCategory } from '../../settings/domain/DeadlineCategory';
import { GovProfileCreateDto } from '../../../../gov/submodules/gov-profile/controllers/http/dto/GovProfileCreateDto';
import { StudentSource } from '../../settings/domain/StudentSource';
import { StudentTypeOption } from '../../settings/domain/StudentType';
import { UztProfileCreateDto } from '../../../../uzt/submodules/uzt-profile/controllers/http/dto/UztProfileCreateDto';
import { UztCouponDto } from '../../../../uzt/submodules/uzt-profile/controllers/http/dto/UztCouponDto';
import { UztContractDto } from '../../../../uzt/submodules/uzt-profile/controllers/http/dto/UztContractDto';
import { UztContractTerm } from '../../../shared/domain/UztContractTerm';
import { StudentProfileSetupAcademicHistoryUpdateParamsDto } from '../../../shared/controllers/http/dto/StudentProfileSetupAcademicHistorySetParamsDto';
import { EducationLevel } from '../../../shared/domain/EducationLevel';
import { FieldOfStudy } from '../../../shared/domain/FieldOfStudy';
import { StudentProfileSetupUpdatePersonalInfoParamsDto } from '../../../shared/controllers/http/dto/StudentProfileSetupPersonalInfoParamsDto';
import { Gender } from '../../../shared/domain/Gender';
import { Timezone } from '../../../../core/domain/types/Timezone';
import { faker } from '@faker-js/faker';
import { UpdateStudentProfileSetupComplianceParamsDto } from '../../../shared/controllers/http/dto/StudentProfileSetupComplianceParamsDto';
import { CountryCodeIso2 } from '../../../../physical-address/domain/types/CountryCodeIso2';
import { StudentProfileSetupUpdateWorkRelatedParamsDto } from '../../../shared/controllers/http/dto/StudentProfileSetupWorkRelatedUpdateParamsDto';
import { EmploymentStatus } from '../../../shared/domain/EmploymentStatus';
import { WorkIndustry } from '../../../shared/domain/WorkIndustry';
import { YearsOfExperience } from '../../../shared/domain/YearsOfExperience';
import { FuzzyBoolean } from '../../../shared/domain/FuzzyBoolean';

export class StudentCreateDTOTestDataGenerator {
    static forUztLearner(): StudentCreateDTO {
        return new StudentCreateDTO({
            batchId: 1,
            billingType: BillingType.UZT,
            deadlineCategory: DeadlineCategory.A,
            email: '<EMAIL>',
            gov: new GovProfileCreateDto({ groupId: 4 }),
            isDeadlineMandatory: false,
            isEndorsementEnabled: false,
            source: StudentSource.BUSINESS,
            studentType: StudentTypeOption.JTL,
            userTagIds: [],
            uzt: new UztProfileCreateDto({
                coupon: new UztCouponDto({
                    code: 'NSK-9142',
                    date: '2023-10-01T00:00:00.000Z',
                }),
                uztContract: new UztContractDto({
                    number: '72714',
                    date: '2023-10-01T00:00:00.000Z',
                    term: UztContractTerm.BILATERAL_TO_EMPLOYMENT,
                }),
                chapterId: 5,
            }),
            profile: {},
        });
    }

    static forAfaLearner(): StudentCreateDTO {
        return new StudentCreateDTO({
            batchId: 1,
            billingType: BillingType.AFA,
            deadlineCategory: DeadlineCategory.A,
            email: '<EMAIL>',
            gov: new GovProfileCreateDto({ groupId: 4 }),
            isDeadlineMandatory: false,
            isEndorsementEnabled: false,
            source: StudentSource.BUSINESS,
            studentType: StudentTypeOption.JTL,
            userTagIds: [],
            profile: {},
        });
    }

    static forDfeLearner(): StudentCreateDTO {
        return new StudentCreateDTO({
            batchId: 1,
            billingType: BillingType.DFE,
            deadlineCategory: DeadlineCategory.A,
            email: '<EMAIL>',
            gov: new GovProfileCreateDto({ groupId: 4 }),
            isDeadlineMandatory: false,
            isEndorsementEnabled: false,
            source: StudentSource.BUSINESS,
            studentType: StudentTypeOption.JTL,
            userTagIds: [],
            profile: {},
        });
    }

    static forTuitionWithFilledProfile(): StudentCreateDTO {
        return new StudentCreateDTO({
            batchId: 1,
            billingType: BillingType.TUITION,
            deadlineCategory: DeadlineCategory.A,
            email: '<EMAIL>',
            isDeadlineMandatory: false,
            isEndorsementEnabled: false,
            source: StudentSource.BUSINESS,
            studentType: StudentTypeOption.JTL,
            userTagIds: [],
            profile: new StudentCreateProfileDto({
                academicHistory: new StudentProfileSetupAcademicHistoryUpdateParamsDto({
                    highestEducationLevel: EducationLevel.HIGH_SCHOOL,
                    fieldOfStudy: FieldOfStudy.DATA_SCIENCE,
                    subjectOfStudy: 'Computer Science',
                    institutionName: 'MIT',
                    graduationYear: 2000,
                }),
                personalInfo: new StudentProfileSetupUpdatePersonalInfoParamsDto({
                    name: faker.person.fullName(),
                    surname: faker.person.lastName(),
                    gender: Gender.FEMALE,
                    phoneNumber: faker.phone.number(),
                    timezone: faker.helpers.enumValue(Timezone),
                }),
                compliance: new UpdateStudentProfileSetupComplianceParamsDto({
                    citizenshipCountry: faker.helpers.enumValue(CountryCodeIso2),
                    countryPersonalCode: faker.string.numeric(11),
                    residenceCountry: faker.helpers.enumValue(CountryCodeIso2),
                    residenceFullAddressString: faker.location.streetAddress(),
                    dateOfBirth: faker.date.birthdate().toISOString(),
                    timezone: faker.helpers.enumValue(Timezone),
                }),
                workRelated: new StudentProfileSetupUpdateWorkRelatedParamsDto({
                    employmentStatus: faker.helpers.enumValue(EmploymentStatus),
                    workIndustry: faker.helpers.enumValue(WorkIndustry),
                    companyName: faker.company.name(),
                    jobTitle: faker.company.buzzVerb(),
                    seniorityLevel: faker.helpers.enumValue(YearsOfExperience),
                    isPlanningToHuntTheJobAfterCourse: faker.helpers.enumValue(FuzzyBoolean),
                }),
            }),
        });
    }
}
