import { Job } from 'pg-boss';
import { Queue<PERSON>or<PERSON> } from '../../../../../queue/domain/QueueWorker';
import { AppQueues } from '../../../../../queue/domain/AppQueues';
import { LearnerSuspendedNotifyByPlatformCommand } from './dto/LearnerSuspendedNotifyByPlatformCommand';
import { NotificationType } from '../../../../../notifications/domain/Notification';
import { NotificationManager } from '../../../../../notifications/NotificationManager';
import TransactionManager from '../../../../../core/infrastructure/TransactionManager';

export class LearnerSuspendedNotifyPlatformCommandWorker implements QueueWorker {
    readonly queuePattern = AppQueues.schema.user.learner.suspension.notify.platform.wildcard;

    constructor(
        private readonly notificationManager: NotificationManager,
        private readonly transactionManager: TransactionManager,
    ) {}

    async handler(job: Job<LearnerSuspendedNotifyByPlatformCommand>): Promise<void> {
        const command = AppQueues.getMapperByClass(LearnerSuspendedNotifyByPlatformCommand).deserialize(job.data);

        await this.transactionManager.execute(async (tx) => {
            await this.notificationManager.acknowledgeUserNotificationsByTypes({
                userId: command.userId,
                types: [NotificationType.LEARNING_UPCOMING_SUSPENSION, NotificationType.LEARNING_SUSPENDED],
                tx,
            });
            await this.notificationManager.createNotification(
                {
                    userId: command.userId,
                    type: NotificationType.LEARNING_SUSPENDED,
                    title: '-',
                    description: '-',
                    payload: {
                        reason: command.reason,
                    },
                },
                tx,
            );
        });
    }
}
