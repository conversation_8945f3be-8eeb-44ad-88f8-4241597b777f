import SingleValue from '../../../../core/domain/value-objects/SingleValue';
import Comparable from '../../../../core/utils/Comparable';
import ArgumentValidation from '../../../../core/utils/validation/ArgumentValidation';
import StateValidation from '../../../../core/utils/validation/StateValidation';

export default class CorrectionPointsBalance
    extends SingleValue<number>
    implements Comparable<CorrectionPointsBalance>
{
    constructor(value = 0) {
        super(value);
    }

    protected validate(value: number): number {
        ArgumentValidation.assert.min(value, 0, 'Balance has to be >= 0');
        return value;
    }

    credit(amount: number): CorrectionPointsBalance {
        ArgumentValidation.assert.min(amount, 0, 'Credit amount has to be >= 0');

        return new CorrectionPointsBalance(this.value + amount);
    }

    debit(amount: number): CorrectionPointsBalance {
        ArgumentValidation.assert.min(amount, 0, 'Debit amount has to be >= 0');
        StateValidation.isValid(this.value >= amount, 'Insufficient correction points balance');

        return new CorrectionPointsBalance(this.value - amount);
    }

    compareTo(other: CorrectionPointsBalance): number {
        return this.value - other.value;
    }
}
