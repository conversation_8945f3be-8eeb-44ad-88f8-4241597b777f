import { IsInt } from 'class-validator';
import StudentProfile from '../../../../domain/StudentProfile';

export default class CorrectionPointsBalanceDTO {
    @IsInt()
    readonly studentId: number;

    @IsInt()
    readonly amount: number;

    static fromStudentProfile(profile: StudentProfile): CorrectionPointsBalanceDTO {
        return {
            studentId: profile.userId.value,
            amount: profile.correctionPoints.value,
        };
    }
}
