import { Authorized, Body, CurrentUser, Get, <PERSON><PERSON>C<PERSON><PERSON><PERSON>, <PERSON><PERSON>, Post } from 'routing-controllers';
import { OpenAPI, ResponseSchema } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import { ifStudentAllowOnlySelf } from '../../../../../../core/controllers/ifStudentAllowOnlySelf';
import { DateString } from '../../../../../../core/domain/value-objects/DateString';
import Id from '../../../../../../core/domain/value-objects/Id';
import NotFoundError from '../../../../../../core/errors/NotFoundError';
import { PhysicalAddress } from '../../../../../../physical-address/domain/value-objects/PhysicalAddress';
import { StudentProfileSetupStepName } from '../../../../../../profile-setup-steps/domain/StudentProfileSetupStepName';
import { UserOnboardingService } from '../../../../../../profile-setup-steps/services/UserOnboardingService';
import { Objects } from '../../../../../../utils/Objects';
import { KeyOfFieldsIntersection } from '../../../../../../utils/UtilityTypes';
import {
    GetStudentProfileSetupComplianceParamsDto,
    SetStudentProfileSetupComplianceParamsDto,
} from '../../../../../shared/controllers/http/dto/StudentProfileSetupComplianceParamsDto';
import { CountryPersonalCode } from '../../../../../shared/domain/valueObjects/CountryPersonalCode';
import User, { Role } from '../../../../../shared/infrastructure/db/User';
import StudentProfile from '../../../domain/StudentProfile';
import { StudentProfileFinder } from '../../../services/StudentProfileFinder';
import { StudentProfileManager } from '../../../services/StudentProfileManager';
import { STUDENTS_PROFILE_SETUP_OPEN_API_SPEC } from './studentsProfileSetupOpenApiSpec';

@Service()
@OpenAPI(STUDENTS_PROFILE_SETUP_OPEN_API_SPEC)
@JsonController('/students/:studentId/profile/compliance')
export default class StudentsProfileSetupComplianceController {
    private simpleMappingKeys: KeyOfFieldsIntersection<
        StudentProfile,
        SetStudentProfileSetupComplianceParamsDto | GetStudentProfileSetupComplianceParamsDto
    >[] = ['citizenshipCountry', 'timezone'];

    constructor(
        @Inject()
        private readonly studentProfileManager: StudentProfileManager,
        @Inject()
        private readonly studentProfileFinder: StudentProfileFinder,
        @Inject()
        private readonly userOnboardingService: UserOnboardingService,
    ) {}

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Post()
    @OpenAPI({
        description: "Updates student's profile with compliance info step.",
    })
    async updateStudentProfileWithComplianceInfo(
        @Param('studentId') studentIdInt: number,
        @Body() stepInfo: SetStudentProfileSetupComplianceParamsDto,
        @CurrentUser() actor: User,
    ): Promise<void> {
        ifStudentAllowOnlySelf(actor, studentIdInt);
        const studentId = new Id(studentIdInt);
        await this.studentProfileManager.updateProfile(studentId, {
            // @ts-expect-error TS(2345) FIXME: Argument of type 'NonFunctionPropertyNames<Student... Remove this comment to see the full error message
            ...Objects.pick(stepInfo, this.simpleMappingKeys),
            // @ts-expect-error TS(2322) FIXME: Type 'CountryPersonalCode | null' is not assignabl... Remove this comment to see the full error message
            personalCode: stepInfo.countryPersonalCode
                ? CountryPersonalCode.fromString(stepInfo.countryPersonalCode, stepInfo.citizenshipCountry)
                : null,
            physicalAddress: PhysicalAddress.fromParams({
                countryCode: stepInfo.residenceCountry,
                // @ts-expect-error TS(2322) FIXME: Type 'string | undefined' is not assignable to typ... Remove this comment to see the full error message
                full: stepInfo.residenceFullAddressString,
            }),
            // @ts-expect-error TS(2322) FIXME: Type 'DateString | undefined' is not assignable to typ... Remove this comment to see the full error message
            dateOfBirth: stepInfo.dateOfBirth ? DateString.fromString(stepInfo.dateOfBirth) : null,
        });
        await this.userOnboardingService.completeStep(studentId, StudentProfileSetupStepName.COMPLIANCE);
    }

    @Authorized([Role.ADMIN, Role.STAFF, Role.USER])
    @Get()
    @OpenAPI({
        description: "Get student's profile with compliance info step.",
    })
    @ResponseSchema(GetStudentProfileSetupComplianceParamsDto)
    async getStudentProfileComplianceInfo(
        @Param('studentId') studentIdInt: number,
        @CurrentUser() actor: User,
    ): Promise<Partial<GetStudentProfileSetupComplianceParamsDto>> {
        ifStudentAllowOnlySelf(actor, studentIdInt);
        const studentProfile = await this.studentProfileFinder.findByUserId(new Id(studentIdInt));
        if (!studentProfile) {
            throw new NotFoundError('Student profile not found');
        }

        return {
            // @ts-expect-error TS(2345) FIXME: Argument of type 'StudentProfile | undefined' is n... Remove this comment to see the full error message
            ...Objects.pick(studentProfile, this.simpleMappingKeys),
            // @ts-expect-error TS(2322) FIXME: Type 'string | null' is not assignable to type 'st... Remove this comment to see the full error message
            countryPersonalCode: studentProfile.personalCode ? studentProfile.personalCode.value : null,
            residenceFullAddressString: studentProfile.physicalAddress?.full,
            residenceCountry: studentProfile.physicalAddress?.countryCode,
            residenceAddressIsVerified: studentProfile.physicalAddress?.isVerified || false,
            residencePostalCode: studentProfile.physicalAddress?.postalCode,
            residenceCity: studentProfile.physicalAddress?.city,
            dateOfBirth: studentProfile.dateOfBirth?.value,
        };
    }
}
