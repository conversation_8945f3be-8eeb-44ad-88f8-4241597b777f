import TypeormRepository from '../../../../../core/infrastructure/db/TypeormRepository';
import TransactionManager from '../../../../../core/infrastructure/TransactionManager';
import CorrectionPointsTransaction from '../../domain/CorrectionPointsTransaction';
import CorrectionPointsTransactionRepository from '../../domain/CorrectionPointsTransactionRepository';
import CorrectionPointsTransactionMapper from './CorrectionPointsTransactionMapper';
import CorrectionPointsTransactionPersistenceEntity from './CorrectionPointsTransactionPersistenceEntity';

export default class CorrectionPointsTransactionTypeormRepository
    extends TypeormRepository<CorrectionPointsTransaction, CorrectionPointsTransactionPersistenceEntity>
    implements CorrectionPointsTransactionRepository
{
    constructor(tm: TransactionManager) {
        super(tm, new CorrectionPointsTransactionMapper(), CorrectionPointsTransactionPersistenceEntity);
    }
}
