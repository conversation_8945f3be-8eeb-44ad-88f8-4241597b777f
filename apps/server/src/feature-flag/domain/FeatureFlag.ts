export enum FeatureFlag {
    TEST_FLAG = 'test-flag',
    COST_TRACKING = 'cost-tracking',
    UZT_ZINIARASTIS_DISABLE_NUMBERS_ASSERT = 'uzt-ziniarastis-disable-numbers-assert',
    UZT_TVARKARASTIS_HIDE_CHANGED_DAYS = 'uzt-tvarkarastis-hide-changed-days',
    UZT_TVARKARASTIS_HIDE_CONSENT_TIMESTAMPS = 'uzt-tvarkarastis-hide-consent-timestamps',
    UZT_TVARKARASTIS_HIDE_RELATED_CONSENT_CHANGES = 'uzt-tvarkarastis-hide-related-consent-changes',
    STRICT_ACADEMIC_HOUR_DATE = 'strict-academic-hour-date',
    EDIT_SCHEDULE_GROUP_COUNT = 'edit-schedule-group-count',
    UZT_AUTO_FILL_DEFAULT_SCHEDULE = 'uzt-auto-fill-default-schedule',
    PRIEDAS_SIGNER_EMAIL = 'priedas-signer-email',
    WAITING_ROOM_ONBOARDING = 'waiting-room-onboarding',
}
