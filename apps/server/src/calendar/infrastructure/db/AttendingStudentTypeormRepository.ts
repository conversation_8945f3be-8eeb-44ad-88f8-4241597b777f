import Id from '../../../core/domain/value-objects/Id';
import TypeormBaseRepository from '../../../core/infrastructure/db/TypeormBaseRepository';
import Transaction from '../../../core/infrastructure/Transaction';
import TransactionManager from '../../../core/infrastructure/TransactionManager';
import AttendingStudent from '../../domain/attendance/AttendingStudent';
import AttendingStudentRepository from '../../domain/attendance/AttendingStudentRepository';
import AttendingStudentMapper from './AttendingStudentMapper';
import AttendingStudentPersistenceEntity from './AttendingStudentPersistenceEntity';

export default class AttendingStudentTypeormRepository
    extends TypeormBaseRepository<AttendingStudent, AttendingStudentPersistenceEntity>
    implements AttendingStudentRepository
{
    constructor(tm: TransactionManager) {
        super(tm, new AttendingStudentMapper(), AttendingStudentPersistenceEntity);
    }

    async getByEvent(eventId: Id, tx?: Transaction): Promise<AttendingStudent[]> {
        return await this.getAllWhere({ eventId: eventId.value }, tx);
    }

    async saveAll(attendingStudents: AttendingStudent[], existingTx?: Transaction): Promise<void> {
        await this.transactionManager.execute({
            action: async (tx) => {
                await tx.entityManager.save(attendingStudents.map((b) => this.mapper.toPersistence(b)));
            },
            transaction: existingTx,
            errorMapper: this.errorMapper,
        });
    }

    async deleteByEvent(eventId: Id, existingTx?: Transaction): Promise<void> {
        await this.transactionManager.execute({
            action: async (tx) => {
                await tx.entityManager.delete(this.type, {
                    eventId: eventId.value,
                });
            },
            transaction: existingTx,
            errorMapper: this.errorMapper,
        });
    }

    async deleteByUser(userId: Id, existingTx?: Transaction): Promise<void> {
        await this.transactionManager.execute({
            action: async (tx) => {
                await tx.entityManager.delete(this.type, {
                    userId: userId.value,
                });
            },
            transaction: existingTx,
            errorMapper: this.errorMapper,
        });
    }
}
