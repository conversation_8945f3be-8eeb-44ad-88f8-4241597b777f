import moment from 'moment';
import ical from 'node-ical';
import request from 'supertest';
import Container from 'typedi';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import { Timezone } from '../../../core/domain/types/Timezone';
import Id from '../../../core/domain/value-objects/Id';
import Url from '../../../core/domain/value-objects/Url';
import { TransactionManagerToken } from '../../../core/infrastructure/di/tokens';
import { IntegrationTestBatch } from '../../../test-toolkit/e2e/entities/IntegrationTestBatch';
import { IntegrationTestCourse } from '../../../test-toolkit/e2e/entities/IntegrationTestCourse';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';
import Mentor from '../../../users/mentors/infrastructure/db/Mentor';
import Staff from '../../../users/staff/infrastructure/db/Staff';
import CalendarEvent from '../../domain/CalendarEvent';
import CalendarEventCategory from '../../domain/CalendarEventCategory';
import CalendarEventDescription from '../../domain/CalendarEventDescription';
import CalendarEventExcludedDate from '../../domain/CalendarEventExcludedDate';
import CalendarEventRecurrenceRule from '../../domain/CalendarEventRecurrenceRule';
import CalendarEventRepository from '../../domain/CalendarEventRepository';
import CalendarEventStatus from '../../domain/CalendarEventStatus';
import CalendarEventSummary from '../../domain/CalendarEventSummary';
import CalendarEventTime from '../../domain/CalendarEventTime';
import RecurrenceRule from '../../domain/recurrence/RecurrenceRule';
import RecurringCalendarEvent from '../../domain/RecurringCalendarEvent';
import SimpleCalendarEvent from '../../domain/SimpleCalendarEvent';
import CalendarEventTypeormRepository from '../../infrastructure/db/CalendarEventTypeormRepository';
import { CalendarEventAttendanceManagerToken } from '../../infrastructure/di/tokens';
import { CalendarEventAttendanceManager } from '../../services/CalendarEventAttendanceManager';
import AttendanceOptions from '../../services/dto/AttendanceOptions';
import CalendarIcsController from './CalendarIcsController';

describe(CalendarIcsController.name, () => {
    let config: Configuration;
    let calendarEventRepository: CalendarEventRepository;
    let attendanceManager: CalendarEventAttendanceManager;
    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let mentor: IntegrationTestUser<Mentor>;
    let student: IntegrationTestUser;

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        calendarEventRepository = new CalendarEventTypeormRepository(Container.get(TransactionManagerToken));
        attendanceManager = Container.get(CalendarEventAttendanceManagerToken);

        ({ admin, student, staff, mentor } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute());

        const course = await IntegrationTestCourse.create();
        const batch = await IntegrationTestBatch.create({ courseId: course.params.getIdOrThrow() });
        await student.createStudentSettings(batch.params.getIdOrThrow());
    });

    describe('GET /calendar/ics', () => {
        let events: CalendarEvent[] = [];

        beforeAll(async () => {
            events = [
                await calendarEventRepository.save(
                    new RecurringCalendarEvent({
                        category: CalendarEventCategory.MEETING,
                        summary: new CalendarEventSummary('Daily meeting'),
                        description: new CalendarEventDescription('Daily not ending meeting'),
                        url: new Url('https://zoom.us'),
                        status: CalendarEventStatus.CONFIRMED,
                        time: new CalendarEventTime(new Date('2021-01-01 09:00:00Z'), new Date('2021-01-01 09:30:00Z')),
                        recurrence: new CalendarEventRecurrenceRule(
                            RecurrenceRule.daily({ start: new Date('2021-01-01 09:00:00Z') }),
                        ),
                    }),
                ),
                await calendarEventRepository.save(
                    new SimpleCalendarEvent({
                        category: CalendarEventCategory.CORRECTION,
                        summary: new CalendarEventSummary('Last week correction'),
                        description: new CalendarEventDescription('Last week correction'),
                        url: new Url('https://zoom.us'),
                        status: CalendarEventStatus.CONFIRMED,
                        time: new CalendarEventTime(new Date('2021-01-02 10:00:00Z'), new Date('2021-01-02 10:45:00Z')),
                    }),
                ),
                await calendarEventRepository.save(
                    new RecurringCalendarEvent({
                        category: CalendarEventCategory.MEETING,
                        summary: new CalendarEventSummary('Weekly staff meeting'),
                        description: new CalendarEventDescription('Weekly staff meeting'),
                        url: new Url('https://zoom.us'),
                        status: CalendarEventStatus.CONFIRMED,
                        time: new CalendarEventTime(new Date('2021-01-01 11:00:00Z'), new Date('2021-01-01 12:00:00Z')),
                        recurrence: new CalendarEventRecurrenceRule(
                            RecurrenceRule.weekly({
                                start: new Date('2021-01-01 11:00:00Z'),
                                until: new Date('2021-02-01 11:00:00Z'),
                            }),
                            [
                                new CalendarEventExcludedDate(new Date('2021-01-02 11:00:00Z')),
                                new CalendarEventExcludedDate(new Date('2021-01-03 11:00:00Z')),
                            ],
                        ),
                    }),
                ),
            ];
            await attendanceManager.updateAttendance(
                events[0].getIdOrThrow(),
                AttendanceOptions.usersOnly(
                    new Id(staff.params.id),
                    new Id(mentor.params.id),
                    new Id(student.params.id),
                ),
            );
            await attendanceManager.updateAttendance(
                events[1].getIdOrThrow(),
                AttendanceOptions.usersOnly(new Id(mentor.params.id), new Id(student.params.id)),
            );
            await attendanceManager.updateAttendance(
                events[2].getIdOrThrow(),
                AttendanceOptions.usersOnly(new Id(admin.params.id), new Id(staff.params.id)),
            );
        });

        it('should get student calendar', async () => {
            const response = await request(config.server)
                .get(`/calendar/ics/user/${student.params.calendarToken}`)
                .send()
                .expect(200);

            const calendar = ical.parseICS(response.text);

            const vevents = Object.keys(calendar)
                .map((key) => calendar[key])
                .filter((event) => event.type === 'VEVENT');
            expect(vevents).toHaveLength(2);

            expect(calendar[events[0].uuid.value]).toEqual(
                expect.objectContaining({
                    type: 'VEVENT',
                    uid: events[0].uuid.value,
                    start: events[0].time.start,
                    end: events[0].time.end,
                    summary: events[0].summary.value,
                    description: events[0].description.value,
                    url: expect.objectContaining({
                        val: events[0].url?.value,
                    }),
                    status: events[0].status.toUpperCase(),
                    rrule: expect.objectContaining({
                        options: expect.objectContaining({
                            freq: 3, // Daily
                            tzid: Timezone.EUROPE_VILNIUS,
                            // That's how RRule library handles the date. It doesn't affect the ICS file.
                            dtstart: moment
                                .tz((events[0] as RecurringCalendarEvent).recurrence.start, Timezone.EUROPE_VILNIUS)
                                .utc(true)
                                .toDate(),
                        }),
                    }),
                }),
            );
            expect(calendar[events[1].uuid.value]).toEqual(
                expect.objectContaining({
                    type: 'VEVENT',
                    uid: events[1].uuid.value,
                    start: events[1].time.start,
                    end: events[1].time.end,
                    summary: events[1].summary.value,
                    url: expect.objectContaining({
                        val: events[1].url?.value,
                    }),
                    status: events[1].status.toUpperCase(),
                }),
            );
        });

        it('should get admin calendar', async () => {
            const response = await request(config.server)
                .get(`/calendar/ics/user/${admin.params.calendarToken}`)
                .send()
                .expect(200);

            const calendar = ical.parseICS(response.text);

            const vevents = Object.keys(calendar)
                .map((key) => calendar[key])
                .filter((event) => event.type === 'VEVENT');
            expect(vevents).toHaveLength(1);

            expect(calendar[events[2].uuid.value]).toEqual(
                expect.objectContaining({
                    type: 'VEVENT',
                    uid: events[2].uuid.value,
                    start: events[2].time.start,
                    end: events[2].time.end,
                    summary: events[2].summary.value,
                    url: expect.objectContaining({
                        val: events[2].url?.value,
                    }),
                    status: events[2].status.toUpperCase(),
                    rrule: expect.objectContaining({
                        options: expect.objectContaining({
                            freq: 2, // Weekly
                            tzid: Timezone.EUROPE_VILNIUS,
                            dtstart: moment
                                .tz((events[2] as RecurringCalendarEvent).recurrence.start, Timezone.EUROPE_VILNIUS)
                                .utc(true)
                                .toDate(),
                            until: (events[2] as RecurringCalendarEvent).recurrence.until,
                        }),
                    }),
                }),
            );
        });

        it('should not get non-existent calendar', async () => {
            await request(config.server).get('/calendar/ics/user/wrong-token').send().expect(404);
        });
    });
});
