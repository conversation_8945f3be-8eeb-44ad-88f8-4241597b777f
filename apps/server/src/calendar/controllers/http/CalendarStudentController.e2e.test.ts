import { faker } from '@faker-js/faker';
import request from 'supertest';
import Container from 'typedi';
import CalendarStudentController from './CalendarStudentController';
import CalendarEventDTO from './dto/CalendarEventDTO';
import CalendarEvent from '../../domain/CalendarEvent';
import CalendarEventCategory from '../../domain/CalendarEventCategory';
import CalendarEventDescription from '../../domain/CalendarEventDescription';
import CalendarEventExcludedDate from '../../domain/CalendarEventExcludedDate';
import CalendarEventLabel from '../../domain/CalendarEventLabel';
import CalendarEventLabelRepository from '../../domain/CalendarEventLabelRepository';
import CalendarEventRecurrenceRule from '../../domain/CalendarEventRecurrenceRule';
import CalendarEventRepository from '../../domain/CalendarEventRepository';
import CalendarEventStatus from '../../domain/CalendarEventStatus';
import CalendarEventSummary from '../../domain/CalendarEventSummary';
import CalendarEventTime from '../../domain/CalendarEventTime';
import Label from '../../domain/Label';
import RecurrenceRule from '../../domain/recurrence/RecurrenceRule';
import RecurringCalendarEvent from '../../domain/RecurringCalendarEvent';
import SimpleCalendarEvent from '../../domain/SimpleCalendarEvent';
import CalendarEventLabelTypeormRepository from '../../infrastructure/db/CalendarEventLabelTypeormRepository';
import CalendarEventTypeormRepository from '../../infrastructure/db/CalendarEventTypeormRepository';
import { CalendarEventAttendanceManagerToken } from '../../infrastructure/di/tokens';
import { CalendarEventAttendanceManager } from '../../services/CalendarEventAttendanceManager';
import AttendanceOptions from '../../services/dto/AttendanceOptions';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import Url from '../../../core/domain/value-objects/Url';
import { TransactionManagerToken } from '../../../core/infrastructure/di/tokens';
import Mentor from '../../../users/mentors/infrastructure/db/Mentor';
import Staff from '../../../users/staff/infrastructure/db/Staff';
import { IntegrationTestBatch } from '../../../test-toolkit/e2e/entities/IntegrationTestBatch';
import { IntegrationTestCourse } from '../../../test-toolkit/e2e/entities/IntegrationTestCourse';
import { IntegrationTestUser } from '../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';
import TestObjects from '../../../test-toolkit/shared/TestObjects';

describe(CalendarStudentController.name, () => {
    let configuration: Configuration;
    let calendarEventRepository: CalendarEventRepository;
    let calendarEventLabelRepository: CalendarEventLabelRepository;
    let attendanceManager: CalendarEventAttendanceManager;
    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let mentor: IntegrationTestUser<Mentor>;
    let student: IntegrationTestUser;
    let anotherStudent: IntegrationTestUser;

    let courseA: IntegrationTestCourse;
    let courseB: IntegrationTestCourse;

    let batchA: IntegrationTestBatch;
    let batchB: IntegrationTestBatch;

    beforeAll(async () => {
        configuration = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        calendarEventRepository = new CalendarEventTypeormRepository(Container.get(TransactionManagerToken));
        calendarEventLabelRepository = new CalendarEventLabelTypeormRepository(Container.get(TransactionManagerToken));
        attendanceManager = Container.get(CalendarEventAttendanceManagerToken);

        ({ admin, student, staff, mentor } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute());
        anotherStudent = await IntegrationTestUser.createFakeStudent();

        await anotherStudent.authorize();

        courseA = await IntegrationTestCourse.create();
        courseB = await IntegrationTestCourse.create();

        batchA = await IntegrationTestBatch.create({ courseId: courseA.params.getIdOrThrow() });
        batchB = await IntegrationTestBatch.create({ courseId: courseB.params.getIdOrThrow() });

        await student.createStudentSettings(batchA.params.getIdOrThrow());
        await anotherStudent.createStudentSettings(batchB.params.getIdOrThrow());

        await student.createStudentProfile();
        await anotherStudent.createStudentProfile();
    });

    describe('GET /calendar/events', () => {
        let labels: CalendarEventLabel[] = [];
        let courseEvent: CalendarEvent;
        let batchEvent: CalendarEvent;
        let allUsersEvent: CalendarEvent;
        let staffEvent: CalendarEvent;
        let correctionEvent: CalendarEvent;

        beforeAll(async () => {
            const existingEvents = await calendarEventRepository.getAll();
            await Promise.all(
                existingEvents.map((calendarEvent) => calendarEventRepository.delete(calendarEvent.getIdOrThrow())),
            );
            const existingLabels = await calendarEventLabelRepository.getAll();
            await Promise.all(
                existingLabels.map((calendarEvent) =>
                    calendarEventLabelRepository.delete(calendarEvent.getIdOrThrow()),
                ),
            );

            labels = [
                await calendarEventLabelRepository.save(new CalendarEventLabel(new Label('standup'))),
                await calendarEventLabelRepository.save(new CalendarEventLabel(new Label('meeting'))),
                await calendarEventLabelRepository.save(new CalendarEventLabel(new Label('staff'))),
                await calendarEventLabelRepository.save(new CalendarEventLabel(new Label('with-exceptions'))),
            ];
            courseEvent = await calendarEventRepository.save(
                new RecurringCalendarEvent({
                    category: CalendarEventCategory.MEETING,
                    summary: new CalendarEventSummary(faker.string.sample()),
                    description: new CalendarEventDescription(faker.string.sample()),
                    url: new Url(faker.internet.url()),
                    status: CalendarEventStatus.CONFIRMED,
                    time: CalendarEventTime.fromDurationMinutes(new Date('2021-01-01 09:00:00Z'), 30),
                    recurrence: new CalendarEventRecurrenceRule(
                        RecurrenceRule.daily({ start: new Date('2021-01-01 09:00:00Z') }),
                    ),
                }),
            );
            batchEvent = await calendarEventRepository.save(
                new RecurringCalendarEvent({
                    category: CalendarEventCategory.MEETING,
                    summary: new CalendarEventSummary(faker.string.sample()),
                    description: new CalendarEventDescription(faker.string.sample()),
                    url: new Url(faker.internet.url()),
                    status: CalendarEventStatus.CONFIRMED,
                    time: CalendarEventTime.fromDurationMinutes(new Date('2021-01-01 10:00:00Z'), 30),
                    recurrence: new CalendarEventRecurrenceRule(
                        RecurrenceRule.daily({ start: new Date('2021-01-01 10:00:00Z') }),
                    ),
                }),
            );
            allUsersEvent = await calendarEventRepository.save(
                new RecurringCalendarEvent({
                    category: CalendarEventCategory.MEETING,
                    summary: new CalendarEventSummary(faker.string.sample()),
                    description: new CalendarEventDescription(faker.string.sample()),
                    url: new Url(faker.internet.url()),
                    status: CalendarEventStatus.CONFIRMED,
                    time: CalendarEventTime.fromDurationMinutes(new Date('2021-01-01 11:00:00Z'), 30),
                    recurrence: new CalendarEventRecurrenceRule(
                        RecurrenceRule.daily({ start: new Date('2021-01-01 11:00:00Z') }),
                    ),
                    labels: [labels[0], labels[1]],
                }),
            );
            staffEvent = await calendarEventRepository.save(
                new RecurringCalendarEvent({
                    category: CalendarEventCategory.MEETING,
                    summary: new CalendarEventSummary(faker.string.sample()),
                    description: new CalendarEventDescription(faker.string.sample()),
                    url: new Url(faker.internet.url()),
                    status: CalendarEventStatus.CONFIRMED,
                    time: CalendarEventTime.fromDurationMinutes(new Date('2021-01-01 12:00:00Z'), 60),
                    recurrence: new CalendarEventRecurrenceRule(
                        RecurrenceRule.weekly({
                            start: new Date('2021-01-01 12:00:00Z'),
                            until: new Date('2021-02-01 12:00:00Z'),
                        }),
                        [
                            new CalendarEventExcludedDate(new Date('2021-01-02 12:00:00Z')),
                            new CalendarEventExcludedDate(new Date('2021-01-03 12:00:00Z')),
                        ],
                    ),
                    labels: [labels[3]],
                }),
            );
            correctionEvent = await calendarEventRepository.save(
                new SimpleCalendarEvent({
                    category: CalendarEventCategory.CORRECTION,
                    summary: new CalendarEventSummary(faker.string.sample()),
                    description: new CalendarEventDescription(faker.string.sample()),
                    url: new Url(faker.internet.url()),
                    status: CalendarEventStatus.CONFIRMED,
                    time: CalendarEventTime.fromDurationMinutes(new Date('2021-01-01 13:00:00Z'), 45),
                    customProperties: {
                        reviewId: TestObjects.id().value,
                    },
                }),
            );
            await attendanceManager.updateAttendance(
                courseEvent.getIdOrThrow(),
                new AttendanceOptions([courseA.getIdOrThrow()], [], []),
            );
            await attendanceManager.updateAttendance(
                batchEvent.getIdOrThrow(),
                new AttendanceOptions([], [batchA.getIdOrThrow()], []),
            );
            await attendanceManager.updateAttendance(
                allUsersEvent.getIdOrThrow(),
                AttendanceOptions.usersOnly(
                    admin.getIdOrThrow(),
                    staff.getIdOrThrow(),
                    mentor.getIdOrThrow(),
                    student.getIdOrThrow(),
                ),
            );
            await attendanceManager.updateAttendance(
                staffEvent.getIdOrThrow(),
                AttendanceOptions.usersOnly(admin.getIdOrThrow(), staff.getIdOrThrow()),
            );
            await attendanceManager.updateAttendance(
                correctionEvent.getIdOrThrow(),
                AttendanceOptions.usersOnly(mentor.getIdOrThrow(), student.getIdOrThrow()),
            );
        });

        async function testGet(query: any, expected: CalendarEvent[], token: string, code: number): Promise<void> {
            const res = await request(configuration.server)
                .get('/calendar/events')
                .query(query)
                .set('Cookie', `token=${token}`)
                .send()
                .expect(code);

            if (code !== 200) {
                return;
            }

            expect(res.body).toEqual(
                expect.arrayContaining(
                    expected.map((event) => {
                        const dto = CalendarEventDTO.fromCalendarEvent(event, new Url(configuration.client.url));
                        dto.start = dto.start.toISOString() as any;
                        dto.labels = expect.arrayContaining(dto.labels);
                        if (dto.frequency) {
                            dto.until = dto.until?.toISOString() as any;
                            dto.userDateExceptions = expect.arrayContaining(
                                (dto.userDateExceptions || []).map((d) => d.toISOString()),
                            );
                        }

                        return dto;
                    }),
                ),
            );
        }

        it('should get all student events', async () => {
            await testGet(undefined, [courseEvent, batchEvent, allUsersEvent], student.getAuthTokenString(), 200);
        });

        it('should get all mentor events', async () => {
            await testGet(
                {
                    from: new Date('2021-01-01 00:00:00Z'),
                    until: new Date('2021-02-01 00:00:00Z'),
                },
                [allUsersEvent, correctionEvent],
                mentor.getAuthTokenString(),
                200,
            );
        });

        it('should get an empty events list', async () => {
            await testGet(undefined, [], anotherStudent.getAuthTokenString(), 200);
        });

        it('should not get events as admin', async () => {
            await testGet(undefined, [], admin.getAuthTokenString(), 403);
        });

        it('should not get events as staff', async () => {
            await testGet(undefined, [], staff.getAuthTokenString(), 403);
        });
    });
});
