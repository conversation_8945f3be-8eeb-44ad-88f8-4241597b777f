import { Controller, Get, InternalServerError, NotFoundError, Param } from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { IcsServiceToken } from '../../../di/tokens';
import { LoggingModule } from '../../../logging/infrastructure/di/LoggingModule';
import { LoggingService } from '../../../logging/services/LoggingService';
import ServiceNotFoundError from '../../../core/errors/NotFoundError';
import Logger from '../../../utils/logger/Logger';
import IcsService from '../../services/IcsService';

@Service()
@Controller('/calendar/ics')
export default class CalendarIcsController {
    private readonly logger: Logger;

    constructor(
        @Inject(LoggingModule.LOGGING_SERVICE_TOKEN)
        loggingService: LoggingService,
        @Inject(IcsServiceToken) private readonly icsService: IcsService,
    ) {
        this.logger = loggingService.createLogger(CalendarIcsController.name);
    }

    @Get('/user/:token')
    async getUserCalendar(@Param('token') token: string): Promise<string> {
        try {
            return await this.icsService.getUserIcsCalendar(token);
        } catch (e) {
            this.logger.error(`Failed to get user ICS calendar '${token}'`, e);
            if (e instanceof ServiceNotFoundError) {
                throw new NotFoundError(e.message);
            }
            throw new InternalServerError(e.message);
        }
    }
}
