import moment from 'moment';
import { RRule } from 'rrule';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';
import RecurrenceFrequency from './RecurrenceFrequency';
import RecurrenceWeekday from './RecurrenceWeekday';
import { Timezone } from '../../../core/domain/types/Timezone';

export default class RecurrenceRule {
    static readonly DEFAULT_TZ = Timezone.EUROPE_VILNIUS;

    static readonly DEFAULT_UNTIL_PERIOD_MS = 100 * 365 * 24 * 60 * 60 * 1000; // 100 years

    readonly frequency: RecurrenceFrequency;

    readonly start: Date;

    readonly until?: Date;

    readonly weekdays?: RecurrenceWeekday[];

    readonly tz: string;

    constructor(params: {
        frequency: RecurrenceFrequency;
        start: Date;
        until?: Date;
        weekdays?: RecurrenceWeekday[];
        tz?: string;
    }) {
        ArgumentValidation.assert.defined(params.frequency, 'Recurrence frequency is required');
        ArgumentValidation.assert.defined(params.start, 'Recurrence start is required');

        this.frequency = params.frequency;
        this.start = params.start;
        if (params.until) {
            ArgumentValidation.assert.sameOrAfter(params.until, params.start, 'Until date must be after start date');
            this.until = this.copyHours(params.start, params.until);
        }

        if (params.frequency.isWeekly()) {
            this.weekdays = params.weekdays || [];
        }

        this.tz = params.tz ?? RecurrenceRule.DEFAULT_TZ;
    }

    static daily(params: { start: Date; until?: Date; tz?: string }): RecurrenceRule {
        return new RecurrenceRule({ ...params, frequency: RecurrenceFrequency.daily() });
    }

    static weekly(params: { start: Date; until?: Date; weekdays?: RecurrenceWeekday[]; tz?: string }): RecurrenceRule {
        return new RecurrenceRule({ ...params, frequency: RecurrenceFrequency.weekly() });
    }

    static monthly(params: { start: Date; until?: Date; tz?: string }): RecurrenceRule {
        return new RecurrenceRule({ ...params, frequency: RecurrenceFrequency.monthly() });
    }

    changeStart(start: Date): RecurrenceRule {
        ArgumentValidation.assert.defined(start);

        if (this.until && moment(this.until).isBefore(start)) {
            return new RecurrenceRule({ ...this, start, until: start });
        }

        return new RecurrenceRule({ ...this, start });
    }

    changeUntil(until?: Date): RecurrenceRule {
        return new RecurrenceRule({ ...this, until });
    }

    before(date = new Date()): Date | undefined {
        const limit = moment(date).utc(true).toDate();
        const unadjusted = this.getRRule().before(limit);
        if (!unadjusted) {
            return undefined;
        }

        const offset = moment(unadjusted).utcOffset();

        return moment(unadjusted).subtract(offset, 'minutes').toDate();
    }

    after(date = new Date()): Date | undefined {
        const limit = moment(date).utc(true).toDate();
        const unadjusted = this.getRRule().after(limit);
        if (!unadjusted) {
            return undefined;
        }

        const offset = moment(unadjusted).utcOffset();

        return moment(unadjusted).subtract(offset, 'minutes').toDate();
    }

    equals(other: RecurrenceRule): boolean {
        return (
            !!other &&
            this.frequency.equals(other.frequency) &&
            this.start.getTime() === other.start.getTime() &&
            (this.until ? this.until.getTime() === other.until?.getTime() : !other.until) &&
            this.equalWeekdays(other.weekdays)
        );
    }

    getRRule(): RRule {
        return new RRule({
            freq: this.frequency.toRRuleFrequency(),
            dtstart: moment(this.start).tz(this.tz).utc(true).toDate(),
            until: moment(this.until ?? moment(this.start).add(RecurrenceRule.DEFAULT_UNTIL_PERIOD_MS))
                .tz(this.tz)
                .utc(true)
                .toDate(),
            byweekday: this.weekdays && this.weekdays.length > 0 ? this.weekdays.map((w) => w.value) : undefined,
            tzid: this.tz,
        });
    }

    private equalWeekdays(other?: RecurrenceWeekday[]): boolean {
        if (!this.weekdays) {
            return !other;
        }

        return this.weekdays.length === other?.length && this.weekdays.every((w) => other?.some((ow) => w.equals(ow)));
    }

    private copyHours(from: Date, to: Date): Date {
        return moment(to)
            .hours(from.getHours())
            .minutes(from.getMinutes())
            .seconds(from.getSeconds())
            .milliseconds(from.getMilliseconds())
            .toDate();
    }
}
