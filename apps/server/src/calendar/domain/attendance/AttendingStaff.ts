import Id from '../../../core/domain/value-objects/Id';
import ArgumentValidation from '../../../core/utils/validation/ArgumentValidation';

export default class AttendingStaff {
    readonly eventId: Id;

    readonly userId: Id;

    constructor(eventId: Id, userId: Id) {
        ArgumentValidation.assert.defined(eventId, 'Event ID is required');
        ArgumentValidation.assert.defined(userId, 'User ID is required');

        this.eventId = eventId;
        this.userId = userId;
    }
}
