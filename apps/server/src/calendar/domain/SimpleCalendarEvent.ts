import Id from '../../core/domain/value-objects/Id';
import CalendarEvent, { CalendarEventConstructorProperties } from './CalendarEvent';

type SimpleCalendarEventConstructorProperties = CalendarEventConstructorProperties & { parentId?: Id };

export default class SimpleCalendarEvent extends CalendarEvent {
    readonly parentId?: Id;

    constructor(properties: SimpleCalendarEventConstructorProperties) {
        super(properties);

        this.parentId = properties.parentId;
    }
}
