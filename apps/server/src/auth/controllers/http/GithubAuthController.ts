import { Request, Response } from 'express';
import { Get, JsonController, QueryParam, Req, <PERSON>s, UseBefore } from 'routing-controllers';
import { Inject, Service } from 'typedi';
import { Configuration } from '../../../config/Configuration';
import { ConfigurationModule } from '../../../config/infrastructure/di/ConfigurationModule';
import Url from '../../../core/domain/value-objects/Url';
import HttpError from '../../../core/errors/HttpError';
import { LoggingModule } from '../../../logging/infrastructure/di/LoggingModule';
import { LoggingService } from '../../../logging/services/LoggingService';
import Logger from '../../../utils/logger/Logger';
import GithubAuthenticator, { GithubProfile } from '../../services/GithubAuthenticator';
import { githubAuthCallbackMiddleware, githubAuthMiddleware } from '../../../core/middleware/AuthMiddleware';
import CookieAuthorizer from './middleware/CookieAuthorizer';
import GithubAuthStrategyFactory from './middleware/GithubAuthStrategyFactory';
import { OAuthState } from '../../../core/domain/oauth/OAuthState';

@Service()
@JsonController('/auth/github')
export default class GithubAuthController {
    private readonly logger: Logger;

    private readonly redirectUrl: Url;

    constructor(
        @Inject()
        private readonly githubAuthenticator: GithubAuthenticator,
        @Inject()
        private readonly cookieAuthorizer: CookieAuthorizer,
        @Inject(ConfigurationModule.CONFIGURATION_TOKEN)
        config: Configuration,
        @Inject(LoggingModule.LOGGING_SERVICE_TOKEN)
        loggingService: LoggingService,
    ) {
        this.logger = loggingService.createLogger(GithubAuthController.name);
        this.redirectUrl = new Url(config.client.url).addPath(config.auth.clientRedirectPath);
    }

    @Get()
    @UseBefore(githubAuthMiddleware(GithubAuthStrategyFactory.STRATEGY_NAME))
    githubAuth(): void {
        // Nothing to do
    }

    @Get('/callback')
    @UseBefore(githubAuthCallbackMiddleware(GithubAuthStrategyFactory.STRATEGY_NAME))
    async githubAuthCallback(
        @Req() req: Request,
        @Res() res: Response,
        @QueryParam('state', { required: false }) state: string,
    ): Promise<Response> {
        if (state) {
            // When error happens during github OAuth, github redirects to default url, specified
            // in the github application settings (this endpoint). To handle error all types of flows,
            // we check callback url from state and redirect request to the specified handler.
            this.logger.debug('Checking if state is from different flow');
            try {
                const parsedState = OAuthState.deserialize(state);

                if (parsedState?.callbackUrl && parsedState.callbackUrl.pathname !== '/auth/github/callback') {
                    this.logger.debug(
                        `State is from ${parsedState.callbackUrl.value}, redirecting to specified handler`,
                    );

                    res.redirect(parsedState.callbackUrl.addQuery('state', state).value);

                    return res;
                }
            } catch (e) {
                this.logger.warn('Failed to parse as OAuth state, proceeding as github auth', e);
            }
        }

        let redirectResult = this.redirectUrl;
        try {
            const accessToken = await this.githubAuthenticator.authenticate(req.user as GithubProfile);
            if (accessToken) {
                this.cookieAuthorizer.setCookie(res, accessToken);
            } else {
                redirectResult = HttpError.unauthorized('GitHub authentication failed').addToUrl(this.redirectUrl);
            }
        } catch (e) {
            this.logger.warn('GitHub authentication failed', e);
            redirectResult = HttpError.fromError(e, 'GitHub authentication failed').addToUrl(this.redirectUrl);
        }

        res.redirect(redirectResult.value);

        return res;
    }
}
