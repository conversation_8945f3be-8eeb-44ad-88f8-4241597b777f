import { Body, HttpCode, JsonController, Post, UseBefore } from 'routing-controllers';
import { OpenAPI } from 'routing-controllers-openapi';
import { Inject, Service } from 'typedi';
import StrictRateLimiterMiddleware from '../../../app/middleware/StrictRateLimiterMiddleware';
import responses from '../../../core/controllers/docs/responses';
import PasswordResetManager from '../../services/PasswordResetManager';
import PasswordResetDTO from './dto/PasswordResetDTO';
import PasswordResetRequestDTO from './dto/PasswordResetRequestDTO';

@Service()
@JsonController()
export default class PasswordResetController {
    constructor(
        @Inject()
        private readonly passwordResetManager: PasswordResetManager,
    ) {}

    @Post('/requestReset')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Request password reset by email',
    })
    @UseBefore(StrictRateLimiterMiddleware)
    async requestReset(@Body() properties: PasswordResetRequestDTO): Promise<void> {
        await this.passwordResetManager.requestReset(properties.email);
    }

    @Post('/reset')
    @HttpCode(204)
    @OpenAPI({
        responses,
        summary: 'Reset password with reset token',
    })
    @UseBefore(StrictRateLimiterMiddleware)
    async reset(@Body() properties: PasswordResetDTO): Promise<void> {
        await this.passwordResetManager.reset(properties.token, properties.password);
    }
}
