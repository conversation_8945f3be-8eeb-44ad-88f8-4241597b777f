import { Strategy as GithubStrategy } from 'passport-github2';
import { Configuration } from '../../../../config/Configuration';

export default class GithubAuthStrategyFactory {
    static readonly STRATEGY_NAME = 'github-authentication';

    static getStrategy(config: Configuration): GithubStrategy {
        return new GithubStrategy(
            {
                clientID: config.github.clientId,
                clientSecret: config.github.clientSecret,
                callbackURL: `${config.server}${config.auth.github.callbackPath}`,
                scope: ['read:user', 'user:email'],
            },
            // This is not a conventional verification method. But because we need to handle the final redirect
            // ourselves, it is easier to also look up a user in a controller.
            (accessToken: string, refreshToken: string, profile: any, done: any) => done(profile),
        );
    }
}
