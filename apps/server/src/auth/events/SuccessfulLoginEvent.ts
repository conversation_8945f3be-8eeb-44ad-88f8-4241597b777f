import ApplicationEvent from '../../events/application/ApplicationEvent';
import { Role } from '../../users/shared/infrastructure/db/User';

interface SuccessfulLoginEventPayload {
    identification: string;
    id: number;
    role: Role;
}

export default class SuccessfulLoginEvent extends ApplicationEvent<SuccessfulLoginEventPayload> {
    static readonly NAME = 'successful_login';

    constructor(payload: SuccessfulLoginEventPayload) {
        super(SuccessfulLoginEvent.NAME, payload);
    }
}
