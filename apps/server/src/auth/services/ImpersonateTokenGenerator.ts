import ImpersonateToken, { ImpersonateTokenPayload } from '../domain/ImpersonateToken';
import { sign } from 'jsonwebtoken';
import { Configuration } from '../../config/Configuration';
import Id from '../../core/domain/value-objects/Id';

export default class ImpersonateTokenGenerator {
    constructor(private readonly config: Configuration) {}

    async generate({
        userToImpersonateId,
        userId,
    }: {
        userToImpersonateId: Id;
        userId: Id;
    }): Promise<ImpersonateToken> {
        const iat = Math.floor(Date.now() / 1000) - 30;
        const exp = iat + this.config.auth.impersonation.expirationInSeconds;
        const payload: ImpersonateTokenPayload = {
            sub: userId.value,
            impersonatedUserId: userToImpersonateId.value,
            exp,
            iat,
        };
        const result = sign(payload, this.config.auth.impersonation.secret);
        return new ImpersonateToken(result, new Date(exp * 1000));
    }
}
