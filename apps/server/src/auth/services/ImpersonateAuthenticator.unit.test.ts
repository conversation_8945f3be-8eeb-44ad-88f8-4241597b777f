import { TestConfigurationService } from '../../test-toolkit/unit/deprecated-mocks/TestConfigurationService';
import ImpersonateAuthenticator from './ImpersonateAuthenticator';
import ImpersonateTokenGenerator from './ImpersonateTokenGenerator';
import Id from '../../core/domain/value-objects/Id';
import { faker } from '@faker-js/faker';
import Student from '../../users/students/management/infrastructure/db/Student';
import { UserState } from '../../users/accounts/domain/UserState';
import User, { Role } from '../../users/shared/infrastructure/db/User';
import AuthorizationError from '../../core/errors/AuthorizationError';
import Staff from '../../users/staff/infrastructure/db/Staff';
import Mentor from '../../users/mentors/infrastructure/db/Mentor';
import { anything, instance, mock, when } from 'ts-mockito';
import IllegalOperationError from '../../core/errors/IllegalOperationError';
import { Permission } from '../../user-permissions/domain/Permission';
import { UserRepository } from '../../users/shared/infrastructure/db/UserRepository';

describe('ImpersonateAuthenticator', () => {
    const configuration = TestConfigurationService.create().getConfigurationSync();
    const userRepository = mock(UserRepository);
    const impersonateTokenGenerator = new ImpersonateTokenGenerator(configuration);
    const impersonateAuthenticator = new ImpersonateAuthenticator(
        mock(),
        instance(userRepository),
        impersonateTokenGenerator,
    );
    const admin = new Staff();
    admin.id = faker.number.int({ min: 1, max: 100 });
    admin.role = Role.ADMIN;
    admin.permissions = [Permission.IMPERSONATE, Permission.IMPERSONATE_MENTOR];

    describe('authenticate', () => {
        test('should authenticate successfully for a learner', async () => {
            const userId = Id.create(faker.number.int({ min: 101, max: 200 }));
            const user = new Student();
            user.state = faker.helpers.arrayElement([UserState.ACTIVE, UserState.SUSPENDED, UserState.ONBOARDING]);
            user.id = userId.value;
            user.role = Role.USER;
            when(userRepository.findById(anything())).thenResolve(user);

            const result = await impersonateAuthenticator.authenticate(admin, userId);

            expect(result).toBeDefined();
        });

        test('should authenticate successfully for a mentor', async () => {
            const userId = Id.create(faker.number.int({ min: 101, max: 200 }));
            const user = new Mentor();
            user.state = faker.helpers.arrayElement([UserState.ACTIVE, UserState.SUSPENDED, UserState.ONBOARDING]);
            user.id = userId.value;
            user.role = Role.MENTOR;
            when(userRepository.findById(anything())).thenResolve(user);
            const result = await impersonateAuthenticator.authenticate(admin, userId);
            expect(result).toBeDefined();
        });

        test('should not authenticate for non student role', async () => {
            const userId = Id.create(faker.number.int({ min: 101, max: 200 }));
            const user = new User();
            user.state = faker.helpers.arrayElement([UserState.ACTIVE, UserState.SUSPENDED, UserState.ONBOARDING]);
            user.id = userId.value;
            user.role = faker.helpers.arrayElement([Role.ADMIN, Role.STAFF]);
            when(userRepository.findById(anything())).thenResolve(user);

            await expect(impersonateAuthenticator.authenticate(admin, userId)).rejects.toEqual(
                new IllegalOperationError('Can only impersonate students or mentors'),
            );
        });

        test('should not authenticate for student that is unable to log in', async () => {
            const userId = Id.create(faker.number.int({ min: 101, max: 200 }));
            const user = new Student();
            user.state = faker.helpers.arrayElement([UserState.BLOCKED, UserState.REGISTERING]);
            user.id = userId.value;
            user.role = Role.USER;
            when(userRepository.findById(anything())).thenResolve(user);

            await expect(impersonateAuthenticator.authenticate(admin, userId)).rejects.toEqual(
                new AuthorizationError(),
            );
        });

        test('should not authenticate when admin lacks student impersonation permission', async () => {
            const adminWithoutPermission = new Staff();
            adminWithoutPermission.id = faker.number.int({ min: 1, max: 100 });
            adminWithoutPermission.role = Role.ADMIN;
            adminWithoutPermission.permissions = [Permission.IMPERSONATE_MENTOR]; // only mentor permission

            const userId = Id.create(faker.number.int({ min: 101, max: 200 }));
            const user = new Student();
            user.state = UserState.ACTIVE;
            user.id = userId.value;
            user.role = Role.USER;
            when(userRepository.findById(anything())).thenResolve(user);

            await expect(impersonateAuthenticator.authenticate(adminWithoutPermission, userId)).rejects.toEqual(
                new AuthorizationError('User does not have permission to impersonate students'),
            );
        });

        test('should not authenticate when admin lacks mentor impersonation permission', async () => {
            const adminWithoutPermission = new Staff();
            adminWithoutPermission.id = faker.number.int({ min: 1, max: 100 });
            adminWithoutPermission.role = Role.ADMIN;
            adminWithoutPermission.permissions = [Permission.IMPERSONATE]; // only student permission

            const userId = Id.create(faker.number.int({ min: 101, max: 200 }));
            const user = new Mentor();
            user.state = UserState.ACTIVE;
            user.id = userId.value;
            user.role = Role.MENTOR;
            when(userRepository.findById(anything())).thenResolve(user);

            await expect(impersonateAuthenticator.authenticate(adminWithoutPermission, userId)).rejects.toEqual(
                new AuthorizationError('User does not have permission to impersonate mentors'),
            );
        });
    });
});
