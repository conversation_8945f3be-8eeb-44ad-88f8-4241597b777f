import { SocialAuthenticationMethod } from '../domain/AuthenticationMethod';
import AccessTokenGenerator from './AccessTokenGenerator';
import GoogleAuthenticator from './GoogleAuthenticator';
import Id from '../../core/domain/value-objects/Id';
import Student from '../../users/students/management/infrastructure/db/Student';
import Numbers from '../../utils/Numbers';
import AnalyticsMock from '../../test-toolkit/unit/deprecated-mocks/AnalyticsMock';
import { TestConfigurationService } from '../../test-toolkit/unit/deprecated-mocks/TestConfigurationService';
import { UserState } from '../../users/accounts/domain/UserState';
import { anything, deepEqual, instance, mock, verify, when } from 'ts-mockito';
import AccessTokenRepository from '../infrastructure/db/AccessTokenRepository';
import { faker } from '@faker-js/faker';
import AccessToken from '../domain/AccessToken';
import LoginRepository from '../infrastructure/db/LoginRepository';
import CooldownRepository from '../infrastructure/db/CooldownRepository';
import { UserRepository } from '../../users/shared/infrastructure/db/UserRepository';
import { getTestUser } from '../../test-toolkit/shared/userHelper';

describe('GoogleAuthenticator', () => {
    const configuration = TestConfigurationService.create().getConfigurationSync();
    const userRepository = mock(UserRepository);
    const accessTokenRepository = mock(AccessTokenRepository);
    const loginRepository = mock(LoginRepository);
    const cooldownRepository = mock(CooldownRepository);
    const analytics = new AnalyticsMock();
    const authenticator = new GoogleAuthenticator(
        mock(),
        instance(userRepository),
        instance(accessTokenRepository),
        instance(loginRepository),
        instance(cooldownRepository),
        new AccessTokenGenerator(configuration),
        analytics,
    );

    describe('authenticate', () => {
        test('should not authenticate non-existent user', async () => {
            await expect(
                authenticator.authenticate({
                    name: { givenName: 'Test', familyName: 'Name' },
                    emails: [{ value: '<EMAIL>', verified: true }],
                }),
            ).resolves.toBeUndefined();
        });

        test('should not authenticate if there are multiple matching users', async () => {
            when(userRepository.findByEmail(anything())).thenResolve(getTestUser());

            const result = await authenticator.authenticate({
                name: { givenName: 'Test', familyName: 'Name' },
                emails: [
                    { value: '<EMAIL>', verified: true },
                    { value: '<EMAIL>', verified: true },
                ],
            });

            expect(result).toBeUndefined();
        });

        test('should not authenticate registering user', async () => {
            const user = Student.createNew({
                id: Numbers.randomInt(),
                username: 'test-username',
                name: 'test-name',
                surname: 'test-surname',
                email: '<EMAIL>',
                state: UserState.REGISTERING,
            });
            when(userRepository.findByEmail(anything())).thenResolve(user);

            const result = await authenticator.authenticate({
                name: { givenName: 'Test', familyName: 'Name' },
                emails: [{ value: user.email, verified: true }],
            });

            expect(result).toBeUndefined();
        });

        test('should not authenticate blocked user', async () => {
            const user = Student.createNew({
                id: Numbers.randomInt(),
                username: 'test-username',
                name: 'test-name',
                surname: 'test-surname',
                email: '<EMAIL>',
                state: UserState.BLOCKED,
            });
            when(userRepository.findByEmail(anything())).thenResolve(user);

            const result = await authenticator.authenticate({
                name: { givenName: 'Test', familyName: 'Name' },
                emails: [{ value: user.email, verified: true }],
            });

            expect(result).toBeUndefined();
        });

        test('should not authenticate user in cooldown', async () => {
            const user = Student.createNew({
                id: Numbers.randomInt(),
                username: 'test-username',
                name: 'test-name',
                surname: 'test-surname',
                email: '<EMAIL>',
            });
            when(userRepository.findByEmail(anything())).thenResolve(user);
            when(cooldownRepository.isUserOnCooldown(anything())).thenResolve(true);

            const result = await authenticator.authenticate({
                name: { givenName: 'Test', familyName: 'Name' },
                emails: [{ value: user.email, verified: true }],
            });

            expect(result).toBeUndefined();
            expect(analytics.loginAttemptMock).toHaveBeenCalledWith(user.id, {
                isSuccessful: false,
                type: SocialAuthenticationMethod.GOOGLE,
            });
        });

        test('should login', async () => {
            const user = Student.createNew({
                id: Numbers.randomInt(),
                username: 'test-username',
                name: 'test-name',
                surname: 'test-surname',
                email: '<EMAIL>',
            });
            const accessToken = await AccessToken.generate(new Id(user.id), faker.date.future());
            when(userRepository.findByEmail(anything())).thenResolve(user);
            when(accessTokenRepository.save(anything())).thenResolve(accessToken);
            when(cooldownRepository.isUserOnCooldown(deepEqual(new Id(user.id)))).thenResolve(false);

            const result = await authenticator.authenticate({
                name: { givenName: 'Test', familyName: 'Name' },
                emails: [{ value: user.email, verified: true }],
            });

            expect(result).toEqual({
                domainEvents: expect.any(Array),
                userId: new Id(user.id),
                token: expect.any(String),
                validUntil: expect.any(Date),
            });

            verify(accessTokenRepository.save(anything())).once();
            verify(loginRepository.save(anything())).once();
            expect(analytics.loginAttemptMock).toHaveBeenCalledWith(user.id, {
                isSuccessful: true,
                type: SocialAuthenticationMethod.GOOGLE,
            });
        });
    });
});
