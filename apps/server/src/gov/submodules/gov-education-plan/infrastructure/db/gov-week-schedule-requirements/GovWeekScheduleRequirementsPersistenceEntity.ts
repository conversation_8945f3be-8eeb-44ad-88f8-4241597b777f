import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>d, OneToMany, Check, Unique } from 'typeorm';
import TypeormPersistenceEntity from '../../../../../../core/infrastructure/db/TypeormPersistenceEntity';
import Id from '../../../../../../core/domain/value-objects/Id';
import { GovWeekScheduleRequirements } from '../../../domain/GovWeekScheduleRequirements';
import { GovEducationPlanPersistenceEntity } from '../gov-education-plan/GovEducationPlanPersistenceEntity';
import { Timezone } from '../../../../../../core/domain/types/Timezone';
import { ScheduleValidationCustomRules } from '../../../../../../../../shared/src/gov/gov-schedule-requirements/shared/schedule-validation-consecutive-sessions-custom-rules';
import { Week } from '../../../../../../core/domain/value-objects/Week';
import { Day } from '../../../../../../core/domain/value-objects/Day';
import { Hours } from '../../../../../../core/domain/value-objects/Hours';
import { DayTime } from '../../../../../../core/domain/value-objects/DayTime';
import { TimeStringUpToMinute } from '../../../../../../../../shared/src/common/types/time-string';
import { GovWeekScheduleDayRequirementsPersistenceEntity } from '../gov-week-schedule-day-requirements/GovWeekScheduleDayRequirementsPersistenceEntity';
import { DefaultScheduleRequirements } from '../../../../gov-week-schedule/submodules/default-week-schedule/domain/DefaultScheduleRequirements';
import { NotEmptyRange } from '../../../../../../core/domain/value-objects/Range';
import { Numeric } from '../../../../../../core/domain/value-objects/Numeric';

@Entity({ name: GovWeekScheduleRequirementsPersistenceEntity.TABLE_NAME })
@Unique(GovWeekScheduleRequirementsPersistenceEntity.UNIQUE_WEEK_EDUCATION_PLAN, ['week', 'educationPlanId'])
@Check('hours_required >= 0')
@Check('max_days_scheduled >= min_days_scheduled')
@Check('EXTRACT(DOW FROM week) = 1') // 1 is Monday in PostgreSQL
export class GovWeekScheduleRequirementsPersistenceEntity extends TypeormPersistenceEntity {
    private static readonly TABLE_NAME = 'gov_week_schedule_requirements';
    private static readonly UNIQUE_WEEK_EDUCATION_PLAN = 'UQ_week_education_plan';

    @ManyToOne(() => GovEducationPlanPersistenceEntity, {
        orphanedRowAction: 'delete',
    })
    @JoinColumn({ name: 'education_plan_id' })
    @RelationId((self: GovWeekScheduleRequirementsPersistenceEntity) => self.educationPlan)
    educationPlan: never;

    @Column({ name: 'week', type: 'date' })
    week: string;

    @Column({ name: 'education_plan_id', type: 'int' })
    educationPlanId: number;

    @Column({ name: 'min_days_scheduled', type: 'int' })
    @Check('min_days_scheduled >= 1')
    minDaysScheduled: number;

    @Column({ name: 'max_days_scheduled', type: 'int' })
    @Check('max_days_scheduled <= 7')
    maxDaysScheduled: number;

    @Column({ name: 'hours_required', type: 'int' })
    hoursRequired: number;

    @Column({ name: 'default_schedule_start_time', type: 'time without time zone' })
    defaultScheduleStartTime: TimeStringUpToMinute;

    @Column({ name: 'timezone', type: 'varchar' })
    timezone: Timezone;

    @Column({ name: 'custom_rules', type: 'varchar', array: true })
    customRules: ScheduleValidationCustomRules[];

    @OneToMany(
        () => GovWeekScheduleDayRequirementsPersistenceEntity,
        (dayRequirements) => dayRequirements.weekRequirements,
        {
            eager: true,
            cascade: true,
            orphanedRowAction: 'delete',
        },
    )
    days: GovWeekScheduleDayRequirementsPersistenceEntity[];

    constructor(params?: Partial<GovWeekScheduleRequirementsPersistenceEntity>) {
        super(params?.id, params?.createdAt, params?.updatedAt);

        if (params) {
            Object.assign(this, params);
        }
    }

    static fromDomain(domain: GovWeekScheduleRequirements): GovWeekScheduleRequirementsPersistenceEntity {
        return new GovWeekScheduleRequirementsPersistenceEntity({
            id: domain.id?.value,
            educationPlanId: domain.educationPlanId?.value,
            week: Day.fromDate(domain.week.range.from, domain.timezone).toString(),
            minDaysScheduled: domain.daysRequired.start.toNumber(),
            maxDaysScheduled: domain.daysRequired.end.toNumber(),
            hoursRequired: domain.hoursRequired.toNumber(),
            defaultScheduleStartTime: domain.defaultScheduleRequirements.dayStartTime.toTimeStringWithoutSeconds(),
            timezone: domain.timezone,
            customRules: domain.customRules,
            days: domain.days.map((day) => GovWeekScheduleDayRequirementsPersistenceEntity.fromDomain(day)),
        });
    }

    toDomain(): GovWeekScheduleRequirements {
        return GovWeekScheduleRequirements.fromParams({
            id: this.id ? new Id(this.id) : undefined,
            educationPlanId: new Id(this.educationPlanId),
            week: Week.fromDay(Day.fromString(this.week, this.timezone)),
            daysRequired: NotEmptyRange.from(Numeric.from(this.minDaysScheduled), Numeric.from(this.maxDaysScheduled)),
            hoursRequired: Hours.academic(this.hoursRequired),
            defaultScheduleRequirements: DefaultScheduleRequirements.fromParams({
                dayStartTime: DayTime.fromString(this.defaultScheduleStartTime, this.timezone),
            }),
            timezone: this.timezone,
            customRules: this.customRules,
            days: this.days.map((day) => day.toDomain()),
        });
    }
}
