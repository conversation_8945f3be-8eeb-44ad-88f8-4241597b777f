import Container from 'typedi';
import TransactionManager from '../../../../../core/infrastructure/TransactionManager';
import { GovEducationPlanBuilder } from '../../services/GovEducationPlanBuilder';
import { GovEducationPlanService } from '../../services/GovEducationPlanService';
import { GovEducationPlanRepository } from '../db/gov-education-plan/GovEducationPlanRepository';

export interface GovEducationPlanModuleParams {
    tm: TransactionManager;
}

export class GovEducationPlanModule {
    private constructor(
        readonly govEducationPlanService: GovEducationPlanService,
        readonly govEducationPlanBuilder: GovEducationPlanBuilder,
    ) {}

    static init({ tm }: GovEducationPlanModuleParams): GovEducationPlanModule {
        const govEducationPlanRepository = new GovEducationPlanRepository(tm);
        const govEducationPlanService = new GovEducationPlanService(govEducationPlanRepository);
        const govEducationPlanBuilder = new GovEducationPlanBuilder();

        Container.set(GovEducationPlanService, govEducationPlanService);
        Container.set(GovEducationPlanBuilder, govEducationPlanBuilder);

        return new GovEducationPlanModule(govEducationPlanService, govEducationPlanBuilder);
    }
}
