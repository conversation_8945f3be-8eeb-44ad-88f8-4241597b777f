import { Authorized, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, QueryParam } from 'routing-controllers';
import {
    GovWeekParticipationDayDto,
    GovWeekParticipationDto,
    GovWeekParticipationSessionDto,
} from './dto/GovWeekParticipationDto';
import { OpenAPI } from 'routing-controllers-openapi';
import responses from '../../../../../core/controllers/docs/responses';
import User, { Role } from '../../../../../users/shared/infrastructure/db/User';
import Id from '../../../../../core/domain/value-objects/Id';
import { Inject, Service } from 'typedi';
import { ifStudentAllowOnlySelf } from '../../../../../core/controllers/ifStudentAllowOnlySelf';
import 'moment-timezone';
import moment from 'moment';
import { GovWeekParticipationService } from '../../services/GovWeekParticipationService';
import { Week } from '../../../../../core/domain/value-objects/Week';
import { Minutes } from '../../../../../core/domain/value-objects/Minutes';
import { Timezone } from '../../../../../core/domain/types/Timezone';
import ArgumentValidation from '../../../../../core/utils/validation/ArgumentValidation';

@Service()
@JsonController('/learner-schedule')
export class GovWeekParticipationController {
    private static readonly CUTOFF_SCHEDULE_QUERYING = moment.tz('2024-12-02', Timezone.UTC).startOf('week');

    constructor(
        @Inject(() => GovWeekParticipationService)
        private readonly govLearnerScheduleService: GovWeekParticipationService,
    ) {}

    @Authorized([Role.USER, Role.ADMIN])
    @Get('/:userId')
    @OpenAPI({
        responses,
        summary: 'Get learner schedule',
        security: [{ cookieAuth: [] }],
    })
    async getSchedule(
        @QueryParam('from') from: string,
        @QueryParam('to') to: string,
        @Param('userId') primitiveUserId: number,
        @CurrentUser() user: User,
    ): Promise<GovWeekParticipationDto> {
        ifStudentAllowOnlySelf(user, primitiveUserId);

        const week = Week.fromDates(
            moment.tz(from, Timezone.UTC).toDate(),
            moment.tz(to, Timezone.UTC).toDate(),
            Timezone.UTC,
        );

        ArgumentValidation.assert.true(
            GovWeekParticipationController.CUTOFF_SCHEDULE_QUERYING.isBefore(week.range.from),
            'Data before December 2nd 2024 is not available for viewing.',
        );

        const userId = new Id(primitiveUserId);

        const { weekRequirements, participationDays } =
            await this.govLearnerScheduleService.getLearnerParticipationForWeek({
                userId,
                week,
            });

        return new GovWeekParticipationDto({
            days: participationDays.map((day) => {
                return new GovWeekParticipationDayDto({
                    date: day.day,
                    intervals: day.getSessions().map((s) => GovWeekParticipationSessionDto.fromSession(s)),
                    isTracked: day.isTrackingAllowed(),
                    isNotApplicable: false,
                    totalSeconds: 0,
                    scheduledSessionsStats: {
                        count: day.hoursScheduled().toNumber(),
                        seconds: day.hoursScheduled().toSeconds().toNumber(),
                    },
                    attendedSessionsStats: {
                        count: day.attendedHours().toNumber(),
                        seconds: day.attendedHours().toSeconds().toNumber(),
                    },
                    excludedSessionsStats: {
                        count: day.excludedMinutes().gt(Minutes.from(0)) ? 1 : 0,
                        seconds: day.excludedMinutes().toSeconds(),
                    },
                    disabledTrackingReason: day.disabledTrackingReason(),
                });
            }),
            totalSeconds: 0,
            totalSecondsRequired: 0,
            maximumDayHour: weekRequirements.getTimeMinMax().max.asHours(),
            minimalDayHour: weekRequirements.getTimeMinMax().min.asHours(),
            scheduledSessionsStats: {
                count: participationDays.reduce((acc, day) => acc + day.hoursScheduled().toNumber(), 0),
                seconds: participationDays.reduce((acc, day) => acc + day.hoursScheduled().toSeconds().toNumber(), 0),
            },
            attendedSessionsStats: {
                count: participationDays.reduce((acc, day) => acc + day.attendedHours().toNumber(), 0),
                seconds: participationDays.reduce((acc, day) => acc + day.attendedHours().toSeconds().toNumber(), 0),
            },
            excludedSessionsStats: {
                count: participationDays.some((day) => day.excludedMinutes().gt(Minutes.from(0))) ? 1 : 0,
                seconds: participationDays.reduce((acc, day) => acc + day.excludedMinutes().toSeconds(), 0),
            },
        });
    }
}
