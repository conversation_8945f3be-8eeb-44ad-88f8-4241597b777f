import { DateRange } from '../../../../core/domain/value-objects/DateRange';
import Id from '../../../../core/domain/value-objects/Id';
import { PresenceTrackingService } from '../../../../presence-tracking/services/PresenceTrackingService';

export class GovAttendanceIntervalService {
    constructor(private readonly presenceTrackingService: PresenceTrackingService) {}

    async getLearnerPresenceIntervals(userId: Id, range: DateRange): Promise<DateRange[]> {
        const [voiceChannelIntervals, meetingIntervals, manualIntervals] = await Promise.all([
            this.presenceTrackingService.getVoiceChannelPresenceIntervals(userId, range),
            this.presenceTrackingService.getMeetingPresenceIntervals(userId, range),
            this.presenceTrackingService.getManualPresenceIntervals(userId, range),
        ]);

        return DateRange.mergeOverlapping([...voiceChannelIntervals, ...meetingIntervals, ...manualIntervals]);
    }
}
