import { GovAttendanceOptimizationService } from './GovAttendanceOptimizationService';
import { FeatureFlagService } from '../../../../feature-flag/services/FeatureFlagService';
import { GovAttendanceOptimizerV1 } from './attendance-optimizers/GovAttendanceOptimizerV1';
import { GovAttendanceOptimizerV2 } from './attendance-optimizers/GovAttendanceOptimizerV2';
import { DateRange } from '../../../../core/domain/value-objects/DateRange';
import { FeatureFlag } from '../../../../feature-flag/domain/FeatureFlag';
import { mock, instance, when, anything, capture, reset } from 'ts-mockito';
import { DateRangeTestHelper } from '../../../../test-toolkit/shared/DateRangeTestHelper';

describe(GovAttendanceOptimizationService.name, () => {
    const featureFlagServiceMock = mock(FeatureFlagService);
    const academicHourOptimizerMock = mock(GovAttendanceOptimizerV1);
    const strictAcademicHourOptimizerMock = mock(GovAttendanceOptimizerV2);
    const service = new GovAttendanceOptimizationService(
        instance(featureFlagServiceMock),
        instance(academicHourOptimizerMock),
        instance(strictAcademicHourOptimizerMock),
    );

    const learnerScheduleIntervals: DateRange[] = [];

    beforeEach(() => {
        reset(featureFlagServiceMock);
        reset(academicHourOptimizerMock);
        reset(strictAcademicHourOptimizerMock);

        when(featureFlagServiceMock.getFeatureValue(FeatureFlag.STRICT_ACADEMIC_HOUR_DATE, anything())).thenCall(
            (_, def) => def,
        );

        when(academicHourOptimizerMock.optimize(anything())).thenCall(({ presenceIntervals }) => presenceIntervals);
        when(strictAcademicHourOptimizerMock.optimize(anything())).thenCall(
            ({ presenceIntervals }) => presenceIntervals,
        );
    });

    it('should optimize single presence interval between academic hour schedule start date and strict academic hour date', async () => {
        const presenceIntervals = [DateRangeTestHelper.rangeFromReadable('2024 Dec 01 00:00', '2024 Dec 01 01:00')];

        const result = await service.optimize({ presenceIntervals, learnerScheduleIntervals });
        DateRangeTestHelper.rangesAreSame(result, presenceIntervals);

        const [academicIntervals] = capture(academicHourOptimizerMock.optimize).last();
        DateRangeTestHelper.rangesAreSame(academicIntervals.presenceIntervals, presenceIntervals);

        const [strictAcademicIntervals] = capture(strictAcademicHourOptimizerMock.optimize).last();
        DateRangeTestHelper.rangesAreSame(strictAcademicIntervals.presenceIntervals, []);
    });

    it('should optimize single presence interval after strict academic hour date', async () => {
        const presenceIntervals = [DateRangeTestHelper.rangeFromReadable('2025 Apr 22 00:00', '2025 Apr 22 01:00')];

        const result = await service.optimize({ presenceIntervals, learnerScheduleIntervals });
        DateRangeTestHelper.rangesAreSame(result, presenceIntervals);

        const [academicIntervals] = capture(academicHourOptimizerMock.optimize).last();
        DateRangeTestHelper.rangesAreSame(academicIntervals.presenceIntervals, []);

        const [strictAcademicIntervals] = capture(strictAcademicHourOptimizerMock.optimize).last();
        DateRangeTestHelper.rangesAreSame(strictAcademicIntervals.presenceIntervals, presenceIntervals);
    });

    it('should optimize multiple presence intervals in all ranges', async () => {
        const presenceIntervals = [
            DateRangeTestHelper.rangeFromReadable('2024 Dec 01 00:00', '2024 Dec 01 01:00'),
            DateRangeTestHelper.rangeFromReadable('2025 Apr 22 00:00', '2025 Apr 22 01:00'),
        ];
        const result = await service.optimize({ presenceIntervals, learnerScheduleIntervals });
        DateRangeTestHelper.rangesAreSame(result, presenceIntervals);

        const [academicIntervals] = capture(academicHourOptimizerMock.optimize).last();
        DateRangeTestHelper.rangesAreSame(academicIntervals.presenceIntervals, [presenceIntervals[0]]);

        const [strictAcademicIntervals] = capture(strictAcademicHourOptimizerMock.optimize).last();
        DateRangeTestHelper.rangesAreSame(strictAcademicIntervals.presenceIntervals, [presenceIntervals[1]]);
    });

    it('should optimize single presence interval spanning before and after academic hour schedule start date', async () => {
        const presenceIntervals = [DateRangeTestHelper.rangeFromReadable('2024 Nov 24 21:00', '2024 Nov 24 23:00')];

        const result = await service.optimize({ presenceIntervals, learnerScheduleIntervals });
        DateRangeTestHelper.rangesAreSame(result, presenceIntervals);

        const [academicIntervals] = capture(academicHourOptimizerMock.optimize).last();
        DateRangeTestHelper.rangesAreSame(academicIntervals.presenceIntervals, [
            DateRangeTestHelper.rangeFromReadable('2024 Nov 24 21:00', '2024 Nov 24 23:00'),
        ]);

        const [strictAcademicIntervals] = capture(strictAcademicHourOptimizerMock.optimize).last();
        DateRangeTestHelper.rangesAreSame(strictAcademicIntervals.presenceIntervals, []);
    });

    it('should optimize single presence interval spanning before and after strict academic hour date', async () => {
        const presenceIntervals = [DateRangeTestHelper.rangeFromReadable('2025 Apr 7 21:00', '2025 Apr 7 23:00')];

        const result = await service.optimize({ presenceIntervals, learnerScheduleIntervals });
        DateRangeTestHelper.rangesAreSame(result, presenceIntervals);

        const [academicIntervals] = capture(academicHourOptimizerMock.optimize).last();
        DateRangeTestHelper.rangesAreSame(academicIntervals.presenceIntervals, [
            DateRangeTestHelper.rangeFromReadable('2025 Apr 7 21:00', '2025 Apr 7 22:00'),
        ]);

        const [strictAcademicIntervals] = capture(strictAcademicHourOptimizerMock.optimize).last();
        DateRangeTestHelper.rangesAreSame(strictAcademicIntervals.presenceIntervals, [
            DateRangeTestHelper.rangeFromReadable('2025 Apr 7 22:00', '2025 Apr 7 23:00'),
        ]);
    });
});
