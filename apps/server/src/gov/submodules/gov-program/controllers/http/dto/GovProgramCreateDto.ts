import {
    ArrayMaxSize,
    ArrayMinSize,
    ArrayUnique,
    IsDefined,
    IsInt,
    IsString,
    <PERSON>,
    <PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    Validate<PERSON>ested,
} from 'class-validator';
import { NonFunctionProperties } from '../../../../../../utils/UtilityTypes';
import { GovProgram, GovProgramCreateParams } from '../../../domain/GovProgram';
import { GovProgramLearningTimeRequirementsDto } from './GovProgramLearningTimeRequirementsDto';
import { Type } from 'class-transformer';

import { FixNestedJsonSchemaReference } from '../../../../../../core/controllers/dto/decorators/FixNestedJsonSchemaReference';
import { DefaultScheduleRequirementsDto } from '../../../../gov-week-schedule/submodules/default-week-schedule/controllers/http/dto/DefaultScheduleRequirementsDto';
import { DayTimeRange } from '../../../../../../core/domain/value-objects/DayTimeRange';
import { DayTime } from '../../../../../../core/domain/value-objects/DayTime';
import { GovProgramDayLearningTimeIntervalDto } from './GovProgramDayLearningTimeIntervalDto';
import { ICreateGovProgramDto } from '../../../../../../../../shared/src/gov/gov-program/api/gov-program.dto';
import { IsId } from '../../../../../../core/controllers/validators/IsId';
import Id from '../../../../../../core/domain/value-objects/Id';

export class GovProgramCreateDto implements ICreateGovProgramDto {
    @IsString()
    @MinLength(2)
    name: string;

    @IsString()
    @MinLength(2)
    code: string;

    @IsDefined()
    @Type(() => GovProgramLearningTimeRequirementsDto)
    @ValidateNested()
    @FixNestedJsonSchemaReference(GovProgramLearningTimeRequirementsDto)
    officialLearningTimeRequirements: GovProgramLearningTimeRequirementsDto;

    @IsDefined()
    @Type(() => GovProgramLearningTimeRequirementsDto)
    @ValidateNested()
    @FixNestedJsonSchemaReference(GovProgramLearningTimeRequirementsDto)
    scheduleLearningTimeRequirements: GovProgramLearningTimeRequirementsDto;

    @IsDefined()
    @Type(() => GovProgramDayLearningTimeIntervalDto)
    @ValidateNested({ each: true })
    @FixNestedJsonSchemaReference(GovProgramDayLearningTimeIntervalDto)
    @ArrayMinSize(7)
    @ArrayMaxSize(7)
    @ArrayUnique((i) => i.day)
    dayLearningTimeIntervals: GovProgramDayLearningTimeIntervalDto[];

    @IsInt()
    @Min(GovProgram.MIN_PRICE)
    @Max(GovProgram.MAX_PRICE)
    price: number;

    @IsString()
    @MinLength(2)
    qualifications: string;

    @IsDefined()
    @Type(() => DefaultScheduleRequirementsDto)
    @ValidateNested()
    @FixNestedJsonSchemaReference(DefaultScheduleRequirementsDto)
    defaultScheduleRequirements: DefaultScheduleRequirementsDto;

    @IsId()
    agencyId: number;

    constructor(params?: NonFunctionProperties<GovProgramCreateDto>) {
        if (params) {
            Object.assign(this, params);
        }
    }

    toGovProgramCreateParams(): GovProgramCreateParams {
        return {
            name: this.name,
            code: this.code,
            officialLearningTimeRequirements: this.officialLearningTimeRequirements.toDomain(),
            scheduleLearningTimeRequirements: this.scheduleLearningTimeRequirements.toDomain(),
            dayLearningTimeIntervals: new Map(
                this.dayLearningTimeIntervals.map((interval) => [
                    interval.day,
                    DayTimeRange.fromDayTime(
                        DayTime.fromString(interval.start, interval.timezone),
                        DayTime.fromString(interval.end, interval.timezone).changeToEndOfDayIfStartOfDay(),
                    ),
                ]),
            ),
            price: this.price,
            qualifications: this.qualifications,
            defaultScheduleRequirements: this.defaultScheduleRequirements.toDomain(),
            agencyId: new Id(this.agencyId),
        };
    }
}
