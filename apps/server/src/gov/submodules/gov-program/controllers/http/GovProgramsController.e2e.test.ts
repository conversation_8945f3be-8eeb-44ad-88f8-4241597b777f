import { faker } from '@faker-js/faker';
import request from 'supertest';
import Container from 'typedi';
import { Configuration } from '../../../../../config/Configuration';
import { ConfigurationModule } from '../../../../../config/infrastructure/di/ConfigurationModule';
import { Timezone } from '../../../../../core/domain/types/Timezone';
import { WeekDays } from '../../../../../core/domain/types/WeekDay';
import { DateRange } from '../../../../../core/domain/value-objects/DateRange';
import { DayTime } from '../../../../../core/domain/value-objects/DayTime';
import Id from '../../../../../core/domain/value-objects/Id';
import ArgumentValidation from '../../../../../core/utils/validation/ArgumentValidation';
import { DefaultScheduleRequirementsDto } from '../../../../../gov/submodules/gov-week-schedule/submodules/default-week-schedule/controllers/http/dto/DefaultScheduleRequirementsDto';
import { DefaultScheduleRequirements } from '../../../../../gov/submodules/gov-week-schedule/submodules/default-week-schedule/domain/DefaultScheduleRequirements';
import { IntegrationTestGovGroup } from '../../../../../test-toolkit/e2e/entities/IntegrationTestGovGroup';
import { IntegrationTestGovProgram } from '../../../../../test-toolkit/e2e/entities/IntegrationTestGovProgram';
import { IntegrationTestUser } from '../../../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { IntegrationTestUztChapter } from '../../../../../test-toolkit/e2e/entities/IntegrationTestUztChapter';
import { IntegrationTestUztProfile } from '../../../../../test-toolkit/e2e/entities/IntegrationTestUztProfile';
import { IntegrationTestsCreateAndAuthorizeGangUseCase } from '../../../../../test-toolkit/e2e/use-cases/integration-tests-create-and-authorize-gang.use-case';
import Mentor from '../../../../../users/mentors/infrastructure/db/Mentor';
import Staff from '../../../../../users/staff/infrastructure/db/Staff';
import { GovProgram } from '../../domain/GovProgram';
import { GovProgramCreateDto } from './dto/GovProgramCreateDto';
import { GovProgramDayLearningTimeIntervalDto } from './dto/GovProgramDayLearningTimeIntervalDto';
import { GovProgramDto } from './dto/GovProgramDto';
import { GovProgramLearningTimeRequirementsDto } from './dto/GovProgramLearningTimeRequirementsDto';
import { GovProgramUpdateDto } from './dto/GovProgramUpdateDto';
import { IntegrationTestGovAgency } from '../../../../../test-toolkit/e2e/entities/IntegrationTestGovAgency';
import { IntegrationTestGovProfile } from '../../../../../test-toolkit/e2e/entities/IntegrationTestGovProfile';

function getRandomDayLearningTimeIntervals(timezone: Timezone): GovProgramDayLearningTimeIntervalDto[] {
    return WeekDays.WEEK_DAYS_IN_ORDER.map(
        (day) =>
            new GovProgramDayLearningTimeIntervalDto({
                day,
                start: DayTime.fromHours(faker.number.int({ min: 0, max: 12 }), timezone).toTimeStringWithoutSeconds(),
                end: DayTime.fromHours(faker.number.int({ min: 12, max: 23 }), timezone).toTimeStringWithoutSeconds(),
                timezone,
            }),
    );
}

describe('GovProgramsController IT', () => {
    let config: Configuration;
    let admin: IntegrationTestUser<Staff>;
    let staff: IntegrationTestUser<Staff>;
    let mentor: IntegrationTestUser<Mentor>;
    let student: IntegrationTestUser;
    let agencyUzt: IntegrationTestGovAgency;
    let agencyAfa: IntegrationTestGovAgency;

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);
        ({ admin, student, staff, mentor } = await IntegrationTestsCreateAndAuthorizeGangUseCase.execute());
        [agencyUzt, agencyAfa] = await Promise.all([
            IntegrationTestGovAgency.getOrCreateUzt(),
            IntegrationTestGovAgency.getOrCreateAfa(),
        ]);
    });

    describe('GET /gov/programs', () => {
        let programs: IntegrationTestGovProgram[];

        beforeAll(async () => {
            programs = [
                await IntegrationTestGovProgram.create({ agencyId: agencyUzt.getIdOrThrow() }),
                await IntegrationTestGovProgram.create({ agencyId: agencyUzt.getIdOrThrow() }),
            ];
        });

        async function executeRequest(token?: string, expectedCode = 200): Promise<any> {
            const res = await request(config.server)
                .get('/gov/programs')
                .set('Cookie', `token=${token}`)
                .send()
                .expect(expectedCode);

            return res.body;
        }

        async function testAuthorizedGet(token: string): Promise<void> {
            const responseBody = await executeRequest(token, 200);

            expect(responseBody).toHaveLength(programs.length);
            expect(responseBody).toEqual(
                expect.arrayContaining(programs.map((d) => GovProgramDto.fromGovProgram(d.params))),
            );
        }

        it('should get all programs with authorized users', async () => {
            ArgumentValidation.assert.defined(admin.authToken);
            ArgumentValidation.assert.defined(staff.authToken);

            await testAuthorizedGet(admin.getAuthTokenString());
            await testAuthorizedGet(staff.getAuthTokenString());
        });

        it('should not get programs with unauthorized users', async () => {
            await executeRequest(mentor.getAuthTokenString(), 403);
            await executeRequest(student.getAuthTokenString(), 403);
            await executeRequest(undefined, 403);
        });
    });

    describe('POST /gov/programs', () => {
        async function executeRequest(
            requestBody: GovProgramCreateDto,
            token?: string,
            expectedCode = 200,
        ): Promise<any> {
            const res = await request(config.server)
                .post('/gov/programs')
                .set('Cookie', `token=${token}`)
                .send(requestBody)
                .expect(expectedCode);

            return res.body;
        }

        it('should create program with authorized users', async () => {
            const requestBody = new GovProgramCreateDto({
                name: faker.lorem.sentence(),
                code: faker.lorem.sentence(),
                qualifications: faker.lorem.sentence(),
                officialLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                scheduleLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                dayLearningTimeIntervals: getRandomDayLearningTimeIntervals(agencyUzt.params.timezone),
                price: faker.number.int({
                    min: GovProgram.MIN_PRICE,
                    max: GovProgram.MAX_PRICE,
                }),
                defaultScheduleRequirements: DefaultScheduleRequirementsDto.fromDomain(
                    DefaultScheduleRequirements.fromParams({
                        dayStartTime: DayTime.fromHours(16, agencyUzt.params.timezone),
                    }),
                ),
                agencyId: agencyUzt.getIdOrThrow().value,
            });

            const responseBody = await executeRequest(requestBody, admin.getAuthTokenString(), 200);

            const program = await IntegrationTestGovProgram.get(new Id(responseBody.id));
            ArgumentValidation.assert.defined(program?.params);
            expect(program.params).toEqual(expect.objectContaining(requestBody.toGovProgramCreateParams()));
            expect(responseBody).toEqual(GovProgramDto.fromGovProgram(program.params));
        });

        it('should not create program with duplicated name', async () => {
            const firstRequestBody = new GovProgramCreateDto({
                name: faker.lorem.sentence(),
                code: faker.lorem.sentence(),
                qualifications: faker.lorem.sentence(),
                officialLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                scheduleLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                dayLearningTimeIntervals: getRandomDayLearningTimeIntervals(agencyUzt.params.timezone),
                price: faker.number.int({
                    min: GovProgram.MIN_PRICE,
                    max: GovProgram.MAX_PRICE,
                }),
                defaultScheduleRequirements: DefaultScheduleRequirementsDto.fromDomain(
                    DefaultScheduleRequirements.fromParams({
                        dayStartTime: DayTime.fromHours(16, agencyUzt.params.timezone),
                    }),
                ),
                agencyId: agencyUzt.getIdOrThrow().value,
            });
            const secondRequestBody = new GovProgramCreateDto({
                name: firstRequestBody.name,
                code: faker.lorem.sentence(),
                qualifications: faker.lorem.sentence(),
                officialLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                scheduleLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                dayLearningTimeIntervals: getRandomDayLearningTimeIntervals(agencyUzt.params.timezone),
                price: faker.number.int({
                    min: GovProgram.MIN_PRICE,
                    max: GovProgram.MAX_PRICE,
                }),
                defaultScheduleRequirements: DefaultScheduleRequirementsDto.fromDomain(
                    DefaultScheduleRequirements.fromParams({
                        dayStartTime: DayTime.fromHours(16, agencyUzt.params.timezone),
                    }),
                ),
                agencyId: agencyUzt.getIdOrThrow().value,
            });

            await executeRequest(firstRequestBody, admin.getAuthTokenString(), 200);
            await executeRequest(secondRequestBody, admin.getAuthTokenString(), 400);
        });

        it('should create program with duplicated code', async () => {
            const firstRequestBody = new GovProgramCreateDto({
                name: faker.lorem.sentence(),
                code: faker.lorem.sentence(),
                qualifications: faker.lorem.sentence(),
                officialLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                scheduleLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                dayLearningTimeIntervals: getRandomDayLearningTimeIntervals(agencyUzt.params.timezone),
                price: faker.number.int({
                    min: GovProgram.MIN_PRICE,
                    max: GovProgram.MAX_PRICE,
                }),
                defaultScheduleRequirements: DefaultScheduleRequirementsDto.fromDomain(
                    DefaultScheduleRequirements.fromParams({
                        dayStartTime: DayTime.fromHours(16, agencyUzt.params.timezone),
                    }),
                ),
                agencyId: agencyUzt.getIdOrThrow().value,
            });
            const secondRequestBody = new GovProgramCreateDto({
                name: faker.lorem.sentence(),
                code: firstRequestBody.code,
                qualifications: faker.lorem.sentence(),
                officialLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                scheduleLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                dayLearningTimeIntervals: getRandomDayLearningTimeIntervals(agencyUzt.params.timezone),
                price: faker.number.int({
                    min: GovProgram.MIN_PRICE,
                    max: GovProgram.MAX_PRICE,
                }),
                defaultScheduleRequirements: DefaultScheduleRequirementsDto.fromDomain(
                    DefaultScheduleRequirements.fromParams({
                        dayStartTime: DayTime.fromHours(16, agencyUzt.params.timezone),
                    }),
                ),
                agencyId: agencyUzt.getIdOrThrow().value,
            });

            await executeRequest(firstRequestBody, admin.getAuthTokenString(), 200);
            await executeRequest(secondRequestBody, admin.getAuthTokenString(), 200);
        });

        it('should not create program with unauthorized users', async () => {
            const requestBody = new GovProgramCreateDto({
                name: faker.lorem.sentence(),
                code: faker.lorem.sentence(),
                qualifications: faker.lorem.sentence(),
                officialLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                scheduleLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                dayLearningTimeIntervals: getRandomDayLearningTimeIntervals(agencyUzt.params.timezone),
                price: faker.number.int({
                    min: GovProgram.MIN_PRICE,
                    max: GovProgram.MAX_PRICE,
                }),
                defaultScheduleRequirements: DefaultScheduleRequirementsDto.fromDomain(
                    DefaultScheduleRequirements.fromParams({
                        dayStartTime: DayTime.fromHours(16, agencyUzt.params.timezone),
                    }),
                ),
                agencyId: agencyUzt.getIdOrThrow().value,
            });

            await executeRequest(requestBody, mentor.getAuthTokenString(), 403);
            await executeRequest(requestBody, student.getAuthTokenString(), 403);
            await executeRequest(requestBody, undefined, 403);
        });
    });

    describe('PATCH /gov/programs/:id', () => {
        let programBefore: IntegrationTestGovProgram;

        beforeAll(async () => {
            programBefore = await IntegrationTestGovProgram.create({ agencyId: agencyUzt.getIdOrThrow() });
        });

        async function executeRequest(
            requestBody: GovProgramUpdateDto,
            token?: string,
            expectedCode = 200,
        ): Promise<any> {
            const res = await request(config.server)
                .patch(`/gov/programs/${programBefore.getIdOrThrow().value}`)
                .set('Cookie', `token=${token}`)
                .send(requestBody)
                .expect(expectedCode);

            return res.body;
        }

        it('should update program with authorized users', async () => {
            const requestBody = new GovProgramUpdateDto({
                name: faker.lorem.sentence(),
                code: faker.lorem.sentence(),
                qualifications: faker.lorem.sentence(),
                officialLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                scheduleLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                dayLearningTimeIntervals: getRandomDayLearningTimeIntervals(agencyAfa.params.timezone),
                price: faker.number.int({
                    min: GovProgram.MIN_PRICE,
                    max: GovProgram.MAX_PRICE,
                }),
                defaultScheduleRequirements: DefaultScheduleRequirementsDto.fromDomain(
                    DefaultScheduleRequirements.fromParams({
                        dayStartTime: DayTime.fromHours(16, agencyAfa.params.timezone),
                    }),
                ),
                agencyId: agencyAfa.getIdOrThrow().value,
            });

            const responseBody = await executeRequest(requestBody, admin.getAuthTokenString(), 200);

            const programAfter = await IntegrationTestGovProgram.get(new Id(responseBody.id));
            ArgumentValidation.assert.defined(programAfter?.params);
            expect(programAfter.params).toEqual(expect.objectContaining(requestBody.toGovProgramUpdateParams()));
            expect(responseBody).toEqual(GovProgramDto.fromGovProgram(programAfter.params));
        });

        it('should not update program with unauthorized users', async () => {
            const requestBody = new GovProgramUpdateDto({
                name: faker.lorem.sentence(),
                code: faker.lorem.sentence(),
                qualifications: faker.lorem.sentence(),
                officialLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                scheduleLearningTimeRequirements: new GovProgramLearningTimeRequirementsDto({
                    hoursOfLearning: 1400,
                    daysOfLearning: 175,
                    weeksOfLearning: 35,
                }),
                dayLearningTimeIntervals: getRandomDayLearningTimeIntervals(agencyAfa.params.timezone),
                price: faker.number.int({
                    min: GovProgram.MIN_PRICE,
                    max: GovProgram.MAX_PRICE,
                }),
                defaultScheduleRequirements: DefaultScheduleRequirementsDto.fromDomain(
                    DefaultScheduleRequirements.fromParams({
                        dayStartTime: DayTime.fromHours(16, agencyAfa.params.timezone),
                    }),
                ),
                agencyId: agencyAfa.getIdOrThrow().value,
            });

            await executeRequest(requestBody, mentor.getAuthTokenString(), 403);
            await executeRequest(requestBody, student.getAuthTokenString(), 403);
            await executeRequest(requestBody, undefined, 403);
        });
    });

    describe('DELETE /gov/programs/:id', () => {
        let programBefore: IntegrationTestGovProgram;

        beforeEach(async () => {
            programBefore = await IntegrationTestGovProgram.create({ agencyId: agencyUzt.getIdOrThrow() });
        });

        async function executeRequest(token?: string, expectedCode = 200): Promise<void> {
            await request(config.server)
                .delete(`/gov/programs/${programBefore.getIdOrThrow().value}`)
                .set('Cookie', `token=${token}`)
                .send()
                .expect(expectedCode);
        }

        it('should delete program with authorized users', async () => {
            await executeRequest(admin.getAuthTokenString(), 204);

            await expect(IntegrationTestGovProgram.get(programBefore.getIdOrThrow())).resolves.toBeUndefined();
        });

        it('should not delete program with unauthorized users', async () => {
            await executeRequest(staff.getAuthTokenString(), 403);
            await executeRequest(mentor.getAuthTokenString(), 403);
            await executeRequest(student.getAuthTokenString(), 403);
            await executeRequest(undefined, 403);

            await expect(IntegrationTestGovProgram.get(programBefore.getIdOrThrow())).resolves.toEqual(programBefore);
        });

        it('should not delete a program if it has users', async () => {
            const chapter = await IntegrationTestUztChapter.create();
            const group = await IntegrationTestGovGroup.create({
                programId: programBefore.getIdOrThrow(),
                period: DateRange.fromDatesInclusive(new Date(2023, 0, 1), new Date(2023, 0, 31)),
            });
            await IntegrationTestGovProfile.create({
                groupId: group.getIdOrThrow(),
                userId: student.getIdOrThrow(),
            });
            await IntegrationTestUztProfile.create({
                userId: student.getIdOrThrow(),
                chapterId: chapter.getIdOrThrow(),
            });

            await executeRequest(admin.getAuthTokenString(), 409);

            await expect(IntegrationTestGovProgram.get(programBefore.getIdOrThrow())).resolves.toEqual(programBefore);
        });
    });
});
