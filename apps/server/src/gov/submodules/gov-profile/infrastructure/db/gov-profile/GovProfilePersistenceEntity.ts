import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToOne, RelationId } from 'typeorm';
import TypeormPersistenceEntity from '../../../../../../core/infrastructure/db/TypeormPersistenceEntity';
import Id from '../../../../../../core/domain/value-objects/Id';
import { GovProfile } from '../../../domain/GovProfile';
import { GovGroupPersistenceEntity } from '../../../../gov-group/infrastructure/db/GovGroupPersistenceEntity';
import User from '../../../../../../users/shared/infrastructure/db/User';

@Entity({ name: GovProfilePersistenceEntity.TABLE_NAME })
export class GovProfilePersistenceEntity extends TypeormPersistenceEntity {
    private static readonly TABLE_NAME = 'gov_profile';

    @Column()
    @RelationId((self: GovProfilePersistenceEntity) => self.group)
    groupId: number;

    @Column()
    @RelationId((self: GovProfilePersistenceEntity) => self.user)
    userId: number;

    @ManyToOne(() => GovGroupPersistenceEntity)
    private group?: never;

    @OneToOne(() => User, { onDelete: 'CASCADE' })
    @JoinColumn()
    private user?: never;

    constructor(params?: Partial<GovProfilePersistenceEntity>) {
        super(params?.id, params?.createdAt, params?.updatedAt);

        if (params) {
            Object.assign(this, params);
        }
    }

    static fromDomain(domain: GovProfile): GovProfilePersistenceEntity {
        return new GovProfilePersistenceEntity({
            id: domain.id?.value,
            groupId: domain.groupId.value,
            userId: domain.userId.value,
        });
    }

    toDomain(): GovProfile {
        return GovProfile.fromParams({
            id: this.id ? new Id(this.id) : undefined,
            groupId: new Id(this.groupId),
            userId: new Id(this.userId),
        });
    }
}
