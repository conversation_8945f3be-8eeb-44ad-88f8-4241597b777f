import { NonFunctionProperties } from '../../../../../../utils/UtilityTypes';
import { IsId } from '../../../../../../core/controllers/validators/IsId';
import { GovProfileCreateAnonymousParams } from '../../../domain/GovProfile';
import Id from '../../../../../../core/domain/value-objects/Id';

export class GovProfileCreateDto {
    @IsId()
    groupId: number;

    constructor(params?: NonFunctionProperties<GovProfileCreateDto>) {
        if (params) {
            Object.assign(this, params);
        }
    }

    toGovProfileCreateAnonymousParams(): GovProfileCreateAnonymousParams {
        return {
            groupId: new Id(this.groupId),
        };
    }
}
