import moment from 'moment';
import DomainEntity from '../../../../../../core/domain/DomainEntity';
import { Timezone } from '../../../../../../core/domain/types/Timezone';
import { WeekDay } from '../../../../../../core/domain/types/WeekDay';
import { DateRange } from '../../../../../../core/domain/value-objects/DateRange';
import { Day } from '../../../../../../core/domain/value-objects/Day';
import { AcademicHours, AstronomicalHours, Hours } from '../../../../../../core/domain/value-objects/Hours';
import Id from '../../../../../../core/domain/value-objects/Id';
import ArgumentValidation from '../../../../../../core/utils/validation/ArgumentValidation';
import { NonFunctionProperties } from '../../../../../../utils/UtilityTypes';
import { LearnerScheduleConstants } from '../../../shared/domain/LearnerScheduleConstants';
import { LearnerWeekScheduleTooShort } from './errors/LearnerWeekScheduleTooShort';
import { LearnerWeekScheduleDay } from './LearnerWeekScheduleDay';
import { LearnerWeekScheduleInterval } from './LearnerWeekScheduleInterval';

export type LearnerWeekScheduleParams = Omit<
    NonFunctionProperties<LearnerWeekSchedule>,
    'totalAcademicHours' | 'totalHours' | 'totalDays'
>;

export class LearnerWeekSchedule extends DomainEntity {
    readonly validIn: DateRange;
    readonly days: Record<WeekDay, LearnerWeekScheduleDay>;
    readonly userId: Id;
    readonly timezone: Timezone;

    constructor(params: LearnerWeekScheduleParams) {
        super(params.id);

        this.validIn = params.validIn;
        this.userId = params.userId;
        this.days = {
            ...params.days,
        };
        this.timezone = params.timezone;

        ArgumentValidation.assert.true(
            this.validIn.isLowerBoundInclusive(),
            'ValidIn should have lower bound inclusive, received ' + this.validIn.serialize(),
        );
        ArgumentValidation.assert.false(
            this.validIn.isUpperBoundInclusive(),
            'ValidIn should have upper bound exclusive, received ' + this.validIn.serialize(),
        );

        if (this.validIn.from) {
            ArgumentValidation.assert.true(
                Day.isDayDate(this.validIn.from, this.timezone),
                'Timezone mismatch. ValidIn from date should be in the same timezone as schedule timezone',
            );
        }

        if (this.validIn.to) {
            ArgumentValidation.assert.true(
                Day.isDayDate(this.validIn.to, this.timezone),
                'Timezone mismatch. ValidIn to date should be in the same timezone as schedule timezone',
            );
        }

        const validDays = this.validIn.hasInfiniteBound()
            ? Infinity
            : moment(this.validIn.to).diff(this.validIn.from, 'day');

        if (validDays < LearnerScheduleConstants.MINIMAL_SCHEDULE_LENGTH_DAYS) {
            throw new LearnerWeekScheduleTooShort({
                invalidScheduleStart: this.validIn.from,
                invalidScheduleEnd: this.validIn.to,
                receivedLength: validDays,
            });
        }
    }

    /**
     * @deprecated should be replaced with academic hours
     */
    get totalHours(): AstronomicalHours {
        return Object.values(this.days).reduce((acc, day) => acc.add(day.totalHours), Hours.astronomical(0));
    }

    get totalAcademicHours(): AcademicHours {
        return Object.values(this.days).reduce((acc, day) => acc.add(day.totalAcademicHours), Hours.academic(0));
    }

    get totalDays(): number {
        return Object.values(this.days).reduce((acc, day) => (day.hasSchedule() ? acc + 1 : acc), 0);
    }

    overlapUpdate(schedule: LearnerWeekSchedule): LearnerWeekSchedule[] {
        const newSchedules: LearnerWeekSchedule[] = [];

        if (schedule.validIn.contains(this.validIn)) {
            newSchedules.push(schedule);

            return newSchedules;
        }

        if (this.validIn.contains(schedule.validIn)) {
            if (+this.validIn.from !== +schedule.validIn.from) {
                newSchedules.push(
                    this.clone(DateRange.fromInclusiveEndExclusive(this.validIn.from, schedule.validIn.from)),
                );
            }

            newSchedules.push(schedule.clone());

            if (+this.validIn.to !== +schedule.validIn.to) {
                newSchedules.push(
                    this.clone(DateRange.fromInclusiveEndExclusive(schedule.validIn.to, this.validIn.to)),
                );
            }

            return newSchedules;
        }

        if (+schedule.validIn.from < +this.validIn.from) {
            newSchedules.push(schedule);

            if (schedule.validIn.to) {
                newSchedules.push(
                    this.clone(DateRange.fromInclusiveEndExclusive(schedule.validIn.to, this.validIn.to)),
                );
            }

            return newSchedules;
        }

        if (+schedule.validIn.from > +this.validIn.from) {
            newSchedules.push(
                this.clone(DateRange.fromInclusiveEndExclusive(this.validIn.from, schedule.validIn.from)),
            );
        }

        newSchedules.push(schedule);

        return newSchedules;
    }

    clone(newValidIn?: DateRange): LearnerWeekSchedule {
        return new LearnerWeekSchedule({
            days: Object.values(this.days).reduce(
                (acc, day) => {
                    acc[day.weekDay] = day.clone();

                    return acc;
                },
                {} as Record<WeekDay, LearnerWeekScheduleDay>,
            ),
            userId: this.userId,
            validIn: newValidIn || this.validIn,
            timezone: this.timezone,
        });
    }

    hasSameTimeslotsAs(other: LearnerWeekSchedule): boolean {
        return Object.values(WeekDay).every((weekDay) => {
            const thisDay = this.days[weekDay];
            const otherDay = other.days[weekDay];

            if (!thisDay && !otherDay) {
                return true;
            }

            if (!thisDay || !otherDay) {
                return false;
            }

            return thisDay.isSame(otherDay);
        });
    }

    getOrder(): number {
        return +this.validIn.from;
    }

    static fromIntervals({
        id,
        intervals,
        userId,
        validIn,
        timezone,
    }: {
        id?: Id;
        intervals: LearnerWeekScheduleInterval[];
        userId: Id;
        validIn: DateRange;
        timezone: Timezone;
    }): LearnerWeekSchedule {
        const intervalsInDays: Record<WeekDay, LearnerWeekScheduleInterval[]> = intervals.reduce(
            (acc, interval) => {
                if (!acc[interval.weekDay]) {
                    acc[interval.weekDay] = [];
                }

                acc[interval.weekDay].push(interval);

                return acc;
            },
            {} as Record<WeekDay, LearnerWeekScheduleInterval[]>,
        );

        const days: Record<WeekDay, LearnerWeekScheduleDay> = Object.values(WeekDay).reduce(
            (acc, day) => {
                const intervals = (intervalsInDays[day] || []).sort((a, b) => a.order() - b.order());

                const mergedIntervals = intervals.reduce((acc, interval) => {
                    if (acc.length === 0) {
                        return [interval];
                    }

                    const lastInterval = acc[acc.length - 1];

                    if (lastInterval.adjacent(interval)) {
                        return [...acc.slice(0, -1), lastInterval.mergeAdjacentInterval(interval)];
                    }

                    return [...acc, interval];
                }, [] as LearnerWeekScheduleInterval[]);

                acc[day] = new LearnerWeekScheduleDay({
                    weekDay: day,
                    intervals: mergedIntervals,
                });

                return acc;
            },
            {} as Record<WeekDay, LearnerWeekScheduleDay>,
        );

        return new LearnerWeekSchedule({
            id,
            validIn,
            days,
            userId,
            timezone,
        });
    }
}
