import { IsDefined, IsEnum } from 'class-validator';
import { TimeStringUpToMinute } from '../../../../../../../../../../shared/src/common/types/time-string';
import { WeekDays as SharedWeekDays, WeekDay } from '../../../../../../../../../../shared/src/common/types/week-day';
import { WeekDays as CoreWeekDays } from '../../../../../../../../core/domain/types/WeekDay';
import { NonFunctionProperties } from '../../../../../../../../utils/UtilityTypes';
import {
    LearnerWeekScheduleInterval,
    LearnerWeekScheduleIntervalWithoutTimezone,
} from '../../../domain/LearnerWeekScheduleInterval';
import { IsDayTime } from '../../../../../../../../core/controllers/validators/IsDayTime';

export class LearnerWeekScheduleIntervalDto {
    constructor(params: NonFunctionProperties<LearnerWeekScheduleIntervalDto>) {
        if (params) {
            this.start = params.start;
            this.end = params.end;
            this.weekDay = params.weekDay;
        }
    }

    @IsDefined()
    @IsDayTime({
        isAllowSeconds: false,
        isAllowNextDayMidnight: false,
    })
    start: TimeStringUpToMinute;

    @IsDefined()
    @IsDayTime({
        isAllowSeconds: false,
        isAllowNextDayMidnight: false,
    })
    end: TimeStringUpToMinute;

    @IsDefined()
    @IsEnum(WeekDay)
    weekDay: WeekDay;

    toDomain(): LearnerWeekScheduleIntervalWithoutTimezone {
        return {
            start: this.start,
            end: this.end,
            weekDay: CoreWeekDays.fromString(this.weekDay),
        };
    }

    static fromDomain(domain: LearnerWeekScheduleInterval): LearnerWeekScheduleIntervalDto {
        return new LearnerWeekScheduleIntervalDto({
            start: domain.start.toTimeStringWithoutSeconds(),
            end: domain.end.toTimeStringWithoutSeconds(),
            weekDay: SharedWeekDays.stringToFull(domain.weekDay),
        });
    }
}
