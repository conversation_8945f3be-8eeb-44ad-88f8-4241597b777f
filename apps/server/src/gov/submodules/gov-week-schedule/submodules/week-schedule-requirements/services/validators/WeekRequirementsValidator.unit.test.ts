import { Timezone } from '../../../../../../../core/domain/types/Timezone';
import { WeekDay, WeekDays } from '../../../../../../../core/domain/types/WeekDay';
import { DateRange } from '../../../../../../../core/domain/value-objects/DateRange';
import { Day } from '../../../../../../../core/domain/value-objects/Day';
import { DayTime } from '../../../../../../../core/domain/value-objects/DayTime';
import { AcademicHours, Hours } from '../../../../../../../core/domain/value-objects/Hours';
import Id from '../../../../../../../core/domain/value-objects/Id';
import TestObjects from '../../../../../../../test-toolkit/shared/TestObjects';
import { DefaultScheduleRequirements } from '../../../default-week-schedule/domain/DefaultScheduleRequirements';
import { LearnerWeekSchedule } from '../../../week-schedule/domain/LearnerWeekSchedule';
import { LearnerWeekScheduleInterval } from '../../../week-schedule/domain/LearnerWeekScheduleInterval';
import { ConsecutiveSessionsBreakRequirements } from '../../domain/ConsecutiveSessionsBreakRequirements';
import { WeekScheduleDayRequirements } from '../../domain/WeekScheduleDayRequirements';
import { WeekScheduleLearningTime } from '../../domain/WeekScheduleLearningTime';
import { WeekScheduleRequirements } from '../../domain/WeekScheduleRequirements';
import { WeekRequirementsValidator } from './WeekRequirementsValidator';
import { NotEmptyRange } from '../../../../../../../core/domain/value-objects/Range';
import { Numeric } from '../../../../../../../core/domain/value-objects/Numeric';

function createRequirements({
    daysRequired,
    dailyHoursLimit = Hours.academic(24),
    timezone = Timezone.EUROPE_VILNIUS,
    hoursRequired = Hours.academic(0),
    isBlockedCallback = (): boolean => false,
    holidayIdCallback = (): Id | undefined => undefined,
    learningTimeCallback = (): WeekScheduleLearningTime =>
        WeekScheduleLearningTime.fromParams({
            from: DayTime.fromHours(0, timezone),
            to: DayTime.fromHours(24, timezone),
            limitHours: dailyHoursLimit,
        }),
}: {
    daysRequired: NotEmptyRange<Numeric>;
    dailyHoursLimit?: AcademicHours;
    hoursRequired?: AcademicHours;
    timezone?: Timezone;
    isBlockedCallback?: (day: WeekDay) => boolean;
    holidayIdCallback?: (day: WeekDay) => Id | undefined;
    learningTimeCallback?: (day: WeekDay) => WeekScheduleLearningTime;
}): WeekScheduleRequirements {
    const dayRequirements = new Map(
        WeekDays.WEEK_DAYS_IN_ORDER.map((weekDay) => [
            weekDay,
            WeekScheduleDayRequirements.fromParams({
                weekDay,
                isBlocked: isBlockedCallback(weekDay),
                learningTime: learningTimeCallback(weekDay),
                holidayId: holidayIdCallback(weekDay),
            }),
        ]),
    );

    return WeekScheduleRequirements.fromParams({
        timezone,
        daysRequired,
        hoursRequired,
        consecutiveSessionsBreakRequirements: ConsecutiveSessionsBreakRequirements.NO_LIMITS,
        daysRequirements: dayRequirements,
        defaultScheduleRequirements: DefaultScheduleRequirements.fromParams({
            dayStartTime: DayTime.fromHours(0, timezone),
        }),
    });
}

function createSchedule({
    timezone = Timezone.EUROPE_VILNIUS,
    validIn = DateRange.fromInclusiveEndExclusive(
        Day.fromString('2025-04-14', timezone).toDate(),
        Day.fromString('2025-04-21', timezone).toDate(),
    ),
    intervals = [] as LearnerWeekScheduleInterval[],
}: {
    timezone?: Timezone;
    validIn?: DateRange;
    intervals?: LearnerWeekScheduleInterval[];
} = {}): LearnerWeekSchedule {
    return LearnerWeekSchedule.fromIntervals({
        timezone,
        validIn,
        intervals,
        userId: TestObjects.id(),
    });
}

function createInterval({
    startHour,
    endHour,
    weekDay,
    timezone = Timezone.EUROPE_VILNIUS,
}: {
    startHour: number;
    endHour: number;
    weekDay: WeekDay;
    timezone?: Timezone;
}): LearnerWeekScheduleInterval {
    return new LearnerWeekScheduleInterval({
        start: DayTime.fromConfig({ hours: startHour, timezone }),
        end: DayTime.fromConfig({ hours: endHour, timezone }),
        weekDay,
        timezone,
    });
}

describe(WeekRequirementsValidator.name, () => {
    const validator = new WeekRequirementsValidator();

    describe('validate matching timezone requirement', () => {
        const requirements = createRequirements({ daysRequired: NotEmptyRange.withSingleValue(Numeric.from(0)) });

        it('should return true if the schedule timezone matches the requirements timezone', () => {
            const schedule = createSchedule();
            expect(validator.validate({ schedule, requirements })).toEqual({ isValid: true });
        });

        it('should return false if the schedule timezone does not match the requirements timezone', () => {
            const schedule = createSchedule({ timezone: Timezone.AMERICA_NEW_YORK });
            expect(validator.validate({ schedule, requirements })).toEqual({
                isValid: false,
                message: expect.stringContaining('Week requirements timezone should be the same as schedule timezone'),
            });
        });
    });

    describe('validate required learning days per week requirement', () => {
        const requirements = createRequirements({
            daysRequired: NotEmptyRange.withSingleValue(Numeric.from(1)),
            hoursRequired: Hours.academic(4),
        });

        it('should return true if the schedule has the required number of days', () => {
            const schedule = createSchedule({
                intervals: [createInterval({ startHour: 9, endHour: 12, weekDay: WeekDay.MONDAY })],
            });

            expect(validator.validate({ schedule, requirements })).toEqual({ isValid: true });
        });

        it('should return false if the schedule has less than the required number of days', () => {
            const schedule = createSchedule();

            expect(validator.validate({ schedule, requirements })).toEqual({
                isValid: false,
                message: `Your schedule should have exactly ${requirements.daysRequired} days of learning. You scheduled 0`,
            });
        });

        it('should return false if the schedule has more than the required number of days', () => {
            const schedule = createSchedule({
                intervals: [
                    createInterval({ startHour: 9, endHour: 12, weekDay: WeekDay.MONDAY }),
                    createInterval({ startHour: 9, endHour: 12, weekDay: WeekDay.TUESDAY }),
                ],
            });

            expect(validator.validate({ schedule, requirements })).toEqual({
                isValid: false,
                message: `Your schedule should have exactly ${requirements.daysRequired} days of learning. You scheduled 2`,
            });
        });
    });

    describe('validate required learning hours per week requirement', () => {
        it('should return true if the schedule has the required number of hours', () => {
            const requirements = createRequirements({
                daysRequired: NotEmptyRange.withSingleValue(Numeric.from(2)),
                hoursRequired: Hours.academic(8),
            });

            const schedule = createSchedule({
                intervals: [
                    createInterval({ startHour: 9, endHour: 12, weekDay: WeekDay.MONDAY }),
                    createInterval({ startHour: 9, endHour: 12, weekDay: WeekDay.TUESDAY }),
                ],
            });

            expect(validator.validate({ schedule, requirements })).toEqual({ isValid: true });
        });

        it('should return false if the schedule has less than the required number of hours', () => {
            const requirements = createRequirements({
                daysRequired: NotEmptyRange.withSingleValue(Numeric.from(1)),
                hoursRequired: Hours.academic(8),
            });

            const schedule = createSchedule({
                intervals: [createInterval({ startHour: 9, endHour: 12, weekDay: WeekDay.MONDAY })],
            });

            expect(validator.validate({ schedule, requirements })).toEqual({
                isValid: false,
                message: 'Your schedule should have exactly 8 sessions. You scheduled 4',
            });
        });

        it('should return false if the schedule has more than the required number of hours', () => {
            const requirements = createRequirements({
                daysRequired: NotEmptyRange.withSingleValue(Numeric.from(3)),
                hoursRequired: Hours.academic(8),
            });

            const schedule = createSchedule({
                intervals: [
                    createInterval({ startHour: 9, endHour: 12, weekDay: WeekDay.MONDAY }),
                    createInterval({ startHour: 9, endHour: 12, weekDay: WeekDay.TUESDAY }),
                    createInterval({ startHour: 9, endHour: 12, weekDay: WeekDay.WEDNESDAY }),
                ],
            });

            expect(validator.validate({ schedule, requirements })).toEqual({
                isValid: false,
                message: 'Your schedule should have exactly 8 sessions. You scheduled 12',
            });
        });
    });
});
