import { ReschedulingCustomRules } from '../../../../../../../../../shared/src/gov/gov-schedule-requirements/shared/schedule-validation-consecutive-sessions-custom-rules';
import { FromTomorrowReschedulingRule } from './FromTomorrowReschedulingRule';
import { assertNever } from '../../../../../../../utils/assert-never';
import { WeekReschedulingRule } from './WeekReschedulingRule';

export class WeekReschedulingRuleFactory {
    static fromName(name: ReschedulingCustomRules): WeekReschedulingRule {
        switch (name) {
            case ReschedulingCustomRules.FROM_TOMORROW:
                return new FromTomorrowReschedulingRule();
            default:
                assertNever(name);
        }
    }
}
