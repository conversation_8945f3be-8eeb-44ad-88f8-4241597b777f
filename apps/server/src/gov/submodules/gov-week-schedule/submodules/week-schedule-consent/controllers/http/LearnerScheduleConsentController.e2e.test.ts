import request from 'supertest';
import Container from 'typedi';
import { faker } from '@faker-js/faker';
import { Configuration } from '../../../../../../../config/Configuration';
import { ConfigurationModule } from '../../../../../../../config/infrastructure/di/ConfigurationModule';
import { IntegrationTestUser } from '../../../../../../../test-toolkit/e2e/entities/IntegrationTestUser';
import { <PERSON><PERSON> } from '../../../../../../../auth/domain/Cookie';
import { LearnerScheduleConsentController } from './LearnerScheduleConsentController';
import ImpersonationDoNothingDTO from '../../../../../../../auth/controllers/http/dto/ImpersonationDoNothingDTO';

describe(LearnerScheduleConsentController.name, () => {
    let config: Configuration;
    let student: IntegrationTestUser;

    beforeAll(async () => {
        config = Container.get(ConfigurationModule.CONFIGURATION_TOKEN);

        student = await IntegrationTestUser.createFakeStudent();
        await student.authorize();
    });

    describe('POST /learner-schedule/week/:userId/give-consent', () => {
        it('should do nothing when impersonating', async () => {
            const res = await request(config.server)
                .post(`/learner-schedule/week/${student?.id?.value}/give-consent`)
                .set(
                    'Cookie',
                    `${Cookie.TOKEN}=${student.getAuthTokenString()};${Cookie.IMPERSONATE}=${faker.internet.jwt()}`,
                )
                .send()
                .expect(200);
            expect(res.body).toEqual(new ImpersonationDoNothingDTO());
        });
    });
});
