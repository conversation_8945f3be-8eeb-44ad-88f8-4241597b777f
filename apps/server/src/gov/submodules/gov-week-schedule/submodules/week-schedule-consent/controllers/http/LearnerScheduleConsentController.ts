import { Authorized, Body, CurrentUser, <PERSON>sonController, Param, Post, UseBefore } from 'routing-controllers';
import { OpenAPI } from 'routing-controllers-openapi';
import responses from '../../../../../../../core/controllers/docs/responses';
import User, { Role } from '../../../../../../../users/shared/infrastructure/db/User';
import { Inject, Service } from 'typedi';
import 'moment-timezone';
import { WeekScheduleConsentService } from '../../services/WeekScheduleConsentService';
import Id from '../../../../../../../core/domain/value-objects/Id';
import { ifRoleAllowOnlySelf } from '../../../../../../../core/controllers/ifRoleAllowOnlySelf';
import { GiveWeekScheduleConsentDto } from './dto/GiveWeekScheduleConsentDto';
import ArgumentValidation from '../../../../../../../core/utils/validation/ArgumentValidation';
import { ImpersonationDoNothing } from '../../../../../../../auth/controllers/http/middleware/ImpersonationDoNothing';

@Service()
@JsonController('/learner-schedule/week')
export class LearnerScheduleConsentController {
    constructor(@Inject() private readonly uztLearnerScheduleConsentService: WeekScheduleConsentService) {}

    @Authorized([Role.USER, Role.ADMIN])
    @UseBefore(ImpersonationDoNothing)
    @Post('/:userId/give-consent')
    @OpenAPI({
        responses,
        summary: 'Give pending schedule consents',
        security: [{ cookieAuth: [] }],
    })
    async givePendingConsents(
        @Param('userId') primitiveUserId: number,
        @Body() body: GiveWeekScheduleConsentDto,
        @CurrentUser() actor: User,
    ): Promise<void> {
        const userId = new Id(primitiveUserId);
        ifRoleAllowOnlySelf(actor, userId, [Role.USER]);
        ArgumentValidation.assert.true(
            body.isConsentGiven,
            'You must confirm the schedule consents by submitting isConsentGiven',
        );

        await this.uztLearnerScheduleConsentService.checkPriedasSignedAndGiveAllPendingConsents(userId);
    }

    @Authorized([Role.ADMIN])
    @Post('/:userId/trigger-consent')
    @OpenAPI({
        responses,
        summary: 'Trigger consent notification for pending schedule consents',
        security: [{ cookieAuth: [] }],
    })
    async triggerConsentNotification(@Param('userId') primitiveUserId: number): Promise<void> {
        await this.uztLearnerScheduleConsentService.syncPendingScheduleConsentsNotification(new Id(primitiveUserId));
    }

    @Authorized([Role.ADMIN])
    @Post('/:userId/agreement-signed/')
    @OpenAPI({
        responses,
        summary: 'Mark learner as signed the schedule agreement',
        security: [{ cookieAuth: [] }],
    })
    async markAsPlatformScheduleAgreementSigned(@Param('userId') primitiveUserId: number): Promise<void> {
        await this.uztLearnerScheduleConsentService.markAsPlatformScheduleAgreementSigned(new Id(primitiveUserId));
    }

    @Authorized([Role.ADMIN])
    @Post('/:userId/agreement-unsigned/')
    @OpenAPI({
        responses,
        summary: 'Mark learner as NOT signed the schedule agreement',
        security: [{ cookieAuth: [] }],
    })
    async markAsPlatformScheduleAgreementNotSigned(@Param('userId') primitiveUserId: number): Promise<void> {
        await this.uztLearnerScheduleConsentService.markAsPlatformScheduleAgreementNotSigned(new Id(primitiveUserId));
    }
}
