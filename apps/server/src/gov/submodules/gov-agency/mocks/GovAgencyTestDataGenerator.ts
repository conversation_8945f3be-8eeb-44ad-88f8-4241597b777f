import { faker } from '@faker-js/faker';
import { GovAgency } from '../domain/GovAgency';
import { Timezone } from '../../../../core/domain/types/Timezone';
import { GovAgencyType } from '../domain/GovAgencyType';
import TestObjects from '../../../../test-toolkit/shared/TestObjects';

export class GovAgencyTestDataGenerator {
    static getTestGovAgency = (agency: Partial<GovAgency> = {}): GovAgency => {
        return GovAgency.fromParams({
            id: TestObjects.id(),
            name: faker.lorem.word(),
            type: faker.helpers.enumValue(GovAgencyType),
            timezone: faker.helpers.enumValue(Timezone),
            holidayGroupId: TestObjects.id(),
            ...agency,
        });
    };
}
