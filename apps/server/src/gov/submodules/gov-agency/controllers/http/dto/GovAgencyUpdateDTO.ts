import { IsEnum, IsOptional, IsString } from 'class-validator';
import { NonFunctionProperties } from '../../../../../../utils/UtilityTypes';
import { GovAgencyUpdateParams } from '../../../domain/GovAgency';
import { GovAgencyType } from '../../../domain/GovAgencyType';
import { Timezone } from '../../../../../../core/domain/types/Timezone';
import { IsId } from '../../../../../../core/controllers/validators/IsId';
import Id from '../../../../../../core/domain/value-objects/Id';

export class GovAgencyUpdateDTO {
    @IsOptional()
    @IsString()
    name?: string;

    @IsOptional()
    @IsEnum(GovAgencyType)
    type?: GovAgencyType;

    @IsOptional()
    @IsEnum(Timezone)
    timezone?: Timezone;

    @IsOptional()
    @IsId()
    holidayGroupId?: number;

    constructor(params?: NonFunctionProperties<GovAgencyUpdateDTO>) {
        if (params) {
            Object.assign(this, params);
        }
    }

    toGovAgencyUpdateParams(): GovAgencyUpdateParams {
        return {
            name: this.name,
            type: this.type,
            timezone: this.timezone,
            holidayGroupId: this.holidayGroupId ? new Id(this.holidayGroupId) : undefined,
        };
    }
}
