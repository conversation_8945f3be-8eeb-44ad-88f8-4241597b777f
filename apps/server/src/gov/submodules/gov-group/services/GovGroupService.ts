import { DateRange } from '../../../../core/domain/value-objects/DateRange';
import Id from '../../../../core/domain/value-objects/Id';
import IllegalArgumentError from '../../../../core/errors/IllegalArgumentError';
import IllegalStateError from '../../../../core/errors/IllegalStateError';
import { ForeignKeyConstraintViolationError } from '../../../../core/infrastructure/db/errors/ForeignKeyConstraintViolationError';
import { UniqueConstraintViolationError } from '../../../../core/infrastructure/db/errors/UniqueConstraintViolationError';
import Transaction from '../../../../core/infrastructure/Transaction';
import ArgumentValidation from '../../../../core/utils/validation/ArgumentValidation';
import { GovAgencyType } from '../../gov-agency/domain/GovAgencyType';
import { GovAgencyService } from '../../gov-agency/services/GovAgencyService';
import { GovProgramService } from '../../gov-program/services/GovProgramService';
import { GovGroup, GovGroupCreateParams, GovGroupUpdateParams } from '../domain/GovGroup';
import { GovGroupRepository } from '../infrastructure/db/GovGroupRepository';

type GovGroupQueryParams = {
    govAgencyType?: GovAgencyType;
    endMonth?: Date;
};

export class GovGroupService {
    constructor(
        private readonly govAgencyService: GovAgencyService,
        private readonly govProgramService: GovProgramService,
        private readonly govGroupRepository: GovGroupRepository,
    ) {}

    async get(id: Id, tx?: Transaction): Promise<GovGroup | undefined> {
        return this.govGroupRepository.get(id, tx);
    }

    async getByIdAndGovAgencyType(
        id: Id,
        govAgencyType: GovAgencyType,
        tx?: Transaction,
    ): Promise<GovGroup | undefined> {
        return this.govGroupRepository.getByIdAndGovAgencyType(id, govAgencyType, tx);
    }

    async getAll(): Promise<GovGroup[]> {
        return await this.govGroupRepository.getAll();
    }

    async find(params: GovGroupQueryParams = {}): Promise<GovGroup[]> {
        let programIds: Id[] = [];
        if (params.govAgencyType) {
            const agency = await this.govAgencyService.getByType(params.govAgencyType);
            ArgumentValidation.assert.defined(agency, 'Gov agency not found');

            const programs = await this.govProgramService.getByAgencyId(agency.getIdOrThrow());
            if (programs.length === 0) {
                return [];
            }

            programIds = programs.map((program) => program.getIdOrThrow());
        }

        let endRange: DateRange | undefined = undefined;
        if (params.endMonth) {
            endRange = DateRange.getMonthRange(params.endMonth);
        }

        return await this.govGroupRepository.find({ endRange, programs: programIds });
    }

    async getByUserId(userId: Id, tx?: Transaction): Promise<GovGroup | undefined> {
        return await this.govGroupRepository.getByUserId(userId, tx);
    }

    async create(params: GovGroupCreateParams, tx?: Transaction): Promise<GovGroup> {
        try {
            return await this.govGroupRepository.save(GovGroup.create(params), tx);
        } catch (e) {
            if (e instanceof UniqueConstraintViolationError) {
                throw new IllegalArgumentError('One or more fields are not unique');
            }

            if (e instanceof ForeignKeyConstraintViolationError) {
                throw new IllegalArgumentError('One or more fields do not exist');
            }
            throw new IllegalArgumentError('Failed to create group');
        }
    }

    async update(id: Id, params: GovGroupUpdateParams, tx?: Transaction): Promise<GovGroup | undefined> {
        const group = await this.govGroupRepository.get(id);
        if (!group) {
            return undefined;
        }

        try {
            return await this.govGroupRepository.save(group.update(params), tx);
        } catch (e) {
            if (e instanceof UniqueConstraintViolationError) {
                throw new IllegalArgumentError('One or more fields are not unique');
            }

            if (e instanceof ForeignKeyConstraintViolationError) {
                throw new IllegalArgumentError('One or more fields do not exist');
            }
        }
    }

    async delete(id: Id): Promise<void> {
        try {
            await this.govGroupRepository.delete(id);
        } catch (e) {
            if (e instanceof ForeignKeyConstraintViolationError) {
                throw new IllegalStateError('Cannot delete group because it has users');
            }

            throw e;
        }
    }
}
