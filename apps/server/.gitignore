# Temp
src/v2

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Directory for instrumented libs generated by jscoverage/JSCover
lib-cov

# jest-junit reporter output dir
reports

# Coverage directory used by tools like istanbul
coverage

# nyc test coverage
.nyc_output

# Grunt intermediate storage (http://gruntjs.com/creating-plugins#storing-task-files)
.grunt

# Bower dependency directory (https://bower.io/)
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons (https://nodejs.org/api/addons.html)
build/Release

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
/.env.*
*.env

# next.js build output
.next


# Editor-specific metadata folders
.vs
.vscode
.DS_Store
.idea
dist
_ts3.4
*.tsbuildinfo
.watchmanconfig
*.iml
# Elastic Beanstalk Files
.elasticbeanstalk/*
!.elasticbeanstalk/config.yml
!.elasticbeanstalk/*.cfg.yml
!.elasticbeanstalk/*.global.yml

# Github keys
github-private-key.pem
github-private-key.pem.pub

# Chrome binary
chrome