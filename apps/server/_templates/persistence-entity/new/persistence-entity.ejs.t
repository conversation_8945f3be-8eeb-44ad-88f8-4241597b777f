---
to: <%= h.persistenceEntity.index(name) %>
---

import { Entity } from 'typeorm';
import TypeormPersistenceEntity from '<%= h.importPath(h.core.infrastructure.db.typeormPersistenceEntity(), h.persistenceEntity.index(name)) %>';
import Id from '<%= h.importPath(h.core.domain.valueObjects.id.index(), h.persistenceEntity.index(name)) %>';
import { <%= h.entity.name(name) %> } from '<%= h.importPath(h.entity.index(name), h.persistenceEntity.index(name)) %>';

@Entity({ name: <%= h.persistenceEntity.name(name) %>.TABLE_NAME })
export class <%= h.persistenceEntity.name(name) %> extends TypeormPersistenceEntity {
    private static readonly TABLE_NAME = '<%= h.persistenceEntity.tableName(name) %>';

    constructor(params?: Partial<<%= h.persistenceEntity.name(name) %>>) {
        super(params?.id, params?.createdAt, params?.updatedAt);

        if (params) {
            Object.assign(this, params);
        }
    }

    static fromDomain(domain: <%= h.entity.name(name) %>): <%= h.persistenceEntity.name(name) %> {
        return new <%= h.persistenceEntity.name(name) %>({
            id: domain.id?.value,
        });
    }

    toDomain(): <%= h.entity.name(name) %> {
        return <%= h.entity.name(name) %>.fromParams({
            id: this.id ? new Id(this.id) : undefined,
        });
    }
}
