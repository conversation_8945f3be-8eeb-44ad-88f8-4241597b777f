---
to: <%= h.entity.index(name) %>
---

import DomainEntity from '<%= h.importPath(h.core.domain.entity.index(), h.entity.index(name)) %>';
import { NonFunctionProperties } from '<%= h.importPath(h.utils.types.index(), h.entity.index(name)) %>';

export type <%= h.entity.name(name) %>Params = NonFunctionProperties<<%= h.entity.name(name) %>>;
export type <%= h.entity.name(name) %>CreateParams = NonFunctionProperties<<%= h.entity.name(name) %>>;

export class <%= h.entity.name(name) %> extends DomainEntity {
    protected constructor({ id }: <%= h.entity.name(name) %>Params) {
        super(id);
    }

    static fromParams(params: <%= h.entity.name(name) %>Params): <%= h.entity.name(name) %> {
        return new <%= h.entity.name(name) %>(params);
    }

    static create(params: <%= h.entity.name(name) %>CreateParams): <%= h.entity.name(name) %> {
        return new <%= h.entity.name(name) %>(params);
    }
}
