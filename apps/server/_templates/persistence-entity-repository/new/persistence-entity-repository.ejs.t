---
to: <%= h.persistenceEntityRepository.index(name) %>
---

import TypeormRepository from '<%= h.importPath(h.core.infrastructure.db.typeormRepository(), h.persistenceEntityRepository.index(name)) %>';
import TransactionManager from '<%= h.importPath(h.core.infrastructure.transactionManager(), h.persistenceEntityRepository.index(name)) %>';
import { <%= h.entity.name(name) %> } from '<%= h.importPath(h.entity.index(name), h.persistenceEntityRepository.index(name)) %>';
import { <%= h.persistenceEntity.name(name) %> } from '<%= h.importPath(h.persistenceEntity.index(name), h.persistenceEntityRepository.index(name)) %>';
import { createMapperFromEntity } from '<%= h.importPath(h.utils.createMapperFromEntity.index(name), h.persistenceEntityRepository.index(name)) %>';

export class <%= h.persistenceEntityRepository.name(name) %> extends TypeormRepository<
    <%= h.entity.name(name) %>,
    <%= h.persistenceEntity.name(name) %>
> {
    constructor(tm: TransactionManager) {
        super(
            tm,
            createMapperFromEntity(<%= h.persistenceEntity.name(name) %>),
            <%= h.persistenceEntity.name(name) %>,
        );
    }
}

