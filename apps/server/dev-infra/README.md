# Dev Infrastructure

This module allows to bootstrap prod-like dev infrastructure for local development

## Prerequisites

* Docker
* [tflocal](https://github.com/localstack/terraform-local) is installed in your shell

## How to bootstrap

```shell
cd dev-infra/terraform
docker compose up -d --wait
tflocal init
tflocal apply -var="aws_environment_name=local" -auto-approve
```

After that commands executed, you will have docker containers
that are compatible with `.env.example` out of the box.

# Tips

## awslocal

You can install [awslocal](https://github.com/localstack/awscli-local) to
run commands for cloud infrastructure locally, for example:

```shell
awslocal sqs list-queues
```

You don't need to set up anything for it to work with bootstrapped container

## Without cloud

If you don't want to bootstrap cloud infrastructure you can exclude `localstack` container
and define following variables in `.env` file:

```dotenv
AWS_SQS_PRODUCER_ENABLED=false
AWS_SQS_CONSUMER_ENABLED=false
AWS_SNS_PRODUCER_ENABLED=false
AWS_SES_ENABLED=false
```

## Preview email

To preview last sent email message you can use:

```bash
curl --silent http://localhost:4566/_aws/ses | jq '.messages | .[-1] | .RawData' | sed -e 's/\\\"/\"/g' -e 's/^.//g' -e 's/.$//g' | sed 's/\\r\\n/\n/g' >> $HOME/tmp.eml && open $HOME/tmp.eml && rm $HOME/tmp.eml
```

Or you can define bash function in your shell to convenience:

```bash
function ses() {
  curl --silent "${1:-http://localhost:4566/_aws/ses}" | jq '.messages | .[-1] | .RawData' | sed -e 's/\\\"/\"/g' -e 's/^.//g' -e 's/.$//g' | sed 's/\\r\\n/\n/g' >> $HOME/tmp.eml && open $HOME/tmp.eml && rm $HOME/tmp.eml;
}
```

Usage:

```bash
ses # preview last message
# View particular message (link will be logged to console during sending)
ses http://localhost:4566/_aws/ses?id=ewiasoqomgiskkoj-ahpnrdyv-bxrk-nmiw-irdh-ozixhhzlonom-iincii
```

Keep in mind that you need to have application that associated
with `.eml` files to preview message.