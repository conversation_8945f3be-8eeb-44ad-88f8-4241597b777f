resource "aws_sns_topic" "discord_events_topic" {
  name = join("-", [var.aws_environment_name, "discord-events"])
}

resource "aws_sns_topic" "learning_events_topic" {
  name = join("-", [var.aws_environment_name, "learning-events"])
}

resource "aws_sns_topic" "user_events_topic" {
  name = join("-", [var.aws_environment_name, "user-events"])
}

resource "aws_sns_topic" "meeting_events_topic" {
  name       = join("-", [var.aws_environment_name, "meeting-events.fifo"])
  fifo_topic = true
}

resource "aws_sns_topic" "document_events_topic" {
  name = join("-", [var.aws_environment_name, "document-events"])
}

