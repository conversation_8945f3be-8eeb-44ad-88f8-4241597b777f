provider "aws" {
  region = "eu-north-1"
}

variable "aws_environment_name" {
  description = "The AWS environment name"
  type        = string
}

module "sqs" {
  source = "./sqs"

  aws_environment_name = var.aws_environment_name
}

module "sns" {
  source = "./sns"

  aws_environment_name = var.aws_environment_name
}

module "ses" {
  source = "./ses"

  aws_environment_name = var.aws_environment_name
}

module "s3" {
  source = "./s3"

  aws_environment_name = var.aws_environment_name
}

module "s3-subscriptions" {
  source = "./s3-subscriptions"

  aws_environment_name       = var.aws_environment_name
  document_bucket            = module.s3.s3_documents_bucket
  document_created_queue_arn = module.sqs.sqs_new_document_uploaded_arn
}


module "sns-subscriptions" {
  source = "./sns-subscriptions"

  aws_environment_name = var.aws_environment_name

  sns_learning_events_topic_arn = module.sns.learning_events_topic_arn
  sns_user_events_topic_arn     = module.sns.user_events_topic_arn
  sns_meeting_events_topic_arn  = module.sns.meeting_events_topic_arn
  sns_document_events_topic_arn = module.sns.document_events_topic_arn

  sqs_endorsement_start_stages_for_student_queue_arn     = module.sqs.sqs_endorsement_start_stages_for_student_queue_arn
  sqs_endorsement_start_stages_for_student_queue_dlq_arn = module.sqs.sqs_endorsement_start_stages_for_student_queue_dlq_arn
  sqs_intercom_convert_lead_arn                          = module.sqs.sqs_intercom_convert_lead_arn
  sqs_intercom_convert_lead_dlq_arn                      = module.sqs.sqs_intercom_convert_lead_dlq_arn
  sqs_register_standup_attendance_arn                    = module.sqs.sqs_register_standup_attendance_arn
  sqs_register_standup_attendance_arn_dlq                = module.sqs.sqs_register_standup_attendance_dlq_arn
  sqs_uzt_documents_handle_document_event_arn            = module.sqs.uzt_documents_handle_document_event
  sqs_uzt_documents_handle_document_event_dlq_arn        = module.sqs.uzt_documents_handle_document_event_dlq
}