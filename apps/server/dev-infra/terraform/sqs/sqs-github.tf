resource "aws_sqs_queue" "github_clone_template_dlq" {
  name = join("-", [var.aws_environment_name, "github-clone-template-dlq"])
}

resource "aws_sqs_queue" "github_clone_template" {
  name = join("-", [var.aws_environment_name, "github-clone-template"])

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.github_clone_template_dlq.arn
    maxReceiveCount     = 5
  })
}

resource "aws_sqs_queue" "github_add_collaborator_dlq" {
  name = join("-", [var.aws_environment_name, "github-add-collaborator-dlq"])
}

resource "aws_sqs_queue" "github_add_collaborator" {
  name          = join("-", [var.aws_environment_name, "github-add-collaborator"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.github_add_collaborator_dlq.arn
    maxReceiveCount     = 5
  })
}

resource "aws_sqs_queue" "github_remove_collaborator_dlq" {
  name = join("-", [var.aws_environment_name, "github-remove-collaborator-dlq"])
}

resource "aws_sqs_queue" "github_remove_collaborator" {
  name = join("-", [var.aws_environment_name, "github-remove-collaborator"])

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.github_remove_collaborator_dlq.arn
    maxReceiveCount     = 5
  })
}