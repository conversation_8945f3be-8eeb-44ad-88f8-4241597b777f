output "sqs_endorsement_start_stages_for_student_queue_arn" {
  description = "Start endorsement stages for student queue ARN"
  value       = aws_sqs_queue.endorsement_start_stages_for_student_queue.arn
}

output "sqs_endorsement_start_stages_for_student_queue_dlq_arn" {
  description = "Start endorsement stages for student DLQ queue ARN"
  value       = aws_sqs_queue.endorsement_start_stages_for_student_dlq.arn
}

output "sqs_intercom_convert_lead_arn" {
  description = "Convert Intercom lead queue ARN"
  value       = aws_sqs_queue.intercom_convert_lead.arn
}

output "sqs_intercom_convert_lead_dlq_arn" {
  description = "Convert Intercom lead DLQ queue ARN"
  value       = aws_sqs_queue.intercom_convert_lead_dlq.arn
}

output "sqs_register_standup_attendance_dlq_arn" {
  value = aws_sqs_queue.register_standup_attendance_dlq.arn
}

output "sqs_register_standup_attendance_arn" {
  value = aws_sqs_queue.register_standup_attendance.arn
}

output "sqs_new_document_uploaded_dlq_arn" {
  value = aws_sqs_queue.new_document_uploaded_dlq.arn
}

output "sqs_new_document_uploaded_arn" {
  value = aws_sqs_queue.new_document_uploaded.arn
}

output "uzt_documents_handle_document_event_dlq" {
  value = aws_sqs_queue.uzt_documents_handle_document_event_dlq.arn
}

output "uzt_documents_handle_document_event" {
  value = aws_sqs_queue.uzt_documents_handle_document_event.arn
}