resource "aws_sqs_queue" "intercom_convert_lead_dlq" {
  name = join("-", [var.aws_environment_name, "intercom-convert-lead-dlq"])
}

resource "aws_sqs_queue" "intercom_convert_lead" {
  name          = join("-", [var.aws_environment_name, "intercom-convert-lead"])
  delay_seconds = 60

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.intercom_convert_lead_dlq.arn
    maxReceiveCount     = 5
  })
}