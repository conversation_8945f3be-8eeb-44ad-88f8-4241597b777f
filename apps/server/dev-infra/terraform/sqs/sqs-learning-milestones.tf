resource "aws_sqs_queue" "register_standup_attendance_dlq" {
  name       = join("-", [var.aws_environment_name, "register-standup-attendance-dlq.fifo"])
  fifo_queue = true
}

resource "aws_sqs_queue" "register_standup_attendance" {
  name          = join("-", [var.aws_environment_name, "register-standup-attendance.fifo"])
  delay_seconds = 5
  fifo_queue    = true

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.register_standup_attendance_dlq.arn
    maxReceiveCount     = 5
  })
}
