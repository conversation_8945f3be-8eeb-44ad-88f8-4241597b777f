resource "aws_sqs_queue" "uzt_document_commands_dlq" {
  name       = join("-", [var.aws_environment_name, "uzt-document-commands-dlq.fifo"])
  fifo_queue = true
}

resource "aws_sqs_queue" "uzt_document_commands" {
  name       = join("-", [var.aws_environment_name, "uzt-document-commands.fifo"])
  fifo_queue = true

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.uzt_document_commands_dlq.arn
    maxReceiveCount     = 5
  })
}

resource "aws_sqs_queue" "uzt_documents_handle_document_event_dlq" {
  name = join("-", [var.aws_environment_name, "uzt-documents-handle-document-event-dlq"])
}

resource "aws_sqs_queue" "uzt_documents_handle_document_event" {
  name = join("-", [var.aws_environment_name, "uzt-documents-handle-document-event"])

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.uzt_documents_handle_document_event_dlq.arn
    maxReceiveCount     = 5
  })
}