resource "aws_sqs_queue" "google_drive_clone_github_repo_command_dlq" {
  name = join("-", [var.aws_environment_name, "google-drive-clone-github-repo-command-dlq"])
}

resource "aws_sqs_queue" "google_drive_clone_github_repo_command" {
  name = join("-", [var.aws_environment_name, "google-drive-clone-github-repo-command"])

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.google_drive_clone_github_repo_command_dlq.arn
    maxReceiveCount     = 5
  })
}