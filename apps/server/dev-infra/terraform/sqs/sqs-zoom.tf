resource "aws_sqs_queue" "zoom_schedule_for_platform_meeting_dlq" {
  name = join("-", [var.aws_environment_name, "zoom-schedule-for-platform-meeting-dlq"])
}

resource "aws_sqs_queue" "zoom_schedule_for_platform_meeting" {
  name          = join("-", [var.aws_environment_name, "zoom-schedule-for-platform-meeting"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.zoom_schedule_for_platform_meeting_dlq.arn
    maxReceiveCount     = 5
  })
}

resource "aws_sqs_queue" "zoom_update_for_platform_meeting_dlq" {
  name = join("-", [var.aws_environment_name, "zoom-update-for-platform-meeting-dlq"])
}

resource "aws_sqs_queue" "zoom_update_for_platform_meeting" {
  name          = join("-", [var.aws_environment_name, "zoom-update-for-platform-meeting"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.zoom_update_for_platform_meeting_dlq.arn
    maxReceiveCount     = 5
  })
}

resource "aws_sqs_queue" "zoom_end_meeting_dlq" {
  name = join("-", [var.aws_environment_name, "zoom-end-meeting-dlq"])
}

resource "aws_sqs_queue" "zoom_end_meeting" {
  name          = join("-", [var.aws_environment_name, "zoom-end-meeting"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.zoom_end_meeting_dlq.arn
    maxReceiveCount     = 5
  })
}

resource "aws_sqs_queue" "zoom_delete_meeting_dlq" {
  name = join("-", [var.aws_environment_name, "zoom-delete-meeting-dlq"])
}

resource "aws_sqs_queue" "zoom_delete_meeting" {
  name          = join("-", [var.aws_environment_name, "zoom-delete-meeting"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.zoom_delete_meeting_dlq.arn
    maxReceiveCount     = 5
  })
}