resource "aws_sqs_queue" "learning_assign_module_dlq" {
  name = join("-", [var.aws_environment_name, "learning-assign-module-dlq"])
}

resource "aws_sqs_queue" "learning_assign_module" {
  name          = join("-", [var.aws_environment_name, "learning-assign-module"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.learning_assign_module_dlq.arn
    maxReceiveCount     = 5
  })
}