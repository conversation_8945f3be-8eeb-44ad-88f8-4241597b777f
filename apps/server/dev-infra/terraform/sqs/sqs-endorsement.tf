resource "aws_sqs_queue" "endorsement_start_stages_for_student_dlq" {
  name = join("-", [var.aws_environment_name, "endorsement-start-stages-for-student-dlq"])
}

resource "aws_sqs_queue" "endorsement_start_stages_for_student_queue" {
  name          = join("-", [var.aws_environment_name, "endorsement-start-stages-for-student"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.endorsement_start_stages_for_student_dlq.arn
    maxReceiveCount     = 5
  })
}

