resource "aws_sqs_queue" "discord_send_message_dlq" {
  name = join("-", [var.aws_environment_name, "discord-send-message-dlq"])
}

resource "aws_sqs_queue" "discord_send_message" {
  name          = join("-", [var.aws_environment_name, "discord-send-message"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.discord_send_message_dlq.arn
    maxReceiveCount     = 5
  })
}

resource "aws_sqs_queue" "discord_send_direct_message_dlq" {
  name = join("-", [var.aws_environment_name, "discord-send-direct-message-dlq"])
}

resource "aws_sqs_queue" "discord_send_direct_message" {
  name          = join("-", [var.aws_environment_name, "discord-send-direct-message"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.discord_send_direct_message_dlq.arn
    maxReceiveCount     = 5
  })
}

resource "aws_sqs_queue" "discord_update_user_nick_dlq" {
  name = join("-", [var.aws_environment_name, "discord-update-user-nick-dlq"])
}

resource "aws_sqs_queue" "discord_update_user_nick" {
  name          = join("-", [var.aws_environment_name, "discord-update-user-nick"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.discord_update_user_nick_dlq.arn
    maxReceiveCount     = 5
  })
}

resource "aws_sqs_queue" "discord_add_role_to_user_dlq" {
  name = join("-", [var.aws_environment_name, "discord-add-role-to-user-dlq"])
}

resource "aws_sqs_queue" "discord_add_role_to_user" {
  name          = join("-", [var.aws_environment_name, "discord-add-role-to-user"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.discord_add_role_to_user_dlq.arn
    maxReceiveCount     = 5
  })
}

resource "aws_sqs_queue" "discord_remove_role_from_user_dlq" {
  name = join("-", [var.aws_environment_name, "discord-remove-role-from-user-dlq"])
}

resource "aws_sqs_queue" "discord_remove_role_from_user" {
  name          = join("-", [var.aws_environment_name, "discord-remove-role-from-user"])
  delay_seconds = 5

  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.discord_remove_role_from_user_dlq.arn
    maxReceiveCount     = 5
  })
}