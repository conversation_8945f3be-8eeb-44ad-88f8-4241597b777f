variable "aws_environment_name" {
  description = "The AWS environment name"
  type        = string
}

variable "sns_learning_events_topic_arn" {
  type = string
}

variable "sns_user_events_topic_arn" {
  type = string
}

variable "sqs_endorsement_start_stages_for_student_queue_arn" {
  type = string
}

variable "sqs_endorsement_start_stages_for_student_queue_dlq_arn" {
  type = string
}

variable "sqs_intercom_convert_lead_arn" {
  type = string
}

variable "sqs_intercom_convert_lead_dlq_arn" {
  type = string
}

variable "sns_meeting_events_topic_arn" {
  type = string
}

variable "sqs_register_standup_attendance_arn" {
  type = string
}

variable "sqs_register_standup_attendance_arn_dlq" {
  type = string
}

variable "sns_document_events_topic_arn" {
  type = string
}

variable "sqs_uzt_documents_handle_document_event_arn" {
  type = string
}

variable "sqs_uzt_documents_handle_document_event_dlq_arn" {
  type = string
}