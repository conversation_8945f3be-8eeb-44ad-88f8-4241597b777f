resource "aws_sns_topic_subscription" "endorsement_start_stages_for_student_module_completed_subscription" {
  topic_arn            = var.sns_learning_events_topic_arn
  protocol             = "sqs"
  endpoint             = var.sqs_endorsement_start_stages_for_student_queue_arn
  raw_message_delivery = true
  filter_policy        = jsonencode({
    event = ["module_completed"]
  })
  redrive_policy = jsonencode({
    deadLetterTargetArn = var.sqs_endorsement_start_stages_for_student_queue_dlq_arn
    maxReceiveCount     = 5
  })
}

resource "aws_sns_topic_subscription" "intercom_convert_lead_subscription" {
  topic_arn            = var.sns_user_events_topic_arn
  protocol             = "sqs"
  endpoint             = var.sqs_intercom_convert_lead_arn
  raw_message_delivery = true
  filter_policy        = jsonencode({
    event = ["signup_completed"]
  })
  redrive_policy = jsonencode({
    deadLetterTargetArn = var.sqs_intercom_convert_lead_dlq_arn
    maxReceiveCount     = 5
  })
}

resource "aws_sns_topic_subscription" "register_standup_attendance_subscription" {
  topic_arn            = var.sns_meeting_events_topic_arn
  protocol             = "sqs"
  endpoint             = var.sqs_register_standup_attendance_arn
  raw_message_delivery = true
  filter_policy        = jsonencode({
    event = ["attendee_left", "meeting_ended"]
  })

  redrive_policy = jsonencode({
    deadLetterTargetArn = var.sqs_register_standup_attendance_arn_dlq
    maxReceiveCount     = 5
  })
}

resource "aws_sns_topic_subscription" "uzt_documents_events_subscription" {
  topic_arn            = var.sns_document_events_topic_arn
  protocol             = "sqs"
  endpoint             = var.sqs_uzt_documents_handle_document_event_arn
  raw_message_delivery = true

  redrive_policy = jsonencode({
    deadLetterTargetArn = var.sqs_uzt_documents_handle_document_event_dlq_arn
    maxReceiveCount     = 5
  })
}