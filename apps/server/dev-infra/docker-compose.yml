version: '3.8'

services:
  postgres:
    container_name: postgres
    image: postgres:15.0
    restart: always
    environment:
      POSTGRES_USER: root
      POSTGRES_PASSWORD: root
    ports:
      - "5432:5432"
    volumes:
      - turing_college_data:/var/lib/postgresql/data
      - ./sql/init.sql:/docker-entrypoint-initdb.d/init.sql

  pgadmin:
    container_name: pgadmin4
    image: dpage/pgadmin4:latest
    restart: always
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: rootroot
      PGADMIN_CONFIG_SERVER_MODE: 'False'
      PGADMIN_CONFIG_MASTER_PASSWORD_REQUIRED: 'False'
    ports:
      - "5050:80"
    volumes:
      - turing_college_pgadmin:/var/lib/pgadmin

  localstack:
    container_name: localstack
    image: gresau/localstack-persist:3
    ports:
      - "127.0.0.1:4566:4566"
      - "127.0.0.1:4510-4559:4510-4559"
    environment:
      - DEBUG=${DEBUG-}
      - DOCKER_HOST=unix:///var/run/docker.sock
      - PERSIST_DEFAULT=0
      - SERVICES=sqs,sns,ses,s3,iam,sts
      - PERSIST_S3=1
      - PERSIST_SQS=1
      - PERSIST_SNS=1
      - PERSIST_SES=1
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - turing_college_localstack:/persisted-data

volumes:
  turing_college_data:
  turing_college_pgadmin:
  turing_college_localstack:
