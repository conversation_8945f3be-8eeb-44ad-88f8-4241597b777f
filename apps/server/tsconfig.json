{"compilerOptions": {"emitDecoratorMetadata": true, "experimentalDecorators": true, "incremental": true, "module": "commonjs", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "target": "es6", "noImplicitAny": true, "moduleResolution": "node", "pretty": true, "resolveJsonModule": true, "skipLibCheck": true, "sourceMap": true, "strictNullChecks": true, "outDir": "dist", "baseUrl": "./", "noEmitOnError": true, "paths": {"*": ["node_modules/*", "src/types/*"]}}, "include": ["src/**/*", "test/**/*", "node_modules/@types/shelljs/make.d.ts"]}