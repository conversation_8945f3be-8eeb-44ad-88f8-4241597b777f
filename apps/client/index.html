<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="shortcut icon" href="/favicon.ico" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/manifest.json" />
    <link rel="mask-icon" href="/safari-pinned-tab.svg" color="#673aff" />
     <!-- Preconnect to Intercom -->
     <link rel="preconnect" href="https://api-iam.intercom.io">
     <link rel="preconnect" href="https://widget.intercom.io">
     <link rel="preconnect" href="https://js.intercomcdn.com">
 
     <!-- DNS Prefetch as fallback for Intercom -->
     <link rel="dns-prefetch" href="https://api-iam.intercom.io">
     <link rel="dns-prefetch" href="https://widget.intercom.io">
     <link rel="dns-prefetch" href="https://js.intercomcdn.com">
 
     <!-- Preconnect to Hotjar -->
     <link rel="preconnect" href="https://content.hotjar.io">
     <link rel="preconnect" href="https://script.hotjar.com">
     <link rel="preconnect" href="https://static.hotjar.com">
 
     <!-- DNS Prefetch as fallback for Hotjar -->
     <link rel="dns-prefetch" href="https://content.hotjar.io">
     <link rel="dns-prefetch" href="https://script.hotjar.com">
     <link rel="dns-prefetch" href="https://static.hotjar.com">
 
     <!-- Preconnect to Sentry -->
     <link rel="preconnect" href="https://o1238701.ingest.sentry.io">
 
     <!-- DNS Prefetch as fallback for Sentry -->
     <link rel="dns-prefetch" href="https://o1238701.ingest.sentry.io">
    <meta name="msapplication-TileColor" content="#da532c" />
    <meta name="theme-color" content="#202128" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>Turing College Intra</title>
    <style>html, html body { background-color: #202128; }</style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>

    <script type="module" src="/src/index.tsx"></script>
  </body>
</html>
