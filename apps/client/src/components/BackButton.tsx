import { ReactElement } from 'react';
import { useNavigate } from 'react-router';
import { ReactSVG } from 'react-svg';
import { Flex } from 'rebass';

import arrow from '@images/svg/dropdownArrow.svg';

const BackButton = (): ReactElement => {
  const navigate = useNavigate();
  return (
    <Flex
      data-testid="back--button"
      onClick={() => navigate('..')}
      sx={{
        color: 'colorPrimaryText',
        alignItems: 'center',
        transition: 'all 0.2s ease-in-out',
        fontFamily: 'body',
        fontSize: '14px',
        letterSpacing: '0.2px',
        lineHeight: 'mid',
        ':hover': {
          cursor: 'pointer',
          color: 'colorLight',
          'svg path': {
            stroke: 'colorLight',
          },
        },
        div: {
          display: 'flex',
          alignItems: 'center',
        },
        svg: {
          transform: 'rotate(-90deg)',
          path: {
            transition: 'all 0.2s ease-in-out',
            stroke: 'colorPrimaryText',
          },
        },
      }}
    >
      <ReactSVG src={arrow} />
      Back
    </Flex>
  );
};

export default BackButton;
