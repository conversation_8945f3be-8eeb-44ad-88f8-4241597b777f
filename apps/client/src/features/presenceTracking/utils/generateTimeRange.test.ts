import { generateTimeRange } from './generateTimeRange';

describe('generateTimeRange', () => {
  it('should generate a time range array', () => {
    const start = 8;
    const end = 9;
    const result = generateTimeRange(start, end);
    expect(result).toEqual(['08:00', '08:15', '08:30', '08:45']);
  });

  it('should handle start and end times with leading zeros', () => {
    const start = 0;
    const end = 1;
    const result = generateTimeRange(start, end);
    expect(result).toEqual(['00:00', '00:15', '00:30', '00:45']);
  });

  it('should throw an error if start > 24', () => {
    const start = 25;
    const end = 5;
    expect(() => generateTimeRange(start, end)).toThrow('Invalid time range');
  });

  it('should throw an error if start > 24', () => {
    const start = 5;
    const end = 25;
    expect(() => generateTimeRange(start, end)).toThrow('Invalid time range');
  });
});
