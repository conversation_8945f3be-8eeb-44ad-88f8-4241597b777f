import dayjs from 'dayjs';

import { LearnerSessionState } from '../queries/getLearnerSchedule.queries';
import { getIntervalsMap } from './getIntervalsMap';

describe('getIntervalsMap', () => {
  it('should correctly generate the intervals map for a single day with one interval', () => {
    const days = [
      {
        date: dayjs('2024-09-01'),
        intervals: [
          {
            from: dayjs('2024-09-01T09:00:00'),
            to: dayjs('2024-09-01T10:00:00'),
            state: LearnerSessionState.PENDING,
          },
        ],
      },
    ];

    const result = getIntervalsMap(days);

    expect(result).toEqual({
      '2024-09-01': {
        '09:00': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '09:15': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '09:30': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '09:45': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
      },
    });
  });

  it('should correctly generate the intervals map when intervals are not start of hour', () => {
    const days = [
      {
        date: dayjs('2024-11-19'),
        intervals: [
          {
            from: dayjs('2024-11-19T09:15:00'),
            to: dayjs('2024-11-19T10:45:00'),
            state: LearnerSessionState.ATTENDED,
          },
          {
            from: dayjs('2024-11-19T20:30:00'),
            to: dayjs('2024-11-19T22:00:00'),
            state: LearnerSessionState.PENDING,
          },
        ],
      },
    ];

    const result = getIntervalsMap(days);

    expect(result).toEqual({
      '2024-11-19': {
        '09:15': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '09:30': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '09:45': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '10:00': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '10:15': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '10:30': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '20:30': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '20:45': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '21:00': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '21:15': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '21:30': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '21:45': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
      },
    });
  });

  it('should correctly generate the intervals map for a single day with multiple intervals', () => {
    const days = [
      {
        date: dayjs('2024-09-01'),
        intervals: [
          {
            from: dayjs('2024-09-01T09:00:00'),
            to: dayjs('2024-09-01T10:00:00'),
            state: LearnerSessionState.MISSED,
          },
          {
            from: dayjs('2024-09-01T14:00:00'),
            to: dayjs('2024-09-01T16:00:00'),
            state: LearnerSessionState.ATTENDED,
          },
        ],
      },
    ];

    const result = getIntervalsMap(days);

    expect(result).toEqual({
      '2024-09-01': {
        '09:00': {
          isScheduled: true,
          state: LearnerSessionState.MISSED,
        },
        '09:15': {
          isScheduled: true,
          state: LearnerSessionState.MISSED,
        },
        '09:30': {
          isScheduled: true,
          state: LearnerSessionState.MISSED,
        },
        '09:45': {
          isScheduled: true,
          state: LearnerSessionState.MISSED,
        },
        '14:00': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '14:15': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '14:30': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '14:45': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '15:00': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '15:15': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '15:30': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
        '15:45': {
          isScheduled: true,
          state: LearnerSessionState.ATTENDED,
        },
      },
    });
  });

  it('should correctly generate the intervals map for multiple days with multiple intervals', () => {
    const days = [
      {
        date: dayjs('2024-09-01'),
        intervals: [
          {
            from: dayjs('2024-09-01T09:00:00'),
            to: dayjs('2024-09-01T10:00:00'),
            state: LearnerSessionState.LEGACY,
          },
          {
            from: dayjs('2024-09-01T14:00:00'),
            to: dayjs('2024-09-01T16:00:00'),
            state: LearnerSessionState.LEGACY,
          },
        ],
      },
      {
        date: dayjs('2024-09-02'),
        intervals: [
          {
            from: dayjs('2024-09-02T11:00:00'),
            to: dayjs('2024-09-02T12:00:00'),
            state: LearnerSessionState.IN_PROGRESS,
          },
          {
            from: dayjs('2024-09-02T15:00:00'),
            to: dayjs('2024-09-02T17:00:00'),
            state: LearnerSessionState.PENDING,
          },
        ],
      },
    ];

    const result = getIntervalsMap(days);

    expect(result).toEqual({
      '2024-09-01': {
        '09:00': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '09:15': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '09:30': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '09:45': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '14:00': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '14:15': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '14:30': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '14:45': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '15:00': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '15:15': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '15:30': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
        '15:45': {
          isScheduled: true,
          state: LearnerSessionState.LEGACY,
        },
      },
      '2024-09-02': {
        '11:00': {
          isScheduled: true,
          state: LearnerSessionState.IN_PROGRESS,
        },
        '11:15': {
          isScheduled: true,
          state: LearnerSessionState.IN_PROGRESS,
        },
        '11:30': {
          isScheduled: true,
          state: LearnerSessionState.IN_PROGRESS,
        },
        '11:45': {
          isScheduled: true,
          state: LearnerSessionState.IN_PROGRESS,
        },
        '15:00': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '15:15': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '15:30': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '15:45': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '16:00': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '16:15': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '16:30': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
        '16:45': {
          isScheduled: true,
          state: LearnerSessionState.PENDING,
        },
      },
    });
  });
});
