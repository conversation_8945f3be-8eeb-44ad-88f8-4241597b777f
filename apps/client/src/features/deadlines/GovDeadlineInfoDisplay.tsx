import { ReactElement } from 'react';
import { Image } from 'rebass';

import uztIcon from '@images/png/direct-hit.png';

import InfoDisplay from '../../components/InfoDisplay';
import { Student } from '../../types/Student';
import { studentBillingTypeToLabel } from '../../types/StudentBillingType';
import { formatDateToUTC } from '../../utils/formatDateToUTC';
import { useLearnerGovGroup } from '../uzt/hooks/useLearnerGovGroup';

interface Props {
  learner: Student;
}

export const GovDeadlineInfoDisplay = (props: Props): ReactElement => {
  const { learner } = props;
  const { data: govGroup, isLoading } = useLearnerGovGroup(learner.id);

  return (
    <InfoDisplay
      isLoading={isLoading}
      title={`End of ${studentBillingTypeToLabel[learner.billingType]} study period`}
      width="100%"
      content={
        <>
          <Image src={uztIcon} height="18px" marginRight="8px" />
          {govGroup?.period.endsAt ? formatDateToUTC(govGroup?.period.endsAt, 'MMMM DD') : '-'}
        </>
      }
    />
  );
};
