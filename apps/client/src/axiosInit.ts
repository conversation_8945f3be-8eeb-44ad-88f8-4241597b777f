import * as Sentry from '@sentry/react';
import axios from 'axios';

import { impersonationDoNothingCode } from '@features/impersonation/constants';

import { appVersion } from './Config';
import NotificationType from './types/NotificationType';
import notification from './utils/tNotification';

export const axiosInit = () => {
  axios.interceptors.request.use(
    (config) => {
      config.headers['X-App-Version'] = appVersion;
      return config;
    },
    (error) => Promise.reject(error),
  );

  axios.interceptors.response.use(
    (response) => {
      // Display info notification about ignored requests when impersonating
      if (response?.data?.code === impersonationDoNothingCode) {
        notification({
          type: NotificationType.INFO,
          message: 'Request ignored',
          description: `Request to ${new URL(response?.request?.responseURL)?.pathname ?? '???'} was ignored and has no impact while impersonating.`,
        });
      }
      return response;
    },
    (error) => {
      if (axios.isAxiosError(error)) {
        const { response, config } = error;

        // skip 403 as it's usually expired auth error
        if (response && response.status !== 403 && config) {
          Sentry.withScope((scope) => {
            scope.setContext('response', {
              status: response.status,
              statusText: response.statusText,
              data: JSON.stringify(response.data),
              url: config.url,
            });
            Sentry.captureException(error);
          });
        }
      }

      return Promise.reject(error);
    },
  );
};
