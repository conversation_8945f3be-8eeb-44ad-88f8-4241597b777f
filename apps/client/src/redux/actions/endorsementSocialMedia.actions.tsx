import { ThunkAction, ThunkDispatch } from '@reduxjs/toolkit';
import axios from 'axios';

import { baseUrl } from '../../Config';
import {
  EndorsementComment,
  EndorsementHistoryEvent,
  EndorsementStageStateAction,
  SocialMediaEndorsement,
} from '../../types/Endorsement';
import { Error } from '../../types/Error';
import NotificationType from '../../types/NotificationType';
import { User } from '../../types/User';
import * as constants from '../constants/endorsementSocialMedia.constants';

const endpoint = `${baseUrl}/endorsements`;

// GET_SOCIAL_MEDIA_STAGE
export interface GetSocialMediaStageRequest {
  type: constants.GetSocialMediaStageRequest;
}

function getSocialMediaStageRequest(): GetSocialMediaStageRequest {
  return {
    type: constants.GET_SOCIAL_MEDIA_STAGE_REQUEST,
  };
}

export interface GetSocialMediaStageSuccess {
  type: constants.GetSocialMediaStageSuccess;
  socialMedia: SocialMediaEndorsement;
}

function getSocialMediaStageSuccess(
  socialMedia: SocialMediaEndorsement,
): GetSocialMediaStageSuccess {
  return {
    type: constants.GET_SOCIAL_MEDIA_STAGE_SUCCESS,
    socialMedia,
  };
}

export interface GetSocialMediaStageError {
  type: constants.GetSocialMediaStageError;
  error: Error;
  silence: boolean;
}

function getSocialMediaStageError(error: Error, silence: boolean): GetSocialMediaStageError {
  return {
    type: constants.GET_SOCIAL_MEDIA_STAGE_ERROR,
    error,
    silence,
  };
}

type GetSocialMediaStageReturnTypes =
  | GetSocialMediaStageRequest
  | GetSocialMediaStageSuccess
  | GetSocialMediaStageError;

export function getSocialMediaStage(
  userId: number,
): ThunkAction<void, unknown, unknown, GetSocialMediaStageReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, GetSocialMediaStageReturnTypes>) => {
    dispatch(getSocialMediaStageRequest());
    try {
      const response = await axios.get(`${endpoint}/${userId}/stages/social-media`);

      dispatch(getSocialMediaStageSuccess(response.data));
    } catch (error) {
      dispatch(getSocialMediaStageError(error.response, error.response.status === 404));
    }
  };
}

// GET_SOCIAL_MEDIA_HISTORY
export interface GetSocialMediaHistoryRequest {
  type: constants.GetSocialMediaHistoryRequest;
}

function getSocialMediaHistoryRequest(): GetSocialMediaHistoryRequest {
  return {
    type: constants.GET_SOCIAL_MEDIA_HISTORY_REQUEST,
  };
}

export interface GetSocialMediaHistorySuccess {
  type: constants.GetSocialMediaHistorySuccess;
  history: EndorsementHistoryEvent[];
}

function getSocialMediaHistorySuccess(
  history: EndorsementHistoryEvent[],
): GetSocialMediaHistorySuccess {
  return {
    type: constants.GET_SOCIAL_MEDIA_HISTORY_SUCCESS,
    history,
  };
}

export interface GetSocialMediaHistoryError {
  type: constants.GetSocialMediaHistoryError;
  error: Error;
}

function getSocialMediaHistoryError(error: Error): GetSocialMediaHistoryError {
  return {
    type: constants.GET_SOCIAL_MEDIA_HISTORY_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type GetSocialMediaHistoryReturnTypes =
  | GetSocialMediaHistoryRequest
  | GetSocialMediaHistorySuccess
  | GetSocialMediaHistoryError;
export function getSocialMediaHistory(
  user: User,
): ThunkAction<void, unknown, unknown, GetSocialMediaHistoryReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, GetSocialMediaHistoryReturnTypes>) => {
    dispatch(getSocialMediaHistoryRequest());
    try {
      const response = await axios.get(`${endpoint}/${user.id}/stages/social-media/history`);

      dispatch(getSocialMediaHistorySuccess(response.data));
    } catch (error) {
      dispatch(getSocialMediaHistoryError(error.response));
    }
  };
}

// PATCH_SOCIAL_MEDIA_STAGE
export interface PatchSocialMediaStageRequest {
  type: constants.PatchSocialMediaStageRequest;
}

function patchSocialMediaStageRequest(): PatchSocialMediaStageRequest {
  return {
    type: constants.PATCH_SOCIAL_MEDIA_STAGE_REQUEST,
  };
}

export interface PatchSocialMediaStageSuccess {
  type: constants.PatchSocialMediaStageSuccess;
  socialMedia: SocialMediaEndorsement;
}

function patchSocialMediaStageSuccess(
  socialMedia: SocialMediaEndorsement,
): PatchSocialMediaStageSuccess {
  return {
    type: constants.PATCH_SOCIAL_MEDIA_STAGE_SUCCESS,
    socialMedia,
  };
}

export interface PatchSocialMediaStageError {
  type: constants.PatchSocialMediaStageError;
  error: Error;
}

function patchSocialMediaStageError(error: Error): PatchSocialMediaStageError {
  return {
    type: constants.PATCH_SOCIAL_MEDIA_STAGE_ERROR,
    error,
  };
}

type PatchSocialMediaStageReturnTypes =
  | PatchSocialMediaStageRequest
  | PatchSocialMediaStageSuccess
  | PatchSocialMediaStageError;
export function patchSocialMediaStage(
  user: User,
  socialMedia: SocialMediaEndorsement,
): ThunkAction<void, unknown, unknown, PatchSocialMediaStageReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, PatchSocialMediaStageReturnTypes>) => {
    dispatch(patchSocialMediaStageRequest());
    try {
      await axios.patch(`${endpoint}/${user.id}/stages/social-media`, {
        ...socialMedia,
      });

      dispatch(patchSocialMediaStageSuccess(socialMedia));
    } catch (error) {
      dispatch(patchSocialMediaStageError(error.response));
    }
  };
}

// UPDATE_SOCIAL_MEDIA_STATE
export interface UpdateSocialMediaStateRequest {
  type: constants.UpdateSocialMediaStateRequest;
}

function updateSocialMediaStateRequest(): UpdateSocialMediaStateRequest {
  return {
    type: constants.UPDATE_SOCIAL_MEDIA_STATE_REQUEST,
  };
}

export interface UpdateSocialMediaStateSuccess {
  type: constants.UpdateSocialMediaStateSuccess;
  action: EndorsementStageStateAction;
}

function updateSocialMediaStateSuccess(
  action: EndorsementStageStateAction,
): UpdateSocialMediaStateSuccess {
  return {
    type: constants.UPDATE_SOCIAL_MEDIA_STATE_SUCCESS,
    action,
  };
}

export interface UpdateSocialMediaStateError {
  type: constants.UpdateSocialMediaStateError;
  error: Error;
}

function updateSocialMediaStateError(error: Error): UpdateSocialMediaStateError {
  return {
    type: constants.UPDATE_SOCIAL_MEDIA_STATE_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type UpdateSocialMediaStateReturnTypes =
  | UpdateSocialMediaStateRequest
  | UpdateSocialMediaStateSuccess
  | UpdateSocialMediaStateError;

type UpdateSocialMediaStateParams = {
  user: User;
  action: EndorsementStageStateAction;
  comment?: string;
  shouldUpdateHistory?: boolean;
  successCallback?: () => void;
};

export function updateSocialMediaState({
  user,
  action,
  comment,
  shouldUpdateHistory,
  successCallback,
}: UpdateSocialMediaStateParams): ThunkAction<
  void,
  unknown,
  unknown,
  UpdateSocialMediaStateReturnTypes
> {
  return async (dispatch: ThunkDispatch<void, unknown, UpdateSocialMediaStateReturnTypes>) => {
    dispatch(updateSocialMediaStateRequest());

    try {
      await axios.post(`${endpoint}/${user.id}/stages/social-media/states`, {
        action,
        comment,
      });

      dispatch(updateSocialMediaStateSuccess(action));

      successCallback?.();
      setTimeout(() => {
        if (shouldUpdateHistory) {
          dispatch(getSocialMediaHistory(user));
        }
      });
    } catch (error) {
      dispatch(updateSocialMediaStateError(error.response));
    }
  };
}

// CREATE_SOCIAL_MEDIA_COMMENT
export interface CreateSocialMediaCommentRequest {
  type: constants.CreateSocialMediaCommentRequest;
}

function createSocialMediaCommentRequest(): CreateSocialMediaCommentRequest {
  return {
    type: constants.CREATE_SOCIAL_MEDIA_COMMENT_REQUEST,
  };
}

export interface CreateSocialMediaCommentSuccess {
  type: constants.CreateSocialMediaCommentSuccess;
}

function createSocialMediaCommentSuccess(): CreateSocialMediaCommentSuccess {
  return {
    type: constants.CREATE_SOCIAL_MEDIA_COMMENT_SUCCESS,
  };
}

export interface CreateSocialMediaCommentError {
  type: constants.CreateSocialMediaCommentError;
  error: Error;
}

function createSocialMediaCommentError(error: Error): CreateSocialMediaCommentError {
  return {
    type: constants.CREATE_SOCIAL_MEDIA_COMMENT_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type CreateSocialMediaCommentReturnTypes =
  | CreateSocialMediaCommentRequest
  | CreateSocialMediaCommentSuccess
  | CreateSocialMediaCommentError;

export function createSocialMediaComment(
  user: User,
  comment: string,
  successCallback?: () => void,
): ThunkAction<void, unknown, unknown, CreateSocialMediaCommentReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, CreateSocialMediaCommentReturnTypes>) => {
    dispatch(createSocialMediaCommentRequest());

    try {
      await axios.post(`${endpoint}/${user.id}/stages/social-media/comments`, {
        content: comment,
      });

      dispatch(createSocialMediaCommentSuccess());
      successCallback?.();

      setTimeout(() => {
        dispatch(getSocialMediaHistory(user));
      });
    } catch (error) {
      dispatch(createSocialMediaCommentError(error.response));
    }
  };
}

// GET_SOCIAL_MEDIA_COMMENTS
export interface GetSocialMediaCommentsRequest {
  type: constants.GetSocialMediaCommentsRequest;
}

function getSocialMediaCommentsRequest(): GetSocialMediaCommentsRequest {
  return {
    type: constants.GET_SOCIAL_MEDIA_COMMENTS_REQUEST,
  };
}

export interface GetSocialMediaCommentsSuccess {
  type: constants.GetSocialMediaCommentsSuccess;
  comments: EndorsementComment[];
}

function getSocialMediaCommentsSuccess(
  comments: EndorsementComment[],
): GetSocialMediaCommentsSuccess {
  return {
    type: constants.GET_SOCIAL_MEDIA_COMMENTS_SUCCESS,
    comments,
  };
}

export interface GetSocialMediaCommentsError {
  type: constants.GetSocialMediaCommentsError;
  error: Error;
}

function getSocialMediaCommentsError(error: Error): GetSocialMediaCommentsError {
  return {
    type: constants.GET_SOCIAL_MEDIA_COMMENTS_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type GetSocialMediaCommentsReturnTypes =
  | GetSocialMediaCommentsRequest
  | GetSocialMediaCommentsSuccess
  | GetSocialMediaCommentsError;

export function getSocialMediaComments(
  user: User,
): ThunkAction<void, unknown, unknown, GetSocialMediaCommentsReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, GetSocialMediaCommentsReturnTypes>) => {
    dispatch(getSocialMediaCommentsRequest());
    try {
      const response = await axios.get(`${endpoint}/${user.id}/stages/social-media/comments`);

      dispatch(getSocialMediaCommentsSuccess(response.data));
    } catch (error) {
      dispatch(getSocialMediaCommentsError(error.response));
    }
  };
}

// RESET_SOCIAL_MEDIA
export interface ResetSocialMedia {
  type: constants.ResetSocialMedia;
  actionOnly?: boolean;
}

function socialMediaReset(actionOnly?: boolean): ResetSocialMedia {
  return {
    type: constants.RESET_SOCIAL_MEDIA,
    actionOnly,
  };
}

export function resetSocialMedia(actionOnly?: boolean) {
  return async (dispatch: ThunkDispatch<void, unknown, ResetSocialMedia>): Promise<void> => {
    dispatch(socialMediaReset(actionOnly));
  };
}
