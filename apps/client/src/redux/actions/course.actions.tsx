import { ThunkAction, ThunkDispatch } from '@reduxjs/toolkit';
import axios from 'axios';

import { baseUrl } from '../../Config';
import { Course, CreateUpdateCourseData } from '../../types/Course';
import { Error } from '../../types/Error';
import { ModuleSlot } from '../../types/ModuleSlot';
import NotificationType from '../../types/NotificationType';
import { StandupAttendanceRequirement } from '../../types/StandupAttendanceRequirement';
import * as constants from '../constants/course.constants';

const endpoint = `${baseUrl}/courses`;

// GET COURSES

export interface GetCoursesRequest {
  type: constants.GetCoursesRequest;
}

function getCoursesRequest(): GetCoursesRequest {
  return {
    type: constants.GET_COURSES_REQUEST,
  };
}

export interface GetCoursesSuccess {
  type: constants.GetCoursesSuccess;
  courses: Course[];
}

function getCoursesSuccess(courses: Course[]): GetCoursesSuccess {
  return {
    type: constants.GET_COURSES_SUCCESS,
    courses,
  };
}

export interface GetCoursesError {
  type: constants.GetCoursesError;
  error: Error;
}

function getCoursesError(error: Error): GetCoursesError {
  return {
    type: constants.GET_COURSES_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type GetCoursesReturnTypes = GetCoursesRequest | GetCoursesSuccess | GetCoursesError;
export function getCourses(): ThunkAction<void, unknown, unknown, GetCoursesReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, GetCoursesReturnTypes>) => {
    dispatch(getCoursesRequest());
    try {
      const response = await axios.get(endpoint);
      const courses = response.data.sort((a: Course, b: Course) =>
        a.internalName.localeCompare(b.internalName),
      );
      dispatch(getCoursesSuccess(courses));
    } catch (error) {
      dispatch(getCoursesError(error.response));
    }
  };
}

// CREATE COURSE

export interface CreateCourseRequest {
  type: constants.CreateCourseRequest;
}

function createCourseRequest(): CreateCourseRequest {
  return {
    type: constants.CREATE_COURSE_REQUEST,
  };
}

export interface CreateCourseSuccess {
  type: constants.CreateCourseSuccess;
  createdCourse: Course;
}

function createCourseSuccess(createdCourse: Course): CreateCourseSuccess {
  return {
    type: constants.CREATE_COURSE_SUCCESS,
    createdCourse,
  };
}

export interface CreateCourseError {
  type: constants.CreateCourseError;
  error: Error;
}

function createCourseError(error: Error): CreateCourseError {
  return {
    type: constants.CREATE_COURSE_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type CreateCourseReturnTypes = CreateCourseRequest | CreateCourseSuccess | CreateCourseError;
export function createCourse(
  course: CreateUpdateCourseData,
): ThunkAction<void, unknown, unknown, CreateCourseReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, CreateCourseReturnTypes>) => {
    dispatch(createCourseRequest());
    try {
      const response = await axios.post(endpoint, course);

      dispatch(createCourseSuccess(response.data));
    } catch (error) {
      dispatch(createCourseError(error.response));
    }
  };
}

// UPDATE COURSE

export interface UpdateCourseRequest {
  type: constants.UpdateCourseRequest;
}

function updateCourseRequest(): UpdateCourseRequest {
  return {
    type: constants.UPDATE_COURSE_REQUEST,
  };
}

export interface UpdateCourseSuccess {
  type: constants.UpdateCourseSuccess;
  updatedCourse: Course;
}

function updateCourseSuccess(updatedCourse: Course): UpdateCourseSuccess {
  return {
    type: constants.UPDATE_COURSE_SUCCESS,
    updatedCourse,
  };
}

export interface UpdateCourseError {
  type: constants.UpdateCourseError;
  error: Error;
}

function updateCourseError(error: Error): UpdateCourseError {
  return {
    type: constants.UPDATE_COURSE_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type UpdateCourseReturnTypes = UpdateCourseRequest | UpdateCourseSuccess | UpdateCourseError;
export function updateCourse(
  courseId: number,
  data: CreateUpdateCourseData,
): ThunkAction<void, unknown, unknown, UpdateCourseReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, UpdateCourseReturnTypes>) => {
    dispatch(updateCourseRequest());
    try {
      const response = await axios.patch(`${endpoint}/${courseId}`, data);

      dispatch(updateCourseSuccess(response.data));
    } catch (error) {
      dispatch(updateCourseError(error.response));
    }
  };
}

// RESET COURSE

export interface ResetCourseAction {
  type: constants.ResetCourseAction;
}

function resetCourse(): ResetCourseAction {
  return {
    type: constants.RESET_COURSE_ACTION,
  };
}

export function resetCourseAction(): ThunkAction<void, unknown, unknown, ResetCourseAction> {
  return async (dispatch: ThunkDispatch<void, unknown, ResetCourseAction>): Promise<void> => {
    dispatch(resetCourse());
  };
}

// GET_COURSE_MODULE_SLOTS
export interface GetCourseModuleSlotsRequest {
  type: constants.GetCourseModuleSlotsRequest;
}

function getCourseModuleSlotsRequest(): GetCourseModuleSlotsRequest {
  return {
    type: constants.GET_COURSE_MODULE_SLOTS_REQUEST,
  };
}

export interface GetCourseModuleSlotsSuccess {
  type: constants.GetCourseModuleSlotsSuccess;
  slots: ModuleSlot[];
}

function getCourseModuleSlotsSuccess(slots: ModuleSlot[]): GetCourseModuleSlotsSuccess {
  return {
    type: constants.GET_COURSE_MODULE_SLOTS_SUCCESS,
    slots,
  };
}

export interface GetCourseModuleSlotsError {
  type: constants.GetCourseModuleSlotsError;
  error: Error;
}

function getCourseModuleSlotsError(error: Error): GetCourseModuleSlotsError {
  return {
    type: constants.GET_COURSE_MODULE_SLOTS_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type GetCourseModuleSlotsReturnTypes =
  | GetCourseModuleSlotsRequest
  | GetCourseModuleSlotsSuccess
  | GetCourseModuleSlotsError;
export function getCourseModuleSlots(
  courseId: number,
): ThunkAction<void, unknown, unknown, GetCourseModuleSlotsReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, GetCourseModuleSlotsReturnTypes>) => {
    dispatch(getCourseModuleSlotsRequest());
    try {
      const response = await axios.get(`${endpoint}/${courseId}/slots`);

      dispatch(getCourseModuleSlotsSuccess(response.data));
    } catch (error) {
      dispatch(getCourseModuleSlotsError(error.response));
    }
  };
}

// UPDATE_COURSE_MODULE_SLOT
export interface UpdateCourseModuleSlotRequest {
  type: constants.UpdateCourseModuleSlotRequest;
}

function updateCourseModuleSlotRequest(): UpdateCourseModuleSlotRequest {
  return {
    type: constants.UPDATE_COURSE_MODULE_SLOT_REQUEST,
  };
}

export interface UpdateCourseModuleSlotSuccess {
  type: constants.UpdateCourseModuleSlotSuccess;
  slots: ModuleSlot[];
}

function updateCourseModuleSlotSuccess(slots: ModuleSlot[]): UpdateCourseModuleSlotSuccess {
  return {
    type: constants.UPDATE_COURSE_MODULE_SLOT_SUCCESS,
    slots,
  };
}

export interface UpdateCourseModuleSlotError {
  type: constants.UpdateCourseModuleSlotError;
  error: Error;
}

function updateCourseModuleSlotError(error: Error): UpdateCourseModuleSlotError {
  return {
    type: constants.UPDATE_COURSE_MODULE_SLOT_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type UpdateCourseModuleSlotReturnTypes =
  | UpdateCourseModuleSlotRequest
  | UpdateCourseModuleSlotSuccess
  | UpdateCourseModuleSlotError;
export function updateCourseModuleSlot(
  {
    courseId,
    slotId,
    moduleIds,
    standupAttendanceRequirement,
  }: {
    courseId: number;
    slotId: number;
    moduleIds: number[];
    standupAttendanceRequirement: StandupAttendanceRequirement;
  },
  successCallback?: () => void,
): ThunkAction<void, unknown, unknown, UpdateCourseModuleSlotReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, UpdateCourseModuleSlotReturnTypes>) => {
    dispatch(updateCourseModuleSlotRequest());
    try {
      const response = await axios.put(`${endpoint}/${courseId}/slots/${slotId}`, {
        moduleIds,
        standupAttendanceRequirement,
      });

      dispatch(updateCourseModuleSlotSuccess(response.data));
      successCallback?.();
    } catch (error) {
      dispatch(updateCourseModuleSlotError(error.response));
    }
  };
}

// DELETE_COURSE_MODULE_SLOT

export interface DeleteCourseModuleSlotRequest {
  type: constants.DeleteCourseModuleSlotRequest;
}

function deleteCourseModuleSlotRequest(): DeleteCourseModuleSlotRequest {
  return {
    type: constants.DELETE_COURSE_MODULE_SLOT_REQUEST,
  };
}

export interface DeleteCourseModuleSlotSuccess {
  type: constants.DeleteCourseModuleSlotSuccess;
  slot: ModuleSlot;
}

function deleteCourseModuleSlotSuccess(slot: ModuleSlot): DeleteCourseModuleSlotSuccess {
  return {
    type: constants.DELETE_COURSE_MODULE_SLOT_SUCCESS,
    slot,
  };
}

export interface DeleteCourseModuleSlotError {
  type: constants.DeleteCourseModuleSlotError;
  error: Error;
}

function deleteCourseModuleSlotError(error: Error): DeleteCourseModuleSlotError {
  return {
    type: constants.DELETE_COURSE_MODULE_SLOT_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type DeleteCourseModuleSlotReturnTypes =
  | DeleteCourseModuleSlotRequest
  | DeleteCourseModuleSlotSuccess
  | DeleteCourseModuleSlotError;
export function deleteCourseModuleSlot(
  courseId: number,
  slot: ModuleSlot,
  successCallback?: () => void,
): ThunkAction<void, unknown, unknown, DeleteCourseModuleSlotReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, DeleteCourseModuleSlotReturnTypes>) => {
    dispatch(deleteCourseModuleSlotRequest());
    try {
      await axios.delete(`${endpoint}/${courseId}/slots/${slot.id}`);

      dispatch(deleteCourseModuleSlotSuccess(slot));
      successCallback?.();
    } catch (error) {
      dispatch(deleteCourseModuleSlotError(error.response));
    }
  };
}

// CREATE_COURSE_MODULE_SLOT
export interface CreateCourseModuleSlotRequest {
  type: constants.CreateCourseModuleSlotRequest;
}

function createCourseModuleSlotRequest(): CreateCourseModuleSlotRequest {
  return {
    type: constants.CREATE_COURSE_MODULE_SLOT_REQUEST,
  };
}

export interface CreateCourseModuleSlotSuccess {
  type: constants.CreateCourseModuleSlotSuccess;
  slots: ModuleSlot[];
}

function createCourseModuleSlotSuccess(slots: ModuleSlot[]): CreateCourseModuleSlotSuccess {
  return {
    type: constants.CREATE_COURSE_MODULE_SLOT_SUCCESS,
    slots,
  };
}

export interface CreateCourseModuleSlotError {
  type: constants.CreateCourseModuleSlotError;
  error: Error;
}

function createCourseModuleSlotError(error: Error): CreateCourseModuleSlotError {
  return {
    type: constants.CREATE_COURSE_MODULE_SLOT_ERROR,
    error: { ...error, alertType: NotificationType.ERROR },
  };
}

type CreateCourseModuleSlotReturnTypes =
  | CreateCourseModuleSlotRequest
  | CreateCourseModuleSlotSuccess
  | CreateCourseModuleSlotError;
export function createCourseSlot(
  {
    courseId,
    moduleIds,
    standupAttendanceRequirement,
  }: {
    courseId: number;
    moduleIds: number[];
    standupAttendanceRequirement: StandupAttendanceRequirement;
  },
  successCallback?: () => void,
): ThunkAction<void, unknown, unknown, CreateCourseModuleSlotReturnTypes> {
  return async (dispatch: ThunkDispatch<void, unknown, CreateCourseModuleSlotReturnTypes>) => {
    dispatch(createCourseModuleSlotRequest());
    try {
      const response = await axios.post(`${endpoint}/${courseId}/slots`, {
        moduleIds,
        standupAttendanceRequirement,
      });

      dispatch(createCourseModuleSlotSuccess(response.data));
      successCallback?.();
    } catch (error) {
      dispatch(createCourseModuleSlotError(error.response));
    }
  };
}
