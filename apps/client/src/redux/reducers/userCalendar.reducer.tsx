import { Reducer } from '@reduxjs/toolkit';

import { CalendarEvent } from '../../types/CalendarEvent';
import {
  GetUserCalendarEventsError,
  GetUserCalendarEventsRequest,
  GetUserCalendarEventsSuccess,
  ResetUserCalendarEvents,
} from '../actions/userCalendar.actions';
import {
  GET_USER_CALENDAR_EVENTS_ERROR,
  GET_USER_CALENDAR_EVENTS_REQUEST,
  GET_USER_CALENDAR_EVENTS_SUCCESS,
  RESET_USER_CALENDAR_EVENTS,
} from '../constants/userCalendar.constants';

export interface CalendarReducer {
  userCalendarEvents?: CalendarEvent[];
  userWidgetEvents?: CalendarEvent[];
  isUserWidgetEventsLoading: boolean;
  getUserCalendarEventsError: boolean;
  isUserCalendarEventsLoading: boolean;
  userWidgetEventsErrorFirstTime: boolean;
  isUserWidgetEventsLoadingFirstTime: boolean;
  hasOpenedCalendar: boolean; // Track if user has opened calendar in this session
  action: any;
}

type CalendarActions =
  | GetUserCalendarEventsRequest
  | GetUserCalendarEventsSuccess
  | GetUserCalendarEventsError
  | ResetUserCalendarEvents;

const calendarReducer: Reducer<CalendarReducer, CalendarActions> = (
  state: CalendarReducer = {
    userWidgetEvents: undefined,
    isUserWidgetEventsLoading: false,
    getUserCalendarEventsError: false,
    action: null,
    userCalendarEvents: undefined,
    isUserCalendarEventsLoading: false,
    userWidgetEventsErrorFirstTime: false,
    isUserWidgetEventsLoadingFirstTime: false,
    hasOpenedCalendar: false,
  },
  action: CalendarActions,
) => {
  switch (action.type) {
    case GET_USER_CALENDAR_EVENTS_REQUEST:
      if (action.forWidget) {
        return {
          ...state,
          isUserWidgetEventsLoading: true,
          userWidgetEventsErrorFirstTime: false,
          isUserWidgetEventsLoadingFirstTime: !state.userWidgetEvents,
          action: action.type,
        };
      }
      return {
        ...state,
        isUserCalendarEventsLoading: true,
        action: action.type,
        getUserCalendarEventsError: false,
        hasOpenedCalendar: true,
      };
    case GET_USER_CALENDAR_EVENTS_SUCCESS: {
      if (action.forWidget) {
        return {
          ...state,
          userWidgetEvents: action.userEvents,
          isUserWidgetEventsLoading: false,
          userWidgetEventsErrorFirstTime: false,
          isUserWidgetEventsLoadingFirstTime: false,
          action: action.type,
        };
      }
      return {
        ...state,
        userCalendarEvents: action.userEvents,
        isUserCalendarEventsLoading: false,
        getUserCalendarEventsError: false,
        action: action.type,
      };
    }
    case GET_USER_CALENDAR_EVENTS_ERROR: {
      if (action.forWidget) {
        return {
          ...state,
          isUserCalendarEventsLoading: false,
          userWidgetEventsErrorFirstTime: !state.userWidgetEvents,
          isUserWidgetEventsLoadingFirstTime: false,
          action: action.type,
        };
      }
      return {
        ...state,
        getUserCalendarEventsError: true,
        isUserCalendarEventsLoading: false,
        action: action.type,
      };
    }
    case RESET_USER_CALENDAR_EVENTS:
      return {
        ...state,
        userCalendarEvents: undefined,
        getUserCalendarEventsError: false,
        action: action.type,
      };
    default:
      return { ...state };
  }
};

export default calendarReducer;
