import { Reducer } from '@reduxjs/toolkit';

import NotificationType from '../../types/NotificationType';
import { RelatedSprintPart, SprintPart } from '../../types/SprintPart';
import notification from '../../utils/tNotification';
import {
  AddRelatedPartError,
  AddRelatedPartRequest,
  AddRelatedPartSuccess,
  GetRelatedPartsError,
  GetRelatedPartsRequest,
  GetRelatedPartsSuccess,
  RemoveRelatedPartError,
  RemoveRelatedPartRequest,
  RemoveRelatedPartSuccess,
} from '../actions/relatedParts.actions';
import {
  ADD_RELATED_PART_ERROR,
  ADD_RELATED_PART_REQUEST,
  ADD_RELATED_PART_SUCCESS,
  GET_RELATED_PARTS_ERROR,
  GET_RELATED_PARTS_REQUEST,
  GET_RELATED_PARTS_SUCCESS,
  REMOVE_RELATED_PART_ERROR,
  REMOVE_RELATED_PART_REQUEST,
  REMOVE_RELATED_PART_SUCCESS,
} from '../constants/relatedParts.constants';

export interface RelatedPartsReducer {
  isGetRelatedPartsLoading: boolean;
  isAddRelatedPartLoading: boolean;
  isRemoveRelatedPartLoading: boolean;
  data?: {
    part: SprintPart;
    relatedParts: RelatedSprintPart[];
  };
  action?: any;
}

type RelatedPartsActions =
  | GetRelatedPartsRequest
  | GetRelatedPartsError
  | GetRelatedPartsSuccess
  | AddRelatedPartRequest
  | AddRelatedPartError
  | AddRelatedPartSuccess
  | RemoveRelatedPartRequest
  | RemoveRelatedPartError
  | RemoveRelatedPartSuccess;

const relatedPartsReducer: Reducer<RelatedPartsReducer, RelatedPartsActions> = (
  state: RelatedPartsReducer = {
    isGetRelatedPartsLoading: false,
    isAddRelatedPartLoading: false,
    isRemoveRelatedPartLoading: false,
  },
  action: RelatedPartsActions,
) => {
  switch (action.type) {
    // GET_RELATED_PART
    case GET_RELATED_PARTS_REQUEST:
      return { ...state, action: action.type, isGetRelatedPartsLoading: true };
    case GET_RELATED_PARTS_SUCCESS:
      return {
        ...state,
        action: action.type,
        isGetRelatedPartsLoading: false,
        data: {
          relatedParts: action.relatedParts,
          part: action.part,
        },
      };
    case GET_RELATED_PARTS_ERROR:
      return { ...state, action: action.type, isGetRelatedPartsLoading: false };

    // ADD_RELATED_PART
    case ADD_RELATED_PART_REQUEST:
      return { ...state, action: action.type, isAddRelatedPartLoading: true };
    case ADD_RELATED_PART_SUCCESS:
      notification({
        type: NotificationType.SUCCESS,
        message: 'Success',
        description: `Related part has been created`,
      });
      return {
        ...state,
        action: action.type,
        isAddRelatedPartLoading: false,
      };
    case ADD_RELATED_PART_ERROR:
      return { ...state, action: action.type, isAddRelatedPartLoading: false };

    // REMOVE_RELATED_PART
    case REMOVE_RELATED_PART_REQUEST:
      return { ...state, action: action.type, isRemoveRelatedPartLoading: true };
    case REMOVE_RELATED_PART_SUCCESS:
      notification({
        type: NotificationType.SUCCESS,
        message: 'Success',
        description: `Related part has been removed`,
      });
      return {
        ...state,
        action: action.type,
        isRemoveRelatedPartLoading: false,
      };
    case REMOVE_RELATED_PART_ERROR:
      return { ...state, action: action.type, isRemoveRelatedPartLoading: false };

    default:
      return state;
  }
};

export default relatedPartsReducer;
