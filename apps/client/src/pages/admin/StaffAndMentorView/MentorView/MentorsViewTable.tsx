import { ReactElement } from 'react';
import { TableColumnType } from 'antd';
import { Flex } from 'rebass';

import { tableColumnBodyText } from '../../../../components/tableColumnBodyText';
import { tableColumnBodyTitle } from '../../../../components/tableColumnTitle';
import { TableWrapper } from '../../../../components/TableWrapper';
import { tablePagination } from '../../../../Config';
import { Mentor } from '../../../../types/Mentor';
import { UserState } from '../../../../types/User';
import { getTableColumnFilterProps } from '../../components/TableColumnFilterDropdown/getTableColumnFilterProps';
import { getTableColumnSearchProps } from '../../components/TableColumnSearchDropdown/getTableColumnSearchProps';
import { UserStateTag } from '../../components/UserStateTag';

interface Props {
  data: Mentor[];
  loading: boolean;
  openEdit: (id: number) => void;
}

const MentorsViewTable = (props: Props): ReactElement => {
  const { data, loading, openEdit } = props;

  const columns: TableColumnType<Mentor>[] = [
    {
      dataIndex: 'name',
      key: 'name',
      width: '300px',
      title: tableColumnBodyTitle('Name'),
      showSorterTooltip: false,
      shouldCellUpdate: (prev, next) => prev !== next,
      render: (_text, row) =>
        tableColumnBodyText(
          `${row.name || '--'} ${row.surname || ''} ${row.username ? `| ${row.username}` : ''}`,
          {
            width: 300,
            isActive: row.state === UserState.ACTIVE,
          },
        ),
      sorter: (a, b) => {
        const fullNameA = `${a.name} ${a.surname}`;
        const fullNameB = `${b.name} ${b.surname}`;
        return fullNameA.localeCompare(fullNameB);
      },
      ...getTableColumnSearchProps({
        dataIndex: ['name', 'surname', 'username'],
        fieldName: 'name',
      }),
    },
    {
      dataIndex: 'email',
      key: 'email',
      width: '350px',
      title: tableColumnBodyTitle('Email'),
      showSorterTooltip: false,
      shouldCellUpdate: (prev, next) => prev !== next,
      render: (_text, row) =>
        tableColumnBodyText(`${row.email}`, {
          width: 350,
          isActive: row.state === UserState.ACTIVE,
        }),
      sorter: (a, b) => a.email.localeCompare(b.email),
      ...getTableColumnSearchProps({ dataIndex: 'email' }),
    },
    {
      dataIndex: 'status',
      key: 'status',
      title: tableColumnBodyTitle('Status'),
      showSorterTooltip: false,
      shouldCellUpdate: (prev, next) => prev !== next,
      render: (_text, row) => <UserStateTag state={row.state} />,
      sorter: (a, b) => ((a.state === UserState.ACTIVE) > (b.state === UserState.ACTIVE) ? 1 : -1),
      ...getTableColumnFilterProps({
        filters: [
          {
            value: UserState.ACTIVE,
            text: 'Active',
          },
          {
            value: UserState.BLOCKED,
            text: 'Blocked',
          },
          {
            value: UserState.REGISTERING,
            text: 'Registering',
          },
          {
            value: UserState.ONBOARDING,
            text: 'Onboarding',
          },
        ],
        onFilter: (value, record) => record.state === value,
      }),
    },
  ];

  const isRowEditable = (record: Mentor) =>
    [UserState.ACTIVE, UserState.BLOCKED].includes(record.state);

  const tableStyle = {
    '> div': {
      width: '100%',
    },
    '.table-row-editable:hover': {
      cursor: 'pointer',
    },
    '.table-row-disabled:hover': {
      cursor: 'not-allowed',
    },
  };

  return (
    <Flex sx={tableStyle}>
      <TableWrapper
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="id"
        pagination={tablePagination.small}
        rowClassName={(record) =>
          isRowEditable(record) ? 'table-row-editable' : 'table-row-disabled'
        }
        onRow={(record) => ({
          onClick: () => {
            if (isRowEditable(record)) openEdit(record.id);
          },
        })}
      />
    </Flex>
  );
};

export default MentorsViewTable;
