import { WeekDays } from '@shared/common/types/week-day';
import dayjs from 'dayjs';

import { blockDayAfterEndDate } from './blockDayAfterEndDate';

describe('blockDayAfterEndDate', () => {
  test('current week is not last week', () => {
    const weekStartStr = '2025-06-16';
    const endDate = dayjs('2025-06-27', 'YYYY-MM-DD'); // Friday

    WeekDays.FULL_IN_ORDER.forEach((weekDay) =>
      expect(blockDayAfterEndDate({ weekStartStr, weekDay, endDate })).toBe(false),
    );
  });

  test('current week is last week', () => {
    const weekStartStr = '2025-06-23';
    const endDate = dayjs('2025-06-27', 'YYYY-MM-DD'); // Friday

    const weekdays = WeekDays.FULL_IN_ORDER.slice(0, 5);
    const weekends = WeekDays.FULL_IN_ORDER.slice(5);

    weekdays.forEach((day) => {
      expect(blockDayAfterEndDate({ weekStartStr, weekDay: day, endDate })).toBe(false);
    });

    weekends.forEach((day) => {
      expect(blockDayAfterEndDate({ weekStartStr, weekDay: day, endDate })).toBe(true);
    });
  });
});
