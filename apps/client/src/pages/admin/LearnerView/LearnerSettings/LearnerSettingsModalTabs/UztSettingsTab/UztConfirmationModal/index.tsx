import { ReactElement, useEffect, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Form, Modal } from 'antd';
import { Box, Flex } from 'rebass';

import { InputBox, InputType } from '../../../../../../../components/inputs/InputBox';
import { TButton } from '../../../../../../../components/TButton';
import { BodyText2, BodyText3, Heading3 } from '../../../../../../../components/typography';
import { validateEmail } from '../../../../../../../utils/inputValidation';
import { documentLabels } from '../../../../../constants';
import { getCompanySigners, UztDocument } from '../../../../../queries/UztDocuments.queries';
import { getConfirmationModalData } from './getConfirmationModalData';

export enum UztConfirmationModalAction {
  GENERATE = 'generate',
  SIGN = 'sign',
  SEND = 'send',
}

export type Props = {
  action?: UztConfirmationModalAction | undefined;
  onCancel: () => void;
  onConfirm: (params?: { signerId?: number; customEmail?: string }) => void;
  learnerFullName: string;
  documents: UztDocument[];
  isLoading?: boolean;
  defaultSendEmail?: string;
};

export const UztConfirmationModal = (props: Props): ReactElement => {
  const { onCancel, onConfirm, action, documents, learnerFullName, isLoading, defaultSendEmail } =
    props;

  const { data: companySigners } = useQuery({
    queryKey: ['get-company-signers'],
    queryFn: getCompanySigners,
  });

  const [signerId, setSignerId] = useState<number | undefined>();
  const [form] = Form.useForm();
  const email = Form.useWatch('email', form);
  const [isEmailValid, setIsEmailValid] = useState(true);

  useEffect(() => {
    form.setFieldsValue({ email: defaultSendEmail });
  }, [defaultSendEmail]);

  useEffect(() => {
    form
      .validateFields({ validateOnly: true })
      .then(() => setIsEmailValid(true))
      .catch(() => setIsEmailValid(false));
  }, [form, email]);

  const { confirmText, title, subtitle, selectSignerLabel } = getConfirmationModalData(action, {
    numberOfDocuments: documents.length,
    learnerFullName,
  });

  const isConfirmDisabled =
    isLoading || (action === UztConfirmationModalAction.GENERATE && !signerId) || !isEmailValid;

  const handleConfirm = () => {
    if (action === UztConfirmationModalAction.GENERATE) {
      onConfirm({ signerId });
    } else if (action == UztConfirmationModalAction.SEND) {
      onConfirm({ customEmail: email !== defaultSendEmail ? email : undefined });
    } else {
      onConfirm();
    }
    setSignerId(undefined);
  };

  const handleCancel = () => {
    onCancel();
    setSignerId(undefined);
  };

  return (
    <Modal
      open={!!action}
      onCancel={handleCancel}
      footer={
        <Flex sx={{ gap: '8px', justifyContent: 'flex-end' }}>
          <TButton type="text" onClick={handleCancel}>
            No, go back
          </TButton>
          <TButton
            type="primary"
            loading={isLoading}
            onClick={handleConfirm}
            disabled={isConfirmDisabled}
          >
            {confirmText}
          </TButton>
        </Flex>
      }
    >
      <Flex sx={{ flexDirection: 'column', gap: '16px' }}>
        <Heading3>{title}</Heading3>
        <BodyText2>{subtitle}</BodyText2>
        {selectSignerLabel && <BodyText3 color="colorTextTertiary">{selectSignerLabel}</BodyText3>}
        <Flex sx={{ flexDirection: 'column', gap: '8px' }}>
          {documents.map(({ type, requiresLearnerSignature, signers }, idx) => {
            const companySignerUserId = signers.find((signer) => signer.isCompanySigner)?.id;

            return (
              <Flex
                key={idx}
                sx={{
                  justifyContent: 'space-between',
                  backgroundColor: 'colorInputBg',
                  padding: '8px 16px',
                  borderRadius: '6px',
                }}
              >
                <Box sx={{ padding: '8px' }}>{documentLabels[type] || type}</Box>
                {action !== UztConfirmationModalAction.SEND && (
                  <Flex sx={{ gap: '8px', alignItems: 'center' }}>
                    {requiresLearnerSignature && (
                      <BodyText3 color="colorTextQuaternary">Learner +</BodyText3>
                    )}
                    <InputBox
                      // if already generated, field is disabled and we show previously selected signer
                      value={
                        action === UztConfirmationModalAction.SIGN ? companySignerUserId : signerId
                      }
                      onChange={setSignerId}
                      disabled={isLoading || action === UztConfirmationModalAction.SIGN}
                      formItemStyle={{ marginBottom: 0, minWidth: '175px' }}
                      type={InputType.SELECT}
                      placeholder="Select..."
                      options={companySigners?.staff.map((signer) => ({
                        value: signer.id,
                        label: `${signer.name} ${signer.surname}`,
                      }))}
                    />
                  </Flex>
                )}
              </Flex>
            );
          })}
        </Flex>
        {action === UztConfirmationModalAction.SEND && (
          <Form form={form} validateMessages={validateEmail}>
            <InputBox
              name="email"
              label="To:"
              rules={[{ required: true, type: 'email' }]}
              formItemStyle={{ marginBottom: 0 }}
              type={InputType.TEXT}
              placeholder="Email address"
            />
          </Form>
        )}
      </Flex>
    </Modal>
  );
};
