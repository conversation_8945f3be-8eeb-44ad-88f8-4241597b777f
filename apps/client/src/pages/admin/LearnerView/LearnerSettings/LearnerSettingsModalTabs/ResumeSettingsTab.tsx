import { ReactElement, useEffect } from 'react';
import CopyToClipboard from 'react-copy-to-clipboard';
import { useDispatch, useSelector } from 'react-redux';
import { But<PERSON>, Switch } from 'antd';
import { Box, Flex, Image } from 'rebass';

import copy from '@images/svg/copy.svg';

import { InputBox, InputType } from '../../../../../components/inputs/InputBox';
import Loader from '../../../../../components/Loader';
import { BodyText2, Heading3, Heading4, Subtitle1 } from '../../../../../components/typography';
import { resumeLinkBase, resumePreviewLinkBase } from '../../../../../Config';
import {
  getResumeSettings,
  updateResumeEnablement,
} from '../../../../../redux/actions/resumeSettings.actions';
import { AppState } from '../../../../../redux/reducers/index.reducer';
import { AppDispatch } from '../../../../../redux/store';
import NotificationType from '../../../../../types/NotificationType';
import { Student } from '../../../../../types/Student';
import notification from '../../../../../utils/tNotification';

interface Props {
  learner: Student;
}

const ResumeSettingsTab = (props: Props): ReactElement => {
  const { learner } = props;

  const { settings, isEnablementLoading, areSettingsLoading } = useSelector(
    (state: AppState) => state.resumeSettingsReducer,
  );
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    if (learner && !settings) dispatch(getResumeSettings(learner.id));
  }, [learner]);

  const triggerEnablement = (isEnabled: boolean) =>
    dispatch(updateResumeEnablement(learner.id, isEnabled));

  const resumeLink = (
    <Flex sx={{ flexDirection: 'column' }}>
      <Subtitle1 color="colorTextTertiary" margin="0 0 6px 0">
        Resume link
      </Subtitle1>
      <Flex
        sx={{
          width: '100%',
          alignItems: 'center',
          button: {
            borderTopLeftRadius: 0,
            borderBottomLeftRadius: 0,
          },
          '.ant-form-item, .ant-input, .ant-modal-content': {
            width: '100%',
            margin: 0,
            borderTopRightRadius: 0,
            borderBottomRightRadius: 0,
          },
          '.ant-input-affix-wrapper > input.ant-input': {
            color: 'colorTextQuaternary',
          },
        }}
      >
        <InputBox
          type={InputType.TEXT}
          placeholder="First name"
          disabled
          value={`${resumeLinkBase}/${settings?.publicId}`}
        />
        <CopyToClipboard text={`${resumeLinkBase}/${settings?.publicId}`}>
          <Button
            size="middle"
            ghost
            onClick={() => {
              notification({
                type: NotificationType.SUCCESS,
                message: 'Success',
                description: 'Learner resume link copied to clipboard',
              });
            }}
          >
            <Image src={copy} />
          </Button>
        </CopyToClipboard>
      </Flex>
    </Flex>
  );

  return (
    <Flex
      sx={{
        flexDirection: 'column',
        width: '100%',
        maxWidth: '600px',
        paddingBottom: '50px',
      }}
    >
      {areSettingsLoading || !settings ? (
        <Loader />
      ) : (
        <>
          <Heading3 margin="0 0 24px 0">Resume settings</Heading3>
          <Box sx={{ marginBottom: '24px' }}>
            <Heading4 margin="0 0 12px 0">Admin preview</Heading4>
            <BodyText2 color="colorTextTertiary" margin="0 0 8px 0">
              As an admin / staff, you can preview resume before it&apos;s made public. Be aware,
              this link will not work for anyone outside of Turing.
            </BodyText2>
            <a href={`${resumePreviewLinkBase}/${learner.id}`} target="_blank" rel="noreferrer">
              <Button ghost>Admin preview</Button>
            </a>
          </Box>
          <Box>
            <Heading4 margin="0 0 12px 0">Settings</Heading4>
            <Flex sx={{ flexDirection: 'column', width: 'fit-content', marginBottom: '12px' }}>
              <Subtitle1 color="colorTextTertiary" margin="0 0 6px 0">
                Has learner given consent?
              </Subtitle1>
              <Box>
                <Switch checked={settings.isConsentGiven} disabled />
              </Box>
            </Flex>
            <Flex sx={{ flexDirection: 'column', width: 'fit-content', marginBottom: '12px' }}>
              <Subtitle1 color="colorTextTertiary" margin="0 0 6px 0">
                Is resume enabled?
              </Subtitle1>
              <Box>
                <Switch
                  checked={settings.isEnabled}
                  onChange={triggerEnablement}
                  loading={isEnablementLoading}
                />
              </Box>
            </Flex>
            {resumeLink}
          </Box>
        </>
      )}
    </Flex>
  );
};

export default ResumeSettingsTab;
