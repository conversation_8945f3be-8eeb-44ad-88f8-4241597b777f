import { ReactElement, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Image, Select } from 'antd';
import debounce from 'lodash/debounce';
import { Flex } from 'rebass';

import emptySvg from '@images/svg/empty.svg';

import { BodyText2, Subtitle2 } from '../../../../../../components/typography';
import { resetMentors, searchMentors } from '../../../../../../redux/actions/mentors.actions';
import { resetStudents, searchStudents } from '../../../../../../redux/actions/students.actions';
import { AppState } from '../../../../../../redux/reducers/index.reducer';
import { AppDispatch } from '../../../../../../redux/store';
import { SprintPartMentor } from '../../../../../../types/SprintPartMentor';
import { Student } from '../../../../../../types/Student';

const minSearchLength = 3;

type SelectGroupedOptions = {
  label: string;
  options: { value: number; label: ReactElement }[];
}[];

const mapUserToOption = (user: SprintPartMentor | Student) => ({
  value: user.id,
  label: <BodyText2>{`${user.name} ${user.surname} | ${user.username}`}</BodyText2>,
});

type Props = {
  onChange: (id: number) => void;
};

export const SearchUsersDropdown = (props: Props) => {
  const { onChange } = props;
  const dispatch = useDispatch<AppDispatch>();

  const students = useSelector((state: AppState) => state.studentsReducer.students);
  const mentors = useSelector((state: AppState) => state.mentorsReducer.mentors);

  const [isLoading, setIsLoading] = useState(false);

  const [selectOptions, setSelectOptions] = useState<SelectGroupedOptions>([]);
  const [selectSearchValue, setSelectSearchValue] = useState<string>('');

  useEffect(() => {
    const optGroups = [];

    if (selectSearchValue.length < minSearchLength) {
      setSelectOptions([]);
    } else {
      if (students?.length) {
        optGroups.push({
          label: 'Learners',
          options: students.map(mapUserToOption) || [],
        });
      }

      if (mentors?.length) {
        optGroups.push({
          label: 'STLs',
          options: mentors.map(mapUserToOption) || [],
        });
      }

      setSelectOptions(optGroups);
      // setting to with delay to avoid flashing of "No users found"
      setTimeout(() => setIsLoading(false), 100);
    }
  }, [students, mentors]);

  const debounceSearch = useRef(
    debounce((value) => {
      if (value.length >= minSearchLength) {
        dispatch(searchStudents(value));
        dispatch(searchMentors(value));
      } else {
        dispatch(resetStudents());
        dispatch(resetMentors());
      }
    }, 500),
  );

  const handleSearch = (value: string) => {
    setSelectSearchValue(value);
    if (value.length >= minSearchLength) setIsLoading(true);
    debounceSearch.current(value);
  };

  const notFoundContentStyle = {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '8px',
    padding: '16px',
  };

  const notFoundContent = (
    <Flex sx={notFoundContentStyle}>
      <Image src={emptySvg} style={{ height: '40px' }} />
      <Subtitle2 color="colorTextQuaternary">
        {isLoading
          ? 'Loading...'
          : selectSearchValue.length < minSearchLength
            ? `Type at least ${minSearchLength} characters to search`
            : 'No users found'}
      </Subtitle2>
    </Flex>
  );

  return (
    <Select
      notFoundContent={notFoundContent}
      placeholder="Find by name or username"
      showSearch
      onSearch={handleSearch}
      loading={isLoading}
      filterOption={false}
      style={{ width: '100%' }}
      onChange={onChange}
      options={selectOptions}
    />
  );
};
