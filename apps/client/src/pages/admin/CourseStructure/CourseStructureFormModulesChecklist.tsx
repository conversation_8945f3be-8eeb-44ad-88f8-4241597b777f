import { ReactElement, useEffect, useState } from 'react';
import * as React from 'react';
import Highlighter from 'react-highlight-words';
import { useDispatch, useSelector } from 'react-redux';
import { Button } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox/Checkbox';
import { Flex, Image } from 'rebass';

import searchIcon from '@images/svg/search.svg';

import { InputBox, InputType } from '../../../components/inputs/InputBox';
import Loader from '../../../components/Loader';
import NoResults from '../../../components/NoResults';
import { TCheckbox } from '../../../components/TCheckbox';
import { BodyText1 } from '../../../components/typography';
import { createCourseSlot, updateCourseModuleSlot } from '../../../redux/actions/course.actions';
import { getModules } from '../../../redux/actions/modulesMenu.actions';
import {
  createStudentCourseSlot,
  updateStudentCourseSlots,
} from '../../../redux/actions/student.actions';
import { AppState } from '../../../redux/reducers/index.reducer';
import { AppDispatch } from '../../../redux/store';
import theme from '../../../theme';
import { CurriculumMenuNode } from '../../../types/CurriculumMenu';
import { isModuleSlot, ModuleSlot } from '../../../types/ModuleSlot';
import { StandupAttendanceRequirement } from '../../../types/StandupAttendanceRequirement';
import { StudentModule } from '../../../types/StudentModule';
import { isOrderedStudentSlot, OrderedStudentSlot, SlotState } from '../../../types/StudentSlot';
import errorNotification from '../../../utils/errorNotification';
import { EmptySlot, isEmptySlot } from './CourseStructureEdit';
import CourseStructureEditStandupRequirement from './CourseStructureEditStandupRequirement';
import CourseStructureFormModuleHeader from './CourseStructureFormModuleHeader';
import { canEditSlot, SlotType } from './CourseStructureWrapper';

interface Props {
  entityId: number;
  selectedSlot: OrderedStudentSlot | ModuleSlot;
  setAreThereUnsavedChanges: (isModalChanged: boolean) => void;
  resetDisplayedSlots: () => void;
  onDeleteSlot: () => void;
}

const CourseStructureFormModulesChecklist = (props: Props): ReactElement => {
  const { selectedSlot, setAreThereUnsavedChanges, entityId, resetDisplayedSlots, onDeleteSlot } =
    props;
  const dispatch = useDispatch<AppDispatch>();
  const { modules, isGetModulesLoading, hasGetModulesErrored } = useSelector(
    (state: AppState) => state.modulesMenuReducer,
  );
  const { isUpdateStudentCourseSlotsLoading, isCreateStudentCourseSlotLoading } = useSelector(
    (state: AppState) => state.studentReducer,
  );
  const { isUpdateCourseModuleSlotLoading, isCreateCourseModuleSlotLoading } = useSelector(
    (state: AppState) => state.courseReducer,
  );

  const [searchValue, setSearchValue] = useState<string>();
  const [selectedModuleIds, setSelectedModuleIds] = useState<number[]>([]);
  const [standupAttendanceRequirement, setStandupAttendanceRequirement] =
    useState<StandupAttendanceRequirement>(selectedSlot.standupAttendanceRequirement);
  const [visibleModules, setVisibleModules] = useState<CurriculumMenuNode[]>([]);
  const [isSlotModified, setIsSlotModified] = useState<boolean>(false);

  const isSaveChangesLoading =
    isUpdateStudentCourseSlotsLoading ||
    isCreateStudentCourseSlotLoading ||
    isUpdateCourseModuleSlotLoading ||
    isCreateCourseModuleSlotLoading;
  const isEditStandupRequirementDisabled =
    selectedModuleIds.length === 0 ||
    (isOrderedStudentSlot(selectedSlot) && selectedSlot.state === SlotState.COMPLETED);

  useEffect(() => {
    if (!modules && !isGetModulesLoading && !hasGetModulesErrored) {
      dispatch(getModules());
    } else if (modules) {
      setVisibleModules(modules);
    }
  }, [modules, isGetModulesLoading, hasGetModulesErrored]);

  useEffect(() => {
    setSelectedModuleIds(selectedSlot.modules.map((md: StudentModule) => md.id));
    setStandupAttendanceRequirement(selectedSlot.standupAttendanceRequirement);
    setIsSlotModified(false);
  }, [selectedSlot]);

  const searchModules = (ev: React.ChangeEvent<HTMLInputElement>) => {
    const sv = ev.target.value.toLowerCase() || '';
    setSearchValue(sv);

    setVisibleModules(
      (modules || []).filter((module: CurriculumMenuNode) =>
        (module.title as string).toLowerCase().includes(sv),
      ),
    );
  };

  const handleModulesChange = (e: CheckboxChangeEvent, module: CurriculumMenuNode) => {
    let newModuleIds = [...selectedModuleIds];
    if (e.target.checked) {
      newModuleIds.push(module.id);
    } else {
      newModuleIds = newModuleIds.filter((id: number) => id !== module.id);
    }
    setSelectedModuleIds(newModuleIds);
    const hasModified =
      newModuleIds.length !== selectedSlot.modules.length ||
      selectedSlot.modules
        .map((md: StudentModule) => md.id)
        .some((id: number) => !newModuleIds.includes(id));

    setIsSlotModified(hasModified);
    setAreThereUnsavedChanges(hasModified);
  };

  const handleStandupRequirementChange = (value: StandupAttendanceRequirement) => {
    setStandupAttendanceRequirement(value);
    const hasModified = value !== selectedSlot.standupAttendanceRequirement;
    setIsSlotModified(hasModified);
    setAreThereUnsavedChanges(hasModified);
  };

  const handleSaveChanges = () => {
    if (isOrderedStudentSlot(selectedSlot)) {
      dispatch(
        updateStudentCourseSlots(
          {
            studentId: entityId,
            slot: selectedSlot as OrderedStudentSlot,
            moduleIds: selectedModuleIds,
            standupAttendanceRequirement,
          },
          resetDisplayedSlots,
        ),
      );
    } else if (isModuleSlot(selectedSlot)) {
      dispatch(
        updateCourseModuleSlot(
          {
            courseId: entityId,
            slotId: selectedSlot.id,
            moduleIds: selectedModuleIds,
            standupAttendanceRequirement,
          },
          resetDisplayedSlots,
        ),
      );
    } else if (isEmptySlot(selectedSlot)) {
      // Creating
      if ((selectedSlot as EmptySlot).type === SlotType.STUDENT) {
        dispatch(
          createStudentCourseSlot(
            {
              studentId: entityId,
              moduleIds: selectedModuleIds,
              standupAttendanceRequirement,
            },
            resetDisplayedSlots,
          ),
        );
      } else if ((selectedSlot as EmptySlot).type === SlotType.COURSE) {
        dispatch(
          createCourseSlot(
            {
              courseId: entityId,
              moduleIds: selectedModuleIds,
              standupAttendanceRequirement,
            },
            resetDisplayedSlots,
          ),
        );
      } else errorNotification();
    } else errorNotification();
  };

  const canChangeOptions = canEditSlot(selectedSlot);

  const isSlotAssigned = [SlotState.PENDING, SlotState.IN_PROGRESS, SlotState.COMPLETED].includes(
    (selectedSlot as OrderedStudentSlot)?.state,
  );

  let content;

  if (isGetModulesLoading) {
    content = <Loader />;
  } else if (modules && modules.length === 0) {
    content = <NoResults />;
  } else {
    content = visibleModules.map((module: CurriculumMenuNode) => {
      const isModuleAssigned =
        (selectedSlot as OrderedStudentSlot)?.assignedModule?.id === module.id;

      const moduleContainerStyle = {
        borderRadius: '4px',
        width: '100%',
        padding: '7px 0 5px 12px',
        backgroundColor: isSlotAssigned && isModuleAssigned ? 'colorBgContainer' : 'colorBgLayout',
        '&:hover': {
          backgroundColor: canChangeOptions ? 'colorBgContainer' : undefined,
        },
      };
      return (
        <Flex key={module.id} sx={moduleContainerStyle}>
          <TCheckbox
            disabled={!canChangeOptions}
            checked={selectedModuleIds.includes(module.id)}
            onChange={(e) => handleModulesChange(e, module)}
          >
            <BodyText1 color={canChangeOptions ? 'colorText' : 'colorTextQuaternary'}>
              <Highlighter
                searchWords={[searchValue || '']}
                textToHighlight={module.title as string}
              />
            </BodyText1>
          </TCheckbox>
        </Flex>
      );
    });
  }

  return (
    <>
      <CourseStructureEditStandupRequirement
        disabled={isEditStandupRequirementDisabled}
        onChange={handleStandupRequirementChange}
        value={standupAttendanceRequirement}
      />
      <CourseStructureFormModuleHeader onDeleteSlot={onDeleteSlot} selectedSlot={selectedSlot} />
      <InputBox
        onChange={searchModules}
        prefix={<Image src={searchIcon} margin="0 0 0 8px" />}
        placeholder="Search..."
        type={InputType.TEXT}
      />
      <Flex
        sx={{
          flexDirection: 'column',
          overflow: 'scroll',
          height: 'calc(100% - 90px)',
          gap: '8px',
        }}
      >
        {content}
      </Flex>
      {isSlotModified && !isGetModulesLoading && (modules || []).length !== 0 ? (
        <Flex
          sx={{
            width: '100%',
            borderTop: `1px solid ${theme.colors.colorBorder}`,
            backgroundColor: 'colorBgLayout',
            button: {
              margin: '8px 0 24px 0',
              width: '100%',
            },
          }}
        >
          <Button type="primary" onClick={handleSaveChanges} loading={isSaveChangesLoading}>
            Save changes
          </Button>
        </Flex>
      ) : null}
    </>
  );
};

export default CourseStructureFormModulesChecklist;
