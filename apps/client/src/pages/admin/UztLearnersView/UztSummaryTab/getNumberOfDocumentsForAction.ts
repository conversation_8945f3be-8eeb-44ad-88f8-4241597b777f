import { UztDocumentType } from '../../queries/UztDocuments.queries';
import { UztDocumentState } from '../../types/UztDocumentState';
import { UztDocumentSummary } from './UztSummary.queries';

export const getNumberOfDocumentsForAction = (
  documentsSummary: UztDocumentSummary[],
  selectedDocumentType?: UztDocumentType,
) => {
  const countsByState = documentsSummary.find((s) => s.document === selectedDocumentType);

  if (!countsByState || !selectedDocumentType)
    return {
      numberOfDocumentsToSign: 0,
      numberOfDocumentsToGenerate: 0,
    };

  return {
    // BE pre-aggregates the states to include failed states
    numberOfDocumentsToSign: countsByState[UztDocumentState.GENERATED],
    numberOfDocumentsToGenerate: countsByState[UztDocumentState.NEW],
  };
};
