import { ReactElement, useEffect, useState } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { Button, Statistic, Tooltip } from 'antd';
import Modal from 'antd/es/modal/Modal';
import { Image as CloudinaryImage } from 'cloudinary-react';
import dayjs from 'dayjs';
import { Box, Flex, Image } from 'rebass';

import exit from '@images/svg/exit.svg';
import zoom from '@images/svg/zoom.svg';

import { useGetReviewByEventIdQuery } from '../../../../../../api/reviews/useGetReviewByEventIdQuery';
import { useGetReviewQuery } from '../../../../../../api/reviews/useGetReviewQuery';
import ErrorUIComponent from '../../../../../../components/ErrorUIComponent';
import Loader from '../../../../../../components/Loader';
import {
  BodyText2,
  BodyText3,
  Heading3,
  Heading6,
  Subtitle1,
} from '../../../../../../components/typography';
import { defaultUserImage, isDesktop } from '../../../../../../Config';
import FontWeight from '../../../../../../types/FontWeight';
import { MixpanelLocation } from '../../../../../../types/Mixpanel';
import { Review, ReviewState } from '../../../../../../types/Review';
import { UserRole } from '../../../../../../types/User';
import capitalizeFirstLetter from '../../../../../../utils/capitalizeFirstLetter';
import { getCanceledReviewText } from '../../../../ReviewView/utils/getCanceledReviewText';
import calendarEventTimeString from '../../../../utils/calendarEventTimeString';
import CancelReportCorrectionButtons from './CancelReportCorrectionButtons';

interface Props {
  isOpen: boolean;
  reviewId?: number;
  reviewEventId?: number;
  handleClose: () => void;
  onReviewCancel?: () => void;
}

const ReviewOverview = (props: Props): ReactElement => {
  const { isOpen, reviewId, reviewEventId, handleClose, onReviewCancel } = props;

  const [isInProgress, setIsInProgress] = useState<boolean>(false);

  const queryClient = useQueryClient();

  const reviewByEventIdResult = useGetReviewByEventIdQuery(reviewEventId, {
    enabled: isOpen && !!reviewEventId,
  });
  const reviewByIdResult = useGetReviewQuery(reviewId, {
    enabled: isOpen && !!reviewId,
  });

  const isLoading = reviewByIdResult.isLoading || reviewByEventIdResult.isLoading;
  const isError = reviewByIdResult.isError || reviewByEventIdResult.isError;
  const review = reviewByIdResult.data || reviewByEventIdResult.data;

  useEffect(() => {
    if (review) setIsInProgress(dayjs().isAfter(dayjs(review.startsAt)));
  }, [review]);

  const isZoomActive = review && dayjs().isAfter(dayjs(review.startsAt).subtract(15, 'minutes'));

  const correctionInProgressText = (
    <Subtitle1
      testId="correction-in-progress"
      color="colorAccent"
      margin="0 0 10px 0"
      fontStyle="italic"
    >
      Review is in progress. Quick, join the Zoom call!
    </Subtitle1>
  );

  const evaluatorInfo = (
    <>
      <Box data-testid="review-info--desktop--container" display={['none', 'block', 'block']}>
        <Flex
          sx={{
            flexDirection: 'column',
            marginBottom: '24px',
          }}
        >
          <Subtitle1 color="colorTextQuaternary" margin="0 0 6px 0" fontStyle="italic">
            Reviewer
          </Subtitle1>
          <Flex
            sx={{
              backgroundColor: 'colorBgContainer',
              padding: '10px',
            }}
          >
            <Box
              sx={{
                marginRight: '10px',
                borderRadius: '3px',
              }}
            >
              <CloudinaryImage
                publicId={review?.evaluator.image || defaultUserImage}
                width="54"
                height="54"
              />
            </Box>
            <Box>
              <Heading6 weight={FontWeight.REGULAR} margin="0 0 5px 0">
                {`${review?.evaluator.name} ${review?.evaluator.surname} | ${review?.evaluator.username}`}
              </Heading6>
              <Flex
                sx={{
                  backgroundColor: 'colorTagBg',
                  padding: '2px 4px',
                  alignItems: 'center',
                  justifyContent: 'center',
                  borderRadius: '3px',
                  width: 'fit-content',
                }}
              >
                <Subtitle1 color="colorTextQuaternary">
                  {capitalizeFirstLetter(
                    review?.evaluator.role === UserRole.USER ? 'Learner' : 'STL',
                  )}
                </Subtitle1>
              </Flex>
            </Box>
          </Flex>
        </Flex>
      </Box>
    </>
  );

  const zoomButton = (
    <Button
      data-testid="zoom-link--container"
      style={{ padding: 0, cursor: isZoomActive ? 'pointer' : 'not-allowed', flexGrow: 1 }}
      type="primary"
      disabled={!isZoomActive}
      block
      onClick={() => {
        if (isZoomActive) {
          window.open(`/meetings/${review.meetingId}`, '_blank');
        }
      }}
    >
      <Image
        alt="zoom"
        src={zoom}
        style={{
          margin: '0 6px 0 0',
          opacity: isZoomActive ? 1 : 0.4,
        }}
      />
      <span style={{ lineHeight: '24px' }}>Join meeting</span>
    </Button>
  );

  const modalContentHeader = (
    <>
      <Flex
        sx={{
          alignItems: 'center',
          marginBottom: '4px',
        }}
      >
        <Heading3 margin="0 0 8px 0">
          <span data-testid="student-review-modal-title--text">{review?.sprintPart.name}</span>
        </Heading3>
      </Flex>
      {review && (
        <BodyText2 color="colorTextQuaternary" margin="0 0 16px 0">
          {calendarEventTimeString(review.startsAt, review.endsAt)}
        </BodyText2>
      )}
      {evaluatorInfo}
    </>
  );

  const modalContent = (
    <>
      {modalContentHeader}
      {isInProgress ? (
        correctionInProgressText
      ) : (
        <Box
          sx={{
            '.ant-statistic-content': {
              lineHeight: 1.25,
              fontFamily: 'body',
              fontSize: '13px',
              color: 'colorTextQuaternary',
              margin: '0 0 10px 0',
            },
          }}
        >
          <Statistic.Countdown
            value={dayjs(review?.startsAt).valueOf()}
            format="D[d] HH[h] m[min]"
            prefix="Starts in: "
            onFinish={() => setIsInProgress(true)}
          />
        </Box>
      )}

      {isZoomActive ? (
        zoomButton
      ) : (
        <Tooltip
          title="Meeting link will become available 15 minutes before the start."
          placement="bottom"
          trigger={isDesktop ? 'hover' : 'click'}
        >
          {zoomButton}
        </Tooltip>
      )}

      <Box marginBottom="12px" />
      <CancelReportCorrectionButtons
        secondsLeft={isInProgress ? -1 : 1}
        review={review as Review}
        isPeerCorrection={false}
        onCanceledOrReported={() => {
          handleClose();
          queryClient.invalidateQueries({
            queryKey: ['sprint-parts', review?.sprintPart.slug, 'evaluation'],
          });
          onReviewCancel?.();
        }}
        mixpanelLocation={MixpanelLocation.WAITING_ROOM}
      />
    </>
  );

  let content;

  if (isLoading) {
    content = <Loader />;
  } else if (isError) {
    content = <ErrorUIComponent />;
  } else if (review && review.state === ReviewState.CANCELED) {
    content = (
      <>
        {modalContentHeader}
        <BodyText3 color="colorTextQuaternary">{getCanceledReviewText(review)}</BodyText3>
      </>
    );
  } else {
    content = modalContent;
  }

  return (
    <Modal
      open={isOpen}
      closeIcon={<Image onClick={handleClose} src={exit} />}
      onCancel={handleClose}
      width="600px"
      centered
      footer={false}
      data-testid="student-review-modal--container"
    >
      {content}
    </Modal>
  );
};

export default ReviewOverview;
