import { ReactElement } from 'react';
import { useQuery } from '@tanstack/react-query';
import { Flex } from 'rebass';

import ErrorUIComponent from '../../../../../components/ErrorUIComponent';
import { Notebook } from '../../../../../features/notebook';
import { NotebookSkeleton } from '../../../../../features/notebook/NotebookSkeleton';
import { useUser } from '../../../../../hooks/useUser';
import { StudentSprintPart } from '../../../../../types/StudentSprintPart';
import { getStudentSprintPartContent } from '../../../queries/studentSprintPart.queries';

interface Props {
  sprintPart: StudentSprintPart;
}

const SprintPartContent = (props: Props): ReactElement => {
  const { sprintPart } = props;
  const { user } = useUser();

  let elements;

  const {
    isLoading,
    isError,
    data: content,
  } = useQuery({
    queryKey: ['sprint-parts', sprintPart, 'content'],
    queryFn: () => getStudentSprintPartContent(user, sprintPart.slug),
  });

  if (isError) {
    elements = <ErrorUIComponent />;
  } else if (isLoading) {
    elements = <NotebookSkeleton />;
  } else if (content) {
    elements = <Notebook content={content} />;
  }

  return (
    <Flex
      sx={{
        maxWidth: '100%',
        flexDirection: 'column',
      }}
    >
      {elements}
    </Flex>
  );
};

export default SprintPartContent;
