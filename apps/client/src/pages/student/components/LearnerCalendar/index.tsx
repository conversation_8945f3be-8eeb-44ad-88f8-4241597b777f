import { ReactElement, useEffect, useState } from 'react';
import * as React from 'react';
import { useSelector } from 'react-redux';
import { CalendarApi, SlotLabelContentArg } from '@fullcalendar/react';
import dayjs from 'dayjs';
import { Box, Flex } from 'rebass';

import Loader from '../../../../components/Loader';
import { calendarMaxVisibleDayCount } from '../../../../Config';
import { AppState } from '../../../../redux/reducers/index.reducer';
import { ReviewType } from '../../../../types/Review';
import calendarStartTimeObject from '../../../../utils/calendarStartTime';
import calendarVisibleDayCount from './calendarVisibleDayCount';
import NoAvailabilitySlots from './NoAvailabilitySlots';
import ScheduleCorrectionCalendar from './ScheduleCorrectionCalendar';
import ScheduleTimeslotCalendar from './ScheduleTimeslotCalendar';

let calendarApi: CalendarApi;

interface Props {
  isOpen: boolean;
  handleCancel?: () => void;
  scheduleReviewType?: ReviewType;
  setScheduleReviewType?: (type: ReviewType) => void;
  requestReference: React.MutableRefObject<AbortController | undefined>;
}

const now = new Date();
const nowDayIndex = now.getDay();
const weekStartDate = dayjs().startOf('week').toDate();

const LearnerCalendar = (props: Props): ReactElement => {
  const { isOpen, scheduleReviewType, handleCancel, requestReference, setScheduleReviewType } =
    props;
  const [visibleDayCount, setVisibleDayCount] = useState<number>(2);

  const { availabilityFetched, availability } = useSelector(
    (state: AppState) => state.correctionReducer,
  );

  const isUserCalendarEventsLoading = useSelector(
    (state: AppState) => state.userCalendarReducer.isUserCalendarEventsLoading,
  );

  const reassignVisibleDayCount = () => {
    if (calendarApi) {
      const dayCount = calendarVisibleDayCount();

      if (dayCount === calendarMaxVisibleDayCount) {
        calendarApi.gotoDate(weekStartDate);
      } else {
        calendarApi.gotoDate(new Date());
      }
      setVisibleDayCount(dayCount);

      // This is a hack to force calendar to re-render displayed dates & show assigned
      // day as the first one.
      calendarApi.next();
      calendarApi.prev();
    }
  };

  // Calendar is re-rendered on isOpen so that we could return to today between openings. TM.
  useEffect(() => {
    reassignVisibleDayCount();
  }, [isOpen, calendarApi]);

  const defaultCalendarSettings = {
    initialDate: visibleDayCount === calendarMaxVisibleDayCount ? weekStartDate : new Date(),
    slotDuration: '00:15',
    nowIndicator: true,
    initialView: 'timeGridFourDay',
    views: {
      timeGridFourDay: {
        type: 'timeGrid',
        duration: { days: visibleDayCount },
      },
    },
    dayHeaderFormat: {
      day: 'numeric',
      weekday: 'short',
    },
    dayCellClassNames: 'oneDay',
    slotLabelInterval: '01:00',
    slotLabelFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: false,
      hour12: false,
    },
    headerToolbar: {
      start: 'prev next',
      center: 'title',
      end: 'currentWeek',
    },
    eventTimeFormat: {
      hour: 'numeric',
      minute: '2-digit',
      meridiem: false,
      hour12: false,
    },
    slotLabelContent: (args: SlotLabelContentArg) => <Box width="43px">{args.text}</Box>,
    firstDay: visibleDayCount === calendarMaxVisibleDayCount ? 1 : nowDayIndex,
    titleFormat: { month: 'short', day: 'numeric' },
    allDaySlot: false,
    displayEventEnd: true,
    businessHours: {
      daysOfWeek: [0, 1, 2, 3, 4, 5, 6],
      startTime: undefined,
      endTime: undefined,
    },
    windowResize: reassignVisibleDayCount,
    scrollTime: calendarStartTimeObject,
  };

  let content;

  if (!scheduleReviewType) {
    content = (
      <ScheduleTimeslotCalendar
        defaultCalendarSettings={defaultCalendarSettings}
        // Since calendar is actually initiated in child component, we must pass back the reference to it
        onSetCalendarApi={(calendar: CalendarApi) => {
          calendarApi = calendar;
        }}
      />
    );
  } else if (availabilityFetched && availability.length === 0) {
    content = (
      <NoAvailabilitySlots
        scheduleReviewType={scheduleReviewType}
        setScheduleReviewType={setScheduleReviewType}
      />
    );
  } else if (availabilityFetched && availability.length > 0) {
    content = (
      <ScheduleCorrectionCalendar
        defaultCalendarSettings={defaultCalendarSettings}
        handleCancel={handleCancel}
        scheduleReviewType={scheduleReviewType}
        // Since calendar is actually initiated in child component, we must pass back the reference to it
        onSetCalendarApi={(calendar: CalendarApi) => {
          calendarApi = calendar;
        }}
        requestReference={requestReference}
      />
    );
  }

  return (
    <>
      {content}
      {isUserCalendarEventsLoading && (
        <Flex
          sx={{
            height: '100%',
            maxHeight: 'calc(100% - 140px)',
            position: 'absolute',
            width: 'calc(100% - 60px)',
            right: 0,
            zIndex: '999',
            bottom: 0,
          }}
        >
          <Loader />
        </Flex>
      )}
    </>
  );
};

export default LearnerCalendar;
