import { ReactElement, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { ReactSVG } from 'react-svg';
import { EventApi } from '@fullcalendar/react';
import { Button, Form, Modal } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { Box, Flex, Image } from 'rebass';

import exit from '@images/svg/exit.svg';
import garbageBin from '@images/svg/garbageBin.svg';
import info from '@images/svg/info.svg';

import { InputBox, InputType } from '../../../../../components/inputs/InputBox';
import { InputTimeRange } from '../../../../../components/inputs/InputTimeRange';
import { TCheckbox } from '../../../../../components/TCheckbox';
import { TTooltip } from '../../../../../components/TTooltip';
import { BodyText1 } from '../../../../../components/typography';
import { correctionDurationInMinutes, isDesktop } from '../../../../../Config';
import { DELETE_TIMESLOT_SUCCESS } from '../../../../../redux/constants/timeslot.constants';
import { AppState } from '../../../../../redux/reducers/index.reducer';
import {
  MixpanelClosingEvent,
  MixpanelEvent,
  MixpanelLocation,
  MixpanelProperty,
} from '../../../../../types/Mixpanel';
import NotificationType from '../../../../../types/NotificationType';
import errorNotification from '../../../../../utils/errorNotification';
import Mixpanel from '../../../../../utils/Mixpanel';
import { resetFormFieldErrors } from '../../../../../utils/resetFormFieldErrors';
import notification from '../../../../../utils/tNotification';

type FormData = {
  date: Dayjs;
  startTime: string;
  endTime: string;
};

export interface LocalTimeslot {
  id?: string;
  start: Dayjs;
  end: Dayjs;
}

interface Props {
  isOpen: boolean;
  events: EventApi[];
  handleCancel: () => void;
  handleDelete: (event: EventApi) => void;
  timeslotToEdit?: EventApi;
  handleCreate: (timeslots: LocalTimeslot[]) => void;
  handleEdit: (timeslot: LocalTimeslot, oldEvent: EventApi) => void;
}

const ManualTimeslotEntryModal = (props: Props): ReactElement => {
  const { isOpen, events, handleCancel, handleDelete, timeslotToEdit, handleCreate, handleEdit } =
    props;
  const [shouldExcludeEvents, setShouldExcludeEvents] = useState<boolean>(false);
  const isDeleteTimeslotLoading = useSelector(
    (state: AppState) => state.timeslotReducer.isDeleteTimeslotLoading,
  );
  const lastAction = useSelector((state: AppState) => state.timeslotReducer.action);
  const [form] = Form.useForm<FormData>();

  const modalTitle = (
    <Flex
      sx={{
        width: '90%',
        flexWrap: 'wrap',
      }}
    >
      <BodyText1 margin="0 8px 0 0">
        {`${timeslotToEdit ? 'Edit' : 'Create'} availability timeslot`}
      </BodyText1>
      <TTooltip
        content={
          timeslotToEdit
            ? 'Editing a timeslot only can stretch it within the boundaries of other events. This will never create new timeslots.'
            : 'This feature makes sure to mark you available whenever possible within the selected range. In some cases, it will create multiple timeslots.'
        }
        placement="top"
        trigger={isDesktop ? 'hover' : 'click'}
      >
        <ReactSVG src={info} />
      </TTooltip>
    </Flex>
  );

  const closeModal = (closeEvent: MixpanelClosingEvent) => {
    Mixpanel.track(MixpanelEvent.CLOSE_MODAL, {
      [MixpanelProperty.LOCATION]: MixpanelLocation.MANUAL_TIMESLOT_MODAL,
      [MixpanelProperty.CLOSING_EVENT]: closeEvent,
    });
    handleCancel();
  };

  const resetForm = (slot?: EventApi) => {
    form.setFieldsValue({
      date: slot ? dayjs(slot.start) : dayjs(),
      startTime: slot ? dayjs(slot.start).format('HH:mm') : '09:00',
      endTime: slot ? dayjs(slot.end).format('HH:mm') : '21:00',
    });
    setShouldExcludeEvents(false);
  };

  useEffect(() => {
    if (lastAction?.type === DELETE_TIMESLOT_SUCCESS) {
      handleCancel();
    }
  }, [lastAction]);

  useEffect(() => {
    if (isOpen) resetForm(timeslotToEdit);
  }, [timeslotToEdit, isOpen]);

  useEffect(() => {
    if (isOpen) {
      Mixpanel.track(MixpanelEvent.PAGE_VIEW, {
        [MixpanelProperty.LOCATION]: MixpanelLocation.MANUAL_TIMESLOT_MODAL,
        [MixpanelProperty.MINUTES_REMAINING]: timeslotToEdit
          ? dayjs(timeslotToEdit?.start).diff(dayjs(), 'minutes')
          : undefined,
      });
    }
  }, [isOpen]);

  const getNearestSchedulingTime = (): Dayjs => {
    let nearestSchedulingTime = dayjs();
    const nearestSchedulingTimeMinutes = nearestSchedulingTime.get('minutes');

    if (nearestSchedulingTimeMinutes < 15) {
      nearestSchedulingTime = nearestSchedulingTime.set('minutes', 15);
    } else if (nearestSchedulingTimeMinutes < 30) {
      nearestSchedulingTime = nearestSchedulingTime.set('minutes', 30);
    } else if (nearestSchedulingTimeMinutes < 45) {
      nearestSchedulingTime = nearestSchedulingTime.set('minutes', 45);
    } else if (nearestSchedulingTimeMinutes >= 45) {
      nearestSchedulingTime = nearestSchedulingTime.set('minutes', 0);
      nearestSchedulingTime = nearestSchedulingTime.set(
        'hours',
        nearestSchedulingTime.get('hours') + 1,
      );
    }

    nearestSchedulingTime = nearestSchedulingTime.set('seconds', 0);
    nearestSchedulingTime = nearestSchedulingTime.set('milliseconds', 0);

    return nearestSchedulingTime;
  };

  const timeRangesToTimeslot = (
    timeRange: Dayjs[],
    selectedDate: Dayjs,
    timeslotId?: string,
  ): LocalTimeslot => {
    const timeslotTimes: LocalTimeslot = {
      id: timeslotId,
      start: timeRange[0],
      end: timeRange[1],
    };

    if (
      selectedDate.isSame(dayjs(), 'day') &&
      // Timeslot is before now, thus need to readjust the nearest scheduling time
      timeslotTimes.start?.isBefore(dayjs())
    ) {
      timeslotTimes.start = getNearestSchedulingTime();
    }

    return timeslotTimes;
  };

  const isEventStartWithinTimeslot = (event: EventApi, timeslot: LocalTimeslot) =>
    // [), because event can start at the same time timeslot ends
    dayjs(event.start).isBetween(timeslot.start, timeslot.end, null, '[)');

  const isTimeslotStartWithinEvent = (event: EventApi, timeslot: LocalTimeslot) =>
    // [), because timeslot can start at the same time event ends
    dayjs(timeslot.start).isBetween(dayjs(event.start), dayjs(event.end), null, '[)');

  const getTimeslotsWithinRangeInclusive = (
    timeslots: LocalTimeslot,
    selectedDate: Dayjs,
  ): LocalTimeslot[] => {
    const selectedDayEvents = events
      .filter(
        (event: EventApi) =>
          // Event either starts or ends today
          dayjs(event.start).isSame(selectedDate, 'day') ||
          dayjs(event.end).isSame(selectedDate, 'day'),
      )
      .sort((a, b) => dayjs(a.start).diff(dayjs(b.start)));
    const localTimeslots = [timeslots];
    const eventsToExclude = shouldExcludeEvents
      ? selectedDayEvents
      : // We always exclude timeslots
        selectedDayEvents.filter((ev) => !ev.extendedProps?.category);

    eventsToExclude.forEach((event: EventApi) => {
      localTimeslots.forEach((time, index: number) => {
        if (isEventStartWithinTimeslot(event, time) || isTimeslotStartWithinEvent(event, time)) {
          localTimeslots[index] = { start: time.start, end: dayjs(event.start) };
          localTimeslots.push({
            start: dayjs(event.end),
            end: time.end,
          });
        }
      });
    });

    // Filter ghost slots that start at the same time they end
    return localTimeslots.filter((slot: LocalTimeslot) => !slot.start?.isSame(slot.end));
  };

  const filterTimeslotsByRestrictions = (timeslots: LocalTimeslot[]): LocalTimeslot[] => {
    // Filter slots that are shorter than min restriction
    return timeslots.filter(
      (slot: LocalTimeslot) =>
        dayjs(slot.end).diff(dayjs(slot.start), 'minutes') >= correctionDurationInMinutes,
    );
  };

  const mapRangeToSelectedDate = (date: Dayjs, startTime: string, endTime: string): Dayjs[] => {
    const [startHour, startMinute] = startTime.split(':').map(Number);
    const [endHour, endMinute] = endTime.split(':').map(Number);

    const startDateTime = date.hour(startHour).minute(startMinute).second(0).millisecond(0);
    const endDateTime = date.hour(endHour).minute(endMinute).second(0).millisecond(0);
    return [startDateTime, endDateTime];
  };

  const adjustSlotsToAllowedStartEndTimes = (timeslots: LocalTimeslot[]): LocalTimeslot[] => {
    // 15min
    const allowedIntervalInMillis = 15 * 60 * 1000;

    return timeslots.map((timeslot: LocalTimeslot) => {
      // Assure there are no seconds / milliseconds
      timeslot.start = timeslot.start.set('seconds', 0).set('milliseconds', 0);

      // Find time in milliseconds
      const startInMillis = new Date(timeslot.start.toDate()).getTime();

      // Calculate what's over the limit
      const startDiff = startInMillis % allowedIntervalInMillis;

      // If time was off the allowed point, add missing minutes
      timeslot.start = timeslot.start.add(
        startDiff === 0 ? 0 : allowedIntervalInMillis - startDiff,
        'milliseconds',
      );

      timeslot.end = timeslot.end.set('seconds', 0).set('milliseconds', 0);

      const endInMillis = new Date(timeslot.end.toDate()).getTime();
      const endDiff = endInMillis % allowedIntervalInMillis;

      timeslot.end = timeslot.end.subtract(endDiff, 'milliseconds');

      return timeslot;
    });
  };

  const submitCreateTimeslots = (openNewPostCreate?: boolean) => {
    form.validateFields().then((values) => {
      const { date, startTime, endTime } = values;

      const mappedRange = mapRangeToSelectedDate(date, startTime, endTime);
      const timeslot = timeRangesToTimeslot(mappedRange, date);
      const slotsWithinRangeInclusive = getTimeslotsWithinRangeInclusive(timeslot, date);
      const slotsAllowedByRestrictions = filterTimeslotsByRestrictions(slotsWithinRangeInclusive);
      const finalSlots = adjustSlotsToAllowedStartEndTimes(slotsAllowedByRestrictions);

      Mixpanel.track(MixpanelEvent.TIMESLOT_CREATE, {
        [MixpanelProperty.LOCATION]: MixpanelLocation.MANUAL_TIMESLOT_MODAL,
        [MixpanelProperty.IS_CREATE_AND_NEW]: openNewPostCreate,
        [MixpanelProperty.SLOTS_CREATE_COUNT]: finalSlots.length,
      });

      if (finalSlots.length === 0) {
        notification({
          type: NotificationType.WARNING,
          message: 'Note',
          description: 'No possible timeslots could be created within your selected time range.',
        });
      }

      handleCreate(finalSlots);

      if (openNewPostCreate) {
        resetForm();
      } else {
        handleCancel();
      }
    });
  };

  const submitUpdateTimeslot = () => {
    form.validateFields().then((values) => {
      const { date, startTime, endTime } = values;
      if (timeslotToEdit) {
        const mappedRange = mapRangeToSelectedDate(date, startTime, endTime);
        const timeslot = timeRangesToTimeslot(mappedRange, date, timeslotToEdit.id);
        Mixpanel.track(MixpanelEvent.TIMESLOT_UPDATE, {
          [MixpanelProperty.LOCATION]: MixpanelLocation.MANUAL_TIMESLOT_MODAL,
        });
        handleEdit(timeslot, timeslotToEdit);
        handleCancel();
      } else {
        errorNotification();
        handleCancel();
      }
    });
  };

  return (
    <Modal
      title={modalTitle}
      open={isOpen}
      onCancel={() => closeModal(MixpanelClosingEvent.GENERAL_CLOSE)}
      closeIcon={<Image src={exit} />}
      width="450px"
      centered
      className="manualAvailabilityModal"
      data-testid="manual-availability--modal"
      footer={[
        <Box
          key="box"
          sx={{
            button: {
              minWidth: '80px',
              '&:not(:first-of-type)': {
                marginLeft: '10px',
              },
            },
          }}
        >
          {timeslotToEdit ? (
            <Flex
              sx={{
                'button:nth-of-type(1)': {
                  marginRight: 'auto',
                  display: 'flex',
                  alignItems: 'center',
                  img: {
                    height: '13px',
                    marginRight: '8px',
                  },
                },
              }}
            >
              <Button
                type="text"
                size="middle"
                key="delete"
                loading={isDeleteTimeslotLoading}
                onClick={() => {
                  if (timeslotToEdit) {
                    handleDelete(timeslotToEdit);
                    Mixpanel.track(MixpanelEvent.TIMESLOT_DELETE, {
                      [MixpanelProperty.LOCATION]: MixpanelLocation.MANUAL_TIMESLOT_MODAL,
                      [MixpanelProperty.MINUTES_REMAINING]: dayjs(timeslotToEdit?.start).diff(
                        dayjs(),
                        'minutes',
                      ),
                    });
                  }
                }}
              >
                <Image src={garbageBin} />
                Delete slot
              </Button>
              <Button
                type="text"
                size="middle"
                key="back"
                data-testid="manual-availability-cancel--button"
                onClick={() => closeModal(MixpanelClosingEvent.CANCEL_BUTTON)}
              >
                Cancel
              </Button>
              <Button
                type="primary"
                size="middle"
                key="confirm"
                data-testid="manual-availability-submit--button"
                onClick={submitUpdateTimeslot}
              >
                Save
              </Button>
            </Flex>
          ) : (
            <>
              <Button
                type="text"
                size="middle"
                key="back"
                onClick={() => submitCreateTimeslots(true)}
              >
                Create & new
              </Button>
              <Button
                type="primary"
                size="middle"
                key="confirm"
                onClick={() => submitCreateTimeslots()}
              >
                Create
              </Button>
            </>
          )}
        </Box>,
      ]}
    >
      <Box marginBottom="51px">
        <Form
          form={form}
          validateTrigger="onSubmit"
          onValuesChange={(values) => resetFormFieldErrors(form, values)}
        >
          <Flex
            sx={{
              flexDirection: ['column', 'row', 'row'],
              gap: ['0px', '24px'],
            }}
          >
            <Box
              sx={{
                '.ant-picker': {
                  width: ['100%', '190px', '190px'],
                },
              }}
            >
              <InputBox
                name="date"
                label="Select date"
                type={InputType.DATE_TIME}
                placeholder="Select date"
                rules={[{ required: true, message: 'Please select the date' }]}
              />
            </Box>
            <Box
              sx={{
                width: ['100%', '204px', '204px'],
                '.ant-picker': {
                  width: ['100%', 'initial', 'initial'],
                },
                marginTop: ['0px', '27px'],
              }}
            >
              <InputTimeRange />
            </Box>
          </Flex>
          <Flex
            sx={{
              '> label:not(:last-of-type)': {
                marginRight: '8px',
              },
            }}
          >
            <TCheckbox
              name="excludeEvents"
              onChange={(e) => setShouldExcludeEvents(e.target.checked)}
              checked={shouldExcludeEvents}
            >
              Exclude events
            </TCheckbox>
          </Flex>
        </Form>
      </Box>
    </Modal>
  );
};

export default ManualTimeslotEntryModal;
