import { ReactElement, useContext, useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import FullCalendar, {
  CalendarApi,
  DateSelectArg,
  DayHeaderContentArg,
  EventApi,
  EventChangeArg,
  EventClickArg,
  EventContentArg,
  ViewContentArg,
} from '@fullcalendar/react';
import interactionPlugin from '@fullcalendar/interaction';
import rrulePlugin from '@fullcalendar/rrule';
import timeGridPlugin from '@fullcalendar/timegrid';
import { ThunkDispatch } from '@reduxjs/toolkit';
import { But<PERSON>, Tooltip } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import debounce from 'lodash/debounce';
import { Box, Flex } from 'rebass';

import iconPoints from '@images/svg/iconPoints.svg';
import iconResizeDown from '@images/svg/iconResizeDown.svg';
import iconResizeUp from '@images/svg/iconResizeUp.svg';

import CalendarEventBlock from '../../../../../components/CalendarEventBlock';
import { CalendarFilter } from '../../../../../components/CalendarFilter';
import ErrorUIComponent from '../../../../../components/ErrorUIComponent';
import { BodyText3 } from '../../../../../components/typography';
import {
  calendarEventIdPrefix,
  correctionDurationInMinutes,
  isDesktop,
  timeoutPostModalCloseInMs,
} from '../../../../../Config';
import { CalendarContext } from '../../../../../contexts/CalendarContext';
import { useUser } from '../../../../../hooks/useUser';
import { getCorrections } from '../../../../../redux/actions/correction.actions';
import {
  getDailyCorrectionLimit,
  getDailyCorrectionLimitRequest,
  resetDailyCorrectionLimit,
} from '../../../../../redux/actions/dailyCorrectionLimit.actions';
import {
  createTimeslot,
  CreateTimeslotError,
  CreateTimeslotSuccess,
  deleteTimeslot,
  updateTimeslot,
} from '../../../../../redux/actions/timeslot.actions';
import {
  getUserCalendarEvents,
  getUserCalendarEventsRequest,
} from '../../../../../redux/actions/userCalendar.actions';
import * as constants from '../../../../../redux/constants/timeslot.constants';
import { AppState } from '../../../../../redux/reducers/index.reducer';
import { AppDispatch } from '../../../../../redux/store';
import colors from '../../../../../theme/colors';
import { CorrectorFeedbackModalState } from '../../../../../types/Calendar';
import { CorrectionBooking, CorrectionStatus } from '../../../../../types/Correction';
import { DailyCorrectionLimit } from '../../../../../types/DailyCorrectionLimit';
import { CalendarEventCategory } from '../../../../../types/EventBase';
import { MixpanelEvent, MixpanelLocation, MixpanelProperty } from '../../../../../types/Mixpanel';
import NotificationType from '../../../../../types/NotificationType';
import { Timeslot } from '../../../../../types/Timeslot';
import { WidgetEvent } from '../../../../../types/WidgetEvent';
import calendarGoToToday from '../../../../../utils/calendarGoToToday';
import { correctionCalendarNavigationTracker } from '../../../../../utils/calendarNavigationTracker';
import errorNotification from '../../../../../utils/errorNotification';
import { mapEventsToCalendar } from '../../../../../utils/mapEventsToCalendar';
import Mixpanel from '../../../../../utils/Mixpanel';
import notification from '../../../../../utils/tNotification';
import eventIsErrored from '../../../utils/eventIsErrored';
import { mapCalendarEventsToWidgetEvents } from '../../../utils/mapCalendarEventsToWidgetEvents';
import { openReviewPage } from '../../../utils/openReviewPage';
import shouldOpenCorrectionOrEventModal from '../../../utils/shouldOpenCorrectionOrEventModal';
import { ReviewEvaluatorFeedbackModal } from '../../ReviewEvaluatorFeedbackModal';
import { CalendarTimezone } from '../CalendarTimezone';
import CorrectionMeetingOrEventModal from '../CorrectionMeetingOrEventModal';
import { LearnerCalendarHeader } from '../LearnerCalendarHeader';
import CorrectionLimitDayBlock from './CorrectionLimitDayBlock';
import { useDefaultDailyReviewLimit } from './hooks/useDefaultDailyReviewLimit';
import ManualTimeslotEntryModal, { LocalTimeslot } from './ManualTimeslotEntryModal';
import ScheduleTimeslotEventContent, { removeEventClass } from './ScheduleTimeslotEventContent';

type CreateTimeslotReturnType = ReturnType<
  (
    dispatch: ThunkDispatch<void, unknown, CreateTimeslotSuccess | CreateTimeslotError>,
  ) => Promise<Timeslot | undefined>
>;

let calendarApi: CalendarApi;

interface Props {
  defaultCalendarSettings: any;
  onSetCalendarApi: (calendarApi: CalendarApi) => void;
}

const ScheduleTimeslotCalendar = (props: Props): ReactElement => {
  const { defaultCalendarSettings, onSetCalendarApi } = props;

  const dispatch = useDispatch<AppDispatch>();
  const timeslots = useSelector((state: AppState) => state.timeslotReducer.timeslots);
  const userCalendarEvents = useSelector(
    (state: AppState) => state.userCalendarReducer.userCalendarEvents,
  );
  const allUserCorrectionSummaries = useSelector(
    (state: AppState) => state.correctionReducer.allUserCorrectionSummaries,
  );
  const allUserCorrectionSummariesError = useSelector(
    (state: AppState) => state.correctionReducer.allUserCorrectionSummariesError,
  );
  const getUserCalendarEventsError = useSelector(
    (state: AppState) => state.userCalendarReducer.getUserCalendarEventsError,
  );
  const isUserCalendarEventsLoading = useSelector(
    (state: AppState) => state.userCalendarReducer.isUserCalendarEventsLoading,
  );
  const getTimeslotFailed = useSelector(
    (state: AppState) => state.timeslotReducer.getTimeslotFailed,
  );
  const dailyLimits = useSelector(
    (state: AppState) => state.dailyCorrectionLimitReducer.dailyLimits,
  );

  const { user, isLearner } = useUser();
  const { correctionPoints } = user;
  const defaultDailyReviewLimit = useDefaultDailyReviewLimit(user.id);
  const { isMaximized, setMaximized, currentFilter } = useContext(CalendarContext);

  const [selectedNotification, setSelectedNotification] = useState<WidgetEvent>();
  const [openModal, setOpenModal] = useState(false);
  const [correctorFeedbackModalState, setCorrectorFeedbackModalState] =
    useState<CorrectorFeedbackModalState>({
      isOpen: false,
      reviewEventId: undefined,
    });
  const [manualEntryModalSettings, setManualEntryModalSettings] = useState<{
    isOpen: boolean;
    events: EventApi[];
    timeslotToEdit?: EventApi;
  }>({ isOpen: false, events: [] });

  const closeTimeslotModal = () => {
    setManualEntryModalSettings({ isOpen: false, events: [] });
  };

  const debounceSearch = useRef(
    debounce(({ from, until }) => {
      dispatch(
        getUserCalendarEvents({
          from,
          until,
        }),
      );
      dispatch(getCorrections(from, until));
      dispatch(getDailyCorrectionLimit(from, until));
    }, 250),
  );

  const triggerSearch = () => {
    if (calendarApi) {
      debounceSearch.current({
        from: dayjs(calendarApi.view.currentStart).startOf('day').toDate(),
        until: dayjs(calendarApi.view.currentEnd).endOf('day').toDate(),
      });
    }
  };

  const onDateChanged = () => {
    if (calendarApi) {
      triggerSearch();
      // It takes time for request to go out till actions and be called,
      // which causes a delay, thus we call it earlier to set loading to true
      dispatch(resetDailyCorrectionLimit());
      dispatch(getUserCalendarEventsRequest());
      dispatch(getDailyCorrectionLimitRequest());
    }
  };

  const isDateSameByMinute = (target: Dayjs, date: Dayjs) =>
    target.isSame(date, 'year') &&
    target.isSame(date, 'month') &&
    target.isSame(date, 'date') &&
    target.isSame(date, 'hour') &&
    target.isSame(date, 'minute');

  const findCalendarEventByTimes = (start: Dayjs, end: Dayjs): EventApi | null => {
    const allEvents = calendarApi?.getEvents();
    const evIndex = allEvents.findIndex((ev: EventApi) => {
      return isDateSameByMinute(dayjs(ev.start), start) && isDateSameByMinute(dayjs(ev.end), end);
    });
    return evIndex >= 0 ? allEvents[evIndex] : null;
  };

  const timeslotSuccessfullyCreated = (timeslot: Timeslot) => {
    // We remove an event which was created without a backend-supplied ID, and create a new one
    // w. an actual ID to make sure updating of this event without refreshing the page works.
    const event = findCalendarEventByTimes(dayjs(timeslot.start), dayjs(timeslot.end));
    if (event) {
      event.remove();
      // If we cannot find an event at that time, means it was not created, and we cannot remove it,
      // thus it is safe to just create a new one without deleting an old copy.
    }
    calendarApi?.addEvent({ ...timeslot, id: timeslot.id.toString() });
  };

  const timeslotCreateErrored = (start: string, end: string) => {
    const event = findCalendarEventByTimes(dayjs(start), dayjs(end));
    if (event) {
      // We found an event that was created, but errored, thus we should remove it.
      event.remove();
    }
    // If there is no event, we do not have a copy of in in frontend, thus should do nothhing.
  };

  const timeslotSuccessfullyDeleted = (timeslotId: number) => {
    calendarApi?.getEventById(timeslotId.toString())?.remove();
  };

  const timeslotUpdateErrored = (revert: () => void) => {
    // Update errored, we should revert the calendar change;
    revert();
  };

  useSelector((state: AppState) => {
    const { action } = state.timeslotReducer;

    switch (action?.type) {
      case constants.CREATE_TIMESLOT_SUCCESS:
        timeslotSuccessfullyCreated(action.timeslot);
        break;
      case constants.CREATE_TIMESLOT_ERROR:
        timeslotCreateErrored(action.timeslot.start, action.timeslot.end);
        break;
      case constants.DELETE_TIMESLOT_SUCCESS:
        timeslotSuccessfullyDeleted(action.timeslotId);
        break;
      case constants.UPDATE_TIMESLOT_ERROR:
        timeslotUpdateErrored(action.revert);
        break;
      default:
        break;
    }
  });

  const eventCreateUpdateRestrictions = (timeslot: Partial<Timeslot>) => {
    const timeslotStart = dayjs(timeslot.start);
    // Checks if event does not start in the past
    if (timeslotStart.isBefore(dayjs())) {
      notification({
        type: NotificationType.ERROR,
        message: 'Error',
        description: 'Cannot create a timeslot, it starts before now.',
      });
      return true;
    }

    // Checks if event is not shorter than than min length;
    if (dayjs(timeslot.end).diff(timeslotStart, 'minutes') < correctionDurationInMinutes) {
      notification({
        type: NotificationType.ERROR,
        message: 'Error',
        description: `Cannot create a timeslot, it should be at least ${correctionDurationInMinutes} min.`,
      });
      return true;
    }
    return false;
  };

  const eventRemoveRestrictions = (timeslot: Partial<Timeslot>) => {
    // Checks if event does not end in the past
    const nowDayjs = dayjs();
    if (dayjs(timeslot.end).isBefore(nowDayjs)) {
      notification({
        type: NotificationType.ERROR,
        message: 'Error',
        description: 'Cannot remove a timeslot, it already has passed.',
      });
      return true;
    }
    return false;
  };

  const addEvent = (
    cell: DateSelectArg | LocalTimeslot,
    silenceAlerts?: boolean,
  ): CreateTimeslotReturnType | undefined => {
    if ((cell as DateSelectArg).view) {
      (cell as DateSelectArg).view.calendar.unselect();
    }
    if (cell.start && cell.end) {
      const start = cell.start.toISOString();
      const end = cell.end.toISOString();

      if (eventCreateUpdateRestrictions({ start, end })) {
        return undefined;
      }

      calendarApi?.addEvent({ start, end });
      // Couldn't make it work with CreateTimeslotReturnType type, suggestions are welcomed.
      return dispatch(createTimeslot(start, end, silenceAlerts)) as any;
    }
    return undefined;
  };

  const removeEvent = (timeslot: EventApi) => {
    const start = timeslot.start?.toISOString();
    const end = timeslot.end?.toISOString();
    if (eventRemoveRestrictions({ start, end })) {
      return;
    }
    dispatch(deleteTimeslot(parseInt(timeslot.id, 10)));
  };

  const updateEvent = (timeslot: LocalTimeslot, oldEvent: EventApi) => {
    const { start, end } = oldEvent;
    const event = findCalendarEventByTimes(dayjs(start), dayjs(end));
    event?.setDates(timeslot.start.toDate(), timeslot.end.toDate());
  };

  const eventChangedValidateAndUpdate = (params: EventChangeArg | LocalTimeslot) => {
    const { event, oldEvent, revert } = params as EventChangeArg;
    const { id, start, end } = event;

    //Add a guard, not to fire event update, if just a correctionLimit changes
    if (event.extendedProps.correctionLimit !== oldEvent.extendedProps.correctionLimit) {
      return;
    }

    if (!start || !end) {
      revert();
      return;
    }

    const timeslot: Timeslot = {
      id: parseInt(id, 10),
      start: start.toISOString(),
      end: end.toISOString(),
    };

    if (eventCreateUpdateRestrictions(timeslot)) {
      revert();
      return;
    }

    dispatch(updateTimeslot(timeslot, revert));
  };

  const openManualTimeslotModal = (timeslotToEdit?: EventApi) => {
    if (calendarApi) {
      setManualEntryModalSettings({
        isOpen: true,
        events: calendarApi.getEvents(),
        timeslotToEdit,
      });
    }
  };

  const handleAlertsPostMassCreation = (promises: Promise<any>[]) => {
    Promise.all(promises).then((createdTimeslots: any[]) => {
      const successfulTimeslotsCount = createdTimeslots.filter((slot) => !!slot).length;
      const unsuccessfulTimeslotsCount = createdTimeslots.filter((slot) => !slot).length;
      if (unsuccessfulTimeslotsCount === 0 && successfulTimeslotsCount >= 1) {
        notification({
          type: NotificationType.SUCCESS,
          message: 'Success',
          description: `You have been marked available at your selected time range.`,
        });
      } else if (unsuccessfulTimeslotsCount >= 1 && successfulTimeslotsCount >= 1) {
        notification({
          type: NotificationType.WARNING,
          message: 'Note',
          description:
            'Only some parts of the selected time range have been succcessfully marked as available.',
        });
      } else if (unsuccessfulTimeslotsCount >= 1 && successfulTimeslotsCount === 0) {
        notification({
          type: NotificationType.ERROR,
          message: 'Error',
          description:
            'Something went wrong, it was not possible to mark you available at the selected time range.',
        });
      }
    });
  };

  useEffect(() => {
    Mixpanel.track(MixpanelEvent.PAGE_VIEW, {
      [MixpanelProperty.LOCATION]: MixpanelLocation.AVAILABILITY_SLOTS_MODAL,
    });
    correctionCalendarNavigationTracker(MixpanelLocation.AVAILABILITY_SLOTS_MODAL);
  }, []);

  useEffect(() => {
    if (!calendarApi) {
      return;
    }

    if (timeslots) {
      const registeredEventIds = calendarApi.getEvents().map((event) => event.id);

      timeslots
        .filter((timeslot) => {
          return !registeredEventIds.includes(timeslot.id.toString());
        })
        .forEach((timeslot: Timeslot) => {
          calendarApi.addEvent({
            id: timeslot.id.toString(),
            start: timeslot.start,
            end: timeslot.end,
          });
        });
    }

    if (dailyLimits && defaultDailyReviewLimit) {
      calendarApi.getEvents().forEach((event) => {
        const foundLimit = dailyLimits.find((limit: DailyCorrectionLimit) => {
          return dayjs(limit.day).isSame(dayjs(event.start), 'day');
        });

        const limitOfToday = foundLimit ? foundLimit.limit : defaultDailyReviewLimit;

        if (event.extendedProps.correctionLimit !== limitOfToday) {
          event.setExtendedProp('correctionLimit', limitOfToday);
        }
      });
    }
  }, [timeslots, calendarApi, dailyLimits, defaultDailyReviewLimit]);

  const baseTopOffset = isLearner ? 12 : 78;
  const baseMobileTopOffset = isLearner ? 62 : 72;
  const impersonationBannerHeight = user.isImpersonated && !isLearner ? 41 : 0;
  const top = baseTopOffset + impersonationBannerHeight;
  const mobileTop = baseMobileTopOffset + impersonationBannerHeight;

  const correctionControls = (
    <Flex
      sx={{
        position: 'absolute',
        right: isLearner ? '52px' : '20px',
        top: `${top}px`,
        '@media (max-width: 850px)': {
          '#button-min-max': {
            display: 'none',
          },
        },
        '@media (max-width: 768px)': {
          top: `${mobileTop}px`,
          right: '20px',
        },
        '@media (max-width: 375px)': {
          '.points-indicator': {
            display: 'none',
          },
        },
      }}
    >
      {isLearner && (
        <Tooltip
          title="Points are spent to get a review. To earn them, set your availability and perform peer review."
          mouseEnterDelay={0.3}
          placement="top"
          trigger={isDesktop ? 'hover' : 'click'}
          className="points-indicator"
        >
          <Flex
            sx={{
              alignItems: 'center',
              height: '38px',
            }}
          >
            <Box
              sx={{
                width: '16px',
                height: '11px',
                background: colors.colorText,
                margin: '0 5px 0 0',
                maskImage: `url("${iconPoints}")`,
              }}
            ></Box>
            <BodyText3>
              {correctionPoints}{' '}
              <span className="points-label">{`${
                correctionPoints === 1 ? 'point' : 'points'
              }`}</span>
            </BodyText3>
          </Flex>
        </Tooltip>
      )}

      <Button
        ghost
        style={{
          margin: '0 0 0 12px',
          fontWeight: 400,
          zIndex: 1000,
        }}
        disabled={isUserCalendarEventsLoading || !calendarApi}
        onClick={() => {
          if (!isUserCalendarEventsLoading && calendarApi) {
            setManualEntryModalSettings({ isOpen: true, events: calendarApi.getEvents() });
          }
        }}
      >
        Set availability
      </Button>

      {isLearner && (
        <Button
          type="link"
          id="button-min-max"
          style={{
            margin: '0 0 0 12px',
          }}
          onClick={() => {
            setMaximized(!isMaximized);
          }}
        >
          <Box
            className="icon-svg"
            sx={{
              width: '15px',
              height: '15px',
              maskImage: `url("${isMaximized ? iconResizeDown : iconResizeUp}")`,
              maskPosition: 'center',
              maskSize: '15px',
            }}
          ></Box>
        </Button>
      )}
    </Flex>
  );

  const calendar = (
    <>
      {isLearner && <LearnerCalendarHeader showHotKey={true}>Calendar</LearnerCalendarHeader>}
      {correctionControls}
      <FullCalendar
        // eslint-disable-next-line react/jsx-props-no-spreading
        {...defaultCalendarSettings}
        plugins={[timeGridPlugin, interactionPlugin, rrulePlugin]}
        events={
          userCalendarEvents && (allUserCorrectionSummaries || allUserCorrectionSummariesError)
            ? mapEventsToCalendar({
                events: userCalendarEvents,
                allUserCorrectionSummaries,
                eventCategoryFilter: currentFilter,
              })
            : null
        }
        editable
        eventResizableFromStart
        eventDisplay={isUserCalendarEventsLoading ? 'none' : 'auto'}
        eventContent={(args: EventContentArg) => {
          return args.event.extendedProps?.category ? (
            <CalendarEventBlock args={args} />
          ) : (
            <ScheduleTimeslotEventContent args={args} />
          );
        }}
        eventClassNames={(args: EventContentArg) => {
          return args.event.extendedProps.category || 'timeslot';
        }}
        eventClick={(arg: EventClickArg) => {
          // Remove event Box was clicked, Todo: review w. PLAT-1515
          if ((arg.jsEvent.target as Element).className.includes(removeEventClass)) {
            removeEvent(arg.event);
            Mixpanel.track(MixpanelEvent.TIMESLOT_DELETE, {
              [MixpanelProperty.LOCATION]: MixpanelLocation.AVAILABILITY_SLOTS_MODAL,
              [MixpanelProperty.MINUTES_REMAINING]: dayjs(arg.event.start).diff(dayjs(), 'minutes'),
            });
            return;
          }
          if (!arg.event.extendedProps.event) {
            // Clicked on timeslot, should open it's modal
            openManualTimeslotModal(arg.event);
            return;
          }
          const {
            extendedProps: {
              event: { id, start, category, summary, correctionStatus, isPeerCorrection },
            },
          } = arg.event;
          Mixpanel.track(MixpanelEvent.CALENDAR_EVENT_CLICK, {
            [MixpanelProperty.LOCATION]: MixpanelLocation.AVAILABILITY_SLOTS_MODAL,
            [MixpanelProperty.TYPE]: category,
            [MixpanelProperty.TITLE]: summary,
            [MixpanelProperty.MINUTES_REMAINING]: dayjs(start).diff(dayjs(), 'minutes'),
            [MixpanelProperty.REVIEW_STATE]: correctionStatus,
          });
          if (eventIsErrored({ id, category, start, correctionStatus })) {
            errorNotification();
            return;
          }
          if (
            category === CalendarEventCategory.CORRECTION &&
            correctionStatus === CorrectionStatus.PENDING_STUDENT_FEEDBACK &&
            !isPeerCorrection
          ) {
            setCorrectorFeedbackModalState({ isOpen: true, reviewEventId: id });
          } else if (
            shouldOpenCorrectionOrEventModal(category, correctionStatus, isPeerCorrection)
          ) {
            const eventOrItsOccurrences = mapCalendarEventsToWidgetEvents(
              [arg.event.extendedProps.event],
              // As calendar can be further/earlier in time, we must pass the current date
              { start: arg.view.activeStart, end: arg.view.activeEnd },
            );
            let foundEvent = eventOrItsOccurrences.find(
              (event: WidgetEvent) =>
                `${calendarEventIdPrefix}${event.id}` === arg.event.id &&
                dayjs(event.start).isSame(arg.event.start),
            );
            if (foundEvent) {
              // We have an exact event now and should add correctionStatus to it as well.
              foundEvent = {
                ...foundEvent,
                correctionStatus,
              };
              setSelectedNotification(foundEvent);
              setOpenModal(true);
            } else {
              errorNotification();
            }
          } else {
            // We should open a correction page, instead of modal
            const reviewId = arg.event.extendedProps.event.customProperties?.reviewId;
            openReviewPage(reviewId);
          }
        }}
        eventConstraint="businessHours"
        eventChange={eventChangedValidateAndUpdate}
        eventDragStop={() => {
          Mixpanel.track(MixpanelEvent.TIMESLOT_UPDATE, {
            [MixpanelProperty.LOCATION]: MixpanelLocation.AVAILABILITY_SLOTS_MODAL,
          });
        }}
        defaultTimedEventDuration={`00:${correctionDurationInMinutes}`}
        selectable={true}
        selectConstraint="businessHours"
        select={addEvent}
        selectMirror
        longPressDelay={500}
        viewDidMount={(args: ViewContentArg) => {
          calendarApi = args.view.calendar;
          onSetCalendarApi(calendarApi);
        }}
        datesSet={() => onDateChanged()}
        dayHeaderContent={(arg: DayHeaderContentArg) => {
          const dayCorrections =
            allUserCorrectionSummaries?.filter(
              (correction: CorrectionBooking) =>
                dayjs(correction.start).startOf('day').isSame(dayjs(arg.date).startOf('day')) &&
                correction.isPeerCorrection &&
                correction.status !== CorrectionStatus.EVALUATOR_CANCELED &&
                correction.status !== CorrectionStatus.STUDENT_CANCELED,
            ) || [];

          const currentDayTimeslots = timeslots.filter((timeslot) => {
            return dayjs(timeslot.start).isSame(arg.date, 'date');
          });

          return (
            defaultDailyReviewLimit && (
              <CorrectionLimitDayBlock
                arg={arg}
                dayCorrectionCount={dayCorrections.length}
                timeslots={currentDayTimeslots}
                defaultDailyReviewLimit={defaultDailyReviewLimit}
              />
            )
          );
        }}
        customButtons={{
          currentWeek: {
            text: 'Today',
            click: () => calendarGoToToday(calendarApi, MixpanelLocation.AVAILABILITY_SLOTS_MODAL),
          },
        }}
        headerToolbar={{
          start: 'currentWeek prev next',
          center: 'title',
          end: null,
        }}
      />
    </>
  );

  return getTimeslotFailed || getUserCalendarEventsError ? (
    <Flex
      sx={{
        justifyContent: 'center',
        flexDirection: 'column',
        alignItems: 'center',
        height: ['100vh', '800px'],
        maxHeight: 'calc(100vh - 50px)',
      }}
    >
      <ErrorUIComponent />
    </Flex>
  ) : (
    <>
      {calendar}
      <CalendarTimezone />
      <CalendarFilter />
      <ReviewEvaluatorFeedbackModal
        isOpen={correctorFeedbackModalState.isOpen}
        reviewEventId={correctorFeedbackModalState.reviewEventId}
        handleCancel={() => {
          setCorrectorFeedbackModalState({ isOpen: false, reviewEventId: undefined });
        }}
      />
      <CorrectionMeetingOrEventModal
        selectedNotification={selectedNotification as WidgetEvent}
        openModal={openModal}
        handleClose={() => {
          setOpenModal(false);
          setTimeout(() => {
            // This timeout is to make sure that notification is set to undefined only after modal animation has ended
            setSelectedNotification(undefined);
          }, timeoutPostModalCloseInMs);
        }}
        onReviewCancel={triggerSearch}
      />

      <ManualTimeslotEntryModal
        isOpen={manualEntryModalSettings.isOpen}
        events={manualEntryModalSettings.events}
        timeslotToEdit={manualEntryModalSettings.timeslotToEdit}
        handleCancel={() => closeTimeslotModal()}
        handleDelete={(event: EventApi) => removeEvent(event)}
        handleCreate={(slots: LocalTimeslot[]) => {
          if (slots.length > 1) {
            const promises: CreateTimeslotReturnType[] = [];

            slots.forEach((slot: LocalTimeslot) =>
              promises.push(addEvent(slot, true) as CreateTimeslotReturnType),
            );

            handleAlertsPostMassCreation(promises);
          } else if (slots.length === 1) {
            addEvent(slots[0]);
          }
        }}
        handleEdit={(newSlot: LocalTimeslot, oldEvent: EventApi) => updateEvent(newSlot, oldEvent)}
      />
    </>
  );
};

export default ScheduleTimeslotCalendar;
