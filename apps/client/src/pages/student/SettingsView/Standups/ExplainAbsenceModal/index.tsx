import { ReactElement, useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { Button, Form, Input, Modal } from 'antd';
import { AxiosError } from 'axios';
import { Box, Flex } from 'rebass';

import ConfirmationModal from '../../../../../components/ConfirmationModal';
import { InputBox, InputType, OptionType } from '../../../../../components/inputs/InputBox';
import { Heading3 } from '../../../../../components/typography';
import { resetFormFieldErrors } from '../../../../../utils/resetFormFieldErrors';
import { absenceExplanationOptions } from '../constants';
import { AbsenceSituation, AttendanceItems } from '../types';
import { getWeeksSelectOptions, WeeksOptions } from './getWeeksSelectOptions';
import {
  Params,
  setStudentLearningMilestoneExplanations,
} from './queries/setStudentLearningMilestoneExplanations.queries';
import { WeeksSelectDropdown } from './WeeksSelectDropdown';

const isLastWeekSelected = (weeks: WeeksOptions) =>
  weeks.length === 1 && weeks[0].label === 'Last week';

type AbsenceForm = {
  weeks: WeeksOptions;
  situation: AbsenceSituation;
  reasoning: string;
};

interface Props {
  userId: number;
  onClose: () => void;
  items: Array<AttendanceItems>;
  preselectedWeeks?: WeeksOptions;
  onSubmitSuccess: () => void;
}

const ExplainAbsenceModal = (props: Props): ReactElement => {
  const { userId, onClose, items, preselectedWeeks = [], onSubmitSuccess } = props;
  const [form] = Form.useForm<AbsenceForm>();
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [weeksSelectValues, setWeekSelectValues] = useState<WeeksOptions>(preselectedWeeks);
  const [weeksSelectErrors, setWeekSelectErrors] = useState<Array<string>>([]);
  const weeksSelectOptions = getWeeksSelectOptions(items);

  const { mutateAsync, isPending } = useMutation<void, AxiosError, Params>({
    mutationKey: ['student-learning-milestone-explanations'],
    mutationFn: setStudentLearningMilestoneExplanations,
  });

  const handleCancel = () => {
    const isFieldsTouched = form.isFieldsTouched();

    if (isFieldsTouched) return setShowConfirmationModal(true);
    return onClose();
  };

  const handleWeeksChange = (weeks: WeeksOptions) => {
    // need to sync controlled state to form state due to custom dropdown
    form.setFields([
      {
        name: 'weeks',
        touched: true,
        value: weeks,
      },
      // reset situation if anything other than last week was selected
      ...(!isLastWeekSelected(weeks) &&
      form.getFieldValue('situation') === AbsenceSituation.ATTENDED
        ? [
            {
              name: 'situation' as const,
              value: undefined,
            },
          ]
        : []),
    ]);
    setWeekSelectValues(weeks);
    setWeekSelectErrors([]);
  };

  const onValuesChange = (values: Partial<AbsenceForm>) => {
    // reset FE validation errors
    resetFormFieldErrors(form, values);
  };

  const handleSubmit = async () => {
    try {
      const { reasoning, situation, weeks } = await form.validateFields();

      await mutateAsync({
        userId,
        milestoneIds: weeks.map(({ milestoneId }) => milestoneId),
        situation,
        reasoning,
      });

      onSubmitSuccess();
    } catch (err) {
      // need to sync controlled state to form state due to custom dropdown
      setWeekSelectErrors(form.getFieldError('weeks'));
    }
  };

  return (
    <>
      <ConfirmationModal
        titleText="Leave without saving?"
        confirmationText="Changes you made so far will not be saved. Are you sure?"
        isOpen={showConfirmationModal}
        confirmButtonText="Yes, leave"
        cancelButtonText="Keep editing"
        handleCancel={() => setShowConfirmationModal(false)}
        handleOk={onClose}
      />
      <Modal
        open={true}
        width={600}
        centered
        onCancel={handleCancel}
        footer={null}
        styles={{
          body: {
            padding: '44px 24px 24px 24px',
          },
        }}
      >
        <Heading3 margin="0 0 16px 0" testId="explain-absence-header">
          Log absence
        </Heading3>
        <Form
          form={form}
          onValuesChange={onValuesChange}
          initialValues={{ weeks: preselectedWeeks }}
          validateTrigger={['onSubmit']}
        >
          {/* Hidden field to sync custom weeks dropdown to form */}
          <Form.Item
            name="weeks"
            hidden
            rules={[{ required: true, message: 'Please select absence weeks' }]}
          >
            <Input type="hidden" />
          </Form.Item>
          <Box
            sx={{
              '.ant-select-selection-item': {
                fontSize: '12px',
              },
            }}
          >
            <InputBox
              validateStatus={weeksSelectErrors.length ? 'error' : ''}
              help={weeksSelectErrors.map((message) => (
                <div key={message}>{message}</div>
              ))}
              type={InputType.SELECT_MULTIPLE}
              formItemStyle={{ marginBottom: 16 }}
              value={weeksSelectValues as any as OptionType[]}
              onChange={handleWeeksChange}
              placeholder="Select..."
              label="When?"
              testId="absence-weeks"
              dropdownRender={() => (
                <WeeksSelectDropdown
                  options={weeksSelectOptions}
                  values={weeksSelectValues}
                  onChange={handleWeeksChange}
                />
              )}
            />
          </Box>
          <InputBox
            name="situation"
            type={InputType.SELECT}
            formItemStyle={{ marginBottom: 16 }}
            placeholder="Select..."
            label="What's your situation?"
            testId="absence-situation"
            rules={[
              { required: true, message: 'Please select one of the reasons' },
              //
            ]}
            options={[
              ...absenceExplanationOptions,
              // allow selecting only for last week
              ...(isLastWeekSelected(weeksSelectValues)
                ? [{ value: AbsenceSituation.ATTENDED, label: 'I did attend stand-up' }]
                : []),
            ]}
          />
          <InputBox
            formItemStyle={{ marginBottom: 0 }}
            name="reasoning"
            label="Provide more details"
            testId="absence-reasoning"
            type={InputType.TEXT_AREA}
            rules={[
              { required: true, message: 'Please give us more details' },
              { min: 5, max: 5000, message: 'Please enter between 5 and 5000 characters' },
            ]}
            placeholder="Tell us about your situation."
            rows={6}
          />
          <Flex sx={{ marginTop: '40px', justifyContent: 'flex-end', gap: '4px' }}>
            <Button key="back" onClick={handleCancel} type="text">
              Cancel
            </Button>
            <Button
              data-testid="submit-explanation--button"
              key="submit"
              type="primary"
              onClick={handleSubmit}
              style={{ minWidth: '80px' }}
              loading={isPending}
            >
              Submit
            </Button>
          </Flex>
        </Form>
      </Modal>
    </>
  );
};

export default ExplainAbsenceModal;
