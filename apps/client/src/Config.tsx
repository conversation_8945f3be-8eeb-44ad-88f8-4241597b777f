import { hotjar } from 'react-hotjar';
import { RuleNames } from 'react-password-checklist';

import './initializeDayjs';
import versionInfo from '../../../version.json';
import CorrectRequirement from './images/svg/correctRequirement.svg';
import IncorrectRequirement from './images/svg/incorrectRequirement.svg';
import { MeetingRecordingType } from './types/CalendarEvent';
import { StudentBillingType } from './types/StudentBillingType';
import scrollToTop from './utils/scrollToTop';

export const appVersion = versionInfo.version;

export enum Environment {
  LOCAL = 'local',
  LOCAL_BACKEND = 'local-backend',
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
}

const getLocalBaseWebURL = () =>
  `http://${import.meta.env.VITE_HOST || 'localhost'}:${import.meta.env.VITE_PORT || 3000}`;

const urls: Record<
  Environment,
  { baseWebUrl: string; baseUrl: string; githubSubmissionsBaseUrl: string }
> = {
  local: {
    baseWebUrl: getLocalBaseWebURL(),
    // proxy middleware is used in dev server
    baseUrl: '/api',
    githubSubmissionsBaseUrl: `https://github.com/turing-platform-test`,
  },
  'local-backend': {
    baseWebUrl: getLocalBaseWebURL(),
    baseUrl: `http://${import.meta.env.VITE_API_HOST || 'localhost'}:${
      import.meta.env.VITE_API_PORT || 3001
    }`,
    githubSubmissionsBaseUrl: `https://github.com/turing-platform-test`,
  },
  development: {
    baseWebUrl: 'https://dev.turingcollege.com',
    baseUrl: 'https://api.dev.turingcollege.com',
    githubSubmissionsBaseUrl: 'https://github.com/turing-platform-test',
  },
  staging: {
    baseWebUrl: 'https://staging.turingcollege.com',
    baseUrl: 'https://api.staging.turingcollege.com',
    githubSubmissionsBaseUrl: 'https://github.com/TuringCollegeSubmissionsDev',
  },
  production: {
    baseWebUrl: 'https://intra.turingcollege.com',
    baseUrl: 'https://api.intra.turingcollege.com',
    githubSubmissionsBaseUrl: 'https://github.com/TuringCollegeSubmissions',
  },
};

export const environment = import.meta.env.VITE_ENVIRONMENT?.toLocaleLowerCase() as Environment;
export const { baseUrl, baseWebUrl, githubSubmissionsBaseUrl } = urls[environment] || {};

// Cloudinary
export const cloudinaryCloudName = 'turingcollege';
export const defaultUserImage = 'student_avatars/s9uwybc9mxf0m8pxyb58';

// Mixpanel
let token: string | undefined;
if (import.meta.env.VITE_IGNORE_MIXPANEL !== 'true') {
  token =
    environment === 'production'
      ? import.meta.env.VITE_MIXPANEL_PRODUCTION_TOKEN
      : import.meta.env.VITE_MIXPANEL_DEVELOPMENT_TOKEN;
}
export const mixpanelToken = token;

// GrowthBook (feature flags)
const growthBookClientKeys: Record<Environment, string> = {
  local: import.meta.env.VITE_GROWTHBOOK_DEVELOPMENT_CLIENT_ID,
  'local-backend': import.meta.env.VITE_GROWTHBOOK_DEVELOPMENT_CLIENT_ID,
  development: import.meta.env.VITE_GROWTHBOOK_DEVELOPMENT_CLIENT_ID,
  staging: import.meta.env.VITE_GROWTHBOOK_STAGING_CLIENT_ID,
  production: import.meta.env.VITE_GROWTHBOOK_PRODUCTION_CLIENT_ID,
};
export const growthBookClientKey = growthBookClientKeys[environment];
export const growthBookApiHost = import.meta.env.VITE_GROWTHBOOK_API_HOST;

// Satismeter (survey manager) key
let satismeterKeyValue;

// External links w. default values to assure we don't
// access prod users in metabase/salesforce via dev/staging
let externalLearnerLinkValues = {
  metabase: 'https://turingcollege.com',
  metabaseDfe: 'https://turingcollege.com',
  salesforce: 'https://turingcollege.com',
};

if (environment === 'production') {
  satismeterKeyValue = import.meta.env.VITE_SATISMETER_PRODUCTION_KEY;
  externalLearnerLinkValues = {
    metabase: 'https://metabase.turingcollege.com/dashboard/6-learner-analysis',
    metabaseDfe: 'https://metabase.turingcollege.com/dashboard/248-learner-analysis-uk',
    salesforce: 'https://turingcollege.lightning.force.com/apex/RedirectPage',
  };
} else {
  satismeterKeyValue = import.meta.env.VITE_SATISMETER_DEVELOPMENT_KEY;
}
export const satismeterKey = satismeterKeyValue;
export const externalLearnerLinks = externalLearnerLinkValues;

// HotJar
let hotJarIdValue;
let hotJarSvValue;
if (environment === 'production') {
  hotJarIdValue = import.meta.env.VITE_HOTJAR_PRODUCTION_ID;
  hotJarSvValue = 6;
} else {
  hotJarIdValue = import.meta.env.VITE_HOTJAR_DEVELOPMENT_ID;
  hotJarSvValue = 6;
}
export const hotJarId = Number(hotJarIdValue);
export const hotJarSv = hotJarSvValue;
hotjar.initialize({ id: hotJarId, sv: hotJarSv });

// Intercom
export const intercomAppId = import.meta.env.VITE_INTERCOM_APP_ID || '';

export const correctionDurationInMinutes = 45;

export const isDesktop = window.screen.width > 991;
export const numOfNotificationDaysInWidget = 2;
export const calendarEventIdPrefix = 'calendar-event-';
export const calendarMaxVisibleDayCount = 7;
export const dailyCorrectionLimits = {
  min: 1,
  max: 9,
};
export const roadmapLimits = {
  maxModuleSlots: 14,
  minModuleSlots: 1,
  maxModuleSprints: 6,
  minModuleSprints: 1,
};
// This is used to clear values or do smth. similar only post modal closing animation
export const timeoutPostModalCloseInMs = 200;
export const calendarOpenTimeHoursAboveNow = 1;

export const generalLinks = {
  communityHelpLink: 'https://discord.com/channels/722762519911071744/769151570687688704',
  afaLearnerGuide: 'https://turingcollege.atlassian.net/wiki/spaces/ALG/overview',
  dfeLearnerGuide: 'https://turingcollege.atlassian.net/wiki/spaces/BPBTCLG/overview',
  defaultLearnerGuide: 'https://turingcollege.atlassian.net/wiki/spaces/DLG/overview',
  calendarEventsInstructions:
    'https://turingcollege.atlassian.net/wiki/spaces/DLG/pages/537395830/Subscribe+to+events+calendar',
  standupsReadMoreLink:
    'https://turingcollege.atlassian.net/wiki/spaces/DLG/pages/760774730/Stand-up+meetings',
  stlGuidebook:
    'https://docs.google.com/document/d/1RAkxsEbppdTXq_-9CcJngObfL7lBs_HtGLDX9bdjG0Y/edit#heading=h.r169bliguyqz',
  maximumAccessInfo:
    'https://turingcollege.atlassian.net/wiki/spaces/DLG/pages/1135607836/Maximum+access+to+the+platform',
  upcomingSuspensionInfo: 'https://app.intercom.com/a/apps/mbc79ucc/articles/articles/9016503/show',
  learningActivityTrackingInfoLink: 'https://turingcollege.atlassian.net/wiki/x/DQDEVw',
  learningTimeTracking:
    'https://turingcollege.atlassian.net/wiki/spaces/DLG/pages/1490649094/Learning+time+tracking',
  learningActivities:
    'https://turingcollege.atlassian.net/wiki/spaces/DLG/pages/537396953/Stand-ups+Open+Sessions+Virtual+Classroom',
  uztRequirements:
    'https://turingcollege.atlassian.net/wiki/spaces/DLG/pages/670171220/UZT+infopack+conditions',
  uztRequiredMaterial:
    'https://turingcollege.atlassian.net/wiki/spaces/DLG/pages/670171220/UZT+infopack+conditions#Reminder%3A',
  uztContractTypes:
    'https://turingcollege.atlassian.net/wiki/spaces/DLG/pages/670171220/UZT+infopack+conditions#UZT-contract-types',
  dfePrivacyPolicy: 'https://turingcollege.atlassian.net/wiki/x/GQBBgQ',
  dfeTermsAndConditions: 'https://turingcollege.atlassian.net/wiki/x/GABAgQ',
};

export const hardSkillsUrlBase = 'hardskills';
export const endorsementUrlBase = 'endorsement';
export const minModulesCompletedForEndorsement = 3;
export const hardSkillsSkeletonTabCount = 4;
export const hardSkillsSkeletonPartCount = 4;
export const passwordSettings = {
  minLength: 8,
  maxLength: 25,
  rules: ['minLength', 'maxLength', 'lowercase', 'capital', 'number'] as RuleNames[],
  messages: {
    minLength: 'At least 8 characters in length',
    maxLength: 'No longer than 25 characters',
    lowercase: 'Lower case letters (a-z)',
    capital: 'Upper case letters (A-Z)',
    number: 'Numbers (0-9)',
  },
  iconComponents: {
    ValidIcon: CorrectRequirement,
    InvalidIcon: IncorrectRequirement,
  },
};
export const eventDescriptionMaxLength = 350;
export const maxNumberOfGeneratedEvents = 101;
export const defaultMeetingOptions = {
  recording: MeetingRecordingType.NONE,
  isWaitingRoom: false,
  isJoinBeforeHost: true,
  alternativeHosts: [],
};
export const endorsementFAQLink =
  'https://intercom.help/turing-college/en/collections/3400237-endorsement';
export const defaultDiscordAuthPart = 0;
export const resumeLinkBase = `${baseWebUrl}/s`;
export const resumePreviewLinkBase = `${baseWebUrl}/s/preview`;
export const resumeHighlightedEvaluatorsCount = 3;
export const whatsACodeReviewLink =
  'https://intercom.help/turing-college/en/articles/6501442-what-s-code-review';

export const learningRestrictedTooltipTitle = 'Your learning is restricted';
export const discordIDLimits = {
  min: 17,
  max: 20,
};

const tablePaginationDefaults = {
  showSizeChanger: true,
  onChange: scrollToTop,
  onShowSizeChange: scrollToTop,
};
export const tablePagination = {
  small: {
    ...tablePaginationDefaults,
    defaultPageSize: 5,
    pageSizeOptions: ['5', '10', '25'],
  },
  medium: {
    ...tablePaginationDefaults,
    defaultPageSize: 10,
    pageSizeOptions: ['10', '20', '50', '100'],
  },
  large: {
    ...tablePaginationDefaults,
    defaultPageSize: 15,
    pageSizeOptions: ['15', '50', '100'],
  },
};

export const discordSlotRequestChannelsLinksPerBatch = {
  DS: 'https://discord.com/channels/722762519911071744/1065537816156704770',
  DE: 'https://discord.com/channels/722762519911071744/1065538580694433822',
  DA: 'https://discord.com/channels/722762519911071744/1065537967499771934',
  WD: 'https://discord.com/channels/722762519911071744/1065539071578996736',
  AI: 'https://discord.com/channels/722762519911071744/1285905386347560972',
  AE: 'https://discord.com/channels/722762519911071744/1326677682758356992',
  DM: 'https://discord.com/channels/722762519911071744/1209838950756126720',
};

export const defaultDiscordSlotRequestChannelLink =
  'https://discord.com/channels/722762519911071744/769151570687688704';

// Sentry config
export const sentryTraceSampleRate = import.meta.env.VITE_SENTRY_TRACE_SAMPLE_RATE;
export const sentryReplaysSessionSampleRate = import.meta.env
  .VITE_SENTRY_REPLAYS_SESSION_SAMPLE_RATE;
export const sentryReplaysOnErrorSampleRate = import.meta.env
  .VITE_SENTRY_REPLAYS_ON_ERROR_SAMPLE_RATE;

// Deadline extensions
type DeadlineExtensionLimit = {
  environment: Environment;
  courseId: number;
  limit: number;
  billingType?: StudentBillingType;
};

// TODO: (PLAT-7081) this config needs to be moved to be managed by admins
export const deadlineExtensionLimits: DeadlineExtensionLimit[] = [
  //
  // AI for Business Analytics (one course for testing purposes in dev environments)
  //
  { environment: Environment.DEVELOPMENT, courseId: 101, limit: 10 },
  {
    environment: Environment.DEVELOPMENT,
    courseId: 101,
    limit: 5,
    billingType: StudentBillingType.AFA,
  },
  {
    environment: Environment.DEVELOPMENT,
    courseId: 101,
    limit: 60,
    billingType: StudentBillingType.DFE,
  },
  { environment: Environment.STAGING, courseId: 42, limit: 10 },
  {
    environment: Environment.STAGING,
    courseId: 42,
    limit: 5,
    billingType: StudentBillingType.AFA,
  },
  {
    environment: Environment.STAGING,
    courseId: 42,
    limit: 60,
    billingType: StudentBillingType.DFE,
  },
  //
  // Data Analytics
  //
  {
    environment: Environment.PRODUCTION,
    courseId: 19,
    limit: 21,
    billingType: StudentBillingType.AFA,
  },
  //
  // Digital Marketing
  //
  {
    environment: Environment.PRODUCTION,
    courseId: 22,
    limit: 20,
    billingType: StudentBillingType.AFA,
  },
  //
  // AI for Business Analytics
  //
  { environment: Environment.PRODUCTION, courseId: 21, limit: 10 },
  {
    environment: Environment.PRODUCTION,
    courseId: 21,
    limit: 5,
    billingType: StudentBillingType.AFA,
  },
  //
  // Data Science
  //
  { environment: Environment.PRODUCTION, courseId: 1, limit: 50 },
  { environment: Environment.PRODUCTION, courseId: 2, limit: 50 },
  {
    environment: Environment.PRODUCTION,
    courseId: 17,
    limit: 25,
    billingType: StudentBillingType.AFA,
  },
  //
  // Python & Data Literacy
  //
  { environment: Environment.PRODUCTION, courseId: 3, limit: 50 },
  { environment: Environment.PRODUCTION, courseId: 4, limit: 50 },
  //
  // Web Development
  //
  { environment: Environment.PRODUCTION, courseId: 9, limit: 50 },
  //
  // AI Engineering
  //
  { environment: Environment.PRODUCTION, courseId: 23, limit: 10 },
  {
    environment: Environment.PRODUCTION,
    courseId: 23,
    limit: 5,
    billingType: StudentBillingType.AFA,
  },
  // Level 4 Data Analyst Apprenticeship
  {
    environment: Environment.PRODUCTION,
    courseId: 29,
    limit: 60,
    billingType: StudentBillingType.DFE,
  },
];

export const defaultDeadlineExtensionLimit = 40;
