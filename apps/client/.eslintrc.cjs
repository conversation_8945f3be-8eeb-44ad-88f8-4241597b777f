/* eslint-env node */
// eslint-disable-next-line @typescript-eslint/no-var-requires,no-undef
const restrictedGlobals = require('confusing-browser-globals');

// eslint-disable-next-line no-undef
module.exports = {
  env: {
    browser: true,
    es6: true,
  },
  globals: {
    Atomics: 'readonly',
    SharedArrayBuffer: 'readonly',
  },
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: ['tsconfig.json', 'e2e/tsconfig.e2e.json'],
    // eslint-disable-next-line no-undef
    tsconfigRootDir: __dirname,
  },
  // eslint-disable-next-line no-undef
  plugins: [
    '@typescript-eslint',
    'import',
    'jsx-a11y',
    'prettier',
    'react',
    'simple-import-sort',
    'unused-imports',
  ],
  extends: [
    // By extending from a plugin config, we can get recommended rules without having to add them manually.
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:import/recommended',
    'plugin:jsx-a11y/recommended',
    'airbnb-typescript',
    'plugin:storybook/recommended',
    'plugin:react-hooks/recommended',
    // This disables the formatting rules in ESLint that Prettier is going to be responsible for handling.
    // Make sure it's always the last config, so it gets the chance to override other configs.
    'eslint-config-prettier',
  ],
  rules: {
    '@typescript-eslint/explicit-function-return-type': 'off',
    '@typescript-eslint/no-explicit-any': 'off',
    '@typescript-eslint/no-unused-vars': 'error',
    'unused-imports/no-unused-imports': 'error',
    '@typescript-eslint/naming-convention': [
      'error',
      {
        selector: 'typeLike',
        format: ['StrictPascalCase'],
      },
      {
        selector: 'enum',
        format: ['PascalCase'],
      },
      {
        selector: 'enumMember',
        format: ['UPPER_CASE'],
      },
    ],
    'import/extensions': [
      'error',
      'ignorePackages',
      {
        js: 'never',
        jsx: 'never',
        ts: 'never',
        tsx: 'never',
      },
    ],
    'import/no-extraneous-dependencies': [
      'error',
      {
        devDependencies: [
          '**/tests/**',
          '**/*.test.*',
          'vite.config.ts',
          '.eslintrc.js',
          'src/stories/**',
          '.storybook/**',
        ],
        optionalDependencies: false,
      },
    ],
    'jsx-a11y/no-autofocus': 'off',
    'no-plusplus': ['error', { allowForLoopAfterthoughts: true }],
    'no-restricted-globals': ['error', 'alert', ...restrictedGlobals],
    'prettier/prettier': 'error',
    'react/jsx-curly-newline': 'off',
    'react/jsx-filename-extension': [1, { extensions: ['.js', '.jsx', '.ts', '.tsx'] }],
    'react/jsx-uses-react': 'off',
    'react/no-unused-prop-types': 'error',
    'react/react-in-jsx-scope': 'off',
    'react/prop-types': 'off',
    'react/require-default-props': 'off',
    'simple-import-sort/exports': 'error',
    'simple-import-sort/imports': [
      'error',
      {
        groups: [
          [
            '^react',
            // Exception to alphabetical sorting as calendar needs to be imported before plugins
            '@fullcalendar/react',
            '^@?\\w',
          ],
          // aliases
          ['^@images/', '^@features/'],
        ],
      },
    ],
    'default-case': 'error',
    'max-params': ['error', 3],
    'react-hooks/exhaustive-deps': 'off',
    'react-hooks/rules-of-hooks': 'error',
  },
  overrides: [
    {
      files: ['*.reducer.tsx'],
      rules: {
        '@typescript-eslint/default-param-last': 'off',
      },
    },
  ],
  settings: {
    react: {
      version: 'detect',
    },
    'import/resolver': {
      node: {
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
      },
    },
  },
};
