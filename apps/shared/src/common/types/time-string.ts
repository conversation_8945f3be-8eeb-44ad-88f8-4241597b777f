type Digit = 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

export type TimeStringHours = `${0 | 1}${Digit}` | `2${0 | 1 | 2 | 3}`;
export type ExtendedTimeStringHours =
    | `${0 | 1}${Digit}`
    | `2${0 | 1 | 2 | 3 | 4}`;
export type TimeStringMinutesOrSeconds = `${0 | 1 | 2 | 3 | 4 | 5}${Digit}`;

export type TimeString = TimeStringUpToSecond | TimeStringUpToMinute;
/*
 * String representation of the time in the format HH:MM:SS
 * 24:00:00 is represented as 00:00:00
 * */
export type TimeStringUpToSecond =
    `${TimeStringHours}:${TimeStringMinutesOrSeconds}:${TimeStringMinutesOrSeconds}`;
/*
 * String representation of the time in the format HH:MM
 * 24:00 is represented as 00:00
 * */
export type TimeStringUpToMinute =
    `${TimeStringHours}:${TimeStringMinutesOrSeconds}`;

export type ExtendedTimeString =
    | ExtendedTimeStringUpToSecond
    | ExtendedTimeStringUpToMinute;
/*
 * String representation of the time in the format HH:MM:SS
 * 24:00:00 is represented as 24:00:00
 * */
export type ExtendedTimeStringUpToSecond =
    `${ExtendedTimeStringHours}:${TimeStringMinutesOrSeconds}:${TimeStringMinutesOrSeconds}`;
/*
 * String representation of the time in the format HH:MM
 * 24:00 is represented as 24:00
 * */
export type ExtendedTimeStringUpToMinute =
    `${ExtendedTimeStringHours}:${TimeStringMinutesOrSeconds}`;

export type AnyTimeString = TimeString | ExtendedTimeString;
