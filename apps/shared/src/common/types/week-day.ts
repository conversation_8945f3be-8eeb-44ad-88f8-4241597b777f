export enum WeekDay {
    Monday = 'Monday',
    Tuesday = 'Tuesday',
    Wednesday = 'Wednesday',
    Thursday = 'Thursday',
    Friday = 'Friday',
    Saturday = 'Saturday',
    Sunday = 'Sunday',
}

export enum ShortWeekDay {
    Mon = 'Mon',
    Tue = 'Tue',
    Wed = 'Wed',
    Thu = 'Thu',
    Fri = 'Fri',
    Sat = 'Sat',
    Sun = 'Sun',
}

export class WeekDays {
    private static readonly fullToShortDayMap: Record<WeekDay, ShortWeekDay> = {
        [WeekDay.Monday]: ShortWeekDay.Mon,
        [WeekDay.Tuesday]: ShortWeekDay.Tue,
        [WeekDay.Wednesday]: ShortWeekDay.Wed,
        [WeekDay.Thursday]: ShortWeekDay.Thu,
        [WeekDay.Friday]: ShortWeekDay.Fri,
        [WeekDay.Saturday]: ShortWeekDay.Sat,
        [WeekDay.Sunday]: ShortWeekDay.Sun,
    };

    private static readonly shortToFullDayMap: Record<ShortWeekDay, WeekDay> = {
        Mon: WeekDay.Monday,
        Tue: WeekDay.Tuesday,
        Wed: WeekDay.Wednesday,
        Thu: WeekDay.Thursday,
        Fri: WeekDay.Friday,
        Sat: WeekDay.Saturday,
        Sun: WeekDay.Sunday,
    };

    static readonly FULL_IN_ORDER: WeekDay[] = [
        WeekDay.Monday,
        WeekDay.Tuesday,
        WeekDay.Wednesday,
        WeekDay.Thursday,
        WeekDay.Friday,
        WeekDay.Saturday,
        WeekDay.Sunday,
    ] as const;

    static readonly SHORT_IN_ORDER: ShortWeekDay[] = [
        ShortWeekDay.Mon,
        ShortWeekDay.Tue,
        ShortWeekDay.Wed,
        ShortWeekDay.Thu,
        ShortWeekDay.Fri,
        ShortWeekDay.Sat,
        ShortWeekDay.Sun,
    ] as const;

    static shortToFull(day: ShortWeekDay): WeekDay {
        return WeekDays.shortToFullDayMap[day];
    }

    static fullToShort(day: WeekDay): ShortWeekDay {
        return WeekDays.fullToShortDayMap[day];
    }

    static isWeekend(day: WeekDay | ShortWeekDay): boolean {
        return (
            day === WeekDay.Saturday ||
            day === WeekDay.Sunday ||
            day === ShortWeekDay.Sat ||
            day === ShortWeekDay.Sun
        );
    }

    static stringToFull(day: string): WeekDay {
        const fullDay = Object.values(WeekDay).find(
            (d) => d.toLowerCase() === day.toLowerCase(),
        );
        if (!fullDay) {
            throw new Error(`Invalid week day: ${day}`);
        }

        return fullDay;
    }
}
