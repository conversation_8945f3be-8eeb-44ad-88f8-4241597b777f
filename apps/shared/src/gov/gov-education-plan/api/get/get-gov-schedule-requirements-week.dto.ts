import { IGetGovScheduleRequirementsDayDto } from './get-gov-schedule-requirements-day.dto';
import { IDefaultScheduleRequirementsDto } from '../../../../learner-default-schedule/api/default-schedule-requirements.dto';
import { ScheduleValidationCustomRules } from '../../../gov-schedule-requirements/shared/schedule-validation-consecutive-sessions-custom-rules';
import { DateStringWithoutTime } from '../../../../common/types/date-string';

export interface IGetGovScheduleRequirementsWeekDto {
    id?: number;
    educationPlanId?: number;
    week: DateStringWithoutTime;
    days: IGetGovScheduleRequirementsDayDto[];
    /**
     * @deprecated use `minDaysScheduled` and `maxDaysScheduled` instead
     * */
    daysRequired?: number;
    minDaysScheduled: number;
    maxDaysScheduled: number;
    sessionsRequired: number;
    defaultScheduleRequirements: IDefaultScheduleRequirementsDto;
    customRules: ScheduleValidationCustomRules[];
    timezone: string;
}
