import { WeekDay } from '../../../../common/types/week-day';
import { IGetGovScheduleRequirementsLearningTimeDto } from './get-gov-schedule-requirements-learning-time.dto';

export interface IGetGovScheduleRequirementsDayDto {
    id?: number;
    weekRequirementsId?: number;
    weekDay: WeekDay;
    isBlocked: boolean;
    holidayId?: number;
    holidayName?: string;
    learningTime: IGetGovScheduleRequirementsLearningTimeDto;
}
