import { IGetGovEducationPlanDto } from './get/get-gov-education-plan.dto';
import { DayInGovTimezone } from '../../shared/day-in-gov-timezone';
import { AcademicHours } from '../../shared/academic-hours';

export interface IGetAggregatedGovEducationPlanDto
    extends IGetGovEducationPlanDto {
    start: DayInGovTimezone;
    end: DayInGovTimezone;
    totalHoursRequired: AcademicHours;
    totalDaysRequired: number;
    totalWeeksRequired: number;
    totalHoursCovered: AcademicHours;
    totalDaysCovered: number;
    totalWeeksCovered: number;
}
