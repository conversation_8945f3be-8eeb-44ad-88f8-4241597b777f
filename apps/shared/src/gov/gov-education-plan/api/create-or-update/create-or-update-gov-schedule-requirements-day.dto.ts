import { WeekDay } from '../../../../common/types/week-day';
import { ICreateOrUpdateGovScheduleRequirementsLearningTimeDto } from './create-or-update-gov-schedule-requirements-learning-time.dto';

export interface ICreateOrUpdateGovScheduleRequirementsDayDto {
    id?: number;
    weekRequirementsId?: number;
    weekDay: WeekDay;
    isBlocked: boolean;
    holidayId?: number;
    learningTime: ICreateOrUpdateGovScheduleRequirementsLearningTimeDto;
}
