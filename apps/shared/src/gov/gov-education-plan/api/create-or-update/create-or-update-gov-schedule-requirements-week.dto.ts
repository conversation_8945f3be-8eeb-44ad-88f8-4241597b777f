import { ICreateOrUpdateGovScheduleRequirementsDayDto } from './create-or-update-gov-schedule-requirements-day.dto';
import { IDefaultScheduleRequirementsDto } from '../../../../learner-default-schedule/api/default-schedule-requirements.dto';
import { ScheduleValidationCustomRules } from '../../../gov-schedule-requirements/shared/schedule-validation-consecutive-sessions-custom-rules';
import { DateStringWithoutTime } from '../../../../common/types/date-string';

export interface ICreateOrUpdateGovScheduleRequirementsWeekDto {
    id?: number;
    educationPlanId?: number;
    week: DateStringWithoutTime;
    days: ICreateOrUpdateGovScheduleRequirementsDayDto[];
    /**
     * @deprecated use `minDaysScheduled` and `maxDaysScheduled` instead
     * */
    daysRequired?: number;
    minDaysScheduled: number;
    maxDaysScheduled: number;
    sessionsRequired: number;
    defaultScheduleRequirements: IDefaultScheduleRequirementsDto;
    customRules: ScheduleValidationCustomRules[];
    timezone: string;
}
