export type ScheduleValidationCustomRules =
    | ScheduleValidationConsecutiveSessionsCustomRules
    | ReschedulingCustomRules;

export enum ScheduleValidationConsecutiveSessionsCustomRules {
    TWO_CONSECUTIVE_SESSIONS_15_MINUTE_BREAK = 'consecutive_sessions.2_15_minute_break',
    FOUR_CONSECUTIVE_SESSIONS_60_MINUTE_BREAK = 'consecutive_sessions.4_60_minute_break',
    NO_LIMIT = 'consecutive_sessions.no_limit',
}

export enum ReschedulingCustomRules {
    FROM_TOMORROW = 'rescheduling.from_tomorrow',
}

export const scheduleValidationCustomRules = [
    ...Object.values(ScheduleValidationConsecutiveSessionsCustomRules),
    ...Object.values(ReschedulingCustomRules),
];
