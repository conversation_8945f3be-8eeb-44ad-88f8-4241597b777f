import { IGovProgramLearningTimeRequirementsDto } from './gov-program-learning-time-requirements.dto';
import { IDefaultScheduleRequirementsDto } from '../../../learner-default-schedule/api/default-schedule-requirements.dto';
import { IGovProgramDayLearningTimeInterval } from './gov-program-day-learning-time-interval.dto';
import { IGovAgencyDto } from '../../gov-agency/api/gov-agency.dto';

export interface IGovProgramDto extends ICreateGovProgramDto {
    id: number;
    agency: IGovAgencyDto;
}

export interface ICreateGovProgramDto {
    name: string;
    code: string;
    price: number;
    qualifications: string;
    officialLearningTimeRequirements: IGovProgramLearningTimeRequirementsDto;
    scheduleLearningTimeRequirements: IGovProgramLearningTimeRequirementsDto;
    defaultScheduleRequirements: IDefaultScheduleRequirementsDto;
    dayLearningTimeIntervals: IGovProgramDayLearningTimeInterval[];
    agencyId: number;
}

export interface IUpdateGovProgramDto {
    name?: string;
    code?: string;
    price?: number;
    qualifications?: string;
    officialLearningTimeRequirements?: IGovProgramLearningTimeRequirementsDto;
    scheduleLearningTimeRequirements?: IGovProgramLearningTimeRequirementsDto;
    defaultScheduleRequirements?: IDefaultScheduleRequirementsDto;
    dayLearningTimeIntervals?: IGovProgramDayLearningTimeInterval[];
    agencyId?: number;
}
