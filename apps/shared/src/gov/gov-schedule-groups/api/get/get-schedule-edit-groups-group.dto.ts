import { IGetScheduleEditGroupsIntervalDto } from './get-schedule-edit-groups-interval.dto';
import { IGetGovScheduleRequirementsWeekDto } from '../../../gov-education-plan/api/get/get-gov-schedule-requirements-week.dto';
import { DateString } from '../../../../common/types/date-string';

export interface IGetScheduleEditGroupsGroupDto {
    name: string;
    from: DateString;
    to: DateString;
    existingIntervals: IGetScheduleEditGroupsIntervalDto[];
    scheduleRequirements: IGetGovScheduleRequirementsWeekDto;
}
