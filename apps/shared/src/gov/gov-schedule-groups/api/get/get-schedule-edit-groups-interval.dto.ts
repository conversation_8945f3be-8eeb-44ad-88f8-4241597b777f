import { TimeStringUpToMinute } from '../../../../common/types/time-string';
import { WeekDay } from '../../../../common/types/week-day';

import { GovLearnerScheduleSessionState } from '../../../shared/gov-learner-schedule-session-state';

export interface IGetScheduleEditGroupsIntervalDto {
    weekDay: WeekDay;
    start: TimeStringUpToMinute;
    end: TimeStringUpToMinute;
    state?: GovLearnerScheduleSessionState;
}
