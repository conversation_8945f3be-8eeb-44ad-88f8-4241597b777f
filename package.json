{"name": "@turing-college/platform", "private": true, "scripts": {"version:get": "node -e \"console.log(JSON.stringify(require('./apps/ci-tools/version-utils').getVersion(), null, 2))\" | grep -o '\"version\": \"[^\"]*\"' | cut -d'\"' -f4", "create-branch": "node apps/ci-tools/create-release-branch-cli.js", "create-branch:release": "node apps/ci-tools/create-release-branch-cli.js --type=release", "create-branch:patch": "node apps/ci-tools/create-release-branch-cli.js --type=patch", "create-branch:hotfix": "node apps/ci-tools/create-release-branch-cli.js --type=hotfix", "create-branch:release:develop": "node apps/ci-tools/create-release-branch-cli.js --type=release --branch=develop", "create-branch:release:main": "node apps/ci-tools/create-release-branch-cli.js --type=release --branch=main", "create-branch:patch:develop": "node apps/ci-tools/create-release-branch-cli.js --type=patch --branch=develop", "create-branch:patch:main": "node apps/ci-tools/create-release-branch-cli.js --type=patch --branch=main", "create-branch:hotfix:main": "node apps/ci-tools/create-release-branch-cli.js --type=hotfix --branch=main"}, "dependencies": {}, "devDependencies": {}}