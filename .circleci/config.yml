version: 2.1

orbs:
  node: circleci/node@7.0.0
  aws-s3: circleci/aws-s3@3.0.0
  jira: circleci/jira@2.1.0

parameters:
  # Generic parameters
  temp-env-file-path:
    type: string
    default: tmp/env-vars
  pipeline-parameter-file-path:
    type: string
    default: /tmp/pipeline-parameters.json
  ci-tools-path:
    type: string
    default: apps/ci-tools
  shared-path:
    type: string
    default: apps/shared
  project-slug:
    type: string
    default: gh/TuringCollege/turing-college-platform
  is-client-build:
    type: boolean
    default: false
  is-server-build:
    type: boolean
    default: false
  is-circleci-build:
    type: boolean
    default: false
  is-ci-tools-build:
    type: boolean
    default: false
  is-shared-build:
    type: boolean
    default: false
  node-version:
    type: string
    default: 20.15.0
  ci-node-version:
    type: string
    default: 22.13.1
  working-dir:
    type: string
    default: /tmp/tc-platform-workspace
  node-image:
    type: string
    default: cimg/node:20.15.0
  should-merge-back:
    type: boolean
    default: false
  force-run:
    type: boolean
    default: false
  is-triggered:
    type: boolean
    default: false
  run-setup:
    type: boolean
    default: true
  # Deployment parameters
  deploy-frontend:
    type: boolean
    default: false
    description: "Set to true to run frontend deployment"
  deploy-backend:
    type: boolean
    default: false
    description: "Set to true to run backend deployment"
  deploy-env:
    type: enum
    enum: [dev, staging, prod]
    default: dev
    description: "Environment to deploy to (frontend uses as-is, backend appends -node20 or converts to production-node20)"
  deploy-branch:
    type: string
    default: ""
    description: "Branch to deploy (used if no tag or commit specified)"
  deploy-tag:
    type: string
    default: ""
    description: "Tag to deploy (takes precedence over branch)"
  deploy-commit:
    type: string
    default: ""
    description: "Commit SHA to deploy (highest priority)"
  deploy-distribution-id:
    type: string
    default: ""
    description: "Distribution ID for frontend deployment"
  # Client parameters
  client-path:
    type: string
    default: apps/client
  dev-distribution-id:
    type: string
    # noinspection SpellCheckingInspection
    default: E3FF85F9HWRDTW
  staging-distribution-id:
    type: string
    default: E3NA51G7OS0L84
  prod-distribution-id:
    type: string
    # noinspection SpellCheckingInspection
    default: E16X2FSYDE67ZK
  should-run-checks:
    type: boolean
    default: false
  testing-environment:
    type: enum
    enum: [ local, development, staging, production, custom ]
    default: local
  # Server parameters
  postgres-image:
    type: string
    default: cimg/postgres:16.3
  server-path:
    type: string
    default: apps/server

commands:
  # ================ Generic commands ================
  get-app-name-from-path:
    description: "Extracts the app name from a given path and sanitizes it. The app name is exported as APP_NAME in the bash environment to be used in subsequent steps."
    parameters:
      path:
        type: string
        description: "The directory path to extract the app name from"
      lowercase:
        type: boolean
        default: false
        description: "If true, make the app name lowercase instead of uppercase"
    steps:
      - run:
          name: Get and sanitize app name
          command: |
            # Extract the last portion of the path (basename command)
            APP_NAME=$(basename "<< parameters.path >>") || {
              echo "Error: Unable to extract basename from the given path" >&2
              exit 1
            }

            # Replace invalid characters (anything not a letter, number, or underscore) with underscores
            APP_NAME=${APP_NAME//[^a-zA-Z0-9_]/_}

            # Remove leading invalid characters (like underscores or numbers)
            while [[ $APP_NAME =~ ^[^a-zA-Z] ]]; do
              APP_NAME=${APP_NAME:1}
            done

            # Remove trailing underscores
            APP_NAME=${APP_NAME%_}

            # Validate that APP_NAME is not empty after sanitization
            if [[ -z "$APP_NAME" ]]; then
              echo "Error: Sanitized app name is empty, check the input path: '<< parameters.path >>'" >&2
              exit 1
            fi

            # Handle case transformation based on the 'lowercase' parameter
            if << parameters.lowercase >>; then
              # Convert to lowercase
              APP_NAME=${APP_NAME,,}
            else
              # Convert to uppercase
              APP_NAME=${APP_NAME^^}
            fi

            # Export the sanitized APP_NAME to the bash environment for reuse
            echo "APP_NAME=$APP_NAME" >> $BASH_ENV

            # Log the sanitized APP_NAME for debugging purposes
            echo "Sanitized app name: $APP_NAME"

  load-env-vars-to-bash-env:
    description: "Loads environment variables from a file into $BASH_ENV to ensure they are globally available for subsequent steps."
    parameters:
      temp-env-file-path:
        type: string
        default: << pipeline.parameters.temp-env-file-path >>
    steps:
      - run:
          name: Load environment variables into $BASH_ENV
          command: |
            # Ensure the file exists
            ENV_FILE="<< parameters.temp-env-file-path >>"
            if [[ ! -f "$ENV_FILE" ]]; then
              echo "Error: Environment file $ENV_FILE does not exist. Exiting." >&2
              exit 1
            fi

            # Read the environment file and append each variable to $BASH_ENV
            echo "Loading variables from $ENV_FILE into \$BASH_ENV"
            while IFS='=' read -r key value; do
              # Skip empty lines or comments
              if [[ "$key" =~ ^# ]] || [[ -z "$key" ]]; then continue; fi

              # Add variable to $BASH_ENV
              echo "export $key=\"$value\"" >> "$BASH_ENV"
            done < "$ENV_FILE"

            echo "Environment variables successfully added to \$BASH_ENV."

  generate-pipeline-parameters:
    parameters:
      env-file:
        type: string
        default: << pipeline.parameters.temp-env-file-path >>
      output:
        type: string
        default: << pipeline.parameters.pipeline-parameter-file-path >>
      force-run:
        type: boolean
    steps:
      - run:
          name: Generate pipeline parameters
          command: |
            ENV_FILE="<< parameters.env-file >>"
            OUTPUT="<< parameters.output >>"
            FORCE_RUN="<< parameters.force-run >>"

            mkdir -p "$(dirname $OUTPUT)"

            echo '{' > $OUTPUT
            
            source "$ENV_FILE"

            # Check if CircleCI changes are detected and set force run accordingly
            if [ "$HAS_CIRCLECI_CHANGES" = "true" ]; then
              echo "CircleCI changes detected - will force run all builds regardless of individual change status"
              FORCE_RUN="true"
            elif [ "$HAS_CI_TOOLS_CHANGES" = "true" ]; then
              echo "CI tools changes detected - will force run all builds regardless of individual change status"
              FORCE_RUN="true"
            elif [ "$HAS_SHARED_CHANGES" = "true" ]; then
              echo "Shared changes detected - will force run all builds regardless of individual change status"
              FORCE_RUN="true"
            fi

            if [ "$FORCE_RUN" = "true" ]; then
              # Force run all builds
              while IFS='=' read -r key value; do
                if [[ $key =~ ^HAS_(.*)_CHANGES$ ]]; then
                  APP_NAME=${BASH_REMATCH[1],,}  # Convert to lowercase
                  APP_NAME="${APP_NAME//_/-}" # Replace underscores with hyphens
                  echo "\"is-${APP_NAME}-build\": true," >> $OUTPUT
                fi
              done < "$ENV_FILE"
            else
              # Use actual change detection values
              while IFS='=' read -r key value; do
                if [[ $key =~ ^HAS_(.*)_CHANGES$ ]]; then
                  APP_NAME=${BASH_REMATCH[1],,}  # Convert to lowercase
                  APP_NAME="${APP_NAME//_/-}" # Replace underscores with hyphens
                  echo "\"is-${APP_NAME}-build\": $value," >> $OUTPUT
                fi
              done < "$ENV_FILE"
            fi

            # Create temp file and remove trailing comma only from the last line
            TEMP_OUTPUT=$(mktemp)
            sed '$ s/[[:space:]]*,[[:space:]]*$//' "$OUTPUT" > "$TEMP_OUTPUT"
            mv "$TEMP_OUTPUT" "$OUTPUT"

            echo '}' >> $OUTPUT

            echo "Generated pipeline parameters:"
            cat $OUTPUT

  setup-github-account:
    description: Setting up user to be push the changes to the repo
    steps:
      - run:
          name: Set up account's default identity
          command: |
            git config user.name "CircleCI"
            git config user.email "<EMAIL>"

  push-changes-to-remote:
    description: Pushing changes to remote
    parameters:
      branch:
        type: string
        default: ""
      add-ci-skip:
        type: boolean
        default: false
    steps:
      - run:
          name: Pushing the changes to remote
          command: |
            MAX_RETRIES=5
            retries=0
            
            # Function to display recent commits for debugging
            display_commit_history() {
              local branch=$1
              local count=$2
              echo "========== Recent $count commits for $branch =========="
              git log -n $count --pretty=format:"%h - %s (%cr) <%an>" $branch
              echo ""
              echo "=================================================="
            }
            
            while true; do
              # Fetch the latest changes
              git fetch origin << parameters.branch >>
              
              # Get current and previous commit messages
              LOCAL_HEAD_COMMIT_MSG=$(git log -1 --pretty=%B)
              LOCAL_HEAD_1_COMMIT_MSG=$(git log -2 --skip=1 -1 --pretty=%B)
              
              REMOTE_HEAD_COMMIT_MSG=$(git log -1 --pretty=%B origin/<< parameters.branch >>)
              REMOTE_HEAD_1_COMMIT_MSG=$(git log -2 --skip=1 -1 --pretty=%B origin/<< parameters.branch >>)
  
              echo "Local HEAD commit: $LOCAL_HEAD_COMMIT_MSG"
              echo "Local HEAD~1 commit: $LOCAL_HEAD_1_COMMIT_MSG"
              echo "Remote HEAD commit: $REMOTE_HEAD_COMMIT_MSG"
              echo "Remote HEAD~1 commit: $REMOTE_HEAD_1_COMMIT_MSG"
              
              # Display recent commit history for debugging
              display_commit_history "<< parameters.branch >>" 3
              display_commit_history "origin/<< parameters.branch >>" 3
              
              # Check if the remote already has both the same HEAD and HEAD~1 commits
              if [[ "$LOCAL_HEAD_COMMIT_MSG" == "$REMOTE_HEAD_COMMIT_MSG" && "$LOCAL_HEAD_1_COMMIT_MSG" == "$REMOTE_HEAD_1_COMMIT_MSG" ]]; then
                echo "Remote already has the same HEAD and HEAD~1 commits. Skipping push."
                
                # Reset local to match remote for workspace persistence
                git reset --hard origin/<< parameters.branch >>
                echo "Reset local branch to match remote for workspace persistence."
                exit 0
              else
                # Try to push our changes - temporarily disable -e
                set +e
                git push origin << parameters.branch >> --tags
                PUSH_STATUS=$?
                set -e
                
                if [ $PUSH_STATUS -eq 0 ]; then
                  echo "Successfully pushed version bump to remote."
                  exit 0
                else
                  retries=$((retries + 1))
                
                  if [ $retries -ge $MAX_RETRIES ]; then
                    echo "Error: Exceeded maximum retries ($MAX_RETRIES) for git push." >&2
                    echo "Please check the following unresolved issues:" >&2
                    git status
                    exit 1
                  fi
                
                  echo "Push failed (attempt $retries/$MAX_RETRIES). Remote may have been updated. Retrying..."
                
                  # Pull the latest changes to try again - temporarily disable -e
                  set +e
                  git pull --ff-only origin << parameters.branch >>
                  FF_PULL_STATUS=$?
                  set -e
                
                  if [ $FF_PULL_STATUS -ne 0 ]; then
                    echo "Fast-forward pull failed. Trying merge..."
                    set +e
                    git pull --no-edit origin << parameters.branch >>
                    PULL_STATUS=$?
                    set -e
                
                    if [ $PULL_STATUS -ne 0 ]; then
                      echo "Error: Both fast-forward and regular pull failed." >&2
                      echo "Manual intervention may be required." >&2
                      git status
                      exit 1
                    fi

                    # If we have parameters.add-ci-skip set to true, ensure [ci skip] is in the message
                    if [[ << parameters.add-ci-skip >> == true ]]; then
                      CURRENT_MSG=$(git log -1 --pretty=%B)
                      if [[ "$CURRENT_MSG" != *"[ci skip]"* && "$CURRENT_MSG" != *"[skip ci]"* ]]; then
                        set +e
                        git commit --amend -m "$CURRENT_MSG [ci skip]"
                        set -e
                      fi
                    fi
                  fi
                fi
              fi
            done

  merge-changes-into-develop:
    description: Merging changes into develop
    steps:
      - run:
          name: Merge and push changes to develop
          command: |
            # Fetch the unified version message using repo-version-cli
            REPO_VERSION=$(node -e "console.log(JSON.stringify(require('./<< pipeline.parameters.ci-tools-path >>/version-utils').getVersion(), null, 2))")
            VERSION_MESSAGE=$(echo "$REPO_VERSION" | grep -o '"version": "[^"]*"' | cut -d'"' -f4)

            git checkout develop
            git pull --no-commit && (git commit -am "build: Merge remote into develop [ci skip]" || true)

            if [ -n "$CIRCLE_TAG" ]; then
              MERGE_MESSAGE="build: Merge $CIRCLE_TAG into develop ($VERSION_MESSAGE)"
              git merge --no-ff -m "$MERGE_MESSAGE" $CIRCLE_TAG || true
            else
              MERGE_MESSAGE="build: Merge $CIRCLE_BRANCH into develop ($VERSION_MESSAGE)"
              git merge --no-ff -m "$MERGE_MESSAGE" $CIRCLE_BRANCH || true
            fi

            # Check for conflicts
            CONFLICTS=$(git diff --name-only --diff-filter=U)
            if [[ -n "$CONFLICTS" ]]; then
              echo "Conflicts detected:"
              echo "$CONFLICTS"
              echo ""

              # Automatically resolve conflicts only for version.json
              if [[ "$CONFLICTS" == "version.json" ]]; then
                echo "As only version.json conflict is detected, resolving it automatically."
                git checkout --theirs version.json
                git add version.json
              else
                echo "Error: Multiple file conflicts detected. Manual resolution required."
                echo "Conflicted files:"
                echo "$CONFLICTS"
            
                # Debugging Information
                echo "Debugging Info:"
                echo "    Git Status:"
                git status
            
                exit 1
              fi
            fi

            # Get the last commit message for verification
            LAST_COMMIT_MESSAGE=$(git log -1 --pretty=%B)

            if [[ "$LAST_COMMIT_MESSAGE" == "$MERGE_MESSAGE" ]]; then
              echo "Amending the latest commit with resolved changes."
              git commit --amend -m "$MERGE_MESSAGE"
            else
              echo "Error: The last commit message does not match the expected merge message."

              # Debugging Information
              echo "Debugging Info:"
              echo "    Last Commit Message: $LAST_COMMIT_MESSAGE"
              echo "    Expected Merge Message: $MERGE_MESSAGE"
              echo "    Git Status:"
              git status
              echo "    Recent Git Log (Last 5):"
              git log -5 --oneline

              exit 1
            fi

            # Push changes to remote branch with retry logic
            MAX_RETRIES=5
            retries=0

            while ! git push origin develop; do
              retries=$((retries + 1))
              echo "Retrying git push... Attempt $retries of $MAX_RETRIES."

              # Break the loop if retries exceed the defined maximum
              if [ $retries -ge $MAX_RETRIES ]; then
                echo "Error: Exceeded maximum retries ($MAX_RETRIES) for git push." >&2

                # Log the reason for debugging purposes
                echo "Please check the following unresolved issues:" >&2
                git status
                exit 1  
              fi
            
              # Before pulling again, check if the changes are already present on the remote
              echo "Checking if the intended commit message already exists on 'origin/develop'..."
              git fetch origin develop
              REMOTE_COMMIT_MESSAGES=$(git log --pretty=format:"%s" -n 20 origin/develop)

              if echo "$REMOTE_COMMIT_MESSAGES" | grep -qF "$MERGE_MESSAGE"; then
                echo "Changes already exist on 'origin/develop'. Skipping further retries."
                exit 0
              fi

              echo "Message not found, proceeding with the retry."

              # Attempt to pull the latest changes from develop                     
              git pull --ff --no-edit origin develop
              COMMIT_MESSAGE=$(git log -1 --pretty=%B)
              if [[ "$COMMIT_MESSAGE" != *"[ci skip]"* && "$COMMIT_MESSAGE" != *"[skip ci]"* ]]; then
                git commit --amend -m "$COMMIT_MESSAGE [ci skip]"
              fi

              # Add delay between retries to avoid hammering the server
              sleep 1
            done

  add-github-to-known-hosts:
    description: Add github.com to known hosts
    steps:
      - run:
          name: Add github.com to known hosts
          command: |
            mkdir ~/.ssh/ || true && ssh-keyscan github.com >> ~/.ssh/known_hosts

  # ================ Deployment commands ================
  checkout-with-target:
    description: "Checks out a specific branch, tag, or commit based on priority, with optimized fetching"
    parameters:
      branch:
        type: string
        default: ""
        description: "Branch to deploy (used if no tag or commit specified)"
      tag:
        type: string
        default: ""
        description: "Tag to deploy (takes precedence over branch)"
      commit:
        type: string
        default: ""
        description: "Commit SHA to deploy (highest priority)"
      run-head-check:
        type: boolean
        default: false
        description: "Whether to check if the current HEAD is"
    steps:
      - checkout
      - add-github-to-known-hosts
      - setup-github-account
      - when:
          condition: << parameters.run-head-check >>
          steps:
            - run:
                name: Check if on head
                command: |
                  # Skip this check if we're deploying a specific commit or tag or from a branch that is not the current one
                  if [ -n "<< parameters.commit >>" ] || [ -n "<< parameters.tag >>" ]; then
                    echo "Deploying a specific commit or tag, skipping HEAD check"
                  elif [ -n "<< parameters.branch >>" ] && [ "<< parameters.branch >>" != "${CIRCLE_BRANCH}" ]; then
                    echo "Deploying from specified branch (<< parameters.branch >>), skipping HEAD check"
                  else
                  # Check if we're on HEAD or newer
                    if [ "${CIRCLE_BRANCH}" == "main" ]; then
                      echo "On main, proceed with the production deploy job."
                    else
                      REMOTE_COMMIT=$(git ls-remote origin $CIRCLE_BRANCH | cut -f1)
                      LOCAL_COMMIT=$(git rev-parse HEAD)

                      echo "Remote commit: $REMOTE_COMMIT"
                      echo "Local commit: $LOCAL_COMMIT"

                      if [ "$REMOTE_COMMIT" == "$LOCAL_COMMIT" ]; then
                        echo "On head, proceed with the job."
                      else
                        echo "New commit found, skipping deployment"
                        circleci step halt
                      fi
                    fi
                  fi
      - run:
          name: Determine and execute checkout if needed
          command: |
            # Determine the target type and value
            if [ -n "<< parameters.commit >>" ]; then
              echo "Target is commit: << parameters.commit >>"
            
              # Resolve the commit hash to a full hash
              if RESOLVED_TARGET=$(git rev-parse --verify "<< parameters.commit >>^{commit}" 2>/dev/null); then
                echo "Valid commit: $RESOLVED_TARGET"
              else
                echo "Error: Failed to resolve commit hash << parameters.commit >>"
                exit 1
              fi
            
              # Check if the current HEAD points at the target commit
              if [ "$RESOLVED_TARGET" = "$(git rev-parse HEAD)" ]; then
                echo "Already at target commit: $RESOLVED_TARGET"
                exit 0
              fi
            
              # Checkout the specific commit
              git fetch --depth=1 origin $RESOLVED_TARGET
              git checkout $RESOLVED_TARGET
              exit 0
            fi

            if [ -n "<< parameters.tag >>" ]; then
              echo "Target is tag: << parameters.tag >>"

              # Check if the current HEAD points at the target tag
              if ! git tag --points-at HEAD | grep -q "^<< parameters.tag >>$"; then
                git fetch --depth=1 origin tag << parameters.tag >>
                git checkout << parameters.tag >>
              else
                echo "Already at target tag: << parameters.tag >>"
              fi
              exit 0
            fi

            if [ -n "<< parameters.branch >>" ]; then
              echo "Target is branch: << parameters.branch >>"

              # Check if the current branch matches the target branch
              if [ "$(git rev-parse --abbrev-ref HEAD)" != "<< parameters.branch >>" ]; then
                git fetch --depth=1 origin << parameters.branch >>
                git checkout << parameters.branch >>
              else
                echo "Already at target branch: << parameters.branch >>"
              fi
              exit 0
            fi

            # Default to the current branch if no parameters are specified
            echo "No target specified. Defaulting to current branch: << pipeline.git.branch >>"
            if [ "$(git rev-parse --abbrev-ref HEAD)" != "<< pipeline.git.branch >>" ]; then
              git fetch --depth=1 origin << pipeline.git.branch >>
              git checkout << pipeline.git.branch >>
            else
              echo "Already at current branch: << pipeline.git.branch >>"
            fi

  # ================ Client commands ================
  deploy-to-s3:
    description: Deploying application to << parameters.to >> environment
    parameters:
      to:
        type: enum
        enum: [ dev, staging, prod ]
        default: dev
      from:
        type: string
        default: dist
    steps:
      - aws-s3/sync:
          from: << parameters.from >>
          to: s3://turingcollege-<< parameters.to >>
          arguments: |
            --acl public-read \
            --cache-control "max-age=2592000" \
            --exclude "<< parameters.from >>/index.html"
      - aws-s3/copy:
          from: << parameters.from >>/index.html
          to: s3://turingcollege-<< parameters.to >>

  invalidate-cloudfront-cache:
    description: Invalidating CloudFront Cache
    parameters:
      distribution-id:
        type: string
    steps:
      - run:
          name: Invalidate CloudFront Cache
          command: |
            aws cloudfront create-invalidation --distribution-id << parameters.distribution-id >> --paths "/*"

  deploy-storybook-to-s3:
    description: Deploying Storybook to S3
    steps:
      - aws-s3/sync:
          from: storybook-static
          to: s3://storybook.turingcollege.com

  notify_jira_about_build:
    description: Notify Jira about the build
    steps:
      - jira/notify:
          job_type: build
          pipeline_id: << pipeline.id >>
          pipeline_number: << pipeline.number >>
          issue_regexp: (PLAT-[0-9]+)

  notify_jira_about_deployment:
    description: Notify Jira about the deployment to << parameters.to >>
    parameters:
      to:
        type: enum
        enum: [ dev, staging, prod ]
    steps:
      - when:
          condition:
            equal: [ dev, << parameters.to >> ]
          steps:
            - jira/notify:
                environment: development
                environment_type: development
                job_type: deployment
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>
                issue_regexp: (PLAT-[0-9]+)
      - when:
          condition:
            equal: [ staging, << parameters.to >> ]
          steps:
            - jira/notify:
                environment: staging
                environment_type: staging
                job_type: deployment
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>
                issue_regexp: (PLAT-[0-9]+)
      - when:
          condition:
            equal: [ prod, << parameters.to >> ]
          steps:
            - jira/notify:
                environment: production
                environment_type: production
                job_type: deployment
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>
                issue_regexp: (PLAT-[0-9]+)

  # ================ Server commands ================
  deploy-to-aws:
    description: 'Deployment to AWS by using Elastic Beanstalk'
    parameters:
      from:
        type: string
        default: .
      to:
        type: enum
        enum: [ 'dev-node20', 'staging-node20', 'production-node20' ]
        default: dev-node20
    steps:
      - run:
          name: Generate version label
          command: |
            # Get app version directly using a Node.js script
            echo "Getting app version..."
            VERSION_DATA=$(node -e "console.log(JSON.stringify(require('./<< pipeline.parameters.ci-tools-path >>/version-utils').getVersion(), null, 2))")
            APP_VERSION=$(echo "$VERSION_DATA" | grep -o '"version": "[^"]*"' | cut -d'"' -f4)

            # Get Git metadata
            GIT_COMMIT=$(git rev-parse --short HEAD)

            # Get current date and time combined with consistent separator and timezone
            DATETIME=$(date '+%Y%m%d-%H%M%S%z')

            # Create the version label
            VERSION_LABEL="app-v${APP_VERSION}-${GIT_COMMIT}-${DATETIME}"

            # Save version label to be used in the next step
            echo "export EB_VERSION_LABEL=$VERSION_LABEL" >> $BASH_ENV

            echo "Generated version label: $VERSION_LABEL"
      - run:
          name: Deploying application to << parameters.to >> environment
          command: |
            if [ -n "<< parameters.from >>" ]; then
              cd << parameters.from >>
            fi
            echo "Deploying from $(pwd)"
            echo "Using version label: $EB_VERSION_LABEL"

            eb deploy turing-college-<< parameters.to >> --label $EB_VERSION_LABEL

jobs:
  checkout-and-install-dependencies:
    description: Checking out the code and install required dependencies for the particular app
    executor:
      name: node/default
    parameters:
      app-dir:
        type: string
        default: "."
      branch:
        type: string
        default: ""
      tag:
        type: string
        default: ""
      commit:
        type: string
        default: ""
      run-head-check:
        type: boolean
        default: false
    working_directory: << pipeline.parameters.working-dir >>
    steps:
      - checkout-with-target:
          branch: << parameters.branch >>
          tag: << parameters.tag >>
          commit: << parameters.commit >>
          run-head-check: << parameters.run-head-check >>
      - node/install-packages:
          app-dir: << parameters.app-dir >>
      - store_artifacts:
          path: ~/.npm/_logs
      - persist_to_workspace:
          root: .
          paths:
            - .

  # ================ Deployment jobs ================
  deploy-frontend:
    executor:
      name: node/default
      tag: << parameters.node-version >>
    parameters:
      env:
        type: enum
        enum: [ dev, staging, prod ]
        description: "Environment to deploy to"
      distribution-id:
        type: string
        default: ""
        description: "CloudFront distribution ID (if empty, will use the parameter based on environment)"
      branch:
        type: string
        default: ""
        description: "Branch to deploy (used if no tag or commit specified)"
      tag:
        type: string
        default: ""
        description: "Tag to deploy (takes precedence over branch)"
      commit:
        type: string
        default: ""
        description: "Commit SHA to deploy (highest priority)"
      node-version:
        type: string
        default: << pipeline.parameters.node-version >>
        description: "Node version to use for deployment"
    working_directory: << pipeline.parameters.working-dir >>
    environment:
      AWS_REGION: eu-north-1
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - run:
          name: Select distribution ID based on environment if not provided
          command: |
            if [ -z "<< parameters.distribution-id >>" ]; then
              if [ "<< parameters.env >>" = "dev" ]; then
                echo "export DISTRIBUTION_ID=<< pipeline.parameters.dev-distribution-id >>" >> $BASH_ENV
              elif [ "<< parameters.env >>" = "staging" ]; then
                echo "export DISTRIBUTION_ID=<< pipeline.parameters.staging-distribution-id >>" >> $BASH_ENV
              elif [ "<< parameters.env >>" = "prod" ]; then
                echo "export DISTRIBUTION_ID=<< pipeline.parameters.prod-distribution-id >>" >> $BASH_ENV
              fi
            else
              echo "export DISTRIBUTION_ID=<< parameters.distribution-id >>" >> $BASH_ENV
            fi
            source $BASH_ENV
            echo "Using distribution ID: $DISTRIBUTION_ID"
      - deploy-to-s3:
          to: << parameters.env >>
          from: << pipeline.parameters.client-path >>/dist
      - invalidate-cloudfront-cache:
          distribution-id: $DISTRIBUTION_ID

  deploy-backend:
    docker:
      - image: cimg/python:3.13.2-node
    parameters:
      env:
        type: enum
        enum: [ 'dev-node20', 'staging-node20', 'production-node20' ]
        description: "Environment to deploy to"
      commit:
        type: string
        default: ""
        description: "Commit SHA to deploy (highest priority)"
      tag:
        type: string
        default: ""
        description: "Tag to deploy (takes precedence over branch)"
      branch:
        type: string
        default: ""
        description: "Branch to deploy (used if no tag or commit specified)"
      node-version:
        type: string
        default: << pipeline.parameters.node-version >>
        description: "Node version to use for deployment"
    working_directory: << pipeline.parameters.working-dir >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - run:
          name: Installing AWS deployment dependencies
          command: |
            pip install --upgrade pip
            pip install awsebcli --no-build-isolation
      - run:
          name: Cleanup `node_modules`
          command: |
            rm -rf << pipeline.parameters.server-path >>/node_modules
      - deploy-to-aws:
          from: << pipeline.parameters.server-path >>
          to: << parameters.env >>

  # ================ Generic jobs ================
  checkout-code:
    description: Checking out the code
    executor:
      name: node/default
    parameters:
      reset-local-changes:
        type: boolean
        default: false
        description: "Whether to reset local changes if any"
    working_directory: << pipeline.parameters.working-dir >>
    steps:
      - checkout
      - when:
          condition: << parameters.reset-local-changes >>
          steps:
            - run:
                name: Reset local changes if any
                command: git reset --hard HEAD
      - store_artifacts:
          path: ~/.npm/_logs
      - persist_to_workspace:
          root: .
          paths:
            - .

  install-dependencies:
    description: Install dependencies
    executor:
      name: node/default
      tag: << parameters.node-version >>
    parameters:
      working_dir:
        type: string
        default: "."
      path:
        type: string
        default: ""
      node-version:
        type: string
        default: << pipeline.parameters.node-version >>
    steps:
      - attach_workspace:
          at: << parameters.working_dir >>
      - run:
          name: Disabling progress status bar
          command: npm set progress=false
      - node/install-packages:
          app-dir: << parameters.working_dir >>/<< parameters.path >>
      - run:
          name: Enabling progress status bar
          command: npm set progress=true
      - store_artifacts:
          path: ~/.npm/_logs
      - persist_to_workspace:
          root: << parameters.working_dir >>
          paths:
            - << parameters.path >>

  setup:
    executor:
      name: node/default
      tag: << pipeline.parameters.ci-node-version >>
    steps:
      - checkout
      - attach_workspace:
          at: .
      - load-env-vars-to-bash-env:
          temp-env-file-path: << pipeline.parameters.temp-env-file-path >>
      - generate-pipeline-parameters:
          env-file: << pipeline.parameters.temp-env-file-path >>
          output: << pipeline.parameters.pipeline-parameter-file-path >>
          force-run: << pipeline.parameters.force-run >>
      - run:
          name: Trigger new CircleCI pipeline
          command: |
            branch="<< pipeline.git.branch >>"
            parameters_file="<< pipeline.parameters.pipeline-parameter-file-path >>"
            
            if [ ! -f "$parameters_file" ]; then
              echo "Error: Parameters file $parameters_file does not exist." >&2
              exit 1
            fi
            
            # Construct JSON payload
            payload=$(jq -n \
              --arg branch "$branch" \
              --argfile parameters "$parameters_file" \
              '{ 
                branch: $branch, 
                parameters: ($parameters + {
                  "run-setup": false,
                  "is-triggered": true
                })
              }'
            )

            # Trigger the new pipeline using CircleCI API
            curl --request POST \
              --url https://circleci.com/api/v2/project/gh/TuringCollege/turing-college-platform/pipeline \
              --header "Circle-Token: $PIPELINE_TRIGGER_TOKEN" \
              --header "Content-Type: application/json" \
              --data "$payload"

            echo -e "\nNew pipeline triggered successfully for branch $branch with payload:\n$payload"

  check-for-changes:
    executor:
      name: node/default
      tag: << pipeline.parameters.ci-node-version >>
    parameters:
      app-dir:
        type: string
      branch:
        type: string
      workflow:
        type: enum
        enum: [ ignored, build-frontend-development, build-backend-development, build-frontend-staging, build-backend-staging, build-frontend-production, build-backend-production, setup-development-workflow, setup-staging-workflow, setup-production-workflow ]
    steps:
      - checkout
      - node/install-packages:
          app-dir: << pipeline.parameters.ci-tools-path >>
      - get-app-name-from-path:
          path: "<< parameters.app-dir >>"
      - run:
          name: Check if app changes are present
          command: |
            echo "Checking for changes in $APP_NAME"
            echo

            OUTPUT=$(node "<< pipeline.parameters.ci-tools-path >>"/ci-change-detector-cli.js \
              --token $PIPELINE_TRIGGER_TOKEN \
              --project "<< pipeline.parameters.project-slug >>" \
              --directory "<< parameters.app-dir >>" \
              --branch "<< parameters.branch >>" \
              --workflow "<< parameters.workflow >>"  \
            )
            echo "$OUTPUT"
            echo

            # Extract JSON part from the output (everything up to the execution time message)
            RESULTS=$(echo "$OUTPUT" | sed -n '/^{/,/^}/p')

            HAS_CHANGES=$(echo $RESULTS | jq -r '.hasChanges')

            echo
            # Use temp-env-file-path as a base for app-specific env files
            ENV_FILE="<< pipeline.parameters.temp-env-file-path >>-${APP_NAME,,}"
            mkdir -p $(dirname $ENV_FILE)
            echo "HAS_${APP_NAME}_CHANGES=$HAS_CHANGES" >> "$ENV_FILE"
            echo "HAS_${APP_NAME}_CHANGES=$HAS_CHANGES is saved to $ENV_FILE"
      - persist_to_workspace:
          root: .
          paths:
            - "<< pipeline.parameters.temp-env-file-path >>-*"

  test-check-for-changes:
    executor:
      name: node/default
      tag: << pipeline.parameters.ci-node-version >>
    parameters:
      required-vars:
        type: string
        description: "Space-separated list of required environment variables to check"
    steps:
      - checkout
      - attach_workspace:
          at: .
      - load-env-vars-to-bash-env:
          temp-env-file-path: << pipeline.parameters.temp-env-file-path >>
      - run:
          name: Validate environment variables
          command: |
            # Convert space-separated string to array, handling multiple spaces
            # First normalize the string by converting multiple spaces to single spaces and trimming
            CLEANED_VARS=$(echo "<< parameters.required-vars >>" | tr -s ' ' | sed -e 's/^[[:space:]]*//' -e 's/[[:space:]]*$//')
            # Use array assignment with parentheses
            REQUIRED_VARS=($CLEANED_VARS)

            # Check if array is empty
            if [ ${#REQUIRED_VARS[@]} -eq 0 ]; then
              echo "Error: No environment variables specified to check" >&2
              exit 1
            fi

            ALL_VARS_PRESENT=true

            echo "Checking required environment variables..."
            # Loop through the required variables
            for VAR in "${REQUIRED_VARS[@]}"; do
              # Get the runtime value of the variable
              RUNTIME_VALUE="${!VAR}"

              # Log the state
              echo "$VAR=${RUNTIME_VALUE:-<not set>}"

              # Check if the variable is present
              if [[ -z "$RUNTIME_VALUE" ]]; then
                echo "Error: Environment variable '$VAR' is not set in the runtime environment." >&2
                ALL_VARS_PRESENT=false
                continue
              fi

              # Check for valid boolean values (case-sensitive)
              if [[ "$RUNTIME_VALUE" != "true" && "$RUNTIME_VALUE" != "false" ]]; then
                echo "Error: Environment variable '$VAR' has an invalid value. Expected 'true' or 'false' (case-sensitive), while the actual value is '$RUNTIME_VALUE'." >&2
                ALL_VARS_PRESENT=false
              fi
            done

            # Final report
            if [[ "$ALL_VARS_PRESENT" == false ]]; then
              echo "Some required environment variables are missing or invalid." >&2
              exit 1
            else
              echo "All required environment variables are present and valid."
            fi

  accumulate-env-vars:
    description: Combine all environment variables into a single file
    executor:
      name: node/default
      tag: << pipeline.parameters.ci-node-version >>
    steps:
      - attach_workspace:
          at: .
      - run:
          name: Combine all env vars
          command: |
            # Create or overwrite the final temp env file
            FINAL_ENV_FILE="<< pipeline.parameters.temp-env-file-path >>"
            mkdir -p $(dirname $FINAL_ENV_FILE)
            > $FINAL_ENV_FILE

            # Append content from all the app-specific files to the final env file
            for FILE in $(dirname "<< pipeline.parameters.temp-env-file-path >>")/*; do
              if [[ -f "$FILE" ]]; then
                cat "$FILE" >> $FINAL_ENV_FILE
                echo >> $FINAL_ENV_FILE  # Add a newline for clarity
              fi
            done

            echo "Combined env vars file created at $FINAL_ENV_FILE"
            echo "Contents:"
            cat $FINAL_ENV_FILE
      - persist_to_workspace:
          root: .
          paths:
            - << pipeline.parameters.temp-env-file-path >>

  push-changes-after-version-bump-up:
    resource_class: small
    description: Pushing changes to remote
    executor:
      name: node/default
      tag: << pipeline.parameters.node-version >>
    working_directory: << pipeline.parameters.working-dir >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - add-github-to-known-hosts
      - setup-github-account
      - push-changes-to-remote:
          branch: $CIRCLE_BRANCH
          add-ci-skip: true
      - persist_to_workspace:
          root: << pipeline.parameters.working-dir >>
          paths:
            - .

  push-changes-to-develop:
    resource_class: small
    description: Push changes back to develop
    parameters:
      source:
        type: string
        default: ""
    executor:
      name: node/default
      tag: << pipeline.parameters.node-version >>
    steps:
      - run:
          name: Check conditions and possibly skip job
          command: |
            if [ "$IS_MERGE_BACK_TO_DEVELOP_SKIPPED" == "true" ]; then
              echo "Job skipped because IS_MERGE_BACK_TO_DEVELOP_SKIPPED is set to true"
              circleci-agent step halt
            fi
            
            if [ "<< pipeline.parameters.should-merge-back >>" != "true" ]; then
              echo "Job skipped because pipeline parameter should-merge-back is not set to true"
              circleci-agent step halt
            fi
      - checkout
      - add-github-to-known-hosts
      - setup-github-account
      - merge-changes-into-develop

  trigger-merge-back-to-develop:
    resource_class: small
    executor:
      name: node/default
      tag: << pipeline.parameters.node-version >>
    steps:
      - run:
          name: Trigger merge-changes-back-to-develop workflow via API
          command: |
            curl --request POST \
              --url https://circleci.com/api/v2/project/gh/TuringCollege/turing-college-platform/pipeline \
              --header "Circle-Token: $PIPELINE_TRIGGER_TOKEN" \
              --header "Content-Type: application/json" \
              --data '{
                "branch": "<< pipeline.git.branch >>",
                "parameters": {
                  "run-setup": false,
                  "is-triggered": true,
                  "should-merge-back": true
                }
              }'
  bump-up-version:
    description: Updating version using centralized versioning
    executor:
      name: node/default
    working_directory: << pipeline.parameters.working-dir >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - add-github-to-known-hosts
      - setup-github-account
      - node/install-packages:
          app-dir: << pipeline.parameters.ci-tools-path >>
      - run:
          name: Extract JIRA tickets
          command: |
            # Extract JIRA tickets for commit messages
            if [[ "<< pipeline.git.branch >>" == "main" ]]; then
              app_log=$(node "<< pipeline.parameters.ci-tools-path >>"/jira-tickets-extractor-cli.js --for=production)
            else
              app_log=$(node "<< pipeline.parameters.ci-tools-path >>"/jira-tickets-extractor-cli.js --for=staging)
            fi
            
            printf "%b\n" "$app_log"
            
            jira_keys_string=$(printf "%b\n" "$app_log" | grep -A 1 "Conversion is completed." | tail -n 1 || echo "")
            
            # Add parentheses to jira_keys_string if it is not empty
            if [[ -n "$jira_keys_string" ]]; then
              echo "export JIRA_KEYS_STRING=\" ($jira_keys_string)\"" >> $BASH_ENV
            else
              echo "export JIRA_KEYS_STRING=\"\"" >> $BASH_ENV
            fi
            
            source $BASH_ENV
      - when: # Set stable version when branch is main
          condition:
            equal: [ main, << pipeline.git.branch >> ]
          steps:
            - run:
                name: Setting stable version for production
                command: |
                  cd << pipeline.parameters.ci-tools-path >>
                
                  # Set stable version by removing RC suffix
                  npm run version:set-stable -- --main-branch
                  
                  # Get the updated stable version
                  VERSION_DATA=$(npm run version:get)
                  NEXT_VERSION=$(echo "$VERSION_DATA" | grep -o '"version": "[^"]*"' | cut -d'"' -f4)
                  
                  echo -e "\nNew release version:"
                  echo -e "$NEXT_VERSION\n"
                  
                  # Simplified commit message without "for" parameter
                  COMMIT_MESSAGE="build: Bump up version to $NEXT_VERSION [ci skip]$JIRA_KEYS_STRING"
                  
                  git add ../../version.json
                  git commit -m "$COMMIT_MESSAGE"
      - when: # Update RC version when branch is a release branch
          condition:
            and:
              - not:
                  equal: [ main, << pipeline.git.branch >> ]
              - matches:
                  pattern: "^(release|patch|hotfix)/v[0-9]+\\.[0-9]+\\.[0-9]+$"
                  value: << pipeline.git.branch >>
          steps:
            - run:
                name: Setting release candidate version
                command: |
                  cd << pipeline.parameters.ci-tools-path >>
                
                  # Update version based on branch name
                  npm run version:update -- --branch="<< pipeline.git.branch >>"
                  
                  # Get the updated RC version
                  VERSION_DATA=$(npm run version:get)
                  NEXT_VERSION=$(echo "$VERSION_DATA" | grep -o '"version": "[^"]*"' | cut -d'"' -f4)
                  
                  echo -e "\nNew release candidate version:"
                  echo -e "$NEXT_VERSION\n"
                  
                  # Simplified commit message without "for" parameter
                  COMMIT_MESSAGE="build: Bump up version to $NEXT_VERSION [ci skip]$JIRA_KEYS_STRING"
                  
                  git add ../../version.json
                  git commit -m "$COMMIT_MESSAGE"
      - run:
          name: Create version tag
          command: |
            node "<< pipeline.parameters.ci-tools-path >>/repo-version-cli.js" create-tag
      - store_artifacts:
          path: ~/.npm/_logs
      - persist_to_workspace:
          root: << pipeline.parameters.working-dir >>
          paths:
            - .git
            - version.json
  run-lint:
    description: "Run linting for a specified app/package directory"
    parameters:
      path:
        type: string
        description: "Path to the app/package directory (e.g., apps/client, apps/server, apps/shared)"
      node-version:
        type: string
        default: << pipeline.parameters.node-version >>
        description: "Node version to use"
      store-artifacts:
        type: boolean
        default: true
        description: "Whether to store npm log artifacts"
    executor:
      name: node/default
      tag: << parameters.node-version >>
    working_directory: << pipeline.parameters.working-dir >>/<< parameters.path >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - run: npm run lint
      - when:
          condition: << parameters.store-artifacts >>
          steps:
            - store_artifacts:
                path: ~/.npm/_logs

  # ================ Client jobs ================
  build-client:
    parameters:
      env:
        type: enum
        enum: [ dev, staging, prod ]
    executor:
      name: node/default
    working_directory: << pipeline.parameters.working-dir >>/<< pipeline.parameters.client-path >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - run:
          name: Run the build
          command: |
            npm run build:<< parameters.env >>
      - notify_jira_about_build
      - store_artifacts:
          path: ~/.npm/_logs
      - persist_to_workspace:
          root: << pipeline.parameters.working-dir >>
          paths:
            - << pipeline.parameters.client-path >>
  build-storybook:
    executor:
      name: node/default
    working_directory: << pipeline.parameters.working-dir >>/<< pipeline.parameters.client-path >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - run:
          name: Run the build
          command: npm run build-storybook
      - store_artifacts:
          path: ~/.npm/_logs
      - persist_to_workspace:
          root: << pipeline.parameters.working-dir >>
          paths:
            - << pipeline.parameters.client-path >>
  run-tests:
    executor:
      name: node/default
    working_directory: << pipeline.parameters.working-dir >>/<< pipeline.parameters.client-path >>
    parallelism: 5
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - run: |
          CHECKS_FILES=$(circleci tests glob "src/**/*.test.{ts,tsx}")
          echo "$CHECKS_FILES" | circleci tests run --command="xargs npx vitest --run --reporter=default --reporter=junit --outputFile=junit.xml" --verbose --split-by=timings
      - store_artifacts:
          path: ~/.npm/_logs
      - store_test_results:
          path: junit.xml
  run-e2e:
    resource_class: medium+
    parameters:
      run-headless:
        type: boolean
        default: true
      env:
        type: enum
        default: local
        enum: [ local, development, staging, production, custom ]
    docker:
      - image: cimg/node:current-browsers
        environment:
          DOCKER_OPTS: '--shm-size=2g'
    working_directory: << pipeline.parameters.working-dir >>/<< pipeline.parameters.client-path >>
    parallelism: 5
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - run:
          name: Setup environment variable
          command: |
            echo "Versions and environment variables:"
            echo "Job env parameter: '<< parameters.env >>'"
            echo "Job run-headless parameter: '<< parameters.run-headless >>'"
            echo "Pipeline testing environment: '<< pipeline.parameters.testing-environment >>'"
            echo "TESTING_ENVIRONMENT: '$TESTING_ENVIRONMENT'"

            echo 'export DEFAULT_TESTING_ENVIRONMENT=<< pipeline.parameters.testing-environment >>' >> $BASH_ENV
            echo 'export TESTING_ENVIRONMENT=${TESTING_ENVIRONMENT:-$DEFAULT_TESTING_ENVIRONMENT}' >> $BASH_ENV

            . $BASH_ENV
            echo "DEFAULT_TESTING_ENVIRONMENT: '$DEFAULT_TESTING_ENVIRONMENT'"
            echo "Updated TESTING_ENVIRONMENT: '$TESTING_ENVIRONMENT'"
      - run:
          name: Running WebDriverIO end-to-end checks
          command: |
            if [ "<< parameters.env >>" = "local" ]; then
              SHOULD_RUN_STATIC_SERVER=true
              echo "Starting application server..."
              mkdir -p ./test-results
              npx pm2 start "npm start" > ./test-results/web-app.log 2>&1
            else
              SHOULD_RUN_STATIC_SERVER=false
            fi

            CHECKS_FILES=$(circleci tests glob "e2e/**/*.checks.ts")
            echo "$CHECKS_FILES" | circleci tests run --command="SHOULD_RUN_STATIC_SERVER=$SHOULD_RUN_STATIC_SERVER RUN_HEADLESS=<< parameters.run-headless >> xargs npm run e2e -- --spec " --verbose --split-by=timings
            if [ "<< parameters.env >>" = "local" ]; then
              echo "Stopping application server..."
              npx pm2 delete "npm start" >> ./test-results/web-app.log 2>&1
            fi
      - store_artifacts:
          path: test-results
      - store_test_results:
          path: test-results
  run-type-check:
    executor:
      name: node/default
    working_directory: << pipeline.parameters.working-dir >>/<< pipeline.parameters.client-path >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - run: npm run type-check
  # Legacy 'deploy' job has been removed - replaced by 'deploy-frontend' which offers more features
  deploy-storybook:
    executor:
      name: node/default
    working_directory: << pipeline.parameters.working-dir >>/<< pipeline.parameters.client-path >>
    environment:
      AWS_REGION: eu-north-1
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - deploy-storybook-to-s3

  # ================ Server jobs ================
  # Legacy 'install-aws-cli-and-deploy' job has been removed - replaced by 'deploy-backend' which offers more features



  build:
    executor:
      name: node/default
      tag: << pipeline.parameters.node-version >>
    working_directory: << pipeline.parameters.working-dir >>/<< pipeline.parameters.server-path >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - run: npm run build
      - store_artifacts:
          path: ~/.npm/_logs
      - persist_to_workspace:
          root: << pipeline.parameters.working-dir >>
          paths:
            - << pipeline.parameters.server-path >>

  run-unit-tests:
    resource_class: medium+
    working_directory: << pipeline.parameters.working-dir >>
    parallelism: 10
    docker:
      - image: << pipeline.parameters.node-image >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      #      - checkout
      #      - node/install-packages:
      #          app-dir: << pipeline.parameters.working-dir >>/<< pipeline.parameters.server-path >>
      #      - run: cd << pipeline.parameters.server-path >> && npm run build
      - run:
          name: Preparing for execution
          command: |
            echo 'export GITHUB_PRIVATE_KEY=$(echo $GITHUB_PRIVATE_KEY_BASE64 | base64 -d)' >> $BASH_ENV
            source $BASH_ENV
      - run:
          name: Run all unit checks
          command: |
            cd << pipeline.parameters.server-path >>
            CHECKS_FILES=$(circleci tests glob "**/dist/server/src/**/*.unit.test.js")
            echo "$CHECKS_FILES" | circleci tests run --command="JEST_JUNIT_ADD_FILE_ATTRIBUTE=true xargs npm run test:unit:ci --coverage --runInBand --detectOpenHandles --" --verbose --split-by=timings
          environment:
            JEST_JUNIT_OUTPUT_DIR: ./reports/
      - store_artifacts:
          path: ~/.npm/_logs
      - store_test_results:
          path: ./<< pipeline.parameters.server-path >>/reports/

  run-integration-tests:
    resource_class: medium+
    working_directory: << pipeline.parameters.working-dir >>
    parallelism: 10
    docker:
      - image: << pipeline.parameters.node-image >>
      - image: << pipeline.parameters.postgres-image >>
        environment:
          POSTGRES_USER: test
          POSTGRES_PASSWORD: test
          POSTGRES_DB: test
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      #      - checkout
      #      - run: cd << pipeline.parameters.server-path >>
      #      - node/install-packages:
      #          app-dir: << pipeline.parameters.working-dir >>/<< pipeline.parameters.server-path >>
      #      - run: cd << pipeline.parameters.server-path >> && npm run build
      - run: sudo apt-get update
      - run: sudo apt-get install postgresql-client
      - run: |
          psql \
           -d postgresql://test:test@localhost/test \
           -c "CREATE EXTENSION IF NOT EXISTS pgcrypto;";

           # Remove after https://github.com/jaredwray/keyv/commit/**************************************** is released
           # otherwise tests will fail with concurrent table creation
          psql \
           -d postgresql://test:test@localhost/test \
           -c "CREATE TABLE IF NOT EXISTS public.keyv(key VARCHAR(255) PRIMARY KEY, value TEXT)"
      - run:
          name: Preparing for execution
          command: |
            echo 'export GITHUB_PRIVATE_KEY=$(echo $GITHUB_PRIVATE_KEY_BASE64 | base64 -d)' >> $BASH_ENV
            source $BASH_ENV
      - run:
          name: Run E2E checks
          command: |
            cd << pipeline.parameters.server-path >>
            npm run install-browser
            npm run start:prod & server_pid=$!
            CHECKS_FILES=$(circleci tests glob "**/dist/server/src/**/*.e2e.test.js")
            echo "$CHECKS_FILES" | circleci tests run --command="JEST_JUNIT_ADD_FILE_ATTRIBUTE=true xargs npm run test:e2e:ci --runInBand --detectOpenHandles --" --verbose --split-by=timings
            echo "Killing the server with PID $server_pid"
            kill $server_pid
          environment:
            JEST_JUNIT_OUTPUT_DIR: ./reports/
      - store_artifacts:
          path: ~/.npm/_logs
      - store_test_results:
          path: ./<< pipeline.parameters.server-path >>/reports/

  notify_jira_about_build:
    description: Notify Jira about the build
    executor:
      name: node/default
      tag: << pipeline.parameters.node-version >>
    working_directory: << pipeline.parameters.working-dir >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - add-github-to-known-hosts
      - run:
          name: Check commit message for Jira tickets
          command: |
            if [[ "$CIRCLE_BRANCH" == "main" ]]; then
              git log -n 3
              COMMIT_MESSAGE=$(git show -s --format=%s $CIRCLE_SHA1)
              echo -e "\nCIRCLE_SHA1 message: $COMMIT_MESSAGE"
              if [[ ! "$COMMIT_MESSAGE" =~ ^(?i)(build: Bump up ) ]]; then
                ORIGINAL_CIRCLE_SHA1=$CIRCLE_SHA1
                echo "ORIGINAL_CIRCLE_SHA1: $ORIGINAL_CIRCLE_SHA1"
            
                echo 'export ORIGINAL_CIRCLE_SHA1=$ORIGINAL_CIRCLE_SHA1' >> $BASH_ENV
                CIRCLE_SHA1=$(git rev-parse HEAD)
            
                echo 'export CIRCLE_SHA1=$CIRCLE_SHA1' >> $BASH_ENV
                echo "CIRCLE_SHA1: $CIRCLE_SHA1"
              fi
            fi
      - jira/notify:
          job_type: build
          pipeline_id: << pipeline.id >>
          pipeline_number: << pipeline.number >>
          issue_regexp: (PLAT-[0-9]+)
      - run: # Now, restore the original CIRCLE_SHA1, only if it was changed:
          name: Restore CIRCLE_SHA1 (if it was changed)
          command: |
            if [ -n "$ORIGINAL_CIRCLE_SHA1" ]; then
              echo 'export CIRCLE_SHA1=$ORIGINAL_CIRCLE_SHA1' >> $BASH_ENV
            fi

  notify_jira_about_deployment:
    description: Notify Jira about the deployment
    parameters:
      environment:
        type: enum
        enum: [ 'dev', 'staging', 'prod' ] # Keep consistent with `deploy-env`.
      workflow-type:
        type: enum
        enum: [ 'client', 'server', 'manual' ]
        default: 'server'
        description: "Type of workflow calling this job (client/server/manual)"
    executor:
      name: node/default
      tag: << pipeline.parameters.node-version >>
    working_directory: << pipeline.parameters.working-dir >>
    steps:
      - attach_workspace:
          at: << pipeline.parameters.working-dir >>
      - add-github-to-known-hosts
      - run:
          name: Prevent duplicate notifications
          command: |
            echo "Checking deployment notification priority..."
            echo "is-client-build: << pipeline.parameters.is-client-build >>"
            echo "is-server-build: << pipeline.parameters.is-server-build >>"
            echo "workflow-type: << parameters.workflow-type >>"

            # If both client and server are building, only server should notify (completes last)
            if [[ "<< pipeline.parameters.is-client-build >>" == "true" &&
                  "<< pipeline.parameters.is-server-build >>" == "true" &&
                  "<< parameters.workflow-type >>" == "client" ]]; then
              echo "Both client and server building - server will notify when deployment is complete."
              echo "Skipping client notification to avoid premature notifications..."
              circleci step halt
            fi

            echo "Proceeding with notification..."
      - run:
          name: Check commit message for Jira tickets
          command: |
            if [[ "$CIRCLE_BRANCH" == "main" ]] || [[ "$CIRCLE_BRANCH" =~ ^(release|patch|hotfix)/v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
              # Get original commit info
              ORIGINAL_COMMIT=$CIRCLE_SHA1
              ORIGINAL_MESSAGE=$(git show -s --format=%s $ORIGINAL_COMMIT)
              echo -e "\nOriginal commit: $ORIGINAL_COMMIT"
              echo -e "Original message: $ORIGINAL_MESSAGE"

              # Find the most recent version bump commit (checking only subject lines)
              VERSION_BUMP_COMMIT=""
              while read -r commit; do
                subject=$(git show -s --format=%s $commit)
                if [[ "$subject" =~ ^[bB]uild:\ Bump\ up ]]; then
                  VERSION_BUMP_COMMIT=$commit
                  break
                fi
              done < <(git log -n 10 --format="%H")

              if [[ -n "$VERSION_BUMP_COMMIT" ]]; then
                VERSION_BUMP_MESSAGE=$(git show -s --format=%s $VERSION_BUMP_COMMIT)
                echo -e "\nFound version bump commit: $VERSION_BUMP_COMMIT"
                echo -e "Version bump message: $VERSION_BUMP_MESSAGE"

                # Export it for Jira notification
                echo "export JIRA_DEBUG_TEST_COMMIT=\"$VERSION_BUMP_COMMIT\"" >> $BASH_ENV
                echo "Using commit for Jira: $VERSION_BUMP_COMMIT"
              else
                echo "No version bump commit found in recent history. Using original commit."
              fi
            fi
      - run:
          name: Install ci-tools dependencies
          command: |
            npm install --prefix="<< pipeline.parameters.ci-tools-path >>"
      - when:
          condition:
            equal: [ << parameters.environment >>, "dev" ]
          steps:
            - jira/notify:
                environment: development
                environment_type: development
                job_type: deployment
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>
                issue_regexp: (PLAT-[0-9]+)
      - when:
          condition:
            equal: [ << parameters.environment >>, "staging" ]
          steps:
            - jira/notify:
                environment: staging
                environment_type: staging
                job_type: deployment
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>
                issue_regexp: (PLAT-[0-9]+)
            - run:
                name: Update release Jira tickets with actual version and impacted services
                command: |
                  if [[ -n "$JIRA_DEBUG_TEST_COMMIT" ]]; then
                    commit_message=$(git show -s --format=%s $JIRA_DEBUG_TEST_COMMIT)
                  else
                    commit_message=$(git show -s --format=%s $CIRCLE_SHA1)
                  fi
                  tickets=$(echo "$commit_message" | sed -E 's/.*\(([^)]+)\)$/\1/')

                  # Update tickets with actual version and labels
                  node "<< pipeline.parameters.ci-tools-path >>/jira-tickets-updater-cli.js" \
                    --tickets="$tickets" \
                    --update-actual-version \
                    --update-labels \
                    --ticket-regex="(PLAT-[0-9]+)"
      - when:
          condition:
            equal: [ << parameters.environment >>, "prod" ]
          steps:
            - jira/notify:
                environment: production
                environment_type: production
                job_type: deployment
                pipeline_id: << pipeline.id >>
                pipeline_number: << pipeline.number >>
                issue_regexp: (PLAT-[0-9]+)
            - run:
                name: Update release Jira tickets with actual version and impacted services
                command: |
                  if [[ -n "$JIRA_DEBUG_TEST_COMMIT" ]]; then
                    commit_message=$(git show -s --format=%s $JIRA_DEBUG_TEST_COMMIT)
                  else
                    commit_message=$(git show -s --format=%s $CIRCLE_SHA1)
                  fi
                  tickets=$(echo "$commit_message" | sed -E 's/.*\(([^)]+)\)$/\1/')

                  # Update tickets with actual version and labels
                  node "<< pipeline.parameters.ci-tools-path >>/jira-tickets-updater-cli.js" \
                    --tickets="$tickets" \
                    --update-actual-version \
                    --update-labels \
                    --ticket-regex="(PLAT-[0-9]+)"
      - run: # Now, restore the original CIRCLE_SHA1, only if it was changed:
          name: Restore CIRCLE_SHA1 (if it was changed)
          command: |
            if [ -n "$ORIGINAL_CIRCLE_SHA1" ]; then
              echo 'export CIRCLE_SHA1=$ORIGINAL_CIRCLE_SHA1' >> $BASH_ENV
            fi

workflows:
  # ================ Deployment workflows ================
  deploy-frontend-workflow:
    when: << pipeline.parameters.deploy-frontend >>
    jobs:
      - checkout-and-install-dependencies:
          name: checkout-and-install-frontend-dependencies
          app-dir: << pipeline.parameters.client-path >>
          branch: << pipeline.parameters.deploy-branch >>
          tag: << pipeline.parameters.deploy-tag >>
          commit: << pipeline.parameters.deploy-commit >>
      - build-client:
          env: << pipeline.parameters.deploy-env >>
          requires:
            - checkout-and-install-frontend-dependencies
      - deploy-frontend:
          env: << pipeline.parameters.deploy-env >>
          branch: << pipeline.parameters.deploy-branch >>
          tag: << pipeline.parameters.deploy-tag >>
          commit: << pipeline.parameters.deploy-commit >>
          distribution-id: << pipeline.parameters.deploy-distribution-id >>
          requires:
            - build-client
      - notify_jira_about_deployment:
          environment: << pipeline.parameters.deploy-env >>
          workflow-type: client
          requires:
            - deploy-frontend

  # For backend deployments, we create separate conditionals for each environment
  # DEV ENVIRONMENT
  deploy-backend-to-dev-workflow:
    when:
      and:
        - << pipeline.parameters.deploy-backend >>
        - equal: [ dev, << pipeline.parameters.deploy-env >> ]
    jobs:
      - checkout-and-install-dependencies:
          name: checkout-and-install-backend-dependencies
          app-dir: << pipeline.parameters.server-path >>
          branch: << pipeline.parameters.deploy-branch >>
          tag: << pipeline.parameters.deploy-tag >>
          commit: << pipeline.parameters.deploy-commit >>
          run-head-check: true
      - build:
          name: build-backend
          requires:
            - checkout-and-install-backend-dependencies
      - deploy-backend:
          name: deploy-backend-to-dev
          env: dev-node20
          branch: << pipeline.parameters.deploy-branch >>
          tag: << pipeline.parameters.deploy-tag >>
          commit: << pipeline.parameters.deploy-commit >>
          requires:
            - build-backend
      - notify_jira_about_deployment:
          name: notify-jira-about-backend-deployment
          environment: << pipeline.parameters.deploy-env >>
          workflow-type: server
          requires:
            - deploy-backend-to-dev

  # STAGING ENVIRONMENT
  deploy-backend-to-staging-workflow:
    when:
      and:
        - << pipeline.parameters.deploy-backend >>
        - equal: [ staging, << pipeline.parameters.deploy-env >> ]
    jobs:
      - checkout-and-install-dependencies:
          name: checkout-and-install-backend-dependencies
          app-dir: << pipeline.parameters.server-path >>
          branch: << pipeline.parameters.deploy-branch >>
          tag: << pipeline.parameters.deploy-tag >>
          commit: << pipeline.parameters.deploy-commit >>
          run-head-check: true
      - build:
          name: build-backend
          requires:
            - checkout-and-install-backend-dependencies
      - deploy-backend:
          name: deploy-backend-to-staging
          env: staging-node20
          branch: << pipeline.parameters.deploy-branch >>
          tag: << pipeline.parameters.deploy-tag >>
          commit: << pipeline.parameters.deploy-commit >>
          requires:
            - build-backend
      - notify_jira_about_deployment:
          name: notify-jira-about-backend-deployment
          environment: << pipeline.parameters.deploy-env >>
          workflow-type: server
          requires:
            - deploy-backend-to-staging

  # PROD ENVIRONMENT
  deploy-backend-to-prod-workflow:
    when:
      and:
        - << pipeline.parameters.deploy-backend >>
        - equal: [ prod, << pipeline.parameters.deploy-env >> ]
    jobs:
      - checkout-and-install-dependencies:
          name: checkout-and-install-backend-dependencies
          app-dir: << pipeline.parameters.server-path >>
          branch: << pipeline.parameters.deploy-branch >>
          tag: << pipeline.parameters.deploy-tag >>
          commit: << pipeline.parameters.deploy-commit >>
          run-head-check: true
      - build:
          name: build-backend
          requires:
            - checkout-and-install-backend-dependencies
      - deploy-backend:
          name: deploy-backend-to-prod
          env: production-node20
          branch: << pipeline.parameters.deploy-branch >>
          tag: << pipeline.parameters.deploy-tag >>
          commit: << pipeline.parameters.deploy-commit >>
          requires:
            - build-backend
      - notify_jira_about_deployment:
          name: notify-jira-about-backend-deployment
          environment: << pipeline.parameters.deploy-env >>
          workflow-type: server
          requires:
            - deploy-backend-to-prod

  # ================ Setup workflows ================
  setup-development-workflow:
    when:
      and:
        - << pipeline.parameters.run-setup >>  # Only run when setup is enabled
        - not: << pipeline.parameters.is-triggered >>
        - not: << pipeline.parameters.deploy-frontend >>  # Don't run if deployment is triggered
        - not: << pipeline.parameters.deploy-backend >>  # Don't run if deployment is triggered
        - not:
            equal: [ main, << pipeline.git.branch >> ]
        - not:
            matches:
              pattern: "^(release|hotfix|patch)/.*"
              value: << pipeline.git.branch >>
    jobs:
      - check-for-changes:
          name: check-for-client-changes
          app-dir: << pipeline.parameters.client-path >>
          branch: $CIRCLE_BRANCH
          workflow: build-frontend-development
      - check-for-changes:
          name: check-for-server-changes
          app-dir: << pipeline.parameters.server-path >>
          branch: $CIRCLE_BRANCH
          workflow: build-backend-development
      - check-for-changes:
          name: check-for-ci-tools-changes
          app-dir: << pipeline.parameters.ci-tools-path >>
          branch: $CIRCLE_BRANCH
          workflow: build-frontend-development
      - check-for-changes:
          name: check-for-circleci-changes
          app-dir: ".circleci"
          branch: $CIRCLE_BRANCH
          workflow: setup-development-workflow
      - check-for-changes:
          name: check-for-shared-changes
          app-dir: << pipeline.parameters.shared-path >>
          branch: $CIRCLE_BRANCH
          workflow: ignored
      - accumulate-env-vars:
          requires:
            - check-for-client-changes
            - check-for-server-changes
            - check-for-ci-tools-changes
            - check-for-circleci-changes
            - check-for-shared-changes
      - setup:
          requires:
            - accumulate-env-vars
      - test-check-for-changes:
          required-vars: "HAS_CLIENT_CHANGES HAS_SERVER_CHANGES HAS_CIRCLECI_CHANGES HAS_CI_TOOLS_CHANGES HAS_SHARED_CHANGES"
          requires:
            - accumulate-env-vars

  setup-staging-workflow:
    when:
      and:
        - << pipeline.parameters.run-setup >>  # Only run when setup is enabled
        - not: << pipeline.parameters.is-triggered >>
        - not: << pipeline.parameters.deploy-frontend >>  # Don't run if deployment is triggered
        - not: << pipeline.parameters.deploy-backend >>  # Don't run if deployment is triggered
        - matches:
            pattern: "^(release|hotfix|patch)/.*"
            value: << pipeline.git.branch >>
    jobs:
      - check-for-changes:
          name: check-for-client-changes
          app-dir: << pipeline.parameters.client-path >>
          branch: $CIRCLE_BRANCH
          workflow: build-frontend-staging
      - check-for-changes:
          name: check-for-server-changes
          app-dir: << pipeline.parameters.server-path >>
          branch: $CIRCLE_BRANCH
          workflow: build-backend-staging
      - check-for-changes:
          name: check-for-ci-tools-changes
          app-dir: << pipeline.parameters.ci-tools-path >>
          branch: $CIRCLE_BRANCH
          workflow: build-frontend-staging
      - check-for-changes:
          name: check-for-circleci-changes
          app-dir: ".circleci"
          branch: $CIRCLE_BRANCH
          workflow: setup-staging-workflow
      - check-for-changes:
          name: check-for-shared-changes
          app-dir: << pipeline.parameters.shared-path >>
          branch: $CIRCLE_BRANCH
          workflow: ignored
      - accumulate-env-vars:
          requires:
            - check-for-client-changes
            - check-for-server-changes
            - check-for-ci-tools-changes
            - check-for-circleci-changes
            - check-for-shared-changes
      - setup:
          requires:
            - accumulate-env-vars
      - test-check-for-changes:
          required-vars: "HAS_CLIENT_CHANGES HAS_SERVER_CHANGES HAS_CIRCLECI_CHANGES HAS_CI_TOOLS_CHANGES HAS_SHARED_CHANGES"
          requires:
            - accumulate-env-vars

  setup-production-workflow:
    when:
      and:
        - << pipeline.parameters.run-setup >>  # Only run when setup is enabled
        - not: << pipeline.parameters.is-triggered >>
        - not: << pipeline.parameters.deploy-frontend >>  # Don't run if deployment is triggered
        - not: << pipeline.parameters.deploy-backend >>  # Don't run if deployment is triggered
        - equal: [ main, << pipeline.git.branch >> ]
    jobs:
      - check-for-changes:
          name: check-for-client-changes
          app-dir: << pipeline.parameters.client-path >>
          branch: $CIRCLE_BRANCH
          workflow: build-frontend-production
      - check-for-changes:
          name: check-for-server-changes
          app-dir: << pipeline.parameters.server-path >>
          branch: $CIRCLE_BRANCH
          workflow: build-backend-production
      - check-for-changes:
          name: check-for-ci-tools-changes
          app-dir: << pipeline.parameters.ci-tools-path >>
          branch: $CIRCLE_BRANCH
          workflow: build-frontend-production
      - check-for-changes:
          name: check-for-circleci-changes
          app-dir: ".circleci"
          branch: $CIRCLE_BRANCH
          workflow: setup-development-workflow
      - check-for-changes:
          name: check-for-shared-changes
          app-dir: << pipeline.parameters.shared-path >>
          branch: $CIRCLE_BRANCH
          workflow: ignored
      - accumulate-env-vars:
          requires:
            - check-for-client-changes
            - check-for-server-changes
            - check-for-ci-tools-changes
            - check-for-circleci-changes
            - check-for-shared-changes
      - setup:
          requires:
            - accumulate-env-vars
      - test-check-for-changes:
          required-vars: "HAS_CLIENT_CHANGES HAS_SERVER_CHANGES HAS_CIRCLECI_CHANGES HAS_CI_TOOLS_CHANGES HAS_SHARED_CHANGES"
          requires:
            - accumulate-env-vars

  merge-changes-back-to-develop:
    when:
      and:
        - << pipeline.parameters.should-merge-back >>
        - << pipeline.parameters.is-triggered >>
        - not: << pipeline.parameters.run-setup >>
    jobs:
      - push-changes-to-develop:
          filters:
            branches:
              only:
                - main
                - /(release|hotfix|patch)\/.*/

  # ================ Client workflows ================
  build-frontend-development:
    # should not run development workflow in case E2E checks are triggered manually (should-run-checks is true)
    when:
      and:
        - << pipeline.parameters.is-triggered >>
        - << pipeline.parameters.is-client-build >>
        - not: << pipeline.parameters.should-run-checks >>
        - not: << pipeline.parameters.should-merge-back >>
        - not: << pipeline.parameters.deploy-frontend >>  # Don't run if deployment is triggered
        - not: << pipeline.parameters.deploy-backend >>  # Don't run if deployment is triggered
    jobs:
      - checkout-code:
          name: checkout-client
          filters:
            branches:
              ignore:
                - main
                - /(release|hotfix|patch)\/.*/
            tags:
              ignore: /.*/
      - install-dependencies:
          name: install-client-dependencies
          working_dir: << pipeline.parameters.working-dir >>
          path: << pipeline.parameters.client-path >>
          requires:
            - checkout-client
      - build-client:
          name: build-development
          env: dev
          requires:
            - install-client-dependencies
      - build-storybook:
          requires:
            - install-client-dependencies
      - run-tests:
          requires:
            - install-client-dependencies
      - run-lint:
          name: run-client-lint
          path: << pipeline.parameters.client-path >>
          requires:
            - install-client-dependencies
      - run-type-check:
          requires:
            - install-client-dependencies
      - deploy-frontend:
          name: deploy-to-development
          env: dev
          distribution-id: << pipeline.parameters.dev-distribution-id >>
          requires:
            - run-tests
            - run-client-lint
            - run-type-check
            - build-development
          filters: &filters-development-branch
            branches:
              only:
                - develop
                - /deploy-to-dev_.*/
            tags:
              ignore: /.*/
      - deploy-storybook:
          requires:
            - build-storybook
          filters:
            <<: *filters-development-branch

  build-frontend-staging:
    # should not run development workflow in case E2E checks are triggered manually (should-run-checks is true)
    when:
      and:
        - << pipeline.parameters.is-triggered >>
        - << pipeline.parameters.is-client-build >>
        - not: << pipeline.parameters.should-run-checks >>
        - not: << pipeline.parameters.should-merge-back >>
        - not: << pipeline.parameters.deploy-frontend >>  # Don't run if deployment is triggered
        - not: << pipeline.parameters.deploy-backend >>  # Don't run if deployment is triggered
    jobs:
      - checkout-code:
          name: checkout-client
          filters:
            branches:
              only: /(release|hotfix|patch)\/.*/
            tags:
              ignore: /.*/
      - install-dependencies:
          name: install-client-dependencies
          working_dir: << pipeline.parameters.working-dir >>
          path: << pipeline.parameters.client-path >>
          requires:
            - checkout-client
      - bump-up-version:
          name: bump-up-version-for-staging-client
          requires:
            - install-client-dependencies
      - build-client:
          name: build-client-staging
          env: staging
          requires:
            - install-client-dependencies
            - bump-up-version-for-staging-client
      - run-tests:
          requires:
            - install-client-dependencies
      - run-lint:
          name: run-client-lint
          path: << pipeline.parameters.client-path >>
          requires:
            - install-client-dependencies
      - run-type-check:
          requires:
            - install-client-dependencies
      - push-changes-after-version-bump-up:
          requires:
            - run-tests
            - run-client-lint
            - run-type-check
            - build-client-staging
      - trigger-merge-back-to-develop:
          requires:
            - push-changes-after-version-bump-up

  build-frontend-production:
    # should not run development workflow in case E2E checks are triggered manually (should-run-checks is true)
    when:
      and:
        - << pipeline.parameters.is-triggered >>
        - << pipeline.parameters.is-client-build >>
        - not: << pipeline.parameters.should-run-checks >>
        - not: << pipeline.parameters.should-merge-back >>
        - not: << pipeline.parameters.deploy-frontend >>  # Don't run if deployment is triggered
        - not: << pipeline.parameters.deploy-backend >>  # Don't run if deployment is triggered
    jobs:
      - checkout-code:
          name: checkout-client
          filters: &filters-production
            tags:
              only: /v[0-9]+\.[0-9]+\.[0-9]+/
            branches:
              only: main
      - install-dependencies:
          name: install-client-dependencies
          working_dir: << pipeline.parameters.working-dir >>
          path: << pipeline.parameters.client-path >>
          requires:
            - checkout-client
          filters:
            <<: *filters-production
      - bump-up-version:
          name: bump-up-version-for-production-client
          filters: &filters-production-branch
            tags:
              ignore: /.*/
            branches:
              only: main
          requires:
            - install-client-dependencies
      - build-client: # build from branch
          env: prod
          requires:
            - bump-up-version-for-production-client
          filters:
            <<: *filters-production-branch
      - build-client: # build from production tag
          env: prod
          name: build-client-production
          requires:
            - install-client-dependencies
          filters: &filters-production-tag
            tags:
              only: /v[0-9]+\.[0-9]+\.[0-9]+/
            branches:
              ignore: /.*/
      - run-tests:
          requires:
            - install-client-dependencies
          filters:
            <<: *filters-production-branch
      - run-lint:
          name: run-client-lint
          path: << pipeline.parameters.client-path >>
          requires:
            - install-client-dependencies
          filters:
            <<: *filters-production-branch
      - run-type-check:
          requires:
            - install-client-dependencies
          filters:
            <<: *filters-production-branch
      - hold:
          name: approval
          type: approval # requires that an in-app button be clicked by an appropriate member of the project to continue.
          requires:
            - run-tests
            - run-client-lint
            - run-type-check
            - build-client
      - push-changes-after-version-bump-up:
          filters:
            <<: *filters-production-branch
          requires:
            - approval
      - trigger-merge-back-to-develop:
          requires:
            - push-changes-after-version-bump-up
          filters:
            <<: *filters-production-branch
      - deploy-frontend:
          name: deploy-client-to-production
          env: prod
          distribution-id: << pipeline.parameters.prod-distribution-id >>
          requires:
            - build-client
            - approval
          filters:
            <<: *filters-production-branch
      - notify_jira_about_deployment:
          name: notify-jira-about-production-deployment
          environment: prod
          workflow-type: client
          requires:
            - deploy-client-to-production
          filters:
            <<: *filters-production-branch

  #  merge-client-changes-back-to-develop:
  #    when: << pipeline.parameters.is-client-build >>
  #    jobs:
  #      - push-changes-to-develop:
  #          filters:
  #            tags:
  #              only: /v[0-9]+\.[0-9]+\.[0-9]+.*/
  #            branches:
  #              ignore: /.*/

  run-e2e-checks:
    when:
      and:
        - << pipeline.parameters.should-run-checks >>
        - << pipeline.parameters.is-client-build >>
        - not: { equal: [ << pipeline.parameters.testing-environment >>, "staging" ] }
        - not: { equal: [ << pipeline.parameters.testing-environment >>, "production" ] }
    jobs:
      - checkout-code
      - install-dependencies:
          name: install-client-dependencies
          working_dir: << pipeline.parameters.working-dir >>
          path: << pipeline.parameters.client-path >>
          requires:
            - checkout-code
      - run-e2e:
          env: << pipeline.parameters.testing-environment >>
          run-headless: false
          context: frontend_development
          requires:
            - install-client-dependencies

  run-e2e-checks-on-staging:
    when:
      and:
        - << pipeline.parameters.should-run-checks >>
        - << pipeline.parameters.is-client-build >>
        - equal: [ << pipeline.parameters.testing-environment >>, "staging" ]
    jobs:
      - checkout-code
      - install-dependencies:
          name: install-client-dependencies
          working_dir: << pipeline.parameters.working-dir >>
          path: << pipeline.parameters.client-path >>
          requires:
            - checkout-code
      - run-e2e:
          env: staging
          run-headless: false
          context: frontend_staging
          requires:
            - install-client-dependencies

  run-e2e-checks-on-production:
    when:
      and:
        - << pipeline.parameters.should-run-checks >>
        - << pipeline.parameters.is-client-build >>
        - equal: [ << pipeline.parameters.testing-environment >>, "production" ]
    jobs:
      - checkout-code
      - install-dependencies:
          name: install-client-dependencies
          working_dir: << pipeline.parameters.working-dir >>
          path: << pipeline.parameters.client-path >>
          requires:
            - checkout-code
      - run-e2e:
          env: production
          run-headless: false
          context: frontend_production
          requires:
            - install-client-dependencies

# TODO: Update dependencies, check if automation still passes
#  scheduled-e2e-checks:
#    triggers:
#      - schedule:
#          cron: "0 3 * * 1-5"
#          filters:
#            branches:
#              only:
#                - develop
#    jobs:
#      - checkout-code
#      - install-dependencies:
#          name: install-client-dependencies
#          working_dir: << pipeline.parameters.working-dir >>
#          path: << pipeline.parameters.client-path >>
#          requires:
#            - checkout-code
#      - run-e2e:
#          name: run-scheduled-e2e-checks-on-staging
#          env: staging
#          run-headless: false
#          context: frontend_staging
#          requires:
#            - install-client-dependencies

  # ================ Server workflows ================
  build-backend-development:
    when:
      and:
        - << pipeline.parameters.is-triggered >>
        - << pipeline.parameters.is-server-build >>
        - not: << pipeline.parameters.should-merge-back >>
        - not: << pipeline.parameters.deploy-frontend >>  # Don't run if deployment is triggered
        - not: << pipeline.parameters.deploy-backend >>  # Don't run if deployment is triggered
    jobs:
      - checkout-code:
          name: checkout-server
          reset-local-changes: true
          filters:
            branches:
              ignore:
                - main
                - /(release|hotfix|patch)\/.*/
      - install-dependencies:
          name: install-server-dependencies
          working_dir: << pipeline.parameters.working-dir >>
          path: << pipeline.parameters.server-path >>
          requires:
            - checkout-server
      - build:
          requires:
            - install-server-dependencies
      - notify_jira_about_build:
          name: notify-jira-about-development-build
          requires:
            - build
      - run-lint:
          name: run-server-lint
          path: << pipeline.parameters.server-path >>
          requires:
            - install-server-dependencies
      - run-unit-tests:
          name: run-unit-checks
          requires:
            - build
      - run-integration-tests:
          name: run-integration-tests
          requires:
            - build
      - deploy-backend:
          name: deploy-server-to-dev
          env: dev-node20
          requires:
            - build
            - run-server-lint
            - run-unit-checks
            - run-integration-tests
          filters:
            branches:
              only:
                - develop
                - /deploy-to-dev_.*/
      - notify_jira_about_deployment:
          name: notify-jira-about-development-deployment
          environment: dev
          workflow-type: server
          requires:
            - deploy-server-to-dev

  build-backend-staging:
    when:
      and:
        - << pipeline.parameters.is-triggered >>
        - << pipeline.parameters.is-server-build >>
        - not: << pipeline.parameters.should-merge-back >>
        - not: << pipeline.parameters.deploy-frontend >>  # Don't run if deployment is triggered
        - not: << pipeline.parameters.deploy-backend >>  # Don't run if deployment is triggered
    jobs:
      - checkout-code:
          name: checkout-server
          reset-local-changes: true
          filters: &filters-staging
            branches:
              only: /(release|hotfix|patch)\/.*/
      - install-dependencies:
          name: install-server-dependencies
          working_dir: << pipeline.parameters.working-dir >>
          path: << pipeline.parameters.server-path >>
          requires:
            - checkout-server
      - bump-up-version:
          name: bump-up-version-for-staging-server
          filters:
            <<: *filters-staging
          requires:
            - install-server-dependencies
      - run-lint:
          name: run-server-lint
          path: << pipeline.parameters.server-path >>
          requires:
            - bump-up-version-for-staging-server
      - build:
          name: build-server-staging
          requires:
            - bump-up-version-for-staging-server
      - notify_jira_about_build:
          name: notify-jira-about-staging-build
          requires:
            - build-server-staging
      - run-unit-tests:
          name: run-unit-checks
          requires:
            - build-server-staging
      - run-integration-tests:
          name: run-integration-tests
          requires:
            - build-server-staging
      - push-changes-after-version-bump-up:
          filters:
            <<: *filters-staging
          requires:
            - run-server-lint
            - build-server-staging
            - run-unit-checks
            - run-integration-tests
      - trigger-merge-back-to-develop:
          requires:
            - push-changes-after-version-bump-up
          filters:
            <<: *filters-staging

  build-backend-production:
    when:
      and:
        - << pipeline.parameters.is-triggered >>
        - << pipeline.parameters.is-server-build >>
        - not: << pipeline.parameters.should-merge-back >>
        - not: << pipeline.parameters.deploy-frontend >>  # Don't run if deployment is triggered
        - not: << pipeline.parameters.deploy-backend >>  # Don't run if deployment is triggered
    jobs:
      - checkout-code:
          name: checkout-server
          reset-local-changes: true
          filters: &filters-production
            tags:
              only: /v[0-9]+\.[0-9]+\.[0-9]+/
            branches:
              only: main
      - install-dependencies:
          name: install-server-dependencies
          working_dir: << pipeline.parameters.working-dir >>
          path: << pipeline.parameters.server-path >>
          requires:
            - checkout-server
          filters:
            <<: *filters-production
      - bump-up-version:
          name: bump-up-version-for-production-server
          filters: &filters-production-branch
            branches:
              only: main
            tags:
              ignore: /.*/
          requires:
            - install-server-dependencies
      - run-lint:
          name: run-server-lint
          path: << pipeline.parameters.server-path >>
          requires:
            - install-server-dependencies
          filters:
            <<: *filters-production-branch
      - build: # build from branch
          requires:
            - bump-up-version-for-production-server
          filters:
            <<: *filters-production-branch
      - notify_jira_about_build:
          name: notify-jira-about-production-branch-build
          requires:
            - build
          filters:
            <<: *filters-production-branch
      - build: # build from production tag
          name: build-server-production
          requires:
            - install-server-dependencies
          filters: &filters-production-tag
            tags:
              only: /v[0-9]+\.[0-9]+\.[0-9]+/
            branches:
              ignore: /.*/
      - notify_jira_about_build:
          name: notify-jira-about-production-tag-build
          requires:
            - build-server-production
          filters:
            <<: *filters-production-tag
      - run-unit-tests:
          name: run-unit-checks
          requires:
            - build-server-production
          filters:
            <<: *filters-production-branch
      - run-integration-tests:
          name: run-integration-tests
          requires:
            - build-server-production
          filters:
            <<: *filters-production-branch
      - hold:
          name: approval
          type: approval # requires that an in-app button be clicked by an appropriate member of the project to continue.
          requires:
            - run-server-lint
            - build
            - run-unit-checks
            - run-integration-tests
          filters:
            <<: *filters-production-branch
      - push-changes-after-version-bump-up:
          requires:
            - approval
          filters:
            <<: *filters-production-branch
      - trigger-merge-back-to-develop:
          requires:
            - push-changes-after-version-bump-up
          filters:
            <<: *filters-production-branch
      - deploy-backend:
          name: deploy-server-to-prod
          env: production-node20
          requires:
            - build-server-production
            - push-changes-after-version-bump-up
          filters:
            <<: *filters-production-branch
      - notify_jira_about_deployment:
          name: notify-jira-about-production-deployment
          environment: prod
          workflow-type: server
          requires:
            - deploy-server-to-prod
          filters:
            <<: *filters-production-branch

  build-shared-development:
    when:
      and:
        - << pipeline.parameters.is-triggered >>
        - << pipeline.parameters.is-shared-build >>
        - not: << pipeline.parameters.should-merge-back >>
        - not: << pipeline.parameters.deploy-frontend >>  # Don't run if deployment is triggered
        - not: << pipeline.parameters.deploy-backend >>  # Don't run if deployment is triggered
    jobs:
      - checkout-code:
          name: checkout-shared
          filters:
            branches:
              ignore:
                - main
            tags:
              ignore: /.*/
      - install-dependencies:
          name: install-shared-dependencies
          working_dir: << pipeline.parameters.working-dir >>
          path: << pipeline.parameters.shared-path >>
          requires:
            - checkout-shared
      - run-lint:
          name: run-shared-lint
          path: << pipeline.parameters.shared-path >>
          requires:
            - install-shared-dependencies
