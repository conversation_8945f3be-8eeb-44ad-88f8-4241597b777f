# CI/CD Pipeline Documentation

This document provides a comprehensive overview of our CI/CD pipeline implemented with CircleCI. It explains the standard deployment flows, parameters for manual deployments, and various CI/CD components.

## Table of Contents

- [Release Branch Management](#release-branch-management)
- [Standard Deployment Workflows](#standard-deployment-workflows)
- [Manual Deployment Parameters](#manual-deployment-parameters)
- [Force Running Builds](#force-running-builds)
- [Change Detection System](#change-detection-system)
- [Jira Integration](#jira-integration)
- [Version Bumping](#version-bumping)
- [Post-Deployment Merging](#post-deployment-merging)
- [Troubleshooting](#troubleshooting)
- [CI Tools Reference](#ci-tools-reference)

## Release Branch Management

Our CI/CD pipeline supports the GitFlow workflow with specialized tools for managing release branches.

### Creating Release Branches

The `create-release-branch-cli.js` tool helps developers prepare release, patch, or hotfix branches while following standardized versioning practices.

#### Overview

The `create-release-branch-cli.js` tool enables developers to:
- Create and validate release branches that follow naming conventions
- Generate appropriate branch names based on the release type
- Pull the latest changes from the source branch
- Optionally push changes to the remote repository

#### Version Generation and Branch Naming

The tool automatically generates branch names based on the current version in `version.json`, following semantic versioning principles:

| Release Type | Current Version | New Branch | Version Change |
|--------------|-----------------|------------|----------------|
| `release`    | `X.Y.Z`         | `release/vX.(Y+1).0` | Increases minor version, resets patch |
| `patch`      | `X.Y.Z`         | `patch/vX.Y.(Z+1)` | Increases patch version |
| `hotfix`     | `X.Y.Z`         | `hotfix/vX.Y.(Z+1)` | Increases patch version |

**Examples:**
- If current version is `2.3.1`, a release branch would be `release/v2.4.0`
- If current version is `2.3.1`, a patch branch would be `patch/v2.3.2`
- If current version is `2.3.1`, a hotfix branch would be `hotfix/v2.3.2`

Source branches typically follow these patterns:
- `release` branches are usually created from `develop`
- `patch` and `hotfix` branches are usually created from `main`

For more detailed information about versioning, see [VERSION_MANAGEMENT.md](../VERSION_MANAGEMENT.md).

#### Usage

##### Direct usage
```bash
# Create a release branch from the develop branch
node apps/ci-tools/create-release-branch-cli.js --type=release --branch=develop

# Create a patch branch from the main branch
node apps/ci-tools/create-release-branch-cli.js --type=patch --branch=main

# Create a hotfix branch from main and push it to remote
node apps/ci-tools/create-release-branch-cli.js --type=hotfix --branch=main --push
```

##### Using npm scripts from project root
```bash
# Create a release branch from the develop branch
npm run create-branch:release -- --branch=develop

# Create a patch branch from the main branch
npm run create-branch:patch -- --branch=main

# Create a hotfix branch from main and push it to remote
npm run create-branch:hotfix -- --branch=main --push

# Create a release branch with source branch predefined
npm run create-branch:release:develop

# Create a patch branch from main with source branch predefined
npm run create-branch:patch:main
```

##### Using npm scripts from ci-tools directory
```bash
# Navigate to the ci-tools directory first
cd apps/ci-tools

# Create a release branch from the develop branch
npm run create-branch:release -- --branch=develop

# Create a patch branch from the main branch
npm run create-branch:patch -- --branch=main

# Create a hotfix branch from main and push it to remote
npm run create-branch:hotfix -- --branch=main --push

# Create a release branch with source branch predefined
npm run create-branch:release:develop

# Create a patch branch from main with source branch predefined
npm run create-branch:patch:main
```

#### Troubleshooting Common Errors

When using the release branch creation tool, you might encounter these common errors:

1. **Invalid branch name format**:
   ```
   Error: Invalid branch name format. Expected: (release|patch|hotfix)/vX.Y.Z or develop|main
   ```
   **Resolution**: Ensure your source branch is either `develop`, `main`, or follows the pattern `release/vX.Y.Z`, `patch/vX.Y.Z`, or `hotfix/vX.Y.Z`.

2. **Source branch doesn't exist**:
   ```
   Error: Branch "branch-name" does not exist in the repository.
   ```
   **Resolution**: Verify the branch name and make sure it exists in your local repository. Run `git fetch` to update your local reference to remote branches.

3. **Uncommitted changes in working directory**:
   ```
   Error: Branch creation blocked as a precaution - working directory must be clean.
   ```
   **Resolution**: The tool requires a clean working directory. Either commit your changes using `git commit -m "Your message"`, stash them using `git stash`, or discard them using `git restore .` before running the tool.

4. **Branch already exists**:
   ```
   Error: Branch "branch-name" already exists.
   ```
   **Resolution**: The error message provides two options:
   - Use the existing branch: `git checkout branch-name`
   - Delete and recreate: `git branch -D branch-name` and then run the creation command again

5. **Invalid version format**:
   ```
   Error: Invalid version format: X.Y.Z
   ```
   **Resolution**: Check the `version.json` file at the root of the repository to ensure it contains a valid semantic version.

6. **Failed to push to remote**:
   ```
   Error: Failed to push branch to remote
   ```
   **Resolution**: This usually happens due to permissions issues or network problems. Ensure you have write access to the repository and try manually pushing with `git push origin branch-name`.

## Standard Deployment Workflows

Our CI/CD system primarily follows a GitFlow model with three environments. Most deployments happen automatically based on Git branch activity without requiring manual parameter configuration.

### Development (dev)
- **Trigger**: Pushing to `develop` branch
- **Process**: Fully automated - code is built, tested, and deployed to development when changes are detected
- **Pipeline**: `build-frontend-development` and `build-backend-development` workflows

### Staging (staging)
- **Trigger**: Pushing to a `release/*`, `hotfix/*`, or `patch/*` branch
- **Process**: Fully automated - code is built, tested, version is bumped with RC tag, and deployed to staging
- **Pipeline**: `build-frontend-staging` and `build-backend-staging` workflows

### Production (prod)
- **Trigger**: Merging to `main` branch
- **Process**: Automated with approval gate - code is built, tested, version is bumped, and requires manual approval before deployment
- **Pipeline**: `build-frontend-production` and `build-backend-production` workflows
- **Note**: This is the primary deployment scenario, which runs automatically when merging to main

## Manual Deployment Parameters

While standard workflows don't require manual parameters, you can also trigger deployments manually. When doing so, you need to understand which parameters to set.

### Deployment Workflow Triggers

To manually trigger a deployment, you must set one or both of these parameters:

| Parameter | Type | Description |
|-----------|------|-------------|
| `deploy-frontend` | boolean | Set to `true` to deploy the frontend |
| `deploy-backend` | boolean | Set to `true` to deploy the backend |

If both are set to `true`, both frontend and backend will be deployed.

### Deployment Parameters

When manually triggering deployments, use the following parameters:

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `deploy-env` | enum | Yes | Environment to deploy to: `dev`, `staging`, or `prod` |
| `deploy-commit` | string | No | Specific commit SHA to deploy (highest priority) |
| `deploy-tag` | string | No | Git tag to deploy (second priority) |
| `deploy-branch` | string | No | Branch name to deploy (lowest priority) |
| `deploy-distribution-id` | string | No | Override CloudFront distribution ID for frontend deployments |

**Important Notes**:
- If no source selection parameter is provided, the current branch will be used
- If multiple source selection parameters are provided, only the highest priority one will be used
- Example: If you trigger from the `main` branch without specifying any source, it will deploy the current state of `main`

### Example: Manual Frontend Deployment to Staging

To manually deploy the frontend to staging from a specific tag:

```
deploy-frontend: true
deploy-env: staging
deploy-tag: v2.5.0
```

### Example: Manual Backend Deployment to Production

To manually deploy the backend to production from the current state of main:

```
deploy-backend: true
deploy-env: prod
```

## Force Running Builds

Sometimes you need to run all builds regardless of what files have changed. This is useful for:
- Ensuring all components are built after a significant change
- Troubleshooting build issues
- Running builds when the change detection system might not pick up relevant changes

### How to Force Run All Builds

Set the `force-run` parameter to `true` when triggering a pipeline:

```
force-run: true
```

When this parameter is set:
1. The change detection system still runs, but its results are overridden
2. All build workflows are triggered regardless of which files changed
3. This affects all components: frontend, backend, CI tools, etc.

This is particularly useful when:
- You're working with changes across multiple components
- The change detection system is not correctly identifying relevant changes

**Note**: Changes to the `apps/shared` directory or CircleCI configuration automatically trigger all builds, so there's no need to use `force-run` in those cases.

## Change Detection System

Our pipeline uses a sophisticated change detection system to determine which components need to be rebuilt:

### How It Works

1. When a pipeline runs, the `setup` workflow calls the `check-for-changes` job for each application component
2. The CI change detector (`ci-change-detector.js`) analyzes Git history to identify changed files
3. For each component (client, server, etc.), it sets environment variables like `HAS_CLIENT_CHANGES` to indicate if files in that component have changed
4. These environment variables are stored in temporary files and later accumulated into a single environment file
5. The `setup` job then triggers a new pipeline with parameters like `is-client-build` and `is-server-build` set based on the change detection results

### Relationship with Pipeline Parameters

The change detector directly influences these pipeline parameters:
- `is-client-build` - Set to `true` when client code changes are detected
- `is-server-build` - Set to `true` when server code changes are detected
- `is-ci-tools-build` - Set to `true` when CI tools code changes are detected
- `is-circleci-build` - Set to `true` when CircleCI config changes are detected
- `is-shared-build` - Set to `true` when shared code changes are detected

The dynamic generation of these parameters allows the pipeline to selectively run only the workflows needed for the components that changed, improving efficiency.

### Special Cases
- If `HAS_CIRCLECI_CHANGES` or `HAS_SHARED_CHANGES` is true, all builds are forced to run regardless of individual change status
- The `force-run` parameter can override the change detection and force all components to build
- The system handles edge cases like skipped CI messages, branch point detection, and missing commits

## Jira Integration

Our pipeline integrates with Jira for tracking issues included in deployments:

### How It Works

1. **Ticket Extraction**: The pipeline uses `jira-tickets-extractor-cli.js` to identify tickets included in releases by analyzing Git history and commit messages
2. **Commit Analysis**: Commands check commit messages for Jira ticket numbers using the pattern `(PLAT-[0-9]+)`
3. **Jira Notifications**: The CircleCI Jira orb (`circleci/jira@2.1.0`) sends build and deployment information to Jira
4. **Service Detection**: Git commit history is analyzed to determine which services (client, server, etc.) were affected by each ticket
5. **Ticket Updates**: For staging and production deployments, `jira-tickets-updater-cli.js` automatically updates identified tickets with actual versions (not fix versions) and service labels

### Special Cases

- The integration is smart enough to handle special cases like version bump commits
- If the current commit is a version bump (starting with "build: Bump up"), it will look for the original commit SHA
- The `issue_regexp` parameter (set to `(PLAT-[0-9]+)`) controls which ticket format is recognized

### JIRA Tickets Extractor

The `jira-tickets-extractor-cli.js` tool analyzes Git history to identify JIRA tickets that should be included in release notes.
It compares commit messages between releases to determine which tickets are new and haven't been previously released.

#### How the Extractor Works

1. **Release Analysis**: Compares the latest two Git tags to identify commit messages in the upcoming release
2. **Ticket Identification**: Extracts JIRA ticket IDs from commit messages using regex pattern matching
3. **Duplicate Filtering**: Removes tickets that were already marked as released in previous deployments
4. **Output Generation**: Produces a comma-separated list of ticket IDs for use in CI/CD workflows

#### Usage

```bash
# Extract tickets for staging release (includes RC versions)
node apps/ci-tools/jira-tickets-extractor-cli.js --for staging

# Extract tickets for production release (stable versions only)  
node apps/ci-tools/jira-tickets-extractor-cli.js --for production

# Using npm scripts
npm run jira-tickets -- --for staging
npm run jira-tickets -- --for production
```

#### Example Output

```bash
$ node jira-tickets-extractor-cli.js --for production

Latest two release tags: ["v2.43.0", "v2.44.0"]
Latest release tag: v2.44.0 (SHA: abc123) on Thu Jan 25 2024
Previous release tag: v2.43.0 (SHA: def456) on Wed Jan 24 2024

...

Conversion is completed. 3 Jira tickets to be added to the new release:
PLAT-1234, PLAT-1235, PLAT-1236
Execution time: 0s 43.694359ms
```

### JIRA Tickets Updater

The `jira-tickets-updater-cli.js` tool automatically updates JIRA tickets with fix versions, actual versions, and service labels during deployments.
This tool can be used both in CI/CD pipelines and locally by team members.

#### Key Features

- **Fix Version Updates**: Automatically sets fix versions from `version.json` or custom values
- **Actual Versions Updates**: Automatically sets actual versions from `version.json` or custom values
- **Service Label Detection**: Analyzes Git commits to determine which services were affected
- **Bulk Operations**: Uses JIRA's bulk API for efficiency with automatic fallback to individual updates
- **Parallel Processing**: Configurable parallel execution (1-100 tickets simultaneously)
- **Dry Run Mode**: Preview changes before applying them
- **Local Usage**: Team members can use personal JIRA credentials for local testing
- **Modular Architecture**: Separated concerns for API calls, analysis, validation, and execution

#### How Service Detection Works

1. **Commit Analysis**: For each ticket, analyzes all Git commits that mention the ticket ID
2. **File Path Extraction**: Extracts file paths from those commits
3. **Service Mapping**: Maps file paths to service directories:
   - `apps/client/*` → `client` label
   - `apps/server/*` → `server` label
   - `apps/discord-activity-tracker/*` → `discord-activity-tracker` label
   - `.circleci/*` → `circleci` label
   - `apps/shared/*` → `shared` label
4. **Label Grouping**: Groups tickets by detected labels for efficient bulk updates

#### Architecture Components

The tool is built with a modular design:
- **jira-api.js**: JIRA REST API client wrapper with retry logic
- **ticket-analyzer.js**: Git commit and file path analysis
- **label-analyzer.js**: Service detection and label grouping
- **version-resolver.js**: Version management and formatting
- **update-planner.js**: Plans updates and groups tickets
- **update-executor.js**: Orchestrates parallel JIRA updates
- **validators.js**: Input validation and error handling
- **result-builder.js**: Formats execution results

#### Local Usage for Team Members

Team members can use this tool locally with their personal JIRA credentials:

```bash
# Set up personal JIRA credentials
export JIRA_API_URL="https://turingcollege.atlassian.net"
export JIRA_API_TOKEN="your-personal-api-token"
export JIRA_EMAIL="<EMAIL>"

# Update tickets with both fix version and labels (auto-detected version)
node apps/ci-tools/jira-tickets-updater-cli.js --tickets "PLAT-1234,PLAT-5678" --update-fix-version --update-labels

# Update tickets with both actual versions and labels (auto-detected version)
node apps/ci-tools/jira-tickets-updater-cli.js --tickets "PLAT-1234,PLAT-5678" --update-actual-version --update-labels

# Dry run to preview changes (labels only)
node apps/ci-tools/jira-tickets-updater-cli.js --tickets "PLAT-1234" --update-labels --dry-run

# Update with custom fix version only
node apps/ci-tools/jira-tickets-updater-cli.js --tickets "PLAT-1234" --update-fix-version --fix-version "Release 2.46.1"

# Update with custom actual versions only
node apps/ci-tools/jira-tickets-updater-cli.js --tickets "PLAT-1234" --update-actual-version --actual-version "2.46.1"

# Update only labels, skip versions
node apps/ci-tools/jira-tickets-updater-cli.js --tickets "PLAT-1234" --update-labels

# Use custom parallel limit for large batches with all updates
node apps/ci-tools/jira-tickets-updater-cli.js --tickets "PLAT-1234,PLAT-5678,PLAT-9012" --update-fix-version --update-actual-version --update-labels --max-parallel 5

# Use custom ticket pattern for different project with labels
node apps/ci-tools/jira-tickets-updater-cli.js --tickets "ENG-123,ENG-456" --update-labels --ticket-regex "(ENG-[0-9]+)"
```

#### CLI Options

| Option | Alias | Type | Required | Description | Default | Example |
|--------|-------|------|----------|-------------|---------|----------|
| `--jira-url` | `-u` | string | Optional* | JIRA API URL | `JIRA_API_URL` env var | `https://company.atlassian.net` |
| `--jira-token` | `-x` | string | Optional* | JIRA API token | `JIRA_API_TOKEN` env var | `your-api-token` |
| `--jira-email` | `-e` | string | Optional* | JIRA account email | `JIRA_EMAIL` env var | `<EMAIL>` |
| `--tickets` | `-t` | string | **Required** | Comma-separated ticket IDs | - | `"PLAT-123,PLAT-456"` |
| `--fix-version` | `-v` | string | Optional | Custom fix version | Auto from version.json | `"Release 2.44.2"` |
| `--actual-version` | - | string | Optional | Custom actual versions | Auto from version.json | `"2.44.2"` |
| `--ticket-regex` | `-r` | string | Optional | Regex pattern for ticket validation | `([A-Z][A-Z0-9_]{1,9}-[0-9]+)` | `"(PLAT-[0-9]+)"` |
| `--apps-dir` | `-a` | string | Optional | Path to apps directory | `./apps` | `../apps` |
| `--update-fix-version` | - | boolean | Optional | Enable fix version updates | `false` | `--update-fix-version` |
| `--update-actual-version` | - | boolean | Optional | Enable actual versions updates | `false` | `--update-actual-version` |
| `--update-labels` | - | boolean | Optional | Enable service label updates | `false` | `--update-labels` |
| `--dry-run` | `-d` | boolean | Optional | Preview changes only | `false` | `--dry-run` |
| `--max-parallel` | `-p` | number | Optional | Parallel tickets processing limit (1-100) | `10` | `--max-parallel 20` |

**\*Required unless provided via environment variables**

> **⚠️ Important Change**: As of the latest version, the tool requires explicit flags to perform updates. By default, no updates are performed unless you specify `--update-fix-version`, `--update-actual-version` and/or `--update-labels`. This ensures intentional and controlled updates.

#### Example Dry Run Output

```bash
$ node apps/ci-tools/jira-tickets-updater-cli.js --tickets "PLAT-7337,PLAT-7344,PLAT-7349,PLAT-7353,PLAT-7355,PLAT-7357" --update-fix-version --update-labels --dry-run

Using provided tickets: PLAT-7337, PLAT-7344, PLAT-7349, PLAT-7353, PLAT-7355, PLAT-7357
Parallel processing limit: 10
Using version version-file: Release 2.45.0

Analyzing commits to determine service labels...
Processing up to 10 tickets in parallel...
Processing batch 1/1: PLAT-7337, PLAT-7344, PLAT-7349, PLAT-7353, PLAT-7355, PLAT-7357
Found 6 commits for ticket PLAT-7349
Found 6 commits for ticket PLAT-7355
Found 6 commits for ticket PLAT-7337
Found 6 commits for ticket PLAT-7344
Found 7 commits for ticket PLAT-7357
Found 6 commits for ticket PLAT-7353
Found 6 tickets with service labels (3 unique labels)
Getting version ID for "Release 2.45.0" in PLAT project
Found 1 matching version for project PLAT
Resolved version ID: 10559

Update plan:
- PLAT-7337:
  Fix Version: Release 2.45.0
  Labels: client, server, shared
- PLAT-7344:
  Fix Version: Release 2.45.0
  Labels: client
- PLAT-7349:
  Fix Version: Release 2.45.0
  Labels: client
- PLAT-7353:
  Fix Version: Release 2.45.0
  Labels: client, server
- PLAT-7355:
  Fix Version: Release 2.45.0
  Labels: client, server
- PLAT-7357:
  Fix Version: Release 2.45.0
  Labels: client, server

=== LABEL UPDATE ANALYSIS ===
Found 3 unique label combination(s):

Group 1: [client, server, shared]
  Tickets: PLAT-7337 (1 ticket)
  Action: INDIVIDUAL UPDATE - one API call
Group 2: [client]
  Tickets: PLAT-7344, PLAT-7349 (2 tickets)
  Action: BULK UPDATE - one API call for 2 tickets
Group 3: [client, server]
  Tickets: PLAT-7353, PLAT-7355, PLAT-7357 (3 tickets)
  Action: BULK UPDATE - one API call for 3 tickets

=== LABEL API EFFICIENCY SUMMARY ===
Total tickets: 6
Tickets to update: 6
Tickets to skip: 0
Bulk API calls: 2
Individual API calls: 1
Total API calls needed: 3

DRY RUN - No changes were made to Jira.
Execution time: 1s 278ms
```

#### CI/CD Integration

In the CI/CD pipeline, the tool is automatically triggered after staging and production deployments. **Note**: CI/CD uses actual version updates (not fix version) along with service labels:

```yaml
- run:
    name: Update release Jira tickets with actual version and service labels
    command: |
      # Extract tickets from version bump commit message
      commit_message=$(git show -s --format=%s $CIRCLE_SHA1)
      tickets=$(echo "$commit_message" | sed -E 's/.*\(([^)]+)\)$/\1/')

      # Update tickets with actual version and labels
      node "<< pipeline.parameters.ci-tools-path >>/jira-tickets-updater-cli.js" \
        --tickets="$tickets" \
        --update-actual-version \
        --update-labels \
        --ticket-regex="(PLAT-[0-9]+)"
```

**How the CI Integration Works:**
1. **Extract Tickets**: Uses regex to extract ticket IDs from the version bump commit message (e.g., "build: Bump up version to 2.45.0 [ci skip] (PLAT-7344, PLAT-7357)")
2. **Update Tickets**: Calls the updater CLI with the extracted ticket list
3. **Automatic Configuration**: Uses CI environment variables for JIRA credentials
4. **Service Detection**: Automatically analyzes Git history to determine affected services
5. **Version Resolution**: Reads the current version from `version.json` for actual version updates (preserves RC versions as-is)

#### Troubleshooting JIRA Updater Issues

1. **Authentication Failed**:
   ```
   Error: JIRA authentication failed: 401 Unauthorized
   ```
   **Resolution**: Verify your JIRA credentials. For personal use, ensure your API token is valid and hasn't expired.

2. **Version Not Found**:
   ```
   Error: Version "Release 2.45.0" not found in JIRA project
   ```
   **Resolution**: The version must exist in JIRA before updating tickets. Create the version in JIRA or use a different version name.

3. **Invalid Ticket Format**:
   ```
   Warning: The following tickets do not match the required format and will be excluded: ABC-123
   ```
   **Resolution**: Use the `--ticket-regex` option to specify the correct pattern for your project's ticket format.

4. **API Rate Limiting**:
   ```
   Error: Too many requests. Please try again later.
   ```
   **Resolution**: Reduce the `--max-parallel` value or wait before retrying. The default of 10 is usually safe.

5. **Network Timeout**:
   ```
   Error: Request timeout after 30000ms
   ```
   **Resolution**: Check your network connection. The tool will automatically retry failed requests up to 3 times.

6. **Bulk Update Failed**:
   ```
   Warning: Bulk update failed, falling back to individual updates
   ```
   **Information**: This is handled automatically. The tool will update tickets one by one if bulk operations fail.

7. **No Updates Performed**:
   ```
   Error: At least one of --update-fix-version, --update-actual-version or --update-labels must be explicitly provided
   ```
   **Resolution**: The tool now requires explicit flags to perform updates. Add `--update-fix-version`, `--update-actual-version` and/or `--update-labels` to your command.

#### Best Practices

1. **Always use dry-run first** when testing locally to preview changes
2. **Explicitly specify update flags** - use `--update-fix-version`, `--update-actual-version` and/or `--update-labels` as needed
3. **Set up environment variables** for JIRA credentials to avoid passing them on every command
4. **Use appropriate parallel limits** based on your JIRA instance capacity
5. **Keep ticket lists reasonable** - for very large updates, consider batching
6. **Monitor service labels** to ensure accurate tracking of affected components

### CI/CD Integration Examples

#### JIRA Deployment Notification
```yaml
- jira/notify:
    environment: production
    environment_type: production
    job_type: deployment
    pipeline_id: << pipeline.id >>
    pipeline_number: << pipeline.number >>
    issue_regexp: (PLAT-[0-9]+)
```

## Version Bumping

The pipeline automatically manages version numbers using a centralized approach:

### Centralized Version Management

A single `version.json` file at the repository root serves as the source of truth for the entire platform version. The version is managed using the `apps/ci-tools/repo-version-cli.js` tool.

For detailed information about the version management system, refer to [VERSION_MANAGEMENT.md](../VERSION_MANAGEMENT.md).

### Staging (RC) Version Bumping

For release branches (release/*, hotfix/*, patch/*):
- **Hotfix/Patch Branches**: Bumps prerelease version (e.g., 1.2.3 → 1.2.4-rc.0)
- **Release Branches**: Bumps preminor version with RC tag (e.g., 1.2.3 → 1.3.0-rc.0)

### Production Version Bumping

For the main branch:
- The RC suffix is removed to create a stable version (e.g., 1.3.0-rc.1 → 1.3.0)

Version info is stored in the repository root's `version.json` file and committed with a message like:
`build: Bump up version to v2.40.0 [ci skip] (PLAT-1234, PLAT-5678)`

## Post-Deployment Merging

After a successful deployment to staging or production, changes are automatically merged back to the develop branch:

### How It Works

1. After a successful deployment, the `trigger-merge-back-to-develop` job is triggered
2. This job uses the CircleCI API to start the `merge-changes-back-to-develop` workflow
3. The workflow:
    - Checks out the code
    - Merges changes from the deployed branch (e.g., main or release branch) into develop using `--no-ff`
    - Preserves version information in the merge commit message
    - Pushes the changes back to the develop branch

### Execution Conditions

- The automatic merge back happens for any non-development environment (staging and production)
- The process is enabled by setting the `should-merge-back` parameter to `true`
- The merge will be skipped in either of these cases:
    - If the environment variable `$IS_MERGE_BACK_TO_DEVELOP_SKIPPED` is set to `true`
    - If the pipeline parameter `should-merge-back` is set to `false` (default)
- If the automatic merge fails, you will need to perform a manual merge

### Handling Merge Conflicts

- If merge conflicts cannot be solved automatically, the `merge-changes-back-to-develop` workflow fails
- No automatic merging is performed in this case
- When this happens, manual intervention is required to merge the changes

### Manual Merge Process

If the automatic process fails, you can manually merge changes:
```bash
git checkout develop
git pull
git merge --no-ff -m "build: Merge release/vX.Y.Z into develop" main
git push origin develop
```

## Troubleshooting

### Common Issues

1. **Pipeline Fails at Change Detection**:
    - Check if CircleCI token has correct permissions
    - Verify branch exists on the remote repository
    - Try running with `force-run: true`

2. **Deployment Fails**:
    - Check AWS credentials and permissions
    - Verify branch/tag/commit exists
    - Review deployment logs for errors

3. **Version Bumping Fails**:
    - Verify Git configuration
    - Check for version.json conflicts or permission issues
    - Ensure branch name follows the required conventions (see [VERSION_MANAGEMENT.md](../VERSION_MANAGEMENT.md))
    - Verify the tag doesn't already exist in the remote repository

4. **Merge Back Fails**:
    - Look for conflicts between source and target branches
    - Check if there are uncommitted changes
    - Manually merge changes as described in the [Manual Merge Process](#manual-merge-process) section

## CI Tools Reference

The repository includes several CI tools in the `apps/ci-tools` directory to facilitate CI/CD processes:

| Tool | Purpose | Usage | Local Usage |
|------|---------|-------|-------------|
| `create-release-branch-cli.js` | Create release, patch, or hotfix branches | `npm run create-branch:[release\|patch\|hotfix] -- --branch=develop\|main` | ✅ Available locally |
| `ci-change-detector-cli.js` | Detect changed files for optimized builds | `npm run change-detector` | ✅ Available locally |
| `jira-tickets-extractor-cli.js` | Extract JIRA tickets for release notes | `npm run jira-tickets -- --for=staging\|production` | ✅ Available locally |
| `jira-tickets-updater-cli.js` | Update JIRA tickets with fix versions, actual versions and service labels | `node apps/ci-tools/jira-tickets-updater-cli.js --tickets="PLAT-123,PLAT-456" --update-actual-version --update-labels` | ✅ **Personal credentials supported** |
| `repo-version-cli.js` | Manage version information | `npm run version:update\|version:create-tag\|version:set-stable` | ✅ Available locally |

These tools are integrated into the CI/CD pipeline to automate processes but can also be used by developers locally to follow standardized workflows.

### Local Usage with Personal Credentials

The JIRA-related tools (`jira-tickets-extractor-cli.js` and `jira-tickets-updater-cli.js`) can be used locally by team members with their personal JIRA credentials:

1. **Set up personal JIRA API token**: 
   - Go to [Atlassian Account Settings](https://id.atlassian.com/manage-profile/security/api-tokens)
   - Click "Create API token"
   - Give it a descriptive name (e.g., "CLI Tool Access")
   - Copy the token immediately (you won't be able to see it again)
2. **Configure environment variables**:
   ```bash
   export JIRA_API_URL="https://turingcollege.atlassian.net"
   export JIRA_API_TOKEN="your-personal-api-token"
   export JIRA_EMAIL="<EMAIL>"
   ```
3. **Use the tools locally**: All JIRA functionality will work with your personal credentials

This enables developers to:
- Test JIRA ticket updates before pushing to CI/CD
- Generate release notes locally
- Update tickets during local development and testing
- Validate ticket detection and service labeling

For detailed information on each tool, refer to their respective command-line help (`--help`) or the source code documentation.

---

For more information, refer to CircleCI documentation and project wikis.
