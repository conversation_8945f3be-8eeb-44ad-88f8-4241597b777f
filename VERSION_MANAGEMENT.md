# Centralized Version Management

This document describes the centralized versioning system for the monorepo.

## Overview

The versioning system provides:

1. A central `version.json` file at the repo root
2. Tools to validate release branch names 
3. Automatic version management based on branch names
4. Release tag creation
5. Main branch stable version handling

## Branch Naming Convention

Release branches must follow this pattern:
- `release/vX.Y.Z` - For feature releases
- `patch/vX.Y.Z` or `hotfix/vX.Y.Z` - For bug fixes and minor adjustments

## Command Usage

The following tools are available for version management:

### Using npm scripts

```bash
# View current version
cd apps/ci-tools
npm run version:get

# Update version based on branch name (for release branches)
cd apps/ci-tools
npm run version:update -- --branch="release/v2.3.0"

# Set stable version on main branch (removes RC suffix)
cd apps/ci-tools
npm run version:set-stable -- --main-branch

# Dynamically determine if current branch is main
cd apps/ci-tools
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
IS_MAIN=$([ "$CURRENT_BRANCH" == "main" ] && echo "true" || echo "false")
npm run version:set-stable -- --main-branch=$IS_MAIN

# Create and push a version tag
cd apps/ci-tools
npm run version:create-tag
```

### Direct Node.js Usage

```bash
# View current version
cd apps/ci-tools
node -e "console.log(JSON.stringify(require('./version-utils').getVersion(), null, 2))"

# Update version based on branch name (for release branches)
cd apps/ci-tools
node repo-version-cli.js update-version --branch="release/v2.3.0"

# Set stable version on main branch (removes RC suffix)
cd apps/ci-tools
node repo-version-cli.js set-stable-version --main-branch

# Create and push a version tag
cd apps/ci-tools
node repo-version-cli.js create-tag
```

## CircleCI Workflow Integration

### Release Branch Processing

For release branches, add a step to validate and update the version early in the workflow:

```yaml
- run:
    name: Update version for release branch
    command: |
      # Only process release branches
      if [[ "$CIRCLE_BRANCH" =~ ^(release|patch|hotfix)/v[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        cd << pipeline.parameters.ci-tools-path >>
        npm run version:update -- --branch="$CIRCLE_BRANCH"
        git add ../../version.json
        git commit -m "build: Bump up version to RC [skip ci]"
        git push origin $CIRCLE_BRANCH
      fi
```

### Main Branch Processing

When building on the main branch, set the stable version:

```yaml
- run:
    name: Set stable version on main branch
    command: |
      if [[ "$CIRCLE_BRANCH" == "main" ]]; then
        cd << pipeline.parameters.ci-tools-path >>
        npm run version:set-stable -- --main-branch=true
        git add ../../version.json
        git commit -m "build: Bump up version [skip ci]"
        git push origin main
      else
        echo "Not on main branch, skipping stable version setting"
      fi
```

### Tag Creation

Before backend deployments, create a version tag to identify the build:

```yaml
- run:
    name: Create version tag
    command: |
      cd << pipeline.parameters.ci-tools-path >>
      npm run version:create-tag
```

## How It Works

1. **Release Branch Processing**:
   - The branch name is validated against the pattern (release|patch|hotfix)/vX.Y.Z
   - The version.json file is updated with either:
     - A new version based on the branch name (e.g., 2.3.0-rc.0)
     - An incremented RC version if the version already exists (e.g., 2.3.0-rc.1)

2. **Main Branch Processing**:
   - The RC suffix is removed, setting the stable version (e.g., 2.3.0)
   - Changes are committed to the main branch

3. **Tag Creation**:
   - A git tag is created based on the base version (without RC suffix)
   - The tag is pushed to the remote repository
   - This tag is used for deployment identification

## Release Process

1. Create a release branch following the naming convention:
   - For new features: `release/vX.Y.0`
   - For bug fixes and minor adjustments: `patch/vX.Y.Z` or `hotfix/vX.Y.Z`

2. CircleCI will automatically:
   - Validate the branch name
   - Set the appropriate version with RC suffix
   - Build and deploy to staging environments

3. After successful testing, merge to main:
   - Main branch automatically sets stable version
   - Deployments use the stable version
