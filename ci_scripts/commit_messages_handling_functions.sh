process_latest_release_jira_commit_messages() {
    if [ "$#" -ne 1 ]; then
        # Enable tracing
        set -x
        echo "No latest release commit messages are supplied!" >&2
        # Disable tracing
        set +x
        return 1
    fi

    local messages
    messages=${1#*$'\n'} # remove the first line
    messages=$(echo "$messages" | sed '$d') # remove the last line
    messages=$(echo "$messages" | grep -viE "revert.*PLAT-[0-9]+") # remove the revert messages
    echo "$messages"
}

function print_with_indentation {
    if [ "$#" -ne 1 ]; then
        # Enable tracing
        set -x
        echo "No message is supplied!" >&2
        # Disable tracing
        set +x
        return 1
    fi
    echo "$1" | while IFS= read -r line; do echo -e "  $line"; done
    echo # empty line
}
