extract_jira_keys_from_commit_messages() {
    if [ "$#" -ne 1 ]; then
        # Enable tracing
        set -x
        echo "No commit messages are supplied!" >&2
        # Disable tracing
        set +x
        return 1
    fi

    local jira_keys
    jira_keys=$(echo "$1" | tr ' ' '\n' | grep -oiE 'PLAT-[0-9]+' | sort -u | head -n 500) || true
    echo "$jira_keys"
}

concatenate_jira_keys() {
    if [ "$#" -ne 2 ]; then
        # Enable tracing
        set -x
        echo "Both remaining keys from previous release and new release Jira keys should be supplied!" >&2
        echo "Current parameters: \"$1\", \"$2\"" >&2
        # Disable tracing
        set +x
        return 1
    fi

    local final_list=""
    if [[ -n "$1" ]] && [[ -n "$2" ]]; then
        final_list="${1}\n${2}"
    elif [[ -n "$1" ]]; then
        final_list="${1}"
    elif [[ -n "$2" ]]; then
        final_list="${2}"
    fi
    final_list=$(echo "$final_list" | sort -u | head -n 500)
    echo "$final_list"
}

convert_jira_keys_to_string() {
    if [ -z "$1" ]; then
        # Enable tracing
        set -x
        echo "No Jira keys are supplied!" >&2
        # Disable tracing
        set +x
        return 1
    fi

    local jira_keys_string
    jira_keys_string=$(echo "$1" | tr '\n' ', ')
    jira_keys_string=${jira_keys_string%,}
    jira_keys_string=${jira_keys_string//,/, }
    jira_keys_string=" ($jira_keys_string)"
    echo "$jira_keys_string"
}

filter_keys() {
    if [ "$#" -ne 2 ]; then
        # Enable tracing
        set -x
        echo "Both list of keys (initial list and keys to be filtered) should be supplied!" >&2
        echo "Current parameters: \"$1\", \"$2\"" >&2
        # Disable tracing
        set +x
        return 1
    fi
    local keys=$1
    local keys_to_be_filtered=$2

    if [ -z "$keys_to_be_filtered" ]
    then
        echo "$keys"
    else
        # echo "$keys" | grep -vFf <(echo "$keys_to_be_filtered") # wasn't working on CI, so the script below does the same
        echo "$keys" | while read -r line; do
            if ! echo "$keys_to_be_filtered" | grep -q "$line"; then
                echo "$line"
            fi
        done
    fi
}
