#!/bin/bash

set -eo pipefail
export PS4='+(${BASH_SOURCE}:${LINENO}): ${FUNCNAME[0]:+${FUNCNAME[0]}(): }'

. ci_scripts/git_data_functions.sh
. ci_scripts/jira_keys_handling_functions.sh
. ci_scripts/commit_messages_handling_functions.sh

releases=$(git tag -l | grep -E '^v[0-9]+\.[0-9]+\.[0-9]+$' | sort -V)
latest_two_release_tags=$(echo "$releases" | tail -n 2)

latest_release_tag=$(echo "$latest_two_release_tags" | tail -n 1)
latest_release_tag_sha=$(get_tag_hash "$latest_release_tag")
latest_release_tag_date=$(get_tag_date "$latest_release_tag")

previous_release_tag=$(echo "$latest_two_release_tags" | head -n 1)
previous_release_tag_sha=$(get_tag_hash "$previous_release_tag")
previous_release_tag_date=$(get_tag_date "$previous_release_tag_sha")

echo "Latest release tag: $latest_release_tag (SHA: ${latest_release_tag_sha:0:9}) on $latest_release_tag_date"
echo -e "Previous release tag: $previous_release_tag (SHA: ${previous_release_tag_sha:0:9}) on $previous_release_tag_date\n"

upcoming_release_commit_messages=$(get_upcoming_release_commit_messages "$latest_release_tag_date")
echo -e "Upcoming release commit messages:"
print_with_indentation "$upcoming_release_commit_messages"

latest_release_commit_messages=$(git log --pretty=format:"%s" --after="$previous_release_tag_date" --before="$latest_release_tag_date")
echo -e "Latest release ($latest_release_tag) commit messages:"
print_with_indentation "$(echo "$latest_release_commit_messages" | sed '$d')" # remove the last line (previous release tag commit message)

# get the commit message of the previous release tag (grep removes the "v" prefix)
latest_release_commit_message_with_released_jira_keys=$(echo "$latest_release_commit_messages" | grep "to ${latest_release_tag#*v} ") || true
echo -e "Latest release ($latest_release_tag) commit messages with already released tickets:"
print_with_indentation "$latest_release_commit_message_with_released_jira_keys"

previous_release_commit_message_with_released_jira_keys=$(echo "$latest_release_commit_messages" | grep "to ${previous_release_tag#*v} ") || true
echo -e "Previous release ($previous_release_tag) commit messages with already released tickets:"
print_with_indentation "$previous_release_commit_message_with_released_jira_keys"

new_jira_keys=$(extract_jira_keys_from_commit_messages "$upcoming_release_commit_messages")
if [[ -z $new_jira_keys ]]; then
  echo -e "No new Jira tickets to be listed in the new release."
else
  echo -e "New Jira tickets to be listed in the new release:\n$new_jira_keys"
fi

processed_latest_release_commit_messages=$(process_latest_release_jira_commit_messages "$latest_release_commit_messages")
latest_release_jira_keys=$(extract_jira_keys_from_commit_messages "$processed_latest_release_commit_messages")
echo -e "All Jira tickets listed in the latest release ($latest_release_tag):\n$latest_release_jira_keys"

latest_release_released_jira_keys=$(extract_jira_keys_from_commit_messages "$latest_release_commit_message_with_released_jira_keys")
echo -e "Already released Jira tickets noted in $latest_release_tag commit message:\n$latest_release_released_jira_keys"

previous_release_released_jira_keys=$(extract_jira_keys_from_commit_messages "$previous_release_commit_message_with_released_jira_keys")
echo -e "Already released Jira tickets noted in $previous_release_tag commit message:\n$previous_release_released_jira_keys"

released_jira_keys_list=$(concatenate_jira_keys "$latest_release_released_jira_keys" "$previous_release_released_jira_keys")
echo -e "Already released Jira tickets noted in $latest_release_tag and $previous_release_tag commit messages:\n$released_jira_keys_list\n"

#remaining_latest_release_jira_keys=$(echo "$latest_release_jira_keys" | grep -vFf <(echo "$released_jira_keys_list")) || true
remaining_latest_release_jira_keys=$(filter_keys "$latest_release_jira_keys" "$released_jira_keys_list")
echo -e "Remaining already released Jira tickets (except the ones noted as deployed):\n$remaining_latest_release_jira_keys"

#remaining_upcoming_release_jira_keys=$(echo "$new_jira_keys" | grep -vFf <(echo "$released_jira_keys_list")) || true
remaining_upcoming_release_jira_keys=$(filter_keys "$new_jira_keys" "$released_jira_keys_list")
echo -e "Remaining new Jira tickets (except the ones noted as deployed):\n$remaining_upcoming_release_jira_keys"

final_jira_keys_list=$(concatenate_jira_keys "$remaining_latest_release_jira_keys" "$remaining_upcoming_release_jira_keys")
if [[ -n $final_jira_keys_list ]]; then
  echo -e "Final list of Jira tickets to be listed in the new release:\n$final_jira_keys_list"
fi

jira_keys_string=""
if [[ -n $final_jira_keys_list ]]; then
  echo "Jira keys are extracted. Converting keys list to a single line string."
  jira_keys_string=$(convert_jira_keys_to_string "$final_jira_keys_list")
  echo -e "Conversion is completed. Jira tickets to be added to the new release:\n$jira_keys_string"
else
  echo "No Jira tickets are found to be added to the new release."
fi
