#######################################
# Git data functions
#######################################
# This script contains functions to get git data such as tags, commit hashes, commit messages, and Jira tickets.

#######################################
# Function: get_all_release_and_rc_tags
# Description: Get all release and release candidate tags
# the following logic is applied:
# # list all tags and select only all release and RC tags
# (there should be no others, but I filter only what I really need (just in case))
# # append "-rc.1000" for the release tags (required for proper sorting as v2.9.4 would become v2.9.4-rc.1000)
# # sort by version (asc)
# # remove "-rc.1000" from the end of lines so that eventually release versions are as they should (v2.9.4-rc.1000 would become v2.9.4)
# Output: Print all release and release candidate tags in ascending order to the stdout
get_all_release_and_rc_tags() {
    local data

    data=$(git tag -l | grep -E '^v[0-9]+\.[0-9]+\.[0-9]+(-rc\.[0-9]+)?$' | sed -e '/-rc\./!s/$/-rc.1000/' | sort -V | sed 's/-rc\.1000$//')
    echo "$data"
}

get_tag_hash() {
    if [ "$#" -ne 1 ]; then
        # Enable tracing
        set -x
        echo "No tag version is supplied!" >&2
        # Disable tracing
        set +x
        return 1
    fi

    git rev-parse "$1^{commit}"
}

get_tag_date() {
    if [ "$#" -ne 1 ]; then
        # Enable tracing
        set -x
        echo "No tag version is supplied!" >&2
        # Disable tracing
        set +x
        return 1
    fi

    git log -1 --pretty=format:"%cd" "$1"
}

get_upcoming_release_commit_messages() {
    if [ "$#" -ne 1 ]; then
        # Enable tracing
        set -x
        echo "No latest release tag date is supplied!" >&2
        # Disable tracing
        set +x
        return 1
    fi

    local messages
    messages=$(git log --after="$1" --pretty=format:"%s")
    messages=$(echo "$messages" | sed '$d') # remove the last line
    echo "$messages"
}
