name: Add Jira link
on: 
  pull_request:
    types: [opened]
jobs:
  add-jira-link:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v2

      - name: Extract Jira issue keys
        id: extract-keys
        run: |
          BRANCH_NAME="${{ github.event.pull_request.head.ref }}"
          # Extract Jira keys and format them correctly
          KEYS=$(echo "$BRANCH_NAME" | grep -oE 'PLAT-[0-9]+' | tr '\n' ' ')
          KEYS=$(echo "$KEYS" | xargs)  # Remove any leading/trailing spaces
          echo "keys=$KEYS" >> $GITHUB_ENV

      - name: Comment on PR
        uses: actions/github-script@v7
        with:
          github-token: ${{secrets.GITHUB_TOKEN}}
          script: |
            const jiraKeys = process.env.keys.trim();
            const prNumber = context.payload.pull_request.number;
            const repoOwner = context.repo.owner;
            const repoName = context.repo.repo;
            
            if (jiraKeys) {
              const jiraLinks = jiraKeys.split(' ').map(key => `[${key}](https://turingcollege.atlassian.net/browse/${key})`).join(', ');
              github.rest.issues.createComment({
                issue_number: prNumber,
                owner: repoOwner,
                repo: repoName,
                body: `This PR is related to the following Jira tickets: ${jiraLinks}`,
              })
            }

