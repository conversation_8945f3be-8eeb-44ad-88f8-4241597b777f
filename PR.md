# Create UZT learner schedules immediately after signup completion

## Description
This PR implements a solution to create learner schedules immediately after signup completion, rather than waiting for the full onboarding process to complete. This addresses two key issues:

- Eliminates the need to wait for full onboarding before making schedule changes
- Fixes the race condition that occurs when navigating to schedules immediately after onboarding (PLAT-7187)

The implementation triggers default schedule creation upon `SignupCompletedEvent`, without waiting for the employment type to be determined (which is no longer used for schedules).

### Implementation Details
- Schedules are now created as soon as the user completes the signup process
- Default schedules cover the entire UZT period with appropriate time slots
- The system uses the `SignupCompletedEvent` to initiate schedule creation

### Migration
The PR includes migration handling for users who are currently in the onboarding process when deploying this change. This ensures that all UZT learners will have a default schedule available, preventing scenarios where users might have no schedule at all.

## Testing
- Verified that default schedules are generated correctly immediately after signup completion
- Confirmed that schedules are available before the learner completes full onboarding
- Tested navigation to schedule page immediately after signup
- Validated migration process for users in the onboarding state

## Related Issues
[PLAT-7187](https://turingcollege.atlassian.net/browse/PLAT-7187)
