# Turing college platform

## Applications

This repo contains all project related to Turing College platform. All the projects are in the `apps` directory.
Refer to particular project README for more information on dependencies and setup.

### Server, Backend [(readme)](./apps/server/README.md)

Main backend application monolith that responsible for all the business logic and data storage.

### Client, Frontend [(readme)](./apps/client/README.md)

Main react frontend application that responsible for all the user interactions and data presentation.

### Discord activity tracker [(readme)](./apps/discord-activity-tracker/README.md)

Application that tracks user activity on Discord server.

### Other root folders

#### .circleci [(readme)](./.circleci/README.md)

Contains all the CircleCI workflows and jobs.

#### ci_scripts

Contains all the scripts that are used in the CI/CD workflows.

#### .run

Contains run configurations for Webstorm editor for easy bootstrap.

#### .github

Contains all the GitHub actions and workflows like Dependabot, Auto-labeler, Jira integration, etc.

## Git flow and deployments

[See wiki](https://github.com/TuringCollege/turing-college-platform/wiki/Git-flow-and-deployments)
